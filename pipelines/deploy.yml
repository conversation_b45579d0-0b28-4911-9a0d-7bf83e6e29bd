trigger:
  - contract

pool:
  vmImage: "ubuntu-latest"

variables:
  buildConfiguration: "Release"
  apiProject: "WHO.MALARIA.Web/WHO.MALARIA.Web.csproj"
  reactAppDir: "WHO.MALARIA.Web/malaria-client"

steps:
  # Install .NET 8 SDK
  - task: UseDotNet@2
    displayName: "Install .NET 8 SDK"
    inputs:
      packageType: "sdk"
      version: "8.x"
      includePreviewVersions: false

  # Restore .NET dependencies
  - task: DotNetCoreCLI@2
    displayName: "Restore .NET packages"
    inputs:
      command: "restore"
      projects: "$(apiProject)"

  # Build .NET Web API
  - task: DotNetCoreCLI@2
    displayName: "Build .NET Web API"
    inputs:
      command: "build"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration)"

  # Install Node.js 22.15.1
  - task: UseNode@1
    displayName: "Install Node.js 22.15.1"
    inputs:
      version: "22.15.1"

  # Install frontend dependencies
  - task: Npm@1
    displayName: "Install frontend dependencies"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "install"

  # Build frontend (React)
  - task: Npm@1
    displayName: "Build frontend"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "custom"
      customCommand: "run build"

  # List build directory contents for debugging
  - script: |
      echo "=== REACT BUILD VERIFICATION ==="
      echo "Contents of build directory:"
      ls -la $(reactAppDir)/build/ || echo "Build directory does not exist"
      echo ""
      echo "Checking for index.html:"
      ls -la $(reactAppDir)/build/index.html || echo "index.html not found"
      echo ""
      echo "Checking for assets directory:"
      ls -la $(reactAppDir)/build/assets/ || echo "assets directory not found"
      echo ""
      echo "Total files in build directory:"
      find $(reactAppDir)/build -type f | wc -l || echo "Cannot count files"
    displayName: "Debug: Verify React build output"

  # Publish .NET Web API
  - task: DotNetCoreCLI@2
    displayName: "Publish Web API"
    inputs:
      command: "publish"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api"

  # Copy frontend build to published API root (for Azure App Service deployment)
  - task: CopyFiles@2
    displayName: "Copy frontend build to published API root"
    inputs:
      SourceFolder: "$(reactAppDir)/build"
      Contents: "**"
      TargetFolder: "$(Build.ArtifactStagingDirectory)/api"
      flattenFolders: false
      preserveTimestamp: true
      OverWrite: true
    condition: succeeded()

  # Additional verification - list what was actually copied
  - script: |
      echo "=== COPY VERIFICATION ==="
      echo "Source directory contents (React build):"
      ls -la $(reactAppDir)/build/ || echo "Build directory not found"
      echo ""
      echo "Target directory contents (after copy):"
      ls -la $(Build.ArtifactStagingDirectory)/api/ || echo "Target directory not found"
      echo ""
      echo "Specifically looking for index.html:"
      find $(Build.ArtifactStagingDirectory)/api -name "index.html" -type f || echo "index.html not found anywhere in target"
    displayName: "Debug: Verify file copy operation"

  # Verify copied files for debugging
  - script: |
      echo "Contents of published API root directory (Azure App Service structure):"
      ls -la $(Build.ArtifactStagingDirectory)/api/
      echo ""
      echo "Checking for index.html in API root:"
      ls -la $(Build.ArtifactStagingDirectory)/api/index.html || echo "index.html not found in API root"
      echo ""
      echo "Checking for React assets:"
      ls -la $(Build.ArtifactStagingDirectory)/api/assets/ || echo "assets directory not found"
      echo ""
      echo "Complete artifact structure (HTML and key files):"
      find $(Build.ArtifactStagingDirectory) -type f -name "*.html" -o -name "*.dll" -o -name "WHO.MALARIA.Web" | head -20
    displayName: "Debug: Verify Azure App Service deployment structure"

  # Publish build artifacts
  - task: PublishBuildArtifacts@1
    displayName: "Publish deployment artifacts"
    inputs:
      PathtoPublish: "$(Build.ArtifactStagingDirectory)"
      ArtifactName: "drop"
      publishLocation: "Container"

  # Publish build artifacts
  - task: PublishBuildArtifacts@1
    displayName: "Publish Artifact: drop"
    inputs:
      PathtoPublish: "$(Build.ArtifactStagingDirectory)"
      ArtifactName: "drop"
      publishLocation: "Container"
