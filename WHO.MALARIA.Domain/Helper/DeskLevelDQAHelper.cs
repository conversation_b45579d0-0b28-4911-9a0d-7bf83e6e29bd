﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;

namespace WHO.MALARIA.Domain.Helper
{
    public static class DeskLevelDQAHelper
    {
        /// <summary>
        /// Get custom columns for the type
        /// </summary>
        /// <param name="type">Type of class</param>
        /// <returns>List of instance of TableColumn</returns>
        public static List<TableColumn> GetReportTableColumnResponse(Type type)
        {
            List<JObject> rowsJObject = new List<JObject>();

            PropertyInfo[] propertyInfos = (from property in type.GetProperties()
                                            where Attribute.IsDefined(property, typeof(TableColumnAttribute))
                                            orderby ((TableColumnAttribute)property
                                                      .GetCustomAttributes(typeof(TableColumnAttribute), false)
                                                      .Single()).Order
                                            select property).ToArray();

            List<TableColumn> columns = new List<TableColumn>();

            foreach (PropertyInfo property in propertyInfos)
            {
                List<dynamic> objectAttributes = property.GetCustomAttributes(true).ToList();

                objectAttributes.ForEach(objectAttribute =>
                {
                    TableColumnAttribute columnAttribute = objectAttribute as TableColumnAttribute;

                    if (columnAttribute != null)
                    {
                        columns.Add(new TableColumn(property.Name.Split(',').ToList(), columnAttribute.Width));
                    }
                });
            }

            return columns;
        }

        /// <summary>
        /// Get custom columns for the type
        /// </summary>
        /// <param name="type">Type of class</param>
        /// <returns>List of instance of TableColumnAttribute</returns>
        public static List<TableColumnAttribute> GetCustomDescriptionForColumn(Type type)
        {
            List<JObject> rowsJObject = new List<JObject>();

            PropertyInfo[] propertyInfos = (from property in type.GetProperties()
                                            where Attribute.IsDefined(property, typeof(TableColumnAttribute))
                                            orderby ((TableColumnAttribute)property
                                                      .GetCustomAttributes(typeof(TableColumnAttribute), false)
                                                      .Single()).Order
                                            select property).ToArray();

            List<TableColumnAttribute> columns = new List<TableColumnAttribute>();

            foreach (PropertyInfo property in propertyInfos)
            {
                List<dynamic> objectAttributes = property.GetCustomAttributes(true).ToList();

                objectAttributes.ForEach(objectAttribute =>
                {
                    TableColumnAttribute columnAttribute = objectAttribute as TableColumnAttribute;

                    if (columnAttribute != null)
                    {
                        columns.Add(new TableColumnAttribute(columnAttribute.Name, columnAttribute.TranslationKey, columnAttribute.Width, columnAttribute.Order));
                    }
                });
            }

            return columns;
        }
    }
}
