﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;

namespace WHO.MALARIA.Domain.Helper
{
    /// <summary>
    /// Class contain analytical output helper method
    /// </summary>
    public static class AnalyticalOutputIndicatorResponseHelper
    {
        /// <summary>
        /// Get analytical output table type response for indicator
        /// </summary>
        /// <param name="type">Response class's type</param>
        /// <param name="data">Response captured for the indicator</param>   
        /// <param name="translator">Delegate object which is used for translation</param>    
        /// <returns> Returns analytical output table type response for indicator </returns>
        public static TableResponse GetAnalyticalOutputIndicatorTable(Type type, IEnumerable data, Delegate translator)
        {
            List<JObject> rowsJObject = new List<JObject>();

            PropertyInfo[] propertyInfos = (from property in type.GetProperties()
                                            where Attribute.IsDefined(property, typeof(TableColumnAttribute))
                                            orderby ((TableColumnAttribute)property
                                                      .GetCustomAttributes(typeof(TableColumnAttribute), false)
                                                      .Single()).Order
                                            select property).ToArray();

            List<Column> columns = new List<Column>();

            foreach (PropertyInfo property in propertyInfos)
            {
                List<dynamic> objectAttributes = property.GetCustomAttributes(true).ToList();

                objectAttributes.ForEach(objectAttribute =>
                {
                    TableColumnAttribute analyticalOutputAttribute = objectAttribute as TableColumnAttribute;

                    if (analyticalOutputAttribute != null)
                    {
                        string displayLabel = translator.DynamicInvoke(analyticalOutputAttribute.TranslationKey).ToString();

                        columns.Add(new Column(property.Name, displayLabel, analyticalOutputAttribute.Width));
                    }
                });
            }

            TableResponse table = new TableResponse();

            foreach (dynamic record in data)
            {
                JObject rowJOject = new JObject();

                foreach (Column columnName in columns)
                {
                    dynamic propertyInfo = record.GetType().GetProperty(columnName.Key);
                    dynamic value = propertyInfo.GetValue(record, null);
                    rowJOject.Add(columnName.Key, value);
                }
                rowsJObject.Add(rowJOject);
            }

            table.Rows = rowsJObject;

            table.Columns = columns;

            return table;
        }
    }
}