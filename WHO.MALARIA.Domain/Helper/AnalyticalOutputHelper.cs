﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Reflection;
using WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.Helper
{
    /// <summary>
    /// Class contain analytical output helper method
    /// </summary>
    public static class AnalyticalOutputHelper
    {
        /// <summary>
        /// Returns datatable base on type and data
        /// </summary>
        /// <param name="type"> Object</param>
        /// <param name="dataObject"> Object with data</param>
        /// <param name="dataTableName"> Data table name</param>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <returns> DataTable </returns>
        public static DataTable GetDataTable(Type type, dynamic dataObject, string dataTableName, Delegate translator)
        {
            DataTable dt = new DataTable();

            Dictionary<string, string> propertyDictionary = new Dictionary<string, string>();

            PropertyInfo[] propertyInfos = (from property in type.GetProperties()
                                            where Attribute.IsDefined(property, typeof(TableColumnAttribute))
                                            orderby ((TableColumnAttribute)property
                                                      .GetCustomAttributes(typeof(TableColumnAttribute), false)
                                                      .Single()).Order
                                            select property).ToArray();

            foreach (PropertyInfo property in propertyInfos)
            {
                List<dynamic> objectAttributes = property.GetCustomAttributes(true).ToList();

                objectAttributes.ForEach(objectAttribute =>
                {
                    TableColumnAttribute analyticalOutputAttribute = objectAttribute as TableColumnAttribute;

                    if (analyticalOutputAttribute != null)
                    {
                        if (translator != null)
                        {
                            propertyDictionary.Add(property.Name, translator.DynamicInvoke(analyticalOutputAttribute.TranslationKey).ToString());
                        }
                        else
                        {
                            propertyDictionary.Add(property.Name, analyticalOutputAttribute.TranslationKey);
                        }
                    }
                });
            }

            dt.TableName = dataTableName;

            foreach (KeyValuePair<string, string> entry in propertyDictionary)
            {
                dt.Columns.Add(new DataColumn(entry.Value, typeof(string)));
            }

            int rowIndex = 0;

            foreach (dynamic record in dataObject as IEnumerable<dynamic>)
            {
                dt.Rows.Add();

                int colIndex = 0;

                foreach (KeyValuePair<string, string> property in propertyDictionary)
                {
                    if (dt.Columns[colIndex].ColumnName == property.Value)
                    {
                        dynamic propertyInfo = record.GetType().GetProperty(property.Key);
                        dynamic value = propertyInfo.GetValue(record, null);
                        dt.Rows[rowIndex][property.Value] = Convert.ToString(value);
                    }

                    colIndex++;
                }

                rowIndex++;
            }

            return dt;
        }

        /// <summary>
        /// Return date string
        /// </summary>
        /// <param name="date">DateTime</param>
        /// <returns>Date string</returns>
        public static string ConvertDateTimeToString(DateTime? date)
        {
            if (date != null)
            {
                string dateString = String.Format("{0:yyyy-MM-dd}", date);
                return dateString;
            }
            else
                return null;
        }


        /// <summary>
        /// Convert boolean value to "Yes" or "No" or ""
        /// </summary>
        /// <param name="inputValue">input value</param>
        /// <returns>Yes or No or ""</returns>
        public static string ConvertBoolToYesNo(this bool? inputValue)
        {
            string value = string.Empty;

            if (inputValue.HasValue)
            {
                if (inputValue.Value)
                {
                    return Common.IndicatorYes;
                }
                else
                {
                    return Common.IndicatorNo;
                }
            }

            return value;
        }

        /// <summary>
        ///  Calculate percentage of two number
        /// </summary>
        /// <param name="number1">1st number</param>
        /// <param name="number2">2nd number</param>
        /// <returns>percentage</returns>
        public static int CalculatePercentage(int number1, int number2)
        {
            int percentage = 0;

            if (number1 > 0)
            {
                decimal value = ((decimal)number2 / number1) * 100;

                percentage = Convert.ToInt32(Math.Round(value, 0));
            }

            return percentage;
        }

        /// <summary>
        /// Get Document Name
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="responseDocuments">Document list</param>
        /// <returns>Document Name</returns>
        public static string GetDocumentName(string documentId, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            string documentName = string.Empty;

            if (!string.IsNullOrEmpty(documentId) && responseDocuments != null && responseDocuments.Any())
            {
                documentName = responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(documentId))?.FileName;
            }

            return documentName;
        }

        /// <summary>
        /// Get Document Name
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="responseDocuments">Document list</param>
        /// <returns>Document Name</returns>
        public static string GetDocumentContent(string documentId, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            string fileContent = string.Empty;

            if (!string.IsNullOrEmpty(documentId) && responseDocuments != null && responseDocuments.Any())
            {
                fileContent = "<button onClick={'" + responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(documentId))?.FileContent + "'}></button>"; ;
            }

            return fileContent;
        }

        /// <summary>
        /// Get the number of records that do not have a null or empty value.
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>Record count</returns>
        public static int CountNotNullAndEmptyProperties(this object obj)
        {
            int recordCount = obj.GetType()
                                .GetProperties()
                                .Select(x => x.GetValue(obj, null))
                                .Count(y => !string.IsNullOrWhiteSpace(Convert.ToString(y)));

            return recordCount;
        }
    }
}
