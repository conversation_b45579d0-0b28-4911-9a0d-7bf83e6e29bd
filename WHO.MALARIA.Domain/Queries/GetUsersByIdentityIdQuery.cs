﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Domain.Queries
{
    public class GetUsersByIdentityIdQuery : QueryBase<List<UserDto>>
    {
        [Required]
        public Guid IdentityId { get; set; }

        public GetUsersByIdentityIdQuery(Guid identityId)
        {
            IdentityId = identityId; 
        }
    }
}
