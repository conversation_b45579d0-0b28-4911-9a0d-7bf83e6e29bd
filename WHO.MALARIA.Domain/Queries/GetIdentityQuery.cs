﻿using System.Collections.Generic;
using Newtonsoft.Json;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Domain.Queries
{ 
    public class GetIdentityQuery : QueryBase<List<IdentityDto>>
    {
        [JsonProperty("filterCriterias")]
        public List<FilterCriteria> FilterCriterias { get; set; }

        public GetIdentityQuery()
        {

        }

        [JsonConstructor]
        public GetIdentityQuery(List<FilterCriteria> filterCriterias = default)
        {
            FilterCriterias = filterCriterias;
        }
    }
}
