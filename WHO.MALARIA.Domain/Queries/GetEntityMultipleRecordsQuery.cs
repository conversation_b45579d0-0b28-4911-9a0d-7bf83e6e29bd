﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Domain.Queries
{
    public class GetEntityMultipleRecordsQuery : QueryBase<IEnumerable<IdAndNameDto>> 
    {
        // Name of the entity(table) which is to be retrieved
        [JsonProperty("entity")]
        public string Entity { get; set; }

        [JsonProperty("filterCriterias")]
        public List<FilterCriteria> FilterCriterias { get; set; }

        public GetEntityMultipleRecordsQuery()
        {

        }

        public GetEntityMultipleRecordsQuery(string entity, List<FilterCriteria> filterCriterias = null)
        {
            Entity = entity;
            FilterCriterias = filterCriterias;
        }
    }
}
