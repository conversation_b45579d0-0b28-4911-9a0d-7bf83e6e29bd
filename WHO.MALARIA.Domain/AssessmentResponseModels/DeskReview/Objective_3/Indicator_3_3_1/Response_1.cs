﻿using FluentValidation.Results;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.SeedingMetadata;
using static WHO.MALARIA.Domain.Constants.Constants;
using ParentResponse = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_1;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_1
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.3.1
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool AreReportingToolsSameAsRecordingTools { get; set; }
        public bool? IsParentCannotBeAssessed { get; set; }
        public List<ReportingTool> ReportingTools { get; set; }

        /// <summary>
        /// Validates indicator 3.3.1
        /// </summary>
        /// <returns>Validation results for indicator 3.3.1</returns>
        public ValidationResult Validate() => new Response_1_Validator().Validate(this);

        /// <summary>
        /// Get analytical report response for indicator 3.3.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>       
        /// <param name="parentResponse">Delegate object which is used for parent indicator response call</param> 
        /// <param name="parentDocuments">Delegate object which is used for parent response document call</param>    
        /// <param name="responseDocuments">Contains response documents </param>   
        /// <param name="assessmentId">Assessment id </param>   
        /// <param name="assessmentStrategyId">Assessment strategy id </param>  
        /// <returns>Object of analytical output indicator response dto</returns> 
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, Delegate parentResponse, Delegate parentDocuments, IEnumerable<ResponseDocumentDto> responseDocuments, Guid assessmentId, Guid assessmentStrategyId)
        {
            TableResponse table = new TableResponse();

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            if (AreReportingToolsSameAsRecordingTools == false)
            {
                List<ReportingTool> recordingTools = GetAnalyticalChildTableResponse(translator, responseDocuments);

                ReportingTool numberOfRecordingForm = new ReportingTool
                {
                    ReportingToolName = translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfReportingTools_3_3_1).ToString(),
                    ToolType = recordingTools.Count.ToString()
                };

                recordingTools.Add(numberOfRecordingForm);

                table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(ReportingTool), ReportingTools, translator);

                table.HasCalculation = true;
            }
            else
            {

                JObject parentData = (JObject)parentResponse.DynamicInvoke(assessmentId, assessmentStrategyId, IndicatorSeedingMetadata.IND_3_2_1);

                ParentResponse.Response_1 parentResponseData = JsonConvert.DeserializeObject<ParentResponse.Response_1>(parentData.ToString());

                IEnumerable<ResponseDocumentDto> parentDocumentData = (IEnumerable<ResponseDocumentDto>)parentDocuments.DynamicInvoke(assessmentId, assessmentStrategyId, IndicatorSeedingMetadata.IND_3_2_1);

                List<ParentResponse.NumberOfRecordingForm> recordingTools = GetAnalyticalParentTableResponse(translator, parentResponseData.RecordingTools, parentDocumentData);

                ParentResponse.NumberOfRecordingForm numberOfRecordingForm = new ParentResponse.NumberOfRecordingForm
                {
                    RecordingToolId = translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfRecordingTools).ToString(),
                    ToolType = parentResponseData.RecordingTools.Count.ToString()
                };

                recordingTools.Add(numberOfRecordingForm);

                table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(ParentResponse.NumberOfRecordingForm), parentResponseData.RecordingTools, translator);

                table.HasCalculation = true;
            }

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.3.1",
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param>      
        /// <param name="parentResponse">Delegate object which is used for parent indicator response call</param> 
        /// <param name="parentDocuments">Delegate object which is used for parent response document call</param>    
        /// <param name="responseDocuments">Contains response documents </param>   
        /// <param name="assessmentId">Assessment id </param>   
        /// <param name="assessmentStrategyId">Assessment strategy id </param>   
        /// <returns>Indicator 3.3.1 response in the form of data table</returns> 
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, Delegate parentResponse, Delegate parentDocuments, IEnumerable<ResponseDocumentDto> responseDocuments, Guid assessmentId, Guid assessmentStrategyId)
        {
            DataSet ds = new DataSet();

            DataTable dt = new DataTable();

            if (AreReportingToolsSameAsRecordingTools == false)
            {
                List<ReportingTool> reportingTools = GetAnalyticalChildTableResponse(translator, responseDocuments, true);

                dt = AnalyticalOutputHelper.GetDataTable(typeof(ReportingTool), reportingTools, indicatorSequence, translator);

                dt.Rows.Add();

                dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfReportingTools_3_3_1), ReportingTools.Count);
            }
            else
            {
                JObject parentData = (JObject)parentResponse.DynamicInvoke(assessmentId, assessmentStrategyId, IndicatorSeedingMetadata.IND_3_2_1);

                ParentResponse.Response_1 parentResponseData = JsonConvert.DeserializeObject<ParentResponse.Response_1>(parentData.ToString());

                IEnumerable<ResponseDocumentDto> parentDocumentData = (IEnumerable<ResponseDocumentDto>)parentDocuments.DynamicInvoke(assessmentId, assessmentStrategyId, IndicatorSeedingMetadata.IND_3_2_1);

                List<ParentResponse.NumberOfRecordingForm> recordingTools = GetAnalyticalParentTableResponse(translator, parentResponseData.RecordingTools, parentDocumentData);

                dt = AnalyticalOutputHelper.GetDataTable(typeof(ParentResponse.NumberOfRecordingForm), recordingTools, indicatorSequence, translator);

                dt.Rows.Add();

                dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfRecordingTools), parentResponseData.RecordingTools.Count);
            }

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get updated value of recording tools
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>            
        /// <param name="responseDocuments">Contains response documents</param>    
        /// <returns>Updated value of recording tools</returns>
        private List<ReportingTool> GetAnalyticalChildTableResponse(Delegate translator, IEnumerable<ResponseDocumentDto> responseDocuments, bool isExport = false)
        {
            int index = 1;

            ReportingTools.ForEach(recordingTool =>
            {
                string documentName = string.Empty;

                if (!string.IsNullOrEmpty(recordingTool.DocumentId) && responseDocuments != null && responseDocuments.Any())
                {
                    documentName = responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(recordingTool.DocumentId))?.FileName;
                }
                if (isExport)
                    recordingTool.LinkOrScreenshot = documentName;
                else
                {
                    recordingTool.LinkOrScreenshot = !string.IsNullOrEmpty(recordingTool.DocumentId) ? bindScreenshot(responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(recordingTool.DocumentId))?.FileContent) : string.Empty;
                }
                //recordingTool.LinkOrScreenshot = documentName;
                var reportingToolText = translator.DynamicInvoke(AnalyticalOutputConstants.ReportingTool_3_3_1).ToString();
                recordingTool.Name = $"{reportingToolText} {index}";

                index++;
            });

            return ReportingTools;
        }

        /// <summary>
        /// Prepare string dynamically to get html button to view content 
        /// </summary>
        /// <param name="fileContent">Content of file</param>
        /// <returns>Html content</returns>
        private string bindScreenshot(string fileContent)
        {
            return "<button onClick={'" + fileContent + "'}></button>";
        }

        /// <summary>
        /// Get updated value of reporting tools
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="recordingTools">Contains reporting tools list</param>      
        /// <param name="responseDocuments">Contains response documents</param>   
        /// <returns>Updated value of reporting tools</returns>
        private List<ParentResponse.NumberOfRecordingForm> GetAnalyticalParentTableResponse(Delegate translator, List<ParentResponse.NumberOfRecordingForm> recordingTools, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            int index = 1;

            recordingTools.ForEach(recordingTool =>
            {
                string documentName = string.Empty;

                if (!string.IsNullOrEmpty(recordingTool.DocumentId) && responseDocuments != null && responseDocuments.Any())
                {
                    documentName = responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(recordingTool.DocumentId))?.FileName;
                }

                recordingTool.LinkOrScreenshot = documentName;
                recordingTool.RecordingToolId = $"Reporting Tool {index}";
                recordingTool.LastUpdatedStringDate = AnalyticalOutputHelper.ConvertDateTimeToString(recordingTool.LastUpdatedDate);

                index++;
            });

            return recordingTools;
        }
    }

    /// <summary>
    /// Contains details of reporting tool
    /// </summary>
    public class ReportingTool
    {
        [TableColumn(Name = "Name", TranslationKey = "Common.Name", Width = Common.Width300, Order = 1)]
        public string Name { get; set; }

        public string DocumentId { get; set; }

        [TableColumn(Name = "ReportingToolName", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.NameOfReportingToolSourceDocument", Width = Common.Width200, Order = 2)]
        public string ReportingToolName { get; set; }

        [TableColumn(Name = "ToolType", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.ToolType", Width = Common.Width200, Order = 3)]
        public string ToolType { get; set; }

        [TableColumn(Name = "YearTool", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.YearToolWasIntroduced", Width = Common.Width200, Order = 4)]
        public string YearTool { get; set; }

        [TableColumn(Name = "ReportingHealthSystemLevel", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.HealthSystemLevelReporting", Width = Common.Width200, Order = 5)]
        public string ReportingHealthSystemLevel { get; set; }

        [TableColumn(Name = "MethodOfReporting", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.MethodOfReportingTransmission", Width = Common.Width200, Order = 6)]
        public string MethodOfReporting { get; set; }

        [TableColumn(Name = "Aggregation", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.Aggregation", Width = Common.Width200, Order = 7)]
        public string Aggregation { get; set; }

        [TableColumn(Name = "FrequencyData", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.FrequencyDataReported", Width = Common.Width200, Order = 7)]
        public string FrequencyData { get; set; }

        [TableColumn(Name = "PersonResponsible", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.PersonResponsibleForReporting", Width = Common.Width200, Order = 7)]
        public string PersonResponsible { get; set; }

        [TableColumn(Name = "ReportsRecipient", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.RecipientOfReports1", Width = Common.Width200, Order = 7)]
        public string ReportsRecipient { get; set; }

        [TableColumn(Name = "VariablesReportedList", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.RecipientListVariablesReported", Width = Common.Width200, Order = 7)]
        public string VariablesReportedList { get; set; }

        [TableColumn(Name = "LinkOrScreenshot", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_1.LinkOrScreenshot", Width = Common.Width200, Order = 7)]
        public string LinkOrScreenshot { get; set; }
    }
}
