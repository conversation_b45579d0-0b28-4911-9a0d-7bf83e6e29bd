﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_1
{
    /// <summary>
    /// Contains validation rules for indicator 3.3.1
    /// </summary>
    public class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            RuleForEach(x => x.ReportingTools).ChildRules(tool =>
            {
                tool.RuleFor(z => z.Aggregation).MaximumLength(2000);
                tool.RuleFor(z => z.FrequencyData).MaximumLength(2000);
                tool.RuleFor(z => z.MethodOfReporting).MaximumLength(2000);
                tool.RuleFor(z => z.PersonResponsible).MaximumLength(2000);
                tool.RuleFor(z => z.ReportingHealthSystemLevel).MaximumLength(2000);
                tool.RuleFor(z => z.ReportingToolName).NotEmpty().MaximumLength(2000);
                tool.RuleFor(z => z.ReportsRecipient).MaximumLength(2000);
                tool.RuleFor(z => z.ToolType).MaximumLength(2000);
                tool.RuleFor(z => z.VariablesReportedList).MaximumLength(2000);
                tool.RuleFor(z => z.YearTool).MaximumLength(2000);
            }).When(x => x.IsParentCannotBeAssessed != true);
        }
    }
}
