﻿using FluentValidation;
using System;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.1.2
    /// </summary>
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        public Response_2_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleFor(x => x.step_A.HealthFacilitiesWithStockOut).NotNull().WithMessage("Please enter numeric value for 'No. of health facilities with stock outs' to finalize the indicator");
                RuleFor(x => x.step_A.HealthFacilitiesReporting).NotNull().WithMessage("Please enter numeric value for 'Number of health facilities reporting' to finalize the indicator");

                //Proportion calculation validation
                RuleFor(x => x).Must(k => (int)Math.Round((100 * (double)k.step_A.HealthFacilitiesWithStockOut) / (double)k.step_A.HealthFacilitiesReporting) <= 100 && (int)Math.Round((100 * (double)k.step_A.HealthFacilitiesWithStockOut) / (double)k.step_A.HealthFacilitiesReporting) >= 0)
              .WithMessage("Proportion value should be from 0 to 100");
            });
        }
    }
}
