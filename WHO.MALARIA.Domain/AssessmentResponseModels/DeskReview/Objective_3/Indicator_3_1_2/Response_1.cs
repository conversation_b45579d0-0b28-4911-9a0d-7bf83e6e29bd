﻿using FluentValidation.Results;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.1.2
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool? CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public AdequateCommoditiesForTreatment step_A { get; set; }

        public AdequateCommoditiesForTreatment step_B { get; set; }

        /// <summary>
        /// Validates indicator 3.1.2
        /// </summary>
        /// <returns>Validation results for indicator 3.1.2</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }
    }

    /// <summary>
    /// Contains details of Adequate Commodities For Treatment
    /// </summary>
    public class AdequateCommoditiesForTreatment
    {
        public int? HealthFacilitiesWithStockOut { get; set; }

        public int? HealthFacilitiesReporting { get; set; }
    }
}
