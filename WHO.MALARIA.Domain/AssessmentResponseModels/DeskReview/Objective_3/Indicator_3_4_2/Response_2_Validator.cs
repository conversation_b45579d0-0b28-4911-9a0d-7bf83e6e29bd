﻿using FluentValidation;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.4.2
    /// </summary>
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        public Response_2_Validator()
        {
            string validationMsg = "Please select 'Yes' or 'No' for all 'Indicator monitored in routine outputs' for indicators list";

            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                //Indicators count is different for each strategy, we get count of only selected indicators from front end, 
                //to complete the assessment all indicators of that strategy must be selected. here we are  checking selected indicators count with total indicators of that strategy
                // we are checking if all indicators of strategy are selected when assessing indicator

                //count checking for strategy
                RuleFor(x => x).Must(k => k.MalariaIndicators.Count == k.CheckListIndicatorsCount).WithMessage(validationMsg);                

                RuleForEach(x => x.MalariaIndicators).
                Must(k => k.IndicatorMonitored != null).WithMessage(validationMsg);
            });
        }
    }
}
