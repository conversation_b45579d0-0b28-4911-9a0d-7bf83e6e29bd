﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_6_1
{
    /// <summary>
    /// Contains validation rules for indicator 3.6.1
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleForEach(x => x.AccessUsers).Must(k => !string.IsNullOrWhiteSpace(k.National)).WithMessage("Please fill out all details for 'National Level' to finalize the indicator");
            });
        }
    }
}
