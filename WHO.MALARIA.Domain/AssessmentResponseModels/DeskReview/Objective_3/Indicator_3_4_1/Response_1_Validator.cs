﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_1
{
    /// <summary>
    /// Contains validation rules for indicator 3.4.1
    /// </summary>
    public class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            RuleFor(x => x.IndicatorsAndVisualizations.Output1).NotEmpty();
            RuleFor(x => x.LevelOfAnalysisDone.Output1).NotEmpty();
            RuleFor(x => x.Method.Output1).NotEmpty();
            RuleFor(x => x.MethodOfDissemination.Output1).NotEmpty();
            RuleFor(x => x.NameTypeExpectedOutput.Output1).NotEmpty();
            RuleFor(x => x.PersonResponsible.Output1).NotEmpty();
            RuleFor(x => x.Recipients.Output1).NotEmpty();
            RuleFor(x => x.ToolsUsed.Output1).NotEmpty();
            RuleFor(x => x.DataSourceUsed.Output1).NotEmpty();
            RuleFor(x => x.FrequencyOfAnalysis.Output1).NotEmpty();
        }
    }
}
