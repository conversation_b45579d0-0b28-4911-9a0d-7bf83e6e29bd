﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_1
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.4.1
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public RoutineAnalysisOutput NameTypeExpectedOutput { get; set; }

        public RoutineAnalysisOutput DataSourceUsed { get; set; }

        public RoutineAnalysisOutput IndicatorsAndVisualizations { get; set; }

        public RoutineAnalysisOutput ToolsUsed { get; set; }

        public RoutineAnalysisOutput Method { get; set; }

        public RoutineAnalysisOutput FrequencyOfAnalysis { get; set; }

        public RoutineAnalysisOutput LevelOfAnalysisDone { get; set; }

        public RoutineAnalysisOutput PersonResponsible { get; set; }

        public RoutineAnalysisOutput Recipients { get; set; }

        public RoutineAnalysisOutput MethodOfDissemination { get; set; }

        public Screenshot Screenshot { get; set; }

        /// <summary>
        /// Validates indicator 3.4.1
        /// </summary>
        /// <returns>Validation results for indicator 3.4.1</returns>
        public ValidationResult Validate() => new Response_1_Validator().Validate(this);

        /// <summary>
        /// Get analytical report along with the documents for the indicator 3.4.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>   
        /// <param name="responseDocuments">Contains response documents</param>   
        /// <returns>Object of analytical output indicator response dto</returns> 
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            List<RoutineAnalysisOutputReport> routineAnalysisOutputReports = GetRoutineAnalysisOutputReports(translator, responseDocuments);

            int recordCount = NameTypeExpectedOutput.CountNotNullAndEmptyProperties();

            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.TotalExpectedOutputs_3_4_1).ToString(), Convert.ToString(recordCount), "", "", "", "", ""));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(RoutineAnalysisOutputReport), routineAnalysisOutputReports, translator);

            table.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.4.1",
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param>      
        /// <param name="responseDocuments">Contains response documents </param>   
        /// <returns>Indicator 3.4.1 response in the form of data table</returns> 
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            DataSet ds = new DataSet();

            List<RoutineAnalysisOutputReport> routineAnalysisOutputReports = GetRoutineAnalysisOutputReports(translator, responseDocuments, true);

            int recordCount = NameTypeExpectedOutput.CountNotNullAndEmptyProperties();

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(RoutineAnalysisOutputReport), routineAnalysisOutputReports, indicatorSequence, translator);

            dt.Rows.Add();

            dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.TotalExpectedOutputs_3_4_1).ToString(), recordCount);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get Routine analysis output list
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <param name="responseDocuments">Contains response documents </param> 
        /// <returns>Routine analysis output list</returns>
        private List<RoutineAnalysisOutputReport> GetRoutineAnalysisOutputReports(Delegate translator, IEnumerable<ResponseDocumentDto> responseDocuments, bool isExport = false)
        {
            List<RoutineAnalysisOutputReport> routineAnalysisOutputReports = new List<RoutineAnalysisOutputReport>();

            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.NameTypeExpectedOutput_3_4_1).ToString(), NameTypeExpectedOutput.Output1, NameTypeExpectedOutput.Output2, NameTypeExpectedOutput.Output3, NameTypeExpectedOutput.Output4, NameTypeExpectedOutput.Output5, NameTypeExpectedOutput.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.DataSourceUsed_3_4_1).ToString(), DataSourceUsed.Output1, DataSourceUsed.Output2, DataSourceUsed.Output3, DataSourceUsed.Output4, DataSourceUsed.Output5, DataSourceUsed.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.IndicatorsAndVisualizations_3_4_1).ToString(), IndicatorsAndVisualizations.Output1, IndicatorsAndVisualizations.Output2, IndicatorsAndVisualizations.Output3, IndicatorsAndVisualizations.Output4, IndicatorsAndVisualizations.Output5, IndicatorsAndVisualizations.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.ToolsUsed_3_4_1).ToString(), ToolsUsed.Output1, ToolsUsed.Output2, ToolsUsed.Output3, ToolsUsed.Output4, ToolsUsed.Output5, ToolsUsed.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.Method_3_4_1).ToString(), Method.Output1, Method.Output2, Method.Output3, Method.Output4, Method.Output5, Method.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.FrequencyOfAnalysis_3_4_1).ToString(), FrequencyOfAnalysis.Output1, FrequencyOfAnalysis.Output2, FrequencyOfAnalysis.Output3, FrequencyOfAnalysis.Output4, FrequencyOfAnalysis.Output5, FrequencyOfAnalysis.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.LevelOfAnalysisDone_3_4_1).ToString(), LevelOfAnalysisDone.Output1, LevelOfAnalysisDone.Output2, LevelOfAnalysisDone.Output3, LevelOfAnalysisDone.Output4, LevelOfAnalysisDone.Output5, LevelOfAnalysisDone.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.PersonResponsible_3_4_1).ToString(), PersonResponsible.Output1, PersonResponsible.Output2, PersonResponsible.Output3, PersonResponsible.Output4, PersonResponsible.Output5, PersonResponsible.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.Recipients_3_4_1).ToString(), Recipients.Output1, Recipients.Output2, Recipients.Output3, Recipients.Output4, Recipients.Output5, Recipients.Output6));
            routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.MethodOfDissemination_3_4_1).ToString(), MethodOfDissemination.Output1, MethodOfDissemination.Output2, MethodOfDissemination.Output3, MethodOfDissemination.Output4, MethodOfDissemination.Output5, MethodOfDissemination.Output6));

            if (!isExport)
            {
                routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.Screenshot_3_4_1).ToString(), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId1, responseDocuments), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId2, responseDocuments), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId3, responseDocuments), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId4, responseDocuments), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId5, responseDocuments), AnalyticalOutputHelper.GetDocumentContent(Screenshot.DocumentId6, responseDocuments)));
            }
            else
            {
                routineAnalysisOutputReports.Add(new RoutineAnalysisOutputReport(translator.DynamicInvoke(AnalyticalOutputConstants.Screenshot_3_4_1).ToString(), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId1, responseDocuments), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId2, responseDocuments), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId3, responseDocuments), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId4, responseDocuments), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId5, responseDocuments), AnalyticalOutputHelper.GetDocumentName(Screenshot.DocumentId6, responseDocuments)));
            }

            return routineAnalysisOutputReports;
        }

    }

    /// <summary>
    /// Contains details of routine analysis outputs
    /// </summary>
    public class RoutineAnalysisOutput
    {
        [TableColumn(Name = "Output1", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output1", Width = Common.Width300, Order = 2)]
        public string Output1 { get; set; }

        [TableColumn(Name = "Output2", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output2", Width = Common.Width200, Order = 3)]
        public string Output2 { get; set; }

        [TableColumn(Name = "Output3", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output3", Width = Common.Width200, Order = 4)]
        public string Output3 { get; set; }

        [TableColumn(Name = "Output4", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output4", Width = Common.Width200, Order = 5)]
        public string Output4 { get; set; }

        [TableColumn(Name = "Output5", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output5", Width = Common.Width200, Order = 6)]
        public string Output5 { get; set; }

        [TableColumn(Name = "Output6", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_1.Output6", Width = Common.Width200, Order = 7)]
        public string Output6 { get; set; }
    }

    /// <summary>
    /// Contains documentId's of screenshot
    /// </summary>
    public class Screenshot
    {
        public string DocumentId1 { get; set; }

        public string DocumentId2 { get; set; }

        public string DocumentId3 { get; set; }

        public string DocumentId4 { get; set; }

        public string DocumentId5 { get; set; }

        public string DocumentId6 { get; set; }
    }

    /// <summary>
    /// Contain details of routine analysis outputs report
    /// </summary>
    public class RoutineAnalysisOutputReport : RoutineAnalysisOutput
    {
        [TableColumn(Name = "Name", TranslationKey = "Common.Name", Width = Common.Width300, Order = 1)]
        public string Name { get; set; }

        public RoutineAnalysisOutputReport(string name, string output1, string output2, string output3, string output4, string output5, string output6)
        {
            Name = name;
            Output1 = output1;
            Output2 = output2;
            Output3 = output3;
            Output4 = output4;
            Output5 = output5;
            Output6 = output6;
        }
    }
}
