﻿using FluentValidation;
using System;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_3
{
    /// <summary>
    /// Contains validation rules for indicator 3.1.3
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleFor(x => x.HealthFacilitiesWithStockOut).NotEmpty().WithMessage("Please enter numeric value for 'Proportion of health facilities with stock outs' to finalize the indicator");
                RuleFor(x => x.HealthFacilitiesReporting).NotEmpty().WithMessage("Please enter numeric value for 'Number of health facilities reporting' to finalize the indicator");

                //Proportion calculation validation
                RuleFor(x => x).Must(k => (int)Math.Round((100 * (double)k.HealthFacilitiesWithStockOut) / (double)k.HealthFacilitiesReporting) <= 100 && (int)Math.Round((100 * (double)k.HealthFacilitiesWithStockOut) / (double)k.HealthFacilitiesReporting) >= 0)
             .WithMessage("Proportion value for step A should be from 0 to 100");
            });
        }
    }
}
