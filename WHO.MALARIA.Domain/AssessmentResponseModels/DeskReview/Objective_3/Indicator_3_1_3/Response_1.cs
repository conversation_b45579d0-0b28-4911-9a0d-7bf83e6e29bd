﻿using FluentValidation.Results;
using System.Collections.Generic;
using WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_2;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_3
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.1.3
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public int? HealthFacilitiesWithStockOut { get; set; }

        public int? HealthFacilitiesReporting { get; set; }

        /// <summary>
        /// Validates indicator 3.1.3
        /// </summary>
        /// <returns>Validation results for indicator 3.1.3</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }
    }
}
