﻿using FluentValidation;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_1
{
    /// <summary>
    /// Contains validation rules for indicator 3.2.1
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleForEach(x => x.RecordingTools)
                .Must(k => !string.IsNullOrWhiteSpace(k.FrequencyDataReported)).WithMessage("Frequency Data Reported should not be empty")
                .Must(k => k.LastUpdatedDate.HasValue == true).WithMessage("Last Updated Date should not be empty")
                .Must(k => !string.IsNullOrWhiteSpace(k.RecordingToolId)).WithMessage("Recording Tool Id should not be empty")
                .Must(k => !string.IsNullOrWhiteSpace(k.NameOfReportingToolSourceDocument)).WithMessage("Name Of Reporting Tool Source Document should not be empty")
                .Must(k => !string.IsNullOrWhiteSpace(k.PersonResponsibleForReporting)).WithMessage("Person Responsible For Reporting should not be empty")
                .Must(k => !string.IsNullOrWhiteSpace(k.RecipientListVariablesReported)).WithMessage("Recipient List Variables sReported should not be empty")
                 .Must(k => !string.IsNullOrWhiteSpace(k.ToolType)).WithMessage("Tool type should not be empty")
                 .Must(k => !string.IsNullOrWhiteSpace(k.YearToolWasIntroduced)).WithMessage("Year Tool Was Introduced should not be empty")
                 .When(z => z.RecordingTools.Count == 1 && z.StrategyId != StrategySeedingMetadata.ELIMINATION_ID);

                RuleForEach(x => x.RecordingTools)
                .Must(k => !string.IsNullOrWhiteSpace(k.NameOfReportingToolSourceDocument)).WithMessage("Name Of Reporting Tool Source Document should not be empty")
                .When(z => z.RecordingTools.Count > 1 && z.StrategyId != StrategySeedingMetadata.ELIMINATION_ID);

                RuleForEach(x => x.RecordingTools)
                .Must(k => !string.IsNullOrWhiteSpace(k.NameOfReportingToolSourceDocument)).WithMessage("Name Of Reporting Tool Source Document should not be empty")
                .When(z => z.StrategyId == StrategySeedingMetadata.ELIMINATION_ID);
            });
        }
    }
}
