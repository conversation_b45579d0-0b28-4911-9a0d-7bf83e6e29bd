﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_1
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.2.1 
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {

        public bool? CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public Guid StrategyId { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<NumberOfRecordingForm> RecordingTools { get; set; }

        /// <summary>
        /// Validates indicator 3.2.1
        /// </summary>
        /// <returns>Validation results for indicator 3.2.1</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Get analytical report response for indicator 3.2.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>   
        /// <param name="responseDocuments">Contains response documents</param>   
        /// <returns>Object of analytical output indicator response dto</returns> 
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            List<NumberOfRecordingForm> recordingTools = GetAnalyticalTableResponse(translator, responseDocuments);

            AnalyticalOutputType outputType = AnalyticalOutputType.InteractiveTable;

            NumberOfRecordingForm numberOfRecordingForm = new NumberOfRecordingForm
            {
                RecordingToolId = translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfRecordingTools).ToString(),
                ToolType = RecordingTools.Count.ToString()
            };

            recordingTools.Add(numberOfRecordingForm);

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(NumberOfRecordingForm), RecordingTools, translator);

            table.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.2.1",
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param>      
        /// <param name="responseDocuments">Contains response documents </param>   
        /// <returns>Indicator 3.2.1 response in the form of data table</returns> 
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<ResponseDocumentDto> responseDocuments)
        {
            List<NumberOfRecordingForm> recordingTools = GetAnalyticalTableResponse(translator, responseDocuments, true);

            DataSet ds = new DataSet();

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(NumberOfRecordingForm), recordingTools, indicatorSequence, translator);

            dt.Rows.Add();

            dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.TotalNumberOfRecordingTools), RecordingTools.Count);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get updated value of recording tools
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains indicator sequence</param>      
        /// <param name="responseDocuments">Contains response documents</param>   
        /// <returns>Updated value of recording tools</returns>
        private List<NumberOfRecordingForm> GetAnalyticalTableResponse(Delegate translator, IEnumerable<ResponseDocumentDto> responseDocuments, bool isExport = false)
        {
            int index = 1;

            RecordingTools.ForEach(recordingTool =>
            {
                string documentName = string.Empty;

                if (!string.IsNullOrEmpty(recordingTool.DocumentId) && responseDocuments != null && responseDocuments.Any())
                {
                    documentName = responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(recordingTool.DocumentId))?.FileName;
                }

                if (isExport)
                    recordingTool.LinkOrScreenshot = documentName;
                else
                {
                    recordingTool.LinkOrScreenshot = !string.IsNullOrEmpty(recordingTool.DocumentId) ? bindScreenshot(responseDocuments.FirstOrDefault(d => d.Id == Guid.Parse(recordingTool.DocumentId))?.FileContent) : string.Empty;
                }
                var recordingToolText = translator.DynamicInvoke(AnalyticalOutputConstants.RecordingTool_3_2_1).ToString();

                recordingTool.RecordingToolId = $"{recordingToolText} {index}";
                recordingTool.LastUpdatedStringDate = AnalyticalOutputHelper.ConvertDateTimeToString(recordingTool.LastUpdatedDate);

                index++;
            });

            return RecordingTools;
        }

        public string bindScreenshot(string fileContent)
        {
            return "<button onClick={'" + fileContent + "'}></button>";
        }
    }

    /// <summary>
    /// Contains details of Number Of Recording Forms
    /// </summary>
    public class NumberOfRecordingForm
    {
        [TableColumn(Name = "RecordingToolId", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.RecordingTool", Width = Common.Width300, Order = 1)]
        public string RecordingToolId { get; set; }

        public string DocumentId { get; set; }

        [TableColumn(Name = "NameOfReportingToolSourceDocument", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.NameOfRecordingTool", Width = Common.Width200, Order = 2)]
        public string NameOfReportingToolSourceDocument { get; set; }

        [TableColumn(Name = "ToolType", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.ToolType", Width = Common.Width200, Order = 3)]
        public string ToolType { get; set; }

        [TableColumn(Name = "YearToolWasIntroduced", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.YearToolWasIntroduced", Width = Common.Width200, Order = 4)]
        public string YearToolWasIntroduced { get; set; }

        [TableColumn(Name = "FrequencyDataReported", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.FrequenceyDataIsRecorded", Width = Common.Width200, Order = 5)]
        public string FrequencyDataReported { get; set; }

        [TableColumn(Name = "PersonResponsibleForReporting", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.ResponsiblePerson", Width = Common.Width200, Order = 6)]
        public string PersonResponsibleForReporting { get; set; }

        [TableColumn(Name = "RecipientListVariablesReported", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.ListOfVariablesRecorded", Width = Common.Width200, Order = 7)]
        public string RecipientListVariablesReported { get; set; }

        public DateTime? LastUpdatedDate { get; set; }

        [TableColumn(Name = "LinkOrScreenshot", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.Screenshot", Width = Common.Width200, Order = 8)]
        public string LinkOrScreenshot { get; set; }

        [TableColumn(Name = "LastUpdatedStringDate", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_1.DateOfLastUpdated", Width = Common.Width200, Order = 9)]
        public string LastUpdatedStringDate { get; set; }

    }
}
