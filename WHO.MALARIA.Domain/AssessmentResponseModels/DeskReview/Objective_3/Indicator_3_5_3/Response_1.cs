﻿using FluentValidation.Results;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_3
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.5.3
    /// </summary>
    public class Response_1 : AssessmentResponseBase
    {       
        public DataQualityControlCheckResponse BuiltInChecksAtDataEntry { get; set; }

        public DataQualityControlCheckResponse ProducesDataVerificationReport { get; set; }

        public DataQualityControlCheckResponse AvoidDuplicateEntryOfRecord { get; set; }
    }

    /// <summary>
    /// Contains data quality control checks properties
    /// </summary>
    public class DataQualityControlCheckResponse
    {
        public string DataQualityControlCheck { get; set; }

        public bool? InPlace { get; set; }
    }
}
