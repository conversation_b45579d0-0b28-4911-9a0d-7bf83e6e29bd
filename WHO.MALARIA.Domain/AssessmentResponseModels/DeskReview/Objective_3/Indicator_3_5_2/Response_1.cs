﻿using FluentValidation.Results;
using System.Collections.Generic;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.5.2
    /// </summary>
    public class Response_1 : AssessmentResponseBase,IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }
        
        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public ValidationActivity NationalValidationActivity { get; set; }

        public ValidationActivity RegionalValidationActivity { get; set; }

        public ValidationActivity DistrictValidationActivity { get; set; }

        public int? FrequencyDenominator { get; set; }

        public int? NationalLevelDataAssessed { get; set; }

        public string MalariaSurveillanceQuality { get; set; }

        /// <summary>
        /// Validates indicator 3.5.2
        /// </summary>
        /// <returns>Validation results for indicator 3.5.2</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }
    }

    /// <summary>
    /// Common model for quality control to capture data cleaning, data review meetings, data quality assessments, data quality indicators
    /// </summary>
    public class ValidationActivity
    {
        public string DataCleaning { get; set; }

        public string DataReviewMeeting { get; set; }

        public string DataQualityAssessment { get; set; }

        public string DataQualityIndicator { get; set; }
    }
}
