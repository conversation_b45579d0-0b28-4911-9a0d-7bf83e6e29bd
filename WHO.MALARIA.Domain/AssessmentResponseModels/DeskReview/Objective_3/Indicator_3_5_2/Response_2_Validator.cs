﻿using FluentValidation;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.5.2
    /// </summary>
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        public Response_2_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleFor(x => x.DataQualityActivity.DataCleaning).NotNull();
                RuleFor(x => x.DataQualityActivity.DataReviewMeeting).NotNull();
                RuleFor(x => x.DataQualityActivity.DataQualityAssessment).NotNull();
                RuleFor(x => x.DataQualityActivity.DataQualityIndicator).NotNull();
                RuleFor(x => x).Must(x =>
                x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Monthly ||
                x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Weekly ||
                x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Quarterly || x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Annually || x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Biannually).WithMessage("Please enter valid value for Frequency Of Monitoring");
                RuleFor(x => x.NationalLevelDataAssessed).NotNull().InclusiveBetween(0, 4).When(x => x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Quarterly);
                RuleFor(x => x.NationalLevelDataAssessed).NotNull().InclusiveBetween(0, 12).When(x => x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Monthly);
                RuleFor(x => x.NationalLevelDataAssessed).NotNull().InclusiveBetween(0, 52).When(x => x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Weekly);
                RuleFor(x => x.NationalLevelDataAssessed).NotNull().InclusiveBetween(0, 1).When(x => x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Annually);
                RuleFor(x => x.NationalLevelDataAssessed).NotNull().InclusiveBetween(0, 2).When(x => x.NationalValidationActivity.DataQualityAssessment == MonitoringFrequencyType.Biannually);
            });
        }
    }
}
