﻿using FluentValidation.Results;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.5.2
    /// </summary>
    public class Response_2 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public DataQualityActivity DataQualityActivity { get; set; }

        public ValidationActivity NationalValidationActivity { get; set; }

        public ValidationActivity RegionalValidationActivity { get; set; }

        public ValidationActivity DistrictValidationActivity { get; set; }

        public int? FrequencyDenominator { get; set; }

        public int? NationalLevelDataAssessed { get; set; }

        public string MalariaSurveillanceQuality { get; set; }

        /// <summary>
        /// Validates indicator 3.5.2
        /// </summary>
        /// <returns>Validation results for indicator 3.5.2</returns>
        public ValidationResult Validate()
        {
            return new Response_2_Validator().Validate(this);
        }
    }

    /// <summary>
    /// Common model for quality control to capture data cleaning, data review meeting, data quality assessment, data quality indicator
    /// </summary>
    public class DataQualityActivity
    {
        public bool? DataCleaning { get; set; }

        public bool? DataReviewMeeting { get; set; }

        public bool? DataQualityAssessment { get; set; }

        public bool? DataQualityIndicator { get; set; }
    }
}