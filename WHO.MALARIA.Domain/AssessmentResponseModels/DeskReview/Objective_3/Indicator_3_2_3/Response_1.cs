﻿using FluentValidation.Results;
using System.Collections.Generic;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_3
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.2.3
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool? CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<StandardizedRecordingTool> StandardizedRecordingTools { get; set; }

        public List<StandardizedFormsDetail> StandardizedFormsDetails { get; set; }

        public int ParentRecordingToolCount { get; set; }

        public RecordingTool RecordingTool1 { get; set; }

        public RecordingTool RecordingTool2 { get; set; }

        public RecordingTool RecordingTool3 { get; set; }

        public RecordingTool RecordingTool4 { get; set; }

        /// <summary>
        /// Validates indicator 3.2.3
        /// </summary>
        /// <returns>Validation results for indicator 3.2.3</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }
    }

    /// <summary>
    /// Contains details of standardized recording tools where they are standardized across all services or not
    /// </summary>
    public class StandardizedRecordingTool
    {
        public string RecordingToolId { get; set; }

        public bool AreStandardizedAcrossAllService { get; set; }
    }

    /// <summary>
    /// Contains details of standardized recording tool's details on forms
    /// </summary>
    public class StandardizedFormsDetail
    {
        public string RecordingToolId { get; set; }

        public string Details { get; set; }
    }

    /// <summary>
    /// Contains details of recording tools only for Elimination strategy if parent screen(3.2.1) data isn't assessed
    /// </summary>
    public class RecordingTool
    {
        public string SourceDocumentName { get; set; }

        public bool? AreStandardizedAcrossAllService { get; set; }

        public string Detail { get; set; }
    }
}
