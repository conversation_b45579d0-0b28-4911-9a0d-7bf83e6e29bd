﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_3
{
    /// <summary>
    /// Contains validation rules for indicator 3.2.3 
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' check box is not checked
            When(x => x.CannotBeAssessed == false && x.ParentRecordingToolCount > 0, () =>
            {
                RuleFor(x => x).Must(x => x.StandardizedRecordingTools.Count == x.ParentRecordingToolCount).WithMessage("Please select Yes/No and Please provide details for each recording tool to finalize the indicator ");
                RuleFor(x => x).Must(x => x.StandardizedFormsDetails.Count == x.ParentRecordingToolCount).WithMessage("Please select Yes/No and Please provide details for each recording tool to finalize the indicator ");
                RuleFor(x => x).Must(x => x.StandardizedRecordingTools.Count.Equals(x.ParentRecordingToolCount)).WithMessage("Please select Yes/No and Please provide details for each recording tool to finalize the indicator ");
                RuleForEach(x => x.StandardizedFormsDetails)
                .Must(k => !string.IsNullOrWhiteSpace(k.Details)).WithMessage("Please provide details for each recording tool to finalize the indicator");
            });
        }
    }
}
