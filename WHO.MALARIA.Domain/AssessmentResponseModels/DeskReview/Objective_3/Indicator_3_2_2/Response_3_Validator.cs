﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.2.2 
    /// </summary>
    class Response_3_Validator : AbstractValidator<Response_3>
    {
        public Response_3_Validator()
        {
            string message = "Please select 'Yes' or 'No' for all 'Recorded in source documents' for variables to finalize the indicator";
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' check box is not checked
            When(x => x.CannotBeAssessed == false, () =>
            {                
                RuleFor(x => x).Must(k => k.Step_A.TransmitMalariaVariables.Count == k.CheckListVariablesCount).WithMessage(message);

                RuleForEach(x => x.Step_A.TransmitMalariaVariables)
              .Must(k => k.RecordedInSource.HasValue).WithMessage(message);

                RuleFor(x => x.Step_B.CorrectlyDistinguisedPresumedCase.Checklist).NotNull();
                RuleFor(x => x.Step_B.CorrectlyDistinguisedPresumedCase.Details).NotNull().When(x=>x.Step_B.CorrectlyDistinguisedPresumedCase.Checklist.GetValueOrDefault() == false);

                RuleFor(x => x.Step_B.RDTConfirmCase.Checklist).NotNull();
                RuleFor(x => x.Step_B.RDTConfirmCase.Details).NotNull().When(x => x.Step_B.RDTConfirmCase.Checklist.GetValueOrDefault() == false); ;

                RuleFor(x => x.Step_B.FalciparumInfection.Checklist).NotNull();
                RuleFor(x => x.Step_B.FalciparumInfection.Details).NotNull().When(x => x.Step_B.FalciparumInfection.Checklist.GetValueOrDefault() == false);
            });
        }
    }
}
