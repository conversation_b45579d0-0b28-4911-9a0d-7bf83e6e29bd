﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.SeedingMetadata;
using static WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2.Response_1;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.2.2
    /// </summary>
    public class Response_3 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public Step_A_Response Step_A { get; set; }

        public Step_B_Response Step_B { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListVariablesCount { get; set; }

        /// <summary>
        /// Contains detail for transmit malaria variables and pVivax cases variable for indicator 3.2.2's step A
        /// </summary>
        public class Step_A_Response
        {
            public bool? HasPVivaxCases { get; set; }

            public List<Response_1.TransmitMalariaVariable> TransmitMalariaVariables { get; set; }
        }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section B class
        /// </summary>
        public class Step_B_Response
        {
            public ToolsMalariaVariables CorrectlyDistinguisedPresumedCase { get; set; }

            public ToolsMalariaVariables RDTConfirmCase { get; set; }

            public ToolsMalariaVariables FalciparumInfection { get; set; }
        }

        /// <summary>
        /// Validates indicator 3.2.2
        /// </summary>
        /// <returns>Validation results for indicator 3.2.2</returns>
        public ValidationResult Validate()
        {
            return new Response_3_Validator().Validate(this);
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Analytical output indicator response of excel export for indicator 3.2.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists, strategyId);

            AnalyticalOutputType outputType = AnalyticalOutputType.InteractiveTable;

            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(Response_1.TransmitMalariaVariableReport), transmitMalariaVariableReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;
            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.2.2",
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        /// <summary>
        /// Prepares analytical report for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains indicator sequence</param>    
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Indicator 3.2.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            DataSet ds = new DataSet();

            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists, strategyId);

            DataTable trasmitMalariaVariableTable = AnalyticalOutputHelper.GetDataTable(typeof(Response_1.TransmitMalariaVariableReport), transmitMalariaVariableReports, indicatorSequence, translator);

            ds.Tables.Add(trasmitMalariaVariableTable);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        ///  Get transmit malaria variable records
        /// </summary>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Transmit malaria variable list</returns>
        private List<Response_1.TransmitMalariaVariableReport> GetTransmitMalariaVariables(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<Response_1.TransmitMalariaVariableReport>();

            int variableCount = 1;

            Step_A.TransmitMalariaVariables.ForEach(variable =>
            {
                string variableName = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId).Name;

                transmitMalariaVariableReports.Add(new Response_1.TransmitMalariaVariableReport(variableCount, variableName, translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString()));

                variableCount++;
            });

            if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                var denominator = Step_A.TransmitMalariaVariables.Count(indicator => indicator.RecordedInSource == true);
                var numerator = Step_A.TransmitMalariaVariables.Count();
                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.WHOBRVariablesRecorded_3_2_2).ToString(), CalculateCheckedForProperty(numerator, denominator)));
            }

            return transmitMalariaVariableReports;
        }

        private string CalculateCheckedForProperty(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(numerator, denominator);

            return $"{percentage}%";
        }

        /// <summary>
        /// Get List of tools for malaria variable
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <returns>List of tools for malaria variable</returns>
        private List<ToolsMalariaVariableReport> GetToolsMalariaVariableData(Delegate translator)
        {
            List<ToolsMalariaVariableReport> toolsMalariaVariableReports = new List<ToolsMalariaVariableReport>();

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.CorrectlyDistinguisedPresumedCases_3_2_2).ToString(), translator.DynamicInvoke(Step_B.CorrectlyDistinguisedPresumedCase.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.CorrectlyDistinguisedPresumedCase.Details));

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.RDTConfirmCases_3_2_2).ToString(), translator.DynamicInvoke(Step_B.RDTConfirmCase.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.RDTConfirmCase.Details));

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.FaclciparumInfections_3_2_2).ToString(), translator.DynamicInvoke(Step_B.FalciparumInfection.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.FalciparumInfection.Details));

            return toolsMalariaVariableReports;
        }
    }
}
