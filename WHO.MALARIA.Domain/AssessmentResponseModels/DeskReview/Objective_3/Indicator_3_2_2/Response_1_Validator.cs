﻿using FluentValidation;
using System;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.2.2 
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            string message = "Please select 'Yes' or 'No' for all 'Recorded in source documents' and 'Disaggregations' for variables to finalize the indicator";
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' check box is not checked
            When(x => x.CannotBeAssessed == false, () =>
            {
                //Here count is taken 69 in equal as total no. pf variables are 69
                RuleFor(x => x.Step_A.TransmitMalariaVariables.Count).Equal(69).WithMessage(message);

                RuleForEach(x => x.Step_A.TransmitMalariaVariables)
               .Must(k => k.UnderFive != null).WithMessage(message)
               .Must(k => k.OverFive != null).WithMessage(message)
               .Must(k => k.Gender != null).WithMessage(message)
               .Must(k => k.PregnantWoman != null).WithMessage(message)
               .Must(k => k.HealthSector != null).WithMessage(message)
               .Must(k => k.Geography != null).WithMessage(message)
               .Must(k => k.Other != null).WithMessage(message)
              .Must(k => k.RecordedInSource.HasValue).WithMessage(message);


                RuleFor(x => x.Step_B.CorrectlyDistinguisedPresumedCase.Checklist).NotNull();
                RuleFor(x => x.Step_B.CorrectlyDistinguisedPresumedCase.Details).NotNull().When(x => x.Step_B.CorrectlyDistinguisedPresumedCase.Checklist.GetValueOrDefault() == false);

                RuleFor(x => x.Step_B.RDTConfirmCase.Checklist).NotNull();
                RuleFor(x => x.Step_B.RDTConfirmCase.Details).NotNull().When(x => x.Step_B.RDTConfirmCase.Checklist.GetValueOrDefault() == false); ;

                RuleFor(x => x.Step_B.FalciparumInfection.Checklist).NotNull();
                RuleFor(x => x.Step_B.FalciparumInfection.Details).NotNull().When(x => x.Step_B.FalciparumInfection.Checklist.GetValueOrDefault() == false);

                RuleFor(x => x.Step_B.IndigenousCase.Checklist).NotNull();
                RuleFor(x => x.Step_B.IndigenousCase.Details).NotNull().When(x => x.Step_B.IndigenousCase.Checklist.GetValueOrDefault() == false);

                RuleFor(x => x.Step_B.IdentifiedCasesThroughProactiveCaseDetection.Checklist).NotNull();
                RuleFor(x => x.Step_B.IdentifiedCasesThroughProactiveCaseDetection.Details).NotNull().When(x => x.Step_B.FalciparumInfection.Checklist.GetValueOrDefault() == false);

                //Proportion calculation validation
                RuleFor(x => (int)Math.Round((double)x.Step_A.TransmitMalariaVariables.FindAll(x => x.RecordedInSource.Value == true).Count) * (double)100 / 69).LessThanOrEqualTo(100).WithMessage("Proportion value should be from 0 to 100 for national level")
                .When(x => x.Step_A.TransmitMalariaVariables.FindAll(x => x.RecordedInSource.Value == true).Count > 0);

                RuleFor(x => (int)Math.Round((double)x.Step_A.TransmitMalariaVariables.FindAll(x => x.RecordedInSource.Value == true).Count) * (double)100 / 69).GreaterThanOrEqualTo(0).WithMessage("Proportion value should be from 0 to 100 for national level")
                .When(x => x.Step_A.TransmitMalariaVariables.FindAll(x => x.RecordedInSource.Value == true).Count > 0);

            });
        }
    }
}
