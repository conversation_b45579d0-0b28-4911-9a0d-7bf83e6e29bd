﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.SeedingMetadata;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.2.2
    /// </summary>
    public class Response_1 : AssessmentResponseBase
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public Step_A_Response Step_A { get; set; }

        public Step_B_Response Step_B { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListVariablesCount { get; set; }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section A class
        /// </summary>
        public class Step_A_Response
        {
            public bool? HasPVivaxCases { get; set; }

            public bool? HasMalariaInpatients { get; set; }

            public List<TransmitMalariaVariable> TransmitMalariaVariables { get; set; }
        }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section A class
        /// </summary>
        public class TransmitMalariaVariable
        {
            public Guid VariableId { get; set; }

            public bool? RecordedInSource { get; set; }

            public bool? UnderFive { get; set; }

            public bool? OverFive { get; set; }

            public bool? Gender { get; set; }

            public bool? PregnantWoman { get; set; }

            public bool? HealthSector { get; set; }

            public bool? Geography { get; set; }

            public bool? Other { get; set; }

            public byte? CaseCategory { get; set; }
            public byte? OriginallyBelongsTo { get; set; }
        }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section B class
        /// </summary>
        public class Step_B_Response
        {
            public ToolsMalariaVariables CorrectlyDistinguisedPresumedCase { get; set; }

            public ToolsMalariaVariables RDTConfirmCase { get; set; }

            public ToolsMalariaVariables FalciparumInfection { get; set; }

            public ToolsMalariaVariables IndigenousCase { get; set; }

            public ToolsMalariaVariables IdentifiedCasesThroughProactiveCaseDetection { get; set; }
        }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section B class
        /// </summary>
        public class ToolsMalariaVariables
        {
            public bool? Checklist { get; set; }

            public string Details { get; set; }
        }

        /// <summary>
        /// Contains detail of indicator 3.2.2's section B classc report
        /// </summary>
        public class ToolsMalariaVariableReport
        {
            [TableColumn(Name = "Name", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_2.Assessment", Width = Common.Width100, Order = 1)]
            public string Name { get; set; }

            [TableColumn(Name = "Checklist", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_2.Checklist", Width = Common.Width300, Order = 2)]
            public string Checklist { get; set; }

            [TableColumn(Name = "Details", TranslationKey = "Common.Details", Width = Common.Width300, Order = 3)]
            public string Details { get; set; }

            public ToolsMalariaVariableReport(string name, string checklist, string details)
            {
                Name = name;
                Checklist = checklist;
                Details = details;
            }
        }

        /// <summary>
        /// Contains details of transmit malaria variables report
        /// </summary>
        public class TransmitMalariaVariableReport
        {
            [TableColumn(Name = "VariableCount", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_2.VariableNumber", Width = Common.Width100, Order = 1)]
            public int? VariableCount { get; set; }

            [TableColumn(Name = "VariableName", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_2.Assessment", Width = Common.Width300, Order = 2)]
            public string VariableName { get; set; }

            [TableColumn(Name = "RecordedInSource", TranslationKey = "DRObjective_3_Responses.Indicator_3_2_2.RecordedSourceDocuments", Width = Common.Width100, Order = 3)]
            public string RecordedInSource { get; set; }

            public TransmitMalariaVariableReport(int? variableCount, string variableName, string recordedInSource)
            {
                VariableCount = variableCount;
                VariableName = variableName;
                RecordedInSource = recordedInSource;
            }
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Analytical output indicator response of excel export for indicator 3.2.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists, strategyId);

            AnalyticalOutputType outputType = AnalyticalOutputType.InteractiveTable;

            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;
            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.2.2",
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        /// <summary>
        /// Prepares analytical report for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains indicator sequence</param>    
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Indicator 3.2.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            DataSet ds = new DataSet();

            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists, strategyId);

            DataTable trasmitMalariaVariableTable = AnalyticalOutputHelper.GetDataTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, indicatorSequence, translator);

            ds.Tables.Add(trasmitMalariaVariableTable);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        ///  Get transmit malaria variable records
        /// </summary>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Transmit malaria variable list</returns>
        private List<TransmitMalariaVariableReport> GetTransmitMalariaVariables(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<TransmitMalariaVariableReport>();

            int variableCount = 1;

            Step_A.TransmitMalariaVariables.ForEach(variable =>
            {
                var variableData = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId);
                variable.OriginallyBelongsTo = variableData.OriginallyBelongsTo;
                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(variableCount, variableData.Name, translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString()));
                variableCount++;
            });

            if (StrategySeedingMetadata.Both_ID == strategyId)
            {
                var denominator = Step_A.TransmitMalariaVariables.Count(t => t.OriginallyBelongsTo == (int)CaseStrategySelectionType.BurdenReduction && t.RecordedInSource == true);
                var numerator = Step_A.TransmitMalariaVariables.Count(t => t.OriginallyBelongsTo == (int)CaseStrategySelectionType.BurdenReduction);
                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.WHOBRVariablesRecorded_3_2_2).ToString(), CalculateCheckedForProperty(numerator, denominator)));

                denominator = Step_A.TransmitMalariaVariables.Count(t => t.OriginallyBelongsTo == (int)CaseStrategySelectionType.Elimination && t.RecordedInSource == true);
                numerator = Step_A.TransmitMalariaVariables.Count(t => t.OriginallyBelongsTo == (int)CaseStrategySelectionType.Elimination);
                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.WHOEliminationVariablesRecorded_3_2_2).ToString(), CalculateCheckedForProperty(numerator, denominator)));
            }
            else if (StrategySeedingMetadata.ELIMINATION_ID == strategyId)
            {
                var denominator = Step_A.TransmitMalariaVariables.Count(t => t.RecordedInSource == true);
                var numerator = Step_A.TransmitMalariaVariables.Count;
                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.WHOBRVariablesRecorded_3_2_2).ToString(), CalculateCheckedForProperty(numerator, denominator)));
            }

            return transmitMalariaVariableReports;
        }


        /// <summary>
        /// Get List of tools for malaria variable
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <returns>List of tools for malaria variable</returns>
        private List<ToolsMalariaVariableReport> GetToolsMalariaVariableData(Delegate translator)
        {
            List<ToolsMalariaVariableReport> toolsMalariaVariableReports = new List<ToolsMalariaVariableReport>();

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.RDTConfirmCases_3_2_2).ToString(), translator.DynamicInvoke(Step_B.RDTConfirmCase.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.RDTConfirmCase.Details));

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.FaclciparumInfections_3_2_2).ToString(), translator.DynamicInvoke(Step_B.FalciparumInfection.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.FalciparumInfection.Details));

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.IndigenousCases_3_2_2).ToString(), translator.DynamicInvoke(Step_B.IndigenousCase.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.IndigenousCase.Details));

            toolsMalariaVariableReports.Add(new ToolsMalariaVariableReport(translator.DynamicInvoke(AnalyticalOutputConstants.IndifiedCasesThroughProactiveCaseDetection_3_2_2).ToString(), translator.DynamicInvoke(Step_B.IdentifiedCasesThroughProactiveCaseDetection.Checklist.ConvertBoolToYesNo()).ToString(), Step_B.IdentifiedCasesThroughProactiveCaseDetection.Details));

            return toolsMalariaVariableReports;
        }

        private string CalculateCheckedForProperty(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(numerator, denominator);

            return $"{percentage}%";
        }
    }

}