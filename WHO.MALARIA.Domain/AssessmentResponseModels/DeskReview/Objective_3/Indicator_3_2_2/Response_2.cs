﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.2.2
    /// </summary>
    public class Response_2 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public Guid StrategyId { get; set; }

        public List<MalariaVariable> MalariaVariables { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListVariablesCount { get; set; }

        /// <summary>
        /// Validates indicator 3.2.2
        /// </summary>
        /// <returns>Validation results for indicator 3.2.2</returns>
        public ValidationResult Validate()
        {
            return new Response_2_Validator().Validate(this);
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Analytical output indicator response of excel export for indicator 3.2.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists);

            AnalyticalOutputType outputType = AnalyticalOutputType.InteractiveTable;

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(Response_1.TransmitMalariaVariableReport), transmitMalariaVariableReports, translator);
            table.HasCalculation = true;
            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                IndicatorSequence = "3.2.2",
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Prepares analytical report for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains indicator sequence</param>    
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Indicator 3.2.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRVariableCheckListDto> drVariableCheckLists, Guid strategyId)
        {
            DataSet ds = new DataSet();

            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = GetTransmitMalariaVariables(translator, drVariableCheckLists);

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(Response_1.TransmitMalariaVariableReport), transmitMalariaVariableReports, $"{indicatorSequence}_A", translator);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        ///  Get transmit malaria variable records
        /// </summary>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Transmit malaria variable list</returns>
        private List<Response_1.TransmitMalariaVariableReport> GetTransmitMalariaVariables(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists)
        {
            List<Response_1.TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<Response_1.TransmitMalariaVariableReport>();

            int variableCount = 1;

            MalariaVariables.ForEach(variable =>
            {
                var variableData = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId);
                transmitMalariaVariableReports.Add(new Response_1.TransmitMalariaVariableReport(variableCount, variableData.Name, translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString()));
                variableCount++;
            });

            transmitMalariaVariableReports.Add(new Response_1.TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooterTitle_3_3_2).ToString(), GetFooterPercentage(MalariaVariables.Count(indicator => indicator.RecordedInSource == true), MalariaVariables.Count)));

            return transmitMalariaVariableReports;
        }

        private string GetFooterPercentage(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(denominator, numerator);

            return $"{percentage}%";
        }

        /// <summary>
        /// Contains details of Transmit Malaria Variables for the indicator
        /// </summary>
        public class MalariaVariable
        {
            public Guid VariableId { get; set; }

            public bool? RecordedInSource { get; set; }

            public bool? Under5 { get; set; }

            public bool? Over5 { get; set; }

            public bool? Gender { get; set; }

            public bool? PregnantWoman { get; set; }

            public bool? HealthSector { get; set; }

            public bool? Geography { get; set; }

            public bool? Other { get; set; }
        }
    }
}