﻿using FluentValidation;
using System;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.2.2 
    /// </summary>
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        public Response_2_Validator()
        {
            string message = "Please select 'Yes' or 'No' for all 'Recorded in source documents' for variables to finalize the indicator";
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' check box is not checked
            When(x => x.CannotBeAssessed == false, () =>
            {
                //Variables count is different for each strategy, we get count of only selected variables from front end, 
                //to complete the assessment all variables of that strategy must be selected. here we are  checking selected variables count with total variables of that strategy
                // we are checking if all variables of strategy are selected when assessing indicator

                //count checking for strategy
                RuleFor(x => x).Must(k => k.MalariaVariables.Count == k.CheckListVariablesCount).WithMessage(message);   
                
                RuleForEach(x => x.MalariaVariables)
              .Must(k => k.RecordedInSource.HasValue).WithMessage(message);

                //Proportion calculation validation
                RuleFor(x => (int)Math.Round((double)(x.MalariaVariables.FindAll(x => x.RecordedInSource.Value == true)).Count * 100 / (double)x.CheckListVariablesCount)).LessThanOrEqualTo(100).WithMessage("Proportion value should be from 0 to 100");
                RuleFor(x => (int)Math.Round((double)(x.MalariaVariables.FindAll(x => x.RecordedInSource.Value == true)).Count * 100 / (double)x.CheckListVariablesCount)).GreaterThanOrEqualTo(0).WithMessage("Proportion value should be from 0 to 100");
            });
        }
    }
}
