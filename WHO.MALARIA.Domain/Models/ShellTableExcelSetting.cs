﻿
namespace WHO.MALARIA.Domain.Models
{
    /// <summary>
    /// Contains shell table's excel details such as sheet, row and column numbers, etc.
    /// </summary>
    /// 
    public class ShellTableExcelSetting
    {
        public const string ShellTableSetting = "ShellTable";
        public Objective_1_Sheet Objective1 { get; set; }
        public Objective_2_Sheet Objective2 { get; set; }
        public Objective_3_Sheet Objective3 { get; set; }
        public Objective_4_Sheet Objective4 { get; set; }
        public HealthFacilitySheet HealthFacility { get; set; }
    }

    /// <summary>
    /// Properties related to objective 1 setting
    /// </summary>
    /// 
    public class Objective_1_Sheet
    {
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public string IndicatorSequence { get; set; }
        public RowColumnRange ProtectCellRange { get; set; }
        public ObjectiveSetting SubObjective_1_3 { get; set; }
    }

    /// <summary>
    /// Properties related to objective 2 setting
    /// </summary>
    /// 
    public class Objective_2_Sheet
    {
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public string IndicatorSequence { get; set; }
        public RowColumnRange ProtectCellRange { get; set; }
        public ObjectiveSetting SubObjective_2_1 { get; set; }       
        public ObjectiveSetting SubObjective_2_2 { get; set; }        
        public ObjectiveSetting SubObjective_2_3 { get; set; }
        public ObjectiveSetting SubObjective_2_4 { get; set; }
    }

    /// <summary>
    /// Properties related to objective 3 setting
    /// </summary>
    /// 
    public class Objective_3_Sheet
    {
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public string IndicatorSequence { get; set; }
        public RowColumnRange ProtectCellRange { get; set; }
        public ObjectiveSetting SubObjective_3_1 { get; set; }       
        public ObjectiveSetting SubObjective_3_2 { get; set; }        
        public ObjectiveSetting SubObjective_3_3 { get; set; }        
        public ObjectiveSetting SubObjective_3_4 { get; set; }       
        public ObjectiveSetting SubObjective_3_4_3 { get; set; }       
        public ObjectiveSetting SubObjective_3_5 { get; set; }        
        public ObjectiveSetting SubObjective_3_6 { get; set; }        
    }

    /// <summary>
    /// Properties related to objective 4 setting
    /// </summary>
    /// 
    public class Objective_4_Sheet
    {
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public string IndicatorSequence { get; set; }
        public RowColumnRange ProtectCellRange { get; set; }
        public ObjectiveSetting SubObjective_4_1 { get; set; }        
        public ObjectiveSetting SubObjective_4_2 { get; set; }       
        public ObjectiveSetting SubObjective_4_3 { get; set; }       
        public ObjectiveSetting SubObjective_4_4 { get; set; }       
    }

    /// <summary>
    /// Properties related to health facility setting
    /// </summary>
    public class HealthFacilitySheet : RowColumnRange
    {
        public string Name { get; set; }
        public string Name_FR { get; set; }
    }
    /// <summary>
    /// Properties related to objective setting
    /// </summary>
    public class ObjectiveSetting : RowColumnRange
    {
        public string IndicatorSequence { get; set; }
    }    

    /// <summary>
    /// Properties related to option setting
    /// </summary>
    public class OptionRange: RowColumnRange
    {
        public bool BothUnlock { get; set; }
    }

    /// <summary>
    /// Properties related to indicator setting
    /// </summary>
    public class IndicatorMapSetting : ObjectiveSetting
    {
        public string ObjectiveSequence { get; set; }
        public string SubObjectiveSequence { get; set; }
        public OptionRange Options { get; set; }
        public bool HasResponseFromDeskReview { get; set; }
        public bool IsParent { get; set; }
        public ResponseOption ResponseOptions { get; set; }
    }

    /// <summary>
    /// Properties related to response option setting
    /// </summary>
    public class ResponseOption : RowColumnRange
    {
        public string CharacterToRemoveFromOption { get; set; }
        public string ParentCode { get; set; }        
    }
}
