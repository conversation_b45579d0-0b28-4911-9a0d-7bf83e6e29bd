﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Models.DeskReview;
using WHO.MALARIA.Domain.Models.DQA;
using WHO.MALARIA.Domain.Models.QuestionBank;

namespace WHO.MALARIA.Domain.Models
{
    /// <summary>
    /// Properties related to Strategy entity
    /// </summary>
    public class Strategy : ModelBase
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public int Type { get; set; } //CASE, MalariaControl
        public bool IsActive { get; set; }
        public int Order { get; set; }
        public string ShortName { get; set; }

        /// <summary>
        /// Indicator strategies for case strategy relationship, for IndicatorStrategy.ParentCaseStrategyId column
        /// </summary>
        public virtual ICollection<IndicatorStrategyMapping> IndicatorCaseStrategies { get; set; }
        
        /// <summary>
        /// Indicator strategies for other strategy relationship, for IndicatorStrategy.StrategyId column
        /// </summary>
        public virtual ICollection<IndicatorStrategyMapping> IndicatorOtherStrategies { get; set; }
        
        public virtual ICollection<DRVariableStrategyMapping> DRVariableStrategies { get; set; }

        public virtual ICollection<AssessmentStrategy> AssessmentStrategies { get; set; }
        public virtual ICollection<DRIndicator> DRIndicators { get; set; }

        public virtual ICollection<DQAVariableStrategyMapping> DQAVariableStrategies { get; set; }

        public virtual ICollection<QBQuestionIndicatorStrategyMapping> QBQuestionIndicatorStrategies { get; set; }

        public virtual ICollection<QBDependentStrategyMapping> QBDependentStrategyMappings { get; set; }

        public virtual ICollection<AnalyticalOutputIndicatorStrategyMapping> AnalyticalOutputStrategyMappings { get; set; }
    }

}
