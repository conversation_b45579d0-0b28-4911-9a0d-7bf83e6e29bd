﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Domain.Models
{
    /// <summary>
    /// Contains properties required for data Sorting
    /// </summary>
    public class SortCriteria
    {
        public string Field { get; set; }

        // Refers to the sort direction i.e Asc or Desc. 
        // Added JsonConverter attribute to convert string value to its associated enum value
        [JsonConverter(typeof(StringEnumConverter))]
        public SortDirection Direction { get; set; }
    }
}
