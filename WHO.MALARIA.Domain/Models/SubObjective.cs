﻿using System;
using System.Collections.Generic;

using WHO.MALARIA.Domain.Models.DeskReview;

namespace WHO.MALARIA.Domain.Models
{
    /// <summary>
    /// Properties related to SubObjective entity
    /// </summary>
    public class SubObjective : ModelBase
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Name_FR { get; set; }
        public string Sequence { get; set; }
        public bool IsActive { get; set; }
        public int Order { get; set; }
        public Guid ObjectiveId { get; set; }
        public virtual Objective Objective { get; set; }
        public virtual ICollection<Indicator> Indicators { get; set; }
        public virtual ICollection<SubObjectiveDiagram> SubObjectiveDiagrams { get; set; }
    }
}
