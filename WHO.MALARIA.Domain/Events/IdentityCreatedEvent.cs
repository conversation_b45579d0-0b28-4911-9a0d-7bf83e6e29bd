﻿using System;
using MediatR;
using WHO.MALARIA.Domain.Commands;

namespace WHO.MALARIA.Domain.Events
{
    public class IdentityCreatedEvent:INotification
    {
        public Guid IdentityId { get; }
        public string Name { get;  }
        public string Email { get;}

        public IdentityCreatedEvent(Guid identityId,  string name, string email)
        {
            this.IdentityId = identityId;
            this.Name = name;
            this.Email = email;
        }
    }
}
