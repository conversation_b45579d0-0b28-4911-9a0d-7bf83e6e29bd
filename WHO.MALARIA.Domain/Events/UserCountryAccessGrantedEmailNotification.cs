﻿using MediatR;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains email address of the user for that the email will be sent when the super manager grants the access for the requested country
    /// </summary>
    public class UserCountryAccessGrantedEmailNotification : INotification
    {
        public string Email { get; set; }
        public string Country { get; set; }

        public UserCountryAccessGrantedEmailNotification(string email, string country)
        {
            Email = email;
            Country = country;
        }
    }
}
