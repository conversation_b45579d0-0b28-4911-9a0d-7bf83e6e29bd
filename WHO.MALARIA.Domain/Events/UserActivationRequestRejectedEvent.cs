﻿using MediatR;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains event data of user activation request rejection
    /// </summary>
    public class UserActivationRequestRejectedEvent : INotification
    {
        public string Email { get; set; }
        public string Country { get; set; }
        public int UserType { get; }
        public string Reason { get; }

        public UserActivationRequestRejectedEvent(string email, string country, int userType, string reason)
        {
            Email = email;
            Country = country;
            UserType = userType;
            Reason = reason;
        }
    }
}
