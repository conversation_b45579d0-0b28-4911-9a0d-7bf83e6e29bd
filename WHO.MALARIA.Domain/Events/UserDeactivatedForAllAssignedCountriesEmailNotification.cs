﻿using MediatR;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains email address of the user for that the email will be sent when the user is deactivated from the system
    /// </summary>
    public class UserDeactivatedForAllAssignedCountriesEmailNotification : INotification
    {
        public string Email { get; set; }

        public UserDeactivatedForAllAssignedCountriesEmailNotification(string email)
        {
            Email = email;
        }
    }
}
