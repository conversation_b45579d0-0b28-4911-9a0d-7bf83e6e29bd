﻿using MediatR;
using System;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains the user's name, email address, and country access for the invitation email
    /// </summary>
    public class UserInvitationEmailNotification : INotification
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Country { get; set; }
        public string CurrentUserName { get; set; }
        public Guid UserCountryAccessId { get; set; }   
        
        public UserInvitationEmailNotification(string name, string email, string country, string currentUserName, Guid userCountryAccessId)
        {
            Name = name;
            Email = email;
            Country = country;
            CurrentUserName = currentUserName;
            UserCountryAccessId = userCountryAccessId;          
        }
    }
}
