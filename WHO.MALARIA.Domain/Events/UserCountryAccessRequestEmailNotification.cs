﻿using MediatR;

namespace WHO.MALARIA.Domain.Events
{    
    /// <summary>
    /// Contains event data of email notification for country access request
    /// </summary>
    public class UserCountryAccessRequestEmailNotification : INotification
    {
        public string Email { get; set; }
        public string Country { get; set; }

        public UserCountryAccessRequestEmailNotification(string email, string country)
        {
            Email = email;
            Country = country;
        }
    }
}
