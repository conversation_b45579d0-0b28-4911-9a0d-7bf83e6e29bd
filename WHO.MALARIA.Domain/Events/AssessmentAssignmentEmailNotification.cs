﻿using System;

using MediatR;

using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains user details such as email and role to send the notification on assignment/un-assignment of an assessment
    /// </summary>
    public class AssessmentAssignmentEmailNotification : INotification
    {
        public string[] EmailAddresses { get; set; }
        public AssessmentUserRole UserRole { get; set; }
        public Guid CountryId { get; set; }
        public bool AreUsersRemoved { get; set; }

        public AssessmentAssignmentEmailNotification(string[] emailAddresses, AssessmentUserRole userRole, Guid countryId, bool areUsersRemoved)
        {
            EmailAddresses = emailAddresses;
            UserRole = userRole;
            CountryId = countryId;
            AreUsersRemoved = areUsersRemoved;
        }
    }
}
