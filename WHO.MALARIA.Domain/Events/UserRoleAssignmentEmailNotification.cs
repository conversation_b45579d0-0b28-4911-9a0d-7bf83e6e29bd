﻿using MediatR;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains notification details along with the user role, country, and email address
    /// </summary>
    public class UserRoleAssignmentEmailNotification : INotification
    {
        public string Email { get; set; }
        public string Country { get; set; }
        public UserRoleEnum UserType { get; }

        public UserRoleAssignmentEmailNotification(string email, string country, UserRoleEnum userType)
        {
            Email = email;
            Country = country;
            UserType = userType;
        }
    }
}
