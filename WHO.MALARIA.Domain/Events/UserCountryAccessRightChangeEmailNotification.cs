﻿using MediatR;

using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Domain.Events
{
    /// <summary>
    /// Contains email address of the user for that the email will be sent when the user's access changes on the assigned country and whether the user is active or in active on that country
    /// </summary>
    public class UserCountryAccessRightChangeEmailNotification : INotification
    {
        public string Email { get; set; }
        public string Country { get; set; }
        public UserRoleEnum UserType { get; }
        public UserCountryAccessRightsEnum UserCountryAccess { get; set; }

        public UserCountryAccessRightChangeEmailNotification(string email, string country, UserRoleEnum userType, UserCountryAccessRightsEnum userCountryAccess)
        {
            Email = email;
            Country = country;
            UserType = userType;
            UserCountryAccess = userCountryAccess;
        }
    }
}
