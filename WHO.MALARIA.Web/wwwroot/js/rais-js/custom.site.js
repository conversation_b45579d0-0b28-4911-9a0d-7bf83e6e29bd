﻿$(document).ready(function () {
    // Initiates the scrollbar for the particular section
    var width = $(window).width();
    if (width >= 575) {
        jQuery('.scrollbar-inner').scrollbar();
    }

    // Change hambuger menu when open in small screens
    $('#ChangeToggle').click(function () {
        $('#navbar-hamburger').toggleClass('hidden');
        $('#navbar-close').toggleClass('hidden');
    });

     // Toggle class to the root element
    const toggleBtn = document.querySelector("#toggle-theme");

    if (toggleBtn) {
        toggleBtn.addEventListener('click', (e) => {
            const root = document.documentElement;

            if (!root.getAttribute('theme', 'dark'))
                root.classList.add('theme-dark');
            else
                root.classList.remove('theme-dark');
        });
    }
});

$(".bd-navbar-nav").delegate("a.nav-link", "click", function () {
    loadChildMenu(this);
});

$(".bd-navbar-nav").delegate("a.dropdown-item", "contextmenu", function () {        
    const parentMenu = $(this).data('parent');
    setSessionStorage(parentMenu);
    markActiveMenu();
});

$(".bd-navbar-nav").delegate("a.dropdown-item", "click", function () {   
    const parentMenu = $(this).data('parent');
    setSessionStorage(parentMenu);
    markActiveMenu();
});

// closes the menu if clicked outside anywhere on the document
const $menuContainer = $('.menu');
$(document).on('click', (e) => {
    e.stopPropagation();
    if ($menuContainer.has(e.target).length === 0) // ... nor a descendant of the container
    {
        $('.sub-menu').addClass('hidden');
    }
});

    

$(document).ready(function () {    
    markActiveMenu();
});

// mark the selected submenu's parent as active
markActiveMenu = () => {
    const activeParentMenu = getSessionStorage();
    // if session storage has any current active parent menu then mark the parent menu as active else mark first menu as active
    if (activeParentMenu) {
        // loop through the menus and match the active parent menu with session storage menu and mark active class
        $('#main-menu').find('li a.nav-link').each(function (index) {
            $(this).parent('li').removeClass('active');
            const text = $(this).text();
            if (activeParentMenu === text) {
                $(this).parent('li').addClass('active');
            } 
        });
    } else {        
        $('#main-menu li.nav-item').eq(0).addClass('active');
    }
}

// load the sub-menu on parent menu click
// it hides the all previous open sub-menu and opens the current one
function loadChildMenu(element) {    
    $('.sub-menu').addClass('hidden', 200);
    $('#main-menu .nav-item').removeClass('active', 200);
    const activeMenu = $(element).parent('li').addClass('active');
    const subMenu = $(element).next('.sub-menu');
    $(activeMenu).addClass('active', 200);
    $(subMenu).removeClass('hidden', 200);
    // set the session variable to current active parent menu
    const parentMenu = $(element).data('parent');
    setSessionStorage(parentMenu);
}

$("#message-wrapper").delegate(".close-btn", "click", function () {
    $('#message-wrapper').removeClass('show', 100).addClass('hide', 100);
});


$("#message-success").delegate(".close-btn", "click", function () {
    $('#message-success').removeClass('show', 100).addClass('hide', 100);
});

// set current parent menu to session storage
setSessionStorage = (activeParentMenu) => {
    sessionStorage.setItem("activeMenu", activeParentMenu);    
}

// get current parent menu to session storage
getSessionStorage = () => {
   return sessionStorage.getItem("activeMenu");
}

//------------- Success Message -----------------------------

// show the success message
// default timeout for the message it 5000 miliseconds.
function showSuccess(message, timeOut = 5000) {
    showSuccessMessage(message);
    setTimeout(() => {
        const successWrapper = document.getElementById('message-success');
        successWrapper.removeAttribute('class');
        successWrapper.setAttribute('class', 'hide');
    }, timeOut);
}

// manipulate the DOM element and show the success message 
function showSuccessMessage(message) {
    const successWrapper = document.getElementById('message-success');
    const errorMessageWrapper = document.getElementById('message-wrapper');
    const messageBody = document.getElementById('message-body');

    errorMessageWrapper.removeAttribute('class');
    errorMessageWrapper.setAttribute('class', 'hide');

    successWrapper.removeAttribute('hide');
    successWrapper.setAttribute('class', 'show');
    messageBody.innerText = message;
}

//------------- Error Message -----------------------------

// parse an response object
function parseToErrorMessage(error) {
    const { exceptionType, localizedMessage, message, requestId, showErrorDetails, stackTrace, statusCode, supportEmail, supportURL } = error.responseJSON;
    return {
        exceptionType,
        localizedMessage,
        message,
        requestId,
        showErrorDetails,
        stackTrace,
        statusCode,
        supportEmail,
        supportURL
    };
}

// generate the error message  
function showError(errorMessage) {
    const errorModel = parseToErrorMessage(errorMessage);
    const errorMessageWrapper = document.getElementById('message-wrapper');
    errorMessageWrapper.removeAttribute('hide');
    errorMessageWrapper.setAttribute("class", "show");
    bindErrorMessageToDOM(errorModel);

    setTimeout(() => {
        const errorMessageWrapper = document.getElementById('message-wrapper');
        errorMessageWrapper.setAttribute("class", "hide");
    }, timeOut);
}

// bind the DOM element with the object value
function bindErrorMessageToDOM(errorModel) {
    const messageContent = document.getElementById('error-content');
    const successWrapper = document.getElementById('message-success');

    successWrapper.removeAttribute('class');
    successWrapper.setAttribute('class', 'hide');
    
    messageContent.innerHTML = errorModel.localizedMessage && errorModel.localizedMessage;
}