﻿// defining the const variables for element id/class so that it doesn't go with the iteration of finding DOM elements, also helps in DOM performance
const QUERY_PARAMETERS_ELEMENTS = {
    TabContent: '.tab-content', SaveParameterButton: '#save-parameter', QueryParameterClass: '.queryParameter', QueryParameterContent: '#queryParameterContent',
    QueryParameter: '#query-parameter', ParameterType: '#parameterType', QueryParamTypeHelpBlock: '#queryParamTypeHelpBlock', SourceField: '#sourceField', SourceFieldHelpBlock: '#sourceFieldHelpBlock', QueryParameterAnchor: '#queryParameterAnchor', Querybuilderform: '#querybuilderform', QueryName: '#queryName', FormQueryType: '#formQueryType', SaveQuery: '#saveQuery', CompileQuery: '#compileQuery', UpdateQuery: '#updateQuery', DeleteQuery: '#deleteQuery', CancelAndGoBack: '#cancelAndGoBack', QueryParameterName: '#query-parameter-name', QueryId: '#queryid', QueryParameterId: '#queryParameterId', ParameterQuery: '#parameter-query', VisibleName: '#visibleName', VisibleNameHelpBlock: '#visibleNameFieldHelpBlock', ParameterQueryHelpBlock: '#parameterQueryFieldHelpBlock', ParameterEntity: '#parameter-entity', Order: '#order',
    QueryTab:"#queryTab"
}

AQB.Web.onQueryBuilderReady(function (qb) {
    $(document).ready(function () {
        const rootUrl = document.getElementById('rootUrl').value;
        const apiUrl = `${rootUrl}api/` + 'process/message';
        const returnPageUrl = document.getElementById('returnUrl').value;
        getQueryTypes(apiUrl);

        ($(QUERY_PARAMETERS_ELEMENTS.QueryName)[0]).onkeyup = validateQueryName;
        ($("textarea")[0]).onkeyup = validateQuery;
        ($(QUERY_PARAMETERS_ELEMENTS.FormQueryType)[0]).onchange = validateQueryType;

        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.SaveQuery, 'click', function () {
            saveQuery(apiUrl, returnPageUrl);
        });

        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.QueryTab, 'click', function () {
            handleQueryTabClick(rootUrl, returnPageUrl);
        });

        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.CompileQuery, 'click', function () {
            compileQuery(apiUrl, returnPageUrl);
        });
        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.UpdateQuery, 'click', function () {
            updateQuery(apiUrl, returnPageUrl);
        });
        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.DeleteQuery, 'click', function () {
            deleteQuery(apiUrl, returnPageUrl);
        });
        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.CancelAndGoBack, 'click', function () {
            const urlRoot = document.getElementById('rootUrl').value;

            window.location.href = returnPageUrl ? returnPageUrl : `${urlRoot}Administration/Query`;
        });
        $(QUERY_PARAMETERS_ELEMENTS.Querybuilderform).delegate(QUERY_PARAMETERS_ELEMENTS.QueryNameueryName, 'blur', function () {
            checkUniqueValue(apiUrl);
        });

        $(QUERY_PARAMETERS_ELEMENTS.QueryParameterAnchor).on('click', function () {
            const queryId = $(this).data('queryid');
            loadParameterView(queryId);
        });


        $(QUERY_PARAMETERS_ELEMENTS.TabContent).on('click', QUERY_PARAMETERS_ELEMENTS.QueryParameterClass, function () {
            const queryId = $(this).data('queryid');
            loadParameterDetails(queryId);
            $(QUERY_PARAMETERS_ELEMENTS.QueryParameterClass).parent('li').removeClass('active');
            $(this).parent('li').addClass('active');
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate(QUERY_PARAMETERS_ELEMENTS.SaveParameterButton, 'click', function (evt) {
            evt.preventDefault();
            saveQueryParameter();

            return false;
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate('input, select', 'focus', function (evt) {
            evt.preventDefault();
            // remove the previously applied class 'mul-active' on any fieldset
            $('fieldset').removeClass('mul-active');

            // apply the 'mul-active' clas to the currently focused control
            $(this).parents('fieldset').toggleClass('mul-active');

            return false;
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate(QUERY_PARAMETERS_ELEMENTS.ParameterType, 'change', function (evt) {
            validateQueryParameterType();

            // add class hided to hide the control
            $('#divVisibleName').addClass('hide_aqb_lookup');
            $('#divQueryEntity').addClass('hide_aqb_lookup');

            // if query is of type lookup and parameter is of type lookup from query then show the lookups
            if (isReportType()) {
                $('#divVisibleName').removeClass('hide_aqb_lookup');
                $('#divQueryEntity').removeClass('hide_aqb_lookup');
            }
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate(QUERY_PARAMETERS_ELEMENTS.SourceField, 'keyup', function (evt) {
            validateSourceField();
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate(QUERY_PARAMETERS_ELEMENTS.VisibleName, 'keyup', function (evt) {
            validateVisibleField();
        });

        $(QUERY_PARAMETERS_ELEMENTS.TabContent).delegate(QUERY_PARAMETERS_ELEMENTS.ParameterQuery, 'change', function (evt) {
            validateQueryField();
        });

        setOnReady();
    });
});

let lastSuccessfullyCompiledQuery = null;
let lastSuccessfullySavedQuery = null;
let isUniqueDataRoleName = true;

function setOnReady() {
    let isCompiled = $('#isCompiled')[0] ? $('#isCompiled')[0].checked : false;

    // set the disable property for compile button 
    $('#compileQuery').prop("disabled", isCompiled);

    // if the query is compiled then set textarea control value to lastSuccessfullyCompiledQuery
    if (isCompiled) {
        lastSuccessfullyCompiledQuery = $('textarea').val();
    }

    lastSuccessfullySavedQuery = $('#editQueryText')[0].innerText;

    if (lastSuccessfullySavedQuery) {
        $('textarea').val(lastSuccessfullySavedQuery);
    }
}

//Check if the mandatory query name field is filled 
function validateQueryName() {
    const queryName = $('#queryName').val();

    // remove unique key validation if query name is unique
    if (isUniqueDataRoleName) {
        $('#formQueryName').removeClass('form-error');
        $("#formQueryName span:nth-child(4)").removeClass("display-aqb-error-block");
    }
    else {
        $('#formQueryName').addClass('form-error');
        $("#formQueryName span:nth-child(4)").addClass("display-aqb-error-block");
        return false;
    }

    if (queryName.length === 0) {
        $('#formQueryName').addClass('form-error');
        $('#formQueryName span:nth-child(2)').addClass("display-aqb-error-block");
        $("#formQueryName span:nth-child(3)").removeClass("display-aqb-error-block");
        return false;
    }
    else if (queryName.length > 100) {
        $('#formQueryName').addClass('form-error');
        $("#formQueryName span:nth-child(3)").addClass("display-aqb-error-block");
        $("#formQueryName span:nth-child(2)").removeClass("display-aqb-error-block");
        return false;
    }
    else {
        $('#formQueryName').removeClass('form-error');
        $('#formQueryName .help-block').removeClass("display-aqb-error-block");
        return true;
    }
}

//Check if the mandatory sql query field is filled 
function validateQuery() {
    const sqlCode = $('textarea').val();
    setValidatationOnQueryEdit();
    if (sqlCode.length === 0) {
        $('#formSqlCode').addClass('form-error');
        $('#formSqlCode .help-block').addClass("display-aqb-error-block");
        return false;
    }
    else {
        $('#formSqlCode').removeClass('form-error');
        $('#formSqlCode .help-block').removeClass("display-aqb-error-block");
        return true;
    }
}

function setValidatationOnQueryEdit() {
    const sqlCode = $('textarea').val();
    if (sqlCode && (sqlCode.trim() === lastSuccessfullyCompiledQuery)) {
        $('#isCompiled').prop("checked", true);
    }
    else {
        $('#isCompiled').prop("checked", false);
    }
}

//Check if the mandatory query type field is filled 
function validateQueryType(event) {
    const queryTypeId = $('#formQueryType').val() || $('#formQueryType option:selected').val() ? $('#formQueryType').val() || $('#formQueryType option:selected').val() : (event ? event.target.value : '');

    if (queryTypeId.length === 0) {
        $('#formQueryType').addClass('form-error');
        $('#formQueryType .help-block').addClass("display-aqb-error-block");
        return false;
    }
    else {
        $('#formQueryType').removeClass('form-error');
        $('#formQueryType .help-block').removeClass("display-aqb-error-block");
        return true;
    }
}

//Validate form
function isValidForm() {
    const isValidQueryName = validateQueryName();
    const isValidQuery = validateQuery();
    const isValidQueryType = validateQueryType();

    if (!isValidQueryName || !isValidQuery || !isValidQueryType) {
        return false;
    }
    else {
        return true;
    }
}

// Handle click on query tab 
function handleQueryTabClick(rootUrl, returnPageUrl) {
    const queryId = $("#queryId").val();

    // Set the location to edit query screen url 
    window.location.href = `${rootUrl}Administration/QueryBuilder/Edit/${queryId}?returnUrl=${returnPageUrl}`;
}

//Save query form 
function saveQuery(apiUrl, returnPageUrl) {
    if (!isValidForm()) {
        return false;
    }

    const queryRequestMessage = { messageType: 'CreateQuery', entity: 'Query', executionMode: 'Sync', Query: { Name: $('#queryName').val(), QueryTypeId: $("#queryType").val(), QueryText: $('textarea').val() } };
    $.post(apiUrl, JSON.stringify(queryRequestMessage), function (response) {
        if (response && response.queryId) {
            showSuccess($('#addedSuccessMessage').val());
            $('#queryId').prop("value", response.queryId);
            $('#saveQuery').prop("disabled", true);

            // redirect to edit page for compiling the saved query 
            const urlRoot = document.getElementById('rootUrl').value;
            setTimeout(() => { window.location.href = `${urlRoot}Administration/QueryBuilder/Edit/${response.queryId}?returnUrl=${returnPageUrl}` }, 1500);
        }
    }).fail(function (error) {
        showError(error);
    });
}

//Update query form 
function updateQuery(apiUrl, returnPageUrl) {
    if (!isValidForm()) {
        return false;
    }

    const queryId = document.getElementById('queryId').value;
    const queryRequestMessage = { messageType: 'UpdateQuery', entity: 'Query', executionMode: 'Sync', Query: { Id: $("#queryId").val(), Name: $('#queryName').val(), QueryText: $('textarea').val() } };
    $.post(apiUrl, JSON.stringify(queryRequestMessage), function (response) {
        if (response && response.messageId) {
            showSuccess($('#savedSuccessMessage').val());
            lastSuccessfullySavedQuery = $('textarea').val();
            $('#queryId').prop("value", response.queryId);
            $('#isCompiled').prop("checked", false);

            ///reload the page to enable query parameter if query contains 
            window.location.href = window.location.href;
        }
    }).fail(function (error) {
        showError(error);
    });
}

//Get all query types
function getQueryTypes(apiUrl) {
    const queryTypeRequestMessage = { messageType: 'RetrieveMultipleQueryType', entity: 'QueryType', executionMode: 'Sync' };
    $.post(apiUrl, JSON.stringify(queryTypeRequestMessage), function (response) {
        if (response.queryTypes) {
            $.each(response.queryTypes, function (data, value) {
                $("#queryType").append($("<option></option>").val(value.id).html(value.name));
            });
            $("#queryType").val($("#selectedQueryType").val());
        }
    }).fail(function (error) {
        showError(error);
    });
}

//Compile query text
function compileQuery(apiUrl, returnPageUrl) {
    if (!isValidForm()) {
        return false;
    }

    const sqlCode = $('textarea').val();
    if (sqlCode && lastSuccessfullySavedQuery && (sqlCode.trim() !== lastSuccessfullySavedQuery.trim())) {
        $('#queryNotificationModal').modal('show');
        return false;
    }

    const compileQueryRequestMessage = { messageType: 'CompileQuery', entity: 'Query', executionMode: 'Sync', QueryId: $("#queryId").val() };
    $.post(apiUrl, JSON.stringify(compileQueryRequestMessage), function (response) {
        if (response && response.isCompiled) {
            lastSuccessfullyCompiledQuery = $('textarea').val();
            showSuccess($('#compiledSuccessMessage').val());
            setOnCompile(true);

            const urlRoot = document.getElementById('rootUrl').value;
            let returnUrl = returnPageUrl;

            const queryId = document.getElementById('queryId').value;

            // check if return url is from add web service account then generate return url with query id and name
            if (returnUrl.indexOf("WebServiceAccount/Add") !== undefined && returnUrl.indexOf("WebServiceAccount/Add") !== null && returnUrl.indexOf("WebServiceAccount/Add") !== -1) {

                // split the return url and get url to return on add web service account page
                returnUrl = returnUrl.replace(returnUrl.split("WebServiceAccount/Add")[1], '');

                // generate url to return on add web service account page
                // add newly created query id and name into return url 
                returnUrl = returnUrl + "/" + queryId + "/" + $('#queryName').val();
            }

            // check if return url is from edit web service account then generate return url with query id and name
            if (returnUrl.indexOf("WebServiceAccount/Edit") !== undefined && returnUrl.indexOf("WebServiceAccount/Edit") !== null && returnUrl.indexOf("WebServiceAccount/Edit") !== -1) {

                // split return url and get web service account id from url
                const accountId = returnUrl.split("WebServiceAccount/Edit/")[1].split("/")[0];

                // split the return url and get url to return on edit web service account page
                returnUrl = returnUrl.replace(returnUrl.split("WebServiceAccount/Edit")[1], '');

                // generate url to return on edit web service account page
                // add account id, newly created query id and name into return url 
                returnUrl = returnUrl + "/" + accountId + "/" + queryId + "/" + $('#queryName').val();
            }

            setTimeout(() => { window.location.href = returnUrl ? returnUrl : `${urlRoot}Administration/Query` }, 1000);
        }
        else {
            setOnCompile(false);
        }
    }).fail(function (error) {
        setOnCompile(false);
        showError(error);
    });
}

//Delete query 
function deleteQuery(apiUrl, returnPageUrl) {
    const queryRequestMessage = { messageType: 'DeleteQuery', entity: 'Query', executionMode: 'Sync', QueryId: $("#queryId").val() };
    $.post(apiUrl, JSON.stringify(queryRequestMessage), function (response) {
        if (response && response.isDeleted) {
            showSuccess($('#deleteSuccessMessage').val());

            // get root url
            const urlRoot = document.getElementById('rootUrl').value;

            // after successfully deleted query redirect to return page url if available else redirect to query listing screen
            setTimeout(() => { window.location.href = returnPageUrl ? returnPageUrl : `${urlRoot}Administration/Query` }, 1000);
        }
    }).fail(function (error) {
        showError(error);
    });
}

//Set as enable/disable compiled checkbox, button & save query button
function setOnCompile(isCompiled) {
    $('#compileQuery').prop("disabled", isCompiled);
    $('#isCompiled').prop("checked", isCompiled);
}

// check if query name is unique
function checkUniqueValue(apiUrl) {
    // If query name is empty then hide the unique error message & return
    if (!$('#queryName').val() || $('#queryName').val().length <= 0) {
        isUniqueDataRoleName = true;
        validateQueryName();
        return;
    }

    const requestMessage = { messageType: 'CheckUniqueValue', entity: 'Query', entityType: "System", executionMode: 'Sync', filterAttributes: [{ attribute: "Name", operator: "=", value: $('#queryName').val() }] };

    // in edit mode, the attribute should exclude current query record (identityId)
    if ($("#queryId").val() && $("#queryId").val().length > 0) {
        requestMessage.filterAttributes.push({ attribute: "Id", operator: "<>", value: $("#queryId").val() });
    }

    //call api
    $.post(apiUrl, JSON.stringify(requestMessage), function (response) {
        isUniqueDataRoleName = response && response.isUnique ? response.isUnique : false;
        validateQueryName();
    }).fail(function (error) {
        showError(error);
    });
}

// Load Query Parameters
function loadParameterView(queryId) {
    $.get(`/Administration/QueryParameters/${queryId}`, function (response) {
        if (response) {
            $(QUERY_PARAMETERS_ELEMENTS.QueryParameter).html('').html(response);
            //Load the very first parameter details
            $(QUERY_PARAMETERS_ELEMENTS.QueryParameterClass)[0].click()
        }
    });
}

//Load query parameter details view
function loadParameterDetails(queryParameterId) {
    $(QUERY_PARAMETERS_ELEMENTS.QueryParameterContent).html('').html($('#loadingMessage').val());
    $.get(`/Administration/QueryParameter/${queryParameterId}`, function (response) {
        if (response) {
            $(QUERY_PARAMETERS_ELEMENTS.QueryParameterContent).html('').html(response);
        }
    });
}

//Save query parameter
function saveQueryParameter() {
    // fetch the values from the controls
    const queryParameterId = $(QUERY_PARAMETERS_ELEMENTS.QueryParameterId).val();
    const queryId = $(QUERY_PARAMETERS_ELEMENTS.QueryId).val();
    const queryParameterName = $(QUERY_PARAMETERS_ELEMENTS.QueryParameterName).val();
    const sourceField = $(QUERY_PARAMETERS_ELEMENTS.SourceField).val();
    const parameterType = $(QUERY_PARAMETERS_ELEMENTS.ParameterType)[0].selectedOptions[0].text;
    const order = $(QUERY_PARAMETERS_ELEMENTS.Order).val();

    // build the query parameter to be passed along with the request
    const param = { Id: queryParameterId, Name: queryParameterName, QueryId: queryId, SourceField: sourceField, Type: parameterType };

    if (isReportType()) {
        param.VisibleName = $(QUERY_PARAMETERS_ELEMENTS.VisibleName).val();
        param.LookupQueryId = $(QUERY_PARAMETERS_ELEMENTS.ParameterQuery).val();
        param.InternalName = $(QUERY_PARAMETERS_ELEMENTS.ParameterEntity).val();
        param.Order = order;
    }

    // when query type id is from report,statistics,preselected filter then validate report query paramter form else query parameter form
    var formValid = isReportType() ? validateReportQueryParamForm() : validateQueryParamForm();

    if (formValid) {
        $.post(`/api/internal/UpdateQueryParameter`, param, function (response) {
            if (response) {
                showSuccess($('#savedQueryParamSuccessMessage').val());
            }
        }).fail(function (error) {
            showError(error);
        });
    }
}

//Validate Query Param type
function validateQueryParameterType(event) {
    const queryTypeId = $(QUERY_PARAMETERS_ELEMENTS.ParameterType).val() || $(`${QUERY_PARAMETERS_ELEMENTS.ParameterType} option:selected`).val() ? $(QUERY_PARAMETERS_ELEMENTS.ParameterType).val() || $(`${QUERY_PARAMETERS_ELEMENTS.ParameterType} option:selected`).val() : (event ? event.target.value : '');

    if (queryTypeId.length === 0) {
        $(QUERY_PARAMETERS_ELEMENTS.ParameterType).parents('fieldset').addClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.QueryParamTypeHelpBlock).addClass("display-aqb-error-block");
        return false;
    }
    else {
        $(QUERY_PARAMETERS_ELEMENTS.ParameterType).parents('fieldset').removeClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.QueryParamTypeHelpBlock).removeClass("display-aqb-error-block");
        return true;
    }
}

//Validate source field
function validateSourceField(event) {
    const sourceField = $(QUERY_PARAMETERS_ELEMENTS.SourceField).val();

    if (sourceField.length === 0) {
        $(QUERY_PARAMETERS_ELEMENTS.SourceField).parents('fieldset').addClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.SourceFieldHelpBlock).addClass("display-aqb-error-block");
        return false;
    }
    else {
        $(QUERY_PARAMETERS_ELEMENTS.SourceField).parents('fieldset').removeClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.SourceFieldHelpBlock).removeClass("display-aqb-error-block");
        return true;
    }
}


//Validate parameter visible name
function validateVisibleField(event) {
    const visibleField = $(QUERY_PARAMETERS_ELEMENTS.VisibleName).val();

    if (visibleField.length === 0) {
        $(QUERY_PARAMETERS_ELEMENTS.VisibleName).parents('fieldset').addClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.VisibleNameHelpBlock).addClass("display-aqb-error-block");
        return false;
    }
    else {
        $(QUERY_PARAMETERS_ELEMENTS.VisibleName).parents('fieldset').removeClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.VisibleNameHelpBlock).removeClass("display-aqb-error-block");
        return true;
    }
}

//Validate parameter query 
function validateQueryField(event) {
    const queryField = $(QUERY_PARAMETERS_ELEMENTS.ParameterQuery).val();

    if (queryField.length === 0) {
        $(QUERY_PARAMETERS_ELEMENTS.ParameterQuery).parents('fieldset').addClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.ParameterQueryHelpBlock).addClass("display-aqb-error-block");
        return false;
    }
    else {
        $(QUERY_PARAMETERS_ELEMENTS.ParameterQuery).parents('fieldset').removeClass('mul-error');
        $(QUERY_PARAMETERS_ELEMENTS.ParameterQueryHelpBlock).removeClass("display-aqb-error-block");
        return true;
    }
}

//Validate Query Param Form
function validateQueryParamForm() {
    var validSourceField = $('#report-query-type').val() === 'true' ? true : validateSourceField();
    var validQueryParamType = validateQueryParameterType();

    if (validSourceField && validQueryParamType) {
        return true;
    }
    else {
        return false;
    }
}

//Validate Query Param Form when query type is report,statistics,preselected filter 
function validateReportQueryParamForm() {
    var validQueryParamType = validateQueryParameterType();
    var validQueryField = validateQueryField();
    var validVisibleName = validateVisibleField();

    if (validQueryParamType && validVisibleName && validQueryField) {
        return true;
    }
    else {
        return false;
    }
}

// checks query type belongs to report query type is or not 
function isReportType() {

    return $('#report-query-type').val() === 'true' && $('#parameterType').val() === "3";
};