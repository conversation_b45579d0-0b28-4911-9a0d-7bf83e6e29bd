﻿
// Change label and placeholder on input field change
function FieldOnChange(inputField) {

    ResetValidations();
    $('#InputValue').val('');

    if (inputField === 'Username') {
        $('#lblInputValue').text('Username');
        $('#InputValueIcon').addClass('fa-address-book');
        $('#InputValueIcon').removeClass('fa-envelope');
    }
    else {
        $('#lblInputValue').text('Email');
        $('#InputValueIcon').addClass('fa-envelope');
        $('#InputValueIcon').removeClass('fa-address-book');
    }
}

// Validate input value on focus-out
$("#InputValue").focusout(function () {
    ValidateInput();
});

// Validate input value usernane/Email while sending forgot password email notification
// Username/Email should not be null and Email value should be in Email format
function ValidateInput() {

    const inputField = $('input[name=InputField]:checked').val();
    const inputValue = $('#InputValue').val();
    ResetValidations();

    if (inputField === 'Username' && (inputValue === undefined || inputValue === "")) {
        $('.username-null-error-message').removeClass('hidden');
        return false;
    }
    else if (inputField === 'Email' && ((inputValue === undefined || inputValue === "") || !/^[+a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(inputValue))) {
        $('.email-null-error-message').removeClass('hidden');
        return false;
    }
    else {
        return true;
    }
}

// Reset validation messages
function ResetValidations() {
    $('.username-null-error-message').addClass('hidden');
    $('.email-null-error-message').addClass('hidden');
    $('.error-message-wrapper').addClass('hidden');
}

// Close success message
$('#btn-close').click(function () {
    $('#message-success').addClass('hidden');
});

// Reset validation message on focus-out of password and confirm password field
$('.password').focusout(function () {
    $('#spErrorMessage').addClass('hidden');
});

// Validate input value on submit to send forgot password email notification
$('#btnSubmit').click(function () {
    if (ValidateInput()) {
        $('#forgotPasswordForm').submit();
    }
});

