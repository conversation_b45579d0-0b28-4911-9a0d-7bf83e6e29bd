﻿$(document).ready(function () {

    var reportParameters = document.getElementById('data-param-reportParameter').value ? JSON.parse(document.getElementById('data-param-reportParameter').value) : [];

    var helpText = document.getElementById('data-param-helpText').value ? document.getElementById('data-param-helpText').value : '';

    $("#reportViewer1")
        .telerik_ReportViewer({
            // The URL of the service which will serve reports.
            // The URL corresponds to the name of the controller class (ReportsController).
            // For more information on how to configure the service please check http://www.telerik.com/help/reporting/telerik-reporting-rest-conception.html.
            serviceUrl: "/api/reports/",

            // The URL for the report viewer template. The template can be edited -
            // new functionalities can be added and unneeded ones can be removed.
            // For more information please check http://www.telerik.com/help/reporting/html5-report-viewer-templates.html.

            //ReportSource - report description
            reportSource: {

                // The report can be set to a report file name (.trdx or .trdp report definition)
                // or CLR type name (report class definition).
                report: document.getElementById('data-param-reportName').value,
            },

            parameters: {
                editors: {
                    singleSelect: telerikReportViewer.ParameterEditorTypes.COMBO_BOX,
                    multiSelect: telerikReportViewer.ParameterEditorTypes.COMBO_BOX,
                }
            },

            // Specifies whether the viewer is in interactive or print preview mode.
            // PRINT_PREVIEW - Displays the paginated report as if it is printed on paper. Interactivity is not enabled.
            // INTERACTIVE - Displays the report in its original width and height without paging. Additionally interactivity is enabled.
            viewMode: telerikReportViewer.ViewModes.INTERACTIVE,

            // Sets the scale mode of the viewer.
            // Three modes exist currently:
            // FIT_PAGE - The whole report will fit on the page (will zoom in or out), regardless of its width and height.
            // FIT_PAGE_WIDTH - The report will be zoomed in or out so that the width of the screen and the width of the report match.
            // SPECIFIC - Uses the scale to zoom in and out the report.
            scaleMode: telerikReportViewer.ScaleModes.FIT_PAGE_WIDTH,

            // Zoom in and out the report using the scale
            // 1.0 is equal to 100%, i.e. the original size of the report
            scale: 2.0,

            //Enables or disables the accessibility features of the report viewer and its contents.
            enableAccessibility: false,
            parametersAreaPosition: "LEFT",
            documentMapVisible: false,
            updateUi: function (e) {

                $('.trv-parameter-title').css({ "font-weight": "100" });

                reportParameters.map(p => {
                    $('[title="' + p + '"]').css({ "font-weight": "700" })
                });
            },

            ready: function () {
                if ($("a[data-command='telerik_ReportViewer_help']").length === 0) {
                    $('ul[data-role="telerik_ReportViewer_MainMenu"]').prepend('<li aria-label="Help" class="k-item k-menu-item k-state-default k-first" role="menuitem"><a data-command="telerik_ReportViewer_help" title = "' + helpText + '" href= "#" class="k-link k-menu-link" > <i class="fa fa-info-circle" aria-hidden="true"></i></a ></li > ');

                }
            }
        });
});
