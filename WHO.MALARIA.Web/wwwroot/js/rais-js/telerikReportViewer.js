﻿/*
* TelerikReporting v14.2.20.1021 (http://www.telerik.com/products/reporting.aspx)
* Copyright 2020 Progress Software EAD. All rights reserved.
*
* Telerik Reporting commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/reporting.aspx
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
(function(e,f){"$:nomunge";var t=e.jQuery||e.Cowboy||(e.Cowboy={}),n;t.throttle=n=function(a,o,l,s){var c,u=0;if(typeof o!=="boolean"){s=l;l=o;o=f}function e(){var e=this,t=+new Date-u,r=arguments;function n(){u=+new Date;l.apply(e,r)}function i(){c=f}if(s&&!c){n()}c&&clearTimeout(c);if(s===f&&t>a){n()}else if(o!==true){c=setTimeout(s?i:n,s===f?a-t:a)}}if(t.guid){e.guid=l.guid=l.guid||t.guid++}return e};t.debounce=function(e,t,r){return r===f?n(e,t,false):n(e,r,t!==false)}})(window);(function(e,l,i,a,s){"use strict";var t=/{(\w+?)}/g;var o={DELETE:46,BACKSPACE:8,TAB:9,ESC:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,END:35,HOME:36};function r(){var e=i.navigator.userAgent.toLowerCase();if(e.indexOf("firefox")>-1){var r=Object.keys(o);var n=r.length;return function(e){for(var t=0;t<n;t++){if(o[r[t]]==e){return true}}}}return function(e){return false}}function c(e,t,r){return{xhr:e,status:t,error:r}}function u(a,o,e,t){return{left:a,top:o,width:e,height:t,right:function(){return a+e},bottom:function(){return o+t},union:function(e){var t=Math.min(a,e.left);var r=Math.min(o,e.top);var n=Math.max(this.right(),e.right())-t;var i=Math.max(this.bottom(),e.bottom())-r;return u(t,r,n,i)}}}var f=e.utils={generateGuidString:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=Math.random()*16|0,r=e=="x"?t:t&3|8;return r.toString(16)})},trim:function(e,t){return this.rtrim(this.ltrim(e,t),t)},replaceAll:function(e,t,r){return e.replace(new RegExp(t,"g"),r)},ltrim:function(e,t){if(t===s){t="s"}return e.replace(new RegExp("^["+t+"]+"),"")},rtrim:function(e,t){if(t===s){t="s"}return e.replace(new RegExp("["+t+"]+$"),"")},stringFormat:function(e,r){var n=Array.isArray(r);return e.replace(t,function(e,t){return r[n?parseInt(t):t]})},escapeHtml:function(e){return l("<div>").text(e).html()},isSpecialKey:r(),tryParseInt:function(e){if(/^(\-|\+)?([0-9]+)$/.test(e)){return Number(e)}return NaN},tryParseFloat:function(e){if(/^(\-|\+)?([0-9]+(\.[0-9]+)?)$/.test(e)){return Number(e)}return NaN},parseToLocalDate:function(e){if(e instanceof Date)return e;var t=/Z|[\+\-]\d\d:?\d\d/i.test(e);if(!t){e+="Z"}return new Date(e)},adjustTimezone:function(e){return new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()))},unadjustTimezone:function(e){return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds())},areEqual:function(e,t){if(e instanceof Date&&t instanceof Date){if(e.getTime()!==t.getTime()){return false}}else if(e!==t){return false}return true},reportSourcesAreEqual:function(e,t){if(e&&t&&e.report===t.report){var r="";if(e.parameters){r=JSON.stringify(e.parameters)}var n="";if(t.parameters){n=JSON.stringify(t.parameters)}return r===n}return false},areEqualArrays:function(e,t){if(e===null){if(t!==null){return false}else{return true}}else{if(t===null){return false}}if(e.length!==t.length){return false}for(var r=e.length-1;r>=0;r--){if(!f.areEqual(e[r],t[r])){return false}}return true},isSvgSupported:function e(){var t=/Version\/(\d+.\d+.\d+) Safari/.exec(navigator.userAgent);if(t&&t.length>1){var r=parseFloat(t[1]);return r>=6}return true},isInvalidClientException:function(e){return f.isExceptionOfType(e,"Telerik.Reporting.Services.Engine.InvalidClientException")},isExceptionOfType:function(e,t){if(!e)return false;if(!e.responseText)return false;var r=f.parseJSON(e.responseText);if(!r)return false;if(!r.exceptionType)return false;return r.exceptionType===t},parseJSON:function(e){try{return JSON.parse(e,function(e,t){if(e&&t){var r=e.charAt(0);if(r===r.toUpperCase()){var n=r.toLowerCase()+e.slice(1);this[n]=t}}return t})}catch(e){return null}},extend:function(){var e,t,r,n,i,a=0,o=arguments.length;i=o>1?arguments[a++]||{}:{};for(;a<o;a++){if((n=arguments[a])!=null){for(r in n){e=i[r];t=n[r];if(i===t){continue}if(t!==s){i[r]=t}}}}return i},each:function(e,t){var r,n=0;if(f.isArray(e)){r=e.length;for(;n<r;n++){if(t.call(e[n],n,e[n])===false){break}}}else{for(n in e){if(t.call(e[n],n,e[n])===false){break}}}return e},selector:function(){return a.querySelectorAll(arguments[0])},isArray:function(e){if(Array.isArray(e))return true;var t=!!e&&"length"in e&&e.length;if(typeof t==="number"){return true}return false},loadScriptWithCallback:function(e,t,r){var n=a.createElement("script");n.src=e;n.onload=function(){t(r)};n.onerror=function(){f.logError(new Error("Failed to load script "+e))};a.head.appendChild(n)},loadScript:function(e){var t={dataType:"script",cache:true,url:e};return f.$ajax(t)},filterUniqueLastOccurance:function(e){function t(e,t,r){return r.lastIndexOf(e)===t}return e.filter(t)},logError:function(e){var t=i.console;if(t&&t.error){t.error(e)}},findElement:function(e){if(e.constructor!=Array){e=[e]}var t=l(e[0]);for(var r=1;r<e.length;r++){t=t.find(e[r])}return t},toRgbColor:function(e){if(e&&e.length<6){var t=1;var r=e.split("");if(r[0]!=="#"){t=0}for(t;t<r.length;t++){r[t]=r[t]+r[t]}e=r.join("")}var n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return n?parseInt(n[1],16)+", "+parseInt(n[2],16)+", "+parseInt(n[3],16):null},isRgbColor:function(e){if(!e){return false}return e.indexOf(",")>-1?true:false},getColorAlphaValue:function(e){if(e==="Transparent"){return 0}if(!this.isRgbColor(e)){return 1}if(e.indexOf("#")!==-1){e=this.toRgbColor(e)}var t=e.split(",").map(function(e){return e.trim()});var r=t.length===4?parseFloat((parseFloat(t[3].replace(/[()]/g,""))/255).toFixed(2)):1;return r},$ajax:function(e){return new Promise(function(t,n){l.ajax(e).done(function(e){return t(e)}).fail(function(e,t,r){n(c(e,t,r))})})},rectangle:u};e.domUtils=function(){function r(e){return parseInt(e,10)||0}return{getMargins:function(e){var t=l(e);return{left:r(t.css("marginLeft")),right:r(t.css("marginRight")),top:r(t.css("marginTop")),bottom:r(t.css("marginBottom"))}},getPadding:function(e){var t=l(e);return{left:r(t.css("paddingLeft")),right:r(t.css("paddingRight")),top:r(t.css("paddingTop")),bottom:r(t.css("paddingBottom"))}},getBorderWidth:function(e){var t=l(e);return{left:r(t.css("borderLeftWidth")),right:r(t.css("borderRightWidth")),top:r(t.css("borderTopWidth")),bottom:r(t.css("borderBottomWidth"))}},scale:function(e,t,r,n,i){t=t||1;r=r||1;n=n||0;i=i||0;var a=f.stringFormat("scale({0}, {1})",[t,r]),o=f.stringFormat("{0} {1}",[n,i]);l(e).css("transform",a).css("-moz-transform",a).css("-ms-transform",a).css("-webkit-transform",a).css("-o-transform",a).css("-moz-transform-origin",o).css("-webkit-transform-origin",o).css("-o-transform-origin",o).css("-ms-transform-origin",o).css("transform-origin",o)}}}()})(window.telerikReportViewer=window.telerikReportViewer||{},window.jQuery,window,document);(function(e){"use strict";var t={controllerNotInitialized:"Controller is not initialized.",noReportInstance:"No report instance.",missingTemplate:"!obsolete resource!",noReport:"No report.",noReportDocument:"No report document.",missingOrInvalidParameter:"Missing or invalid parameter value. Please input valid data for all parameters.",invalidParameter:"Please input a valid value.",invalidDateTimeValue:"Please input a valid date.",parameterIsEmpty:"Parameter value cannot be empty.",cannotValidateType:"Cannot validate parameter of type {type}.",loadingFormats:"Loading...",loadingReport:"Loading report...",preparingDownload:"Preparing document to download. Please wait...",preparingPrint:"Preparing document to print. Please wait...",errorLoadingTemplates:"Error loading the report viewer's templates. (templateUrl = '{0}').",errorServiceUrl:"Cannot access the Reporting REST service. (serviceUrl = '{0}'). Make sure the service address is correct and enable CORS if needed. (https://enable-cors.org)",loadingReportPagesInProgress:"{0} pages loaded so far...",loadedReportPagesComplete:"Done. Total {0} pages loaded.",noPageToDisplay:"No page to display.",errorDeletingReportInstance:"Error deleting report instance: '{0}'.",errorRegisteringViewer:"Error registering the viewer with the service.",noServiceClient:"No serviceClient has been specified for this controller.",errorRegisteringClientInstance:"Error registering client instance.",errorCreatingReportInstance:"Error creating report instance (Report = '{0}').",errorCreatingReportDocument:"Error creating report document (Report = '{0}'; Format = '{1}').",unableToGetReportParameters:"Unable to get report parameters.",errorObtainingAuthenticationToken:"Error obtaining authentication token.",clientExpired:"Click 'Refresh' to restore client session.",promisesChainStopError:"Error shown. Throwing promises chain stop error.",renderingCanceled:"Report processing was canceled.",parameterEditorSelectNone:"clear selection",parameterEditorSelectAll:"select all",parametersAreaPreviewButton:"Preview",menuNavigateBackwardText:"Navigate Backward",menuNavigateBackwardTitle:"Navigate Backward",menuNavigateForwardText:"Navigate Forward",menuNavigateForwardTitle:"Navigate Forward",menuStopRenderingText:"Stop Rendering",menuStopRenderingTitle:"Stop Rendering",menuRefreshText:"Refresh",menuRefreshTitle:"Refresh",menuFirstPageText:"First Page",menuFirstPageTitle:"First Page",menuLastPageText:"Last Page",menuLastPageTitle:"Last Page",menuPreviousPageTitle:"Previous Page",menuNextPageTitle:"Next Page",menuPageNumberTitle:"Page Number Selector",menuDocumentMapTitle:"Toggle Document Map",menuParametersAreaTitle:"Toggle Parameters Area",menuZoomInTitle:"Zoom In",menuZoomOutTitle:"Zoom Out",menuPageStateTitle:"Toggle FullPage/PageWidth",menuPrintText:"Print...",menuContinuousScrollText:"Toggle Continuous Scrolling",menuSendMailText:"Send an email",menuPrintTitle:"Print",menuContinuousScrollTitle:"Toggle Continuous Scrolling",menuSendMailTitle:"Send an email",menuExportText:"Export",menuExportTitle:"Export",menuPrintPreviewText:"Toggle Print Preview",menuPrintPreviewTitle:"Toggle Print Preview",menuSearchText:"Search",menuSearchTitle:"Toggle Search",menuSideMenuTitle:"Toggle Side Menu",sendEmailFromLabel:"From:",sendEmailToLabel:"To:",sendEmailCCLabel:"CC:",sendEmailSubjectLabel:"Subject:",sendEmailFormatLabel:"Format:",sendEmailSendLabel:"Send",sendEmailCancelLabel:"Cancel",ariaLabelPageNumberSelector:"Page number selector. Showing page {0} of {1}.",ariaLabelPageNumberEditor:"Page number editor",ariaLabelExpandable:"Expandable",ariaLabelSelected:"Selected",ariaLabelParameter:"parameter",ariaLabelErrorMessage:"Error message",ariaLabelParameterInfo:"Contains {0} options",ariaLabelMultiSelect:"Multiselect",ariaLabelMultiValue:"Multivalue",ariaLabelSingleValue:"Single value",ariaLabelParameterDateTime:"DateTime",ariaLabelParameterString:"String",ariaLabelParameterNumerical:"Numerical",ariaLabelParameterBoolean:"Boolean",ariaLabelParametersAreaPreviewButton:"Preview the report",ariaLabelMainMenu:"Main menu",ariaLabelCompactMenu:"Compact menu",ariaLabelSideMenu:"Side menu",ariaLabelDocumentMap:"Document map area",ariaLabelDocumentMapSplitter:"Document map area splitbar.",ariaLabelParametersAreaSplitter:"Parameters area splitbar.",ariaLabelPagesArea:"Report contents area",ariaLabelSearchDialogArea:"Search area",ariaLabelSendEmailDialogArea:"Send email area",ariaLabelSearchDialogStop:"Stop search",ariaLabelSearchDialogOptions:"Search options",ariaLabelSearchDialogNavigateUp:"Navigate up",ariaLabelSearchDialogNavigateDown:"Navigate down",ariaLabelSearchDialogMatchCase:"Match case",ariaLabelSearchDialogMatchWholeWord:"Match whole word",ariaLabelSearchDialogUseRegex:"Use regex",ariaLabelMenuNavigateBackward:"Navigate backward",ariaLabelMenuNavigateForward:"Navigate forward",ariaLabelMenuStopRendering:"Stop rendering",ariaLabelMenuRefresh:"Refresh",ariaLabelMenuFirstPage:"First page",ariaLabelMenuLastPage:"Last page",ariaLabelMenuPreviousPage:"Previous page",ariaLabelMenuNextPage:"Next page",ariaLabelMenuPageNumber:"Page number selector",ariaLabelMenuDocumentMap:"Toggle document map",ariaLabelMenuParametersArea:"Toggle parameters area",ariaLabelMenuZoomIn:"Zoom in",ariaLabelMenuZoomOut:"Zoom out",ariaLabelMenuPageState:"Toggle FullPage/PageWidth",ariaLabelMenuPrint:"Print",ariaLabelMenuContinuousScroll:"Continuous scrolling",ariaLabelMenuSendMail:"Send an email",ariaLabelMenuExport:"Export",ariaLabelMenuPrintPreview:"Toggle print preview",ariaLabelMenuSearch:"Search in report contents",ariaLabelMenuSideMenu:"Toggle side menu",ariaLabelSendEmailFrom:"From email address",ariaLabelSendEmailTo:"Recipient email address",ariaLabelSendEmailCC:"Carbon Copy email address",ariaLabelSendEmailSubject:"Email subject:",ariaLabelSendEmailFormat:"Report format:",ariaLabelSendEmailSend:"Send email",ariaLabelSendEmailCancel:"Cancel sending email",searchDialogTitle:"Search in report contents",searchDialogSearchInProgress:"searching...",searchDialogNoResultsLabel:"No results",searchDialogResultsFormatLabel:"Result {0} of {1}",searchDialogStopTitle:"Stop Search",searchDialogNavigateUpTitle:"Navigate Up",searchDialogNavigateDownTitle:"Navigate Down",searchDialogMatchCaseTitle:"Match Case",searchDialogMatchWholeWordTitle:"Match Whole Word",searchDialogUseRegexTitle:"Use Regex",searchDialogCaptionText:"Find",searchDialogPageText:"page",sendEmailDialogTitle:"Send Email",sendEmailValidationEmailRequired:"Email field is required",sendEmailValidationEmailFormat:"Email format is not valid",sendEmailValidationSingleEmail:"The field accepts a single email address only",sendEmailValidationFormatRequired:"Format field is required",errorSendingDocument:"Error sending report document (Report = '{0}')."};e.sr=e.utils.extend(t,e.sr)})(window.telerikReportViewer=window.telerikReportViewer||{});(function(e,b,t,r,n){"use strict";var i=e.utils;if(!i){throw"Missing telerikReportViewer.utils"}var f=i.rectangle;var a={$placeholder:null,$scrollableContainer:null,itemsInitialState:{},xFrozenAreasBounds:{},yFrozenAreasBounds:{},freezeMaxZIndex:{},zIndex:1,freezeBGColor:{},currentlyfreezedContainer:{vertical:{},horizontal:{}},isInitialize:false,init:function(e){this.$placeholder=e;this.$scrollableContainer=e.find(".trv-page-container");if(this.isInitialize){this.reset(e)}this._attachToScrollEvent();this.isInitialize=true},reset:function(e){this.$placeholder=e;this.$scrollableContainer=e.find(".trv-page-container");this.itemsInitialState={};this.xFrozenAreasBounds={};this.yFrozenAreasBounds={};this.currentlyfreezedContainer={vertical:{},horizontal:{}}},_attachToScrollEvent:function(){var s=this;this.$scrollableContainer.scroll(function e(){var t=s.$scrollableContainer.find("div[data-sticky-id]");if(t.length){var r=t.map(function(e,t){return b(t).attr("data-sticky-id")}).get();var n=r.filter(function(e,t){return t===r.indexOf(e)});var i=s.$scrollableContainer.scrollTop();var a=s.$scrollableContainer.scrollLeft();for(var o=0;o<n.length;o++){var l=n[o];if(!s.itemsInitialState[l]){s._saveFreezeItemsInitialState(l)}s._updateFreezeItemsOnScroll(l,i,a)}}})},_saveFreezeItemsInitialState:function(e){var t=b("[data-sticky-direction][data-sticky-id='"+e+"']");var r=b("[data-reporting-action][data-sticky-id='"+e+"']");var n;var i;this.itemsInitialState[e]={};this.freezeBGColor[e]=b("[data-id='"+e+"']").attr("data-sticky-bg-color");for(var a=0;a<t.length;a++){var o=b(t[a]);var l=o.attr("data-sticky-direction");var s=o.attr("data-id");var c=o.position();var u=f(c.left,c.top,o.outerWidth(true),o.outerHeight(true));switch(l){case"Vertical":n=n?n.union(u):u;break;case"Horizontal":i=i?i.union(u):u;break;default:}this._saveFreezeItemInitialState(e,o,s,c)}this.freezeMaxZIndex[e]=r.length?r.css("zIndex"):this.zIndex;this.yFrozenAreasBounds[e]=n;this.xFrozenAreasBounds[e]=i},_saveFreezeItemInitialState:function(e,t,r,n){var i=t.css("background-color");var a=this._hasSetBgColor(i);var o={top:n.top,left:n.left,zIndex:t.css("zIndex"),hasBgColor:a};this.itemsInitialState[e][r]=o},_updateFreezeItemsOnScroll:function(e,t,r){var n=b("div[data-id='"+e+"']");if(this._isInScrollVisibleArea(n)){var i=n.closest(".trv-report-page");var a=i.position();var o=parseFloat(i.css("margin-top"));var l=parseFloat(i.css("padding-top"));var s=parseFloat(i.css("padding-left"));var c=parseFloat(i.css("border-width"));var u=b("[data-sticky-direction*='Horizontal'][data-sticky-id='"+e+"']");var f=b("[data-sticky-direction*='Vertical'][data-sticky-id='"+e+"']");var d=u.length>0;var p=f.length>0;var v=n.position();var g=v.top+a.top+o+l+c;var h=v.left+s+c;var m=t-g;var E=r-h;if(p&&m>0){if(t<=n.outerHeight()+g-this.yFrozenAreasBounds[e].height){this.currentlyfreezedContainer.vertical[e]=true;this._updateUIElementsPosition(f,"top",m,e)}}else{if(this.currentlyfreezedContainer.vertical[e]){delete this.currentlyfreezedContainer.vertical[e];this._updateUIElementsPosition(f,"top",-1,e)}}if(d&&E>0){if(r<=n.outerWidth()+h-this.xFrozenAreasBounds[e].width){this.currentlyfreezedContainer.horizontal[e]=true;this._updateUIElementsPosition(u,"left",E,e)}}else{if(this.currentlyfreezedContainer.horizontal[e]){delete this.currentlyfreezedContainer.horizontal[e];this._updateUIElementsPosition(u,"left",-1,e)}}}else{if(this.currentlyfreezedContainer.horizontal[e]||this.currentlyfreezedContainer.vertical[e]){this._resetToDefaultPosition(e)}}},_resetToDefaultPosition:function(e){var t=b("[data-sticky-direction*='Horizontal'][data-sticky-id='"+e+"']");var r=b("[data-sticky-direction*='Vertical'][data-sticky-id='"+e+"']");this._updateUIElementsPosition(r,"top",-1,e);this._updateUIElementsPosition(t,"left",-1,e);delete this.currentlyfreezedContainer.horizontal[e];delete this.currentlyfreezedContainer.vertical[e]},_updateUIElementsPosition:function(e,t,r,n){for(var i=0;i<e.length;i++){var a=b(e[i]);var o=a.attr("data-sticky-direction");var l=o.indexOf(",")>0;var s=a.attr("data-id");var c=this.itemsInitialState[n][s];var u=c[t];var f=c["zIndex"];var d=c["hasBgColor"];var p=1;var v=this.freezeMaxZIndex[n]?this.freezeMaxZIndex[n]:p;if(l){p=f!=="auto"?f:v+2}else{p=f!=="auto"?f+1:v}var g={"z-index":p};if(r>=0){u=u+r}else{g["z-index"]=f}if(!d){this._applyBgColorOnScroll(a,l,d,r>=0,n)}g[t]=u+"px";a.css(g)}},_applyBgColorOnScroll:function(e,t,r,n,i){if(e.is("img")){return true}if(t&&this._isFrozen(i)&&!r){e.css("background-color",this.freezeBGColor[i]);return true}if(n){e.css("background-color",this.freezeBGColor[i])}else{e.css("background-color","initial")}},_hasSetBgColor:function(e){return i.getColorAlphaValue(e)>0},_isFrozen:function(e){return this.currentlyfreezedContainer.horizontal[e]||this.currentlyfreezedContainer.vertical[e]},_isInScrollVisibleArea:function(e){var t=e.closest(".trv-report-page");var r=e.position();return this._isVisibleVertically(e,t,r)&&this._isVisibleHorizontally(e,t,r)},_isVisibleHorizontally:function(e,t,r){var n=parseFloat(t.css("padding-left"));var i=this.$scrollableContainer.scrollLeft();var a=this.$scrollableContainer.width();var o=e.outerWidth(true);var l=r.left+n;return l>i-o&&l<i+o+a},_isVisibleVertically:function(e,t,r){var n=parseFloat(t.css("padding-top"));var i=t.position();var a=this.$scrollableContainer.scrollTop();var o=this.$scrollableContainer.height();var l=e.outerHeight(true);var s=r.top+n+i.top;return s>a-l&&s<a+l+o}};e.uiFreezeCoordinator=a})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,u,t,r,n){"use strict";var c=e.utils;if(!c){throw"Missing telerikReportViewer.utils"}var i={controller:{},$placeholder:null,$pageContainer:null,pageContainer:null,$pageWrapper:null,pageWrapper:null,viewMode:null,loadedPage:{},scrollInProgress:false,enabled:false,pageCount:0,additionalTopOffset:130,pageDistance:20,oldScrollTopPosition:0,skeletonTemplate:'<div class="trv-report-page trv-skeleton-page trv-skeleton-{0}" style="{1}" data-page="{0}">'+'<div class="trv-skeleton-wrapper" style="{2}"></div></div>',init:function e(t,r){var n=this;n.$placeholder=u("[data-selector='"+r.viewerSelector+"']").find(t);n.$pageContainer=this.$placeholder.find(".trv-page-container");n.pageContainer=this.$pageContainer[0];n.$pageWrapper=this.$placeholder.find(".trv-page-wrapper");n.pageWrapper=this.$pageWrapper[0];n.controller=r.controller;n.viewMode=null;n.loadedPage={};n.scrollInProgress=false;n.enabled=false;n.pageCount=0;n.controller.scale(function(e,t){if(n.enabled){setTimeout(function(){n._loadMorePages();n._keepCurrentPageInToView()},100)}}).onLoadedReportChange(function(e){if(n.enabled){n.disable();if(e!=="trv.ON_LOADED_REPORT_CHANGE"){setTimeout(function(){n.controller.getPageData(1).then(function(e){n.renderPage(e)})})}}}).viewModeChanged(function(e){if(n.enabled){n.disable()}}).interactiveActionExecuting(function(e,t){var r=t.action.Type;if(n.enabled&&(r==="sorting"||r==="toggleVisibility")){n.disable()}}).updatePageDimensionsReady(function(e,t){if(n.enabled&&n._currentPageNumber()>0){n._keepCurrentPageInToView()}}).pageCountChange(function(e,t){if(n.enabled&&n.pageCount!==t){if(n._currentPageNumber()>0&&!n.scrollInProgress){n._loadMorePages()}if(t>1){n._initEvents()}n.pageCount=t}})},isEnabled:function e(){return this.enabled},disable:function e(){this.$pageWrapper.empty();this.enabled=false;this.loadedPage={};this.pageCount=0;this.$placeholder.removeClass("scrollable");this._unbind()},enable:function(){this.enabled=true;this.$placeholder.addClass("scrollable");this._initEvents()},renderPage:function e(t){var r=this,n=r.controller.viewMode(),i=r.$placeholder.find('[data-page="'+t.pageNumber+'"]');if(!r.enabled){r.enabled=true;r.$placeholder.addClass("scrollable");if(n!==r.viewMode||!i.length){r._updatePageArea(t)}else{r._render(t,true);this.$pageContainer.scrollTop(3);r._setCurrentPage(t.pageNumber)}r.viewMode=r.controller.viewMode();r._loadMorePages()}else{if(n!==r.viewMode||!i.length){r._updatePageArea(t)}else{r._navigateToPage(t,i)}r.viewMode=r.controller.viewMode()}},navigateToElement:function e(t,r){var n=this;n.scrollInProgress=true;if(n._isSkeletonScreen(null,r)){n.controller.getPageData(r).then(function(e){n._render(e,false);n.$pageContainer.animate({scrollTop:t},500,function(){n._setCurrentPage(r);setTimeout(function(){n.scrollInProgress=false},100)})})}else{n.$pageContainer.animate({scrollTop:t},500,function(){n._setCurrentPage(r);setTimeout(function(){n.scrollInProgress=false},100)})}},_setCurrentPage:function e(t){var r=this;if(t!==r._currentPageNumber()){r.controller.currentPageNumber(t)}if(r.controller.pageCount()>1){r.$placeholder.find(".k-state-default").removeClass("k-state-default");r.$placeholder.find('[data-page="'+t+'"]').addClass("k-state-default")}r._loadNextPreviousPage(t)},_updatePageArea:function e(t){var r=this,n=0,i=t.pageNumber;r.scrollInProgress=true;if(i>1){r._generateSkeletonScreens(i)}r._render(t,false);r._setCurrentPage(t.pageNumber);setTimeout(function(){n=i>1?r.$placeholder.find('[data-page="'+i+'"]').position().top:0;r.$pageContainer.animate({scrollTop:n},0,function(){r.scrollInProgress=false})},100)},_navigateToPage:function e(t,r){var n=this;n.scrollInProgress=true;var i=r.position().top,a=n.$placeholder.find(".trv-report-page"),o=t.pageNumber,l=u(a[0]).height();if(n._isSkeletonScreen(r,o)){n.controller.getPageData(o).then(function(e){n._render(e,false);n.$pageContainer.animate({scrollTop:i},500,function(){setTimeout(function(){n._setCurrentPage(e.pageNumber);n.scrollInProgress=false})})})}else{n._updatePageContent(t,r);n.$pageContainer.animate({scrollTop:i},500,function(){setTimeout(function(){n._setCurrentPage(t.pageNumber);n.scrollInProgress=false})})}},_updatePageContent:function e(t,r){this._updatePageStyle(t);var n=t.pageNumber,i=u(u.parseHTML(t.pageContent)),a=i.find("div.sheet"),o=this.$placeholder.find('[data-page="'+n+'"]');a.css("margin",0);o.append(a).append(u('<div class="trv-page-overlay"></div>'));r.replaceWith(o);this.controller.scrollPageReady({page:t,target:o})},_currentPageNumber:function e(){return this.controller.currentPageNumber()},_isSkeletonScreen:function e(t,r){if(!t){t=this.$placeholder.find('[data-page="'+r+'"]')}return t.hasClass("trv-skeleton-"+r)},_addSkeletonScreen:function e(t,r){var n=this,i=r?parseInt(t+1):parseInt(t-1),a=n.$placeholder.find('[data-page="'+i+'"]'),o=a.attr("style"),l=a.find("sheet").attr("style"),s=c.stringFormat(n.skeletonTemplate,[t,o,l]);if(r){n.$pageWrapper.prepend(s)}else{n.$pageWrapper.append(s)}},_generateSkeletonScreens:function e(t){var r=this,n="",i=this.$placeholder.find('[data-page="1"]'),a=i.attr("style"),o=i.find("sheet").attr("style"),l=r.$placeholder.find(".trv-report-page").last().attr("data-page"),s=l?parseInt(l)+1:1;for(s;s<t;s++){n=n+c.stringFormat(r.skeletonTemplate,[s,a,o])}r.$pageWrapper.append(u(n))},_loadMorePages:function e(){var t=this,r=t.controller.pageCount(),n=t.$pageContainer.innerHeight()>t.$pageWrapper.innerHeight();if(r>1){if(n){t.scrollInProgress=true;var i=parseInt(t.$placeholder.find(".trv-report-page").last().attr("data-page")),a=i+1;if(a<=r){t.controller.getPageData(a).then(function(e){t._render(e,false);t._loadMorePages();t.scrollInProgress=false})}}else{t._loadVisiblePages();t.scrollInProgress=false}}},_loadVisiblePages:function e(){var i=this,t=i.$placeholder.find(".trv-report-page");u.each(t,function(e,t){var r=u(t),n=parseInt(r.attr("data-page"));if(i._scrolledInToView(r)&&i._isSkeletonScreen(r,n)){i.controller.getPageData(n).then(function(e){i._render(e,false)})}})},_scrolledInToView:function e(t){var r=t[0].getBoundingClientRect(),n=t.closest(".trv-pages-area")[0].getBoundingClientRect(),i=n.top,a=n.top+n.height,o=r.top,l=o+t.outerHeight(true),s=this.additionalTopOffset+i,c=o>0&&o<a,u=l<a&&l>s;return c||u},_render:function e(t,r){var n=this,i=t.pageNumber,a=n.$placeholder.find('[data-page="'+i+'"]');if(!r&&a&&a.length&&!n._isSkeletonScreen(a,i)){return}n.loadedPage[i]=t;n._updatePageStyle(t);var o=u(u.parseHTML(t.pageContent)),l=o.find("div.sheet"),s=u('<div class="trv-report-page" data-page="'+i+'"></div>');l.css("margin",0);s.append(l).append(u('<div class="trv-page-overlay"></div>'));if(r){n.$pageWrapper.empty()}n.$pageWrapper.removeData().data("pageNumber",i);var c=n.$placeholder.find(".trv-skeleton-"+i);if(c.length){c.replaceWith(s)}else{n.$pageWrapper.append(s)}n.controller.scrollPageReady({page:t,target:s})},_updatePageStyle:function e(t){var r=this,n=r.loadedPage[r._lastLoadedPage()]||t,i="trv-"+r.controller.clientId()+"-styles",a;u("#"+i).remove();a=u("<style id="+i+"></style>");a.append(n.pageStyles);a.appendTo("head")},_lastLoadedPage:function e(){var t=this,r;for(var n in t.loadedPage){if(t.loadedPage.hasOwnProperty(n)){r=n}}return r},_loadNextPreviousPage:function e(t){var r=this,n,i,a,o;if(t<r.controller.pageCount()){n=t+1;a=r.$placeholder.find('[data-page="'+n+'"]')}if(t>1){i=t-1;o=r.$placeholder.find('[data-page="'+i+'"]')}if(o&&o.length&&r._isSkeletonScreen(o,i)){r.controller.getPageData(i).then(function(e){r._render(e,false)})}if(a&&a.length&&r._isSkeletonScreen(a,n)){r.controller.getPageData(n).then(function(e){r._render(e,false)})}},_clickPage:function e(t){var r=this,n=r._currentPageNumber(),i=parseInt(t.attr("data-page"));if(n!==i){if(r._isSkeletonScreen(t,i)){r.controller.getPageData(i).then(function(e){r._render(e,false,true);r._setCurrentPage(e.pageNumber)})}else{r._setCurrentPage(i)}}},_initEvents:function e(){var r=this;r.$pageContainer.off("click",".trv-report-page").on("click",".trv-report-page",function(e){r._clickPage(u(e.currentTarget))});r.$pageContainer.scroll(u.throttle(250,function(){var e=r.$placeholder.find(".trv-report-page"),t=parseInt((r.$pageContainer.scrollTop()+r.$pageContainer.innerHeight()).toFixed(0));if(!r.scrollInProgress&&r.oldScrollTopPosition!==t){if(r.oldScrollTopPosition>t){r._scrollUp(e)}else{r._scrollDown(e,t)}}r.oldScrollTopPosition=t}));r.$pageContainer.scroll(u.debounce(250,function(){var e=r.$placeholder.find(".trv-report-page"),t=parseInt((r.$pageContainer.scrollTop()+r.$pageContainer.innerHeight()).toFixed(0));if(!r.scrollInProgress&&e.length&&r.oldScrollTopPosition!==t){r._advanceCurrentPage(e)}}))},_unbind:function(){var e=this;e.$pageContainer.off("click",".trv-report-page");e.$pageContainer.off("scroll")},_advanceCurrentPage:function e(t){var r=this;var n=r._findNewCurrentPage(t),i,a=r._currentPageNumber(),o=r._scrolledInToView(r.$placeholder.find('[data-page="'+a+'"]'));if(n!==-1){n=u(n);i=parseInt(n.attr("data-page"));if(a!==i&&!o){if(r._isSkeletonScreen(n,i)){r.controller.getPageData(i).then(function(e){r._render(e,false,true);r._setCurrentPage(e.pageNumber)})}else{r._setCurrentPage(i)}}}else{console.log("Page not found - ",n)}},_findNewCurrentPage:function e(t){var r=this,n=Math.floor(t.length/2),i=r._findPageInViewPort(n,t);if(t.length===1){return t[0]}if(i===0){return t[n]}else if(i<0&&t.length>1){return r._findNewCurrentPage(t.splice(n,Number.MAX_VALUE))}else if(i>0&&t.length>1){return r._findNewCurrentPage(t.splice(0,n))}else{return-1}},_findPageInViewPort:function e(t,r){var n=this.$placeholder.find(r[t]),i=n[0].getBoundingClientRect(),a=n.closest(".trv-pages-area")[0].getBoundingClientRect(),o=a.top,l=a.top+a.height,s=i.top,c=s+n.outerHeight(true),u=this.additionalTopOffset+o,f=s<=u&&u<c;if(f){return 0}if(c<u){return-1}else{return 1}},_scrollDown:function e(t,r){var n=this;if(r>=n.pageContainer.scrollHeight){var i=parseInt(u(t[t.length-1]).attr("data-page")),a=i+1;if(n._currentPageNumber()<a&&a<=n.controller.pageCount()){n._addSkeletonScreen(a,false);n.controller.getPageData(a).then(function(e){n._render(e,false)})}}else{n._advanceCurrentPage(t);n._loadVisiblePages()}},_scrollUp:function e(t){var r=this;if(r.$pageContainer.scrollTop()===0){var n=u(t[0]),i=parseInt(n.attr("data-page")),a=i-1;if(r._currentPageNumber()>a&&a>=1){r._addSkeletonScreen(a,true);r.controller.getPageData(a).then(function(e){r._render(e,false);r.$pageContainer.scrollTop(3)})}}else{r._advanceCurrentPage(t);r._loadVisiblePages()}},_keepCurrentPageInToView:function e(){var t=this,r=t.$placeholder.find('[data-page="'+t._currentPageNumber()+'"]'),n=r.position().top,i=r.innerHeight(),a=t.$pageContainer.innerHeight(),o;t.scrollInProgress=true;if(i<a){o=(a-i)/2;n=parseInt(n-o)}t.$pageContainer.animate({scrollTop:n},0,function(){setTimeout(function(){t.scrollInProgress=false},100)})}};e.scroll=i})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,l,s,t){"use strict";var c=e.utils;if(!c){throw"Missing telerikReportViewer.utils"}function r(){function e(){var e=["AcroPDF.PDF.1","PDF.PdfCtrl.6","PDF.PdfCtrl.5"];var r=null;c.each(e,function(e,t){try{r=new ActiveXObject(t);if(r){return false}}catch(e){}});return r}return{hasPdfPlugin:function(){return e()!==null}}}function n(){function e(){var e=/Firefox[\/\s](\d+\.\d+)/.exec(navigator.userAgent);if(null!==e&&e.length>1){var t=parseFloat(e[1]);if(t>=19){return false}}var r=navigator.mimeTypes["application/pdf"];var n=r!==null?r.enabledPlugin:null;if(n){var i=n.description;return i.indexOf("Adobe")!==-1&&(i.indexOf("Version")===-1||parseFloat(i.split("Version")[1])>=6)}return false}return{hasPdfPlugin:function(){return e()}}}function i(i){function e(){var r=navigator.plugins;var n=false;c.each(r,function(e,t){if(r[e].name===i||r[e].name==="Adobe Acrobat"){n=true;return false}});return n}return{hasPdfPlugin:function(){return e()}}}function a(){return{hasPdfPlugin:function(){return false}}}function o(){if(l.navigator){var e=l.navigator.userAgent.toLowerCase();if(e.indexOf("msie")>-1||e.indexOf("mozilla")>-1&&e.indexOf("trident")>-1)return r();else if(e.indexOf("firefox")>-1)return n();else if(e.indexOf("edg/")>-1)return i("Microsoft Edge PDF Plugin");else if(e.indexOf("chrome")>-1)return i("Chrome PDF Viewer");else if(e.indexOf("safari")>-1)return i("WebKit built-in PDF");else return a()}return null}var u=o();var f=u?u.hasPdfPlugin():false;e.printManager=function(){var n;function e(e){var r=null;if(!n){n=s.createElement("iframe");n.style.display="none";n.onload=function(){try{n.contentDocument.execCommand("print",true,null)}catch(e){c.logError(e)}finally{if(r){(l.URL||l.webkitURL).revokeObjectURL(r)}}}}if(a(e)&&i()){n.src=e;s.body.appendChild(n);return}var t=new XMLHttpRequest;t.open("GET",e,true);t.responseType="arraybuffer";t.onload=function(e){if(this.status===200){var t=new Blob([this.response],{type:"application/pdf"});if(i()){l.navigator.msSaveOrOpenBlob(t)}else{r=(l.URL||l.webkitURL).createObjectURL(t);n.src=r;s.body.appendChild(n)}}else{console.log("Could not retrieve remote PDF document.")}};t.send()}function i(){return l.navigator&&l.navigator.msSaveOrOpenBlob}function a(e){var t=l.location;var r=s.createElement("a");r.setAttribute("href",e);if(r.host==""){r.href=r.href}return t.hostname===r.hostname&&t.protocol===r.protocol&&t.port===r.port}function t(e){l.open(e,"_self")}var r=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);var o=r?t:e;return{print:function(e){o(e)},getDirectPrintState:function(){return f}}}()})(window.telerikReportViewer=window.telerikReportViewer||{},window,document);(function(Y,K,X,e,t){"use strict";var Q=Y.sr;if(!Q){throw"Missing telerikReportViewer.sr"}var r=Y.utils;if(!r){throw"Missing telerikReportViewer.utils"}var q={};function n(e,t,r){t=K.extend({},q,t);var n=t.controller,i=false,a=false,o,u,l=r.viewerSelector,s,c,f,d,p,v,g,h,m,E,b,T=K("[data-selector='"+l+"']").find(".trv-report-viewer");if(!n){throw"No controller (telerikReporting.ReportViewerController) has been specified."}if(!r.sendEmail||!r.sendEmail.enabled){var P=K("[data-selector='"+l+"']").find("a[data-command='telerik_ReportViewer_toggleSendEmailDialog']").closest(".k-item ");P.hide();return}n.getSendEmailDialogState(function(e,t){t.visible=a}).setSendEmailDialogVisible(function(e,t){_(t.visible)}).setSearchDialogVisible(function(e,t){if(t.visible&&a){_(!a)}}).beginLoadReport(w).viewModeChanged(w);n.getDocumentFormats().then(function(e){m=e});function w(){_(false)}function _(e){a=e;if(e){I();V(r.sendEmail);u.open()}else{if(u&&u.options.visible){u.close()}}}function R(){return h?h.value():bodyText.val()}function I(){if(!i){o=K(e);s=o.find("[name='from']");c=o.find("[name='to']");f=o.find("[name='cc']");d=o.find("[name='subject']");v=o.find("[name='format']");g=o.find("textarea");S();D();H(o);u=T.find(".trv-send-email-window").kendoWindow({title:Q.sendEmailDialogTitle,minWidth:350,minHeight:350,maxHeight:900,modal:true,close:function(){C();$()},open:function(){A();M()},deactivate:function(){n.setSendEmailDialogVisible({visible:false})},activate:function(){u.wrapper.find(".trv-send-email-fields input[type='email']:visible").first().focus();setTimeout(function(){k()},250)}}).data("kendoWindow");u.wrapper.addClass("trv-send-email");p=v.kendoComboBox({dataTextField:"localizedName",dataValueField:"name",dataSource:m||[],filter:"startswith",dataBound:function(){this.select(0);this.trigger("change")}}).data("kendoComboBox");o.on("keydown",'[name="format_input"]',function(e){var t=9;if(e.keyCode===t&&h){setTimeout(function(){h.focus()})}});h=g.kendoEditor({tools:["bold","italic","underline","strikethrough","justifyLeft","justifyCenter","justifyRight","justifyFull","insertUnorderedList","insertOrderedList","indent","outdent","createLink","unlink","cleanFormatting","formatting","fontName","fontSize","foreColor","backColor","subscript","superscript"]}).data("kendoEditor");V(r.sendEmail);i=true}}K(X).resize(function(){if(u&&u.options.visible){C();A();M()}});function S(){o.find(".trv-send-email-field input").each(function(){var e=K(this),t=e.attr("name");e.attr("id",l+"-"+t)});o.find(".trv-send-email-label label").each(function(){var e=K(this),t=e.attr("for");e.attr("for",l+"-"+t)})}function C(){var e=u.element.parent(".k-window");b=e.offset()}function A(){var e=u.element.parent(".k-window"),t=K(X).width(),r=350;if(t>800){r=720}e.css({width:r});u.refresh({width:r})}function M(){if(!b){u.center()}else{var e=10,t=K(X).innerWidth(),r=K(X).innerHeight(),n=u.wrapper,i=n.outerWidth(true),a=n.outerHeight(true),o=b.left,l=b.top,s=o+i,c=l+a;if(s>t-e){o=Math.max(e,t-i-e);n.css({left:o});u.setOptions({position:{left:o}})}if(c>r-e){l=Math.max(e,r-a-e);n.css({top:l});u.setOptions({position:{top:l}})}}}var N={Send:"sendEmail_Send",Cancel:"sendEmail_Cancel"};function D(){E={sendEmail_Cancel:new j(function(){B()}),sendEmail_Send:new j(function(e){y()})};var e=Y.binder;e.bind(o.find(".trv-send-email-actions"),{controller:n,commands:E},r)}function y(e,t){var r={from:s.val(),to:c.val(),cc:f.val(),subject:d.val(),format:p.value(),body:R(),deviceInfo:{}};if(O()){n.sendReport(r);B()}}function k(){s.off("blur").on("blur",function(e){if(!L(K(this))){F(K(this),false)}});c.off("blur").on("blur",function(e){if(!L(K(this))){F(K(this),true)}});f.off("blur").on("blur",function(e){if(K(this).val().length){F(K(this),true)}else{G(K(this))}})}function O(){var e=L(s)||!F(s,false),t=L(c)||!F(c,true),r=f.val().length&&!F(f,true),n=p.value().length;if(!n){x(v,"data-required-msg")}if(e||t||r||!n){return false}return true}function V(e){s.val(e&&e.from||"");c.val(e&&e.to||"");f.val(e&&e.cc||"");d.val(e&&e.subject||"");if(e&&e.format){p.value(e.format)}else{p.select(0)}p.trigger("change");h.value(e&&e.body||"")}function L(e){if(!e.val().length){x(e,"data-required-msg");return true}G(e);return false}function x(e,t){var r=Q[e.attr(t)];K('[data-for="'+e.attr("name")+'"]').addClass("-visible").html(r)}function G(e){K('[data-for="'+e.attr("name")+'"]').removeClass("-visible")}function F(e,t){var r=e.val();if(t){var n=r.split(/[\s,;]+/);for(var i=0;i<n.length;i++){if(!U(n[i].trim(),e)){return false}}return true}else{return U(r,e)}}function U(e,t){var r=/\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;if(e.indexOf(",")>-1||e.indexOf(";")>-1){x(t,"data-single-email-msg");return false}if(!r.test(e)){x(t,"data-email-msg");return false}return true}function B(){u.close()}function $(){K(".k-invalid-msg").removeClass("-visible")}function H(e){if(!e){return}var t=e.find(".trv-replace-string"),r=e.find("[aria-label]"),n=e.find("[title]");if(t.length){K.each(t,function(e,t){W(K(t))})}if(r.length){K.each(r,function(e,t){z(K(t),"aria-label")})}if(n.length){K.each(n,function(e,t){z(K(t),"title")})}}function W(e){if(e){e.text(Q[e.text()])}}function z(e,t){if(e){e.attr(t,Q[e.attr(t)])}}function j(e){var r=true;var n=false;var i={enabled:function(e){if(arguments.length===0){return r}var t=Boolean(e);r=t;K(this).trigger("enabledChanged");return i},checked:function(e){if(arguments.length===0){return n}var t=Boolean(e);n=t;K(this).trigger("checkedChanged");return i},exec:e};return i}}var i="telerik_ReportViewer_SendEmail";K.fn[i]=function(e,t){return r.each(this,function(){if(!K.data(this,i)){K.data(this,i,new n(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,t,r){"use strict";var h=e.utils;if(!h){throw"Missing telerikReporting.utils"}var n="application/json",m="application/json; charset=UTF-8",i="application/x-www-form-urlencoded; charset=UTF-8",l="GET",E="POST",a="PUT",o="DELETE";var s={};e.ServiceClient=function(t){t=h.extend({},s,t);var u=h.rtrim(t.serviceUrl||t.baseUrl,"\\/"),r;var f=h.$ajax;function d(e){if(!e)throw"Invalid clientID"}function p(e,t){t=h.extend({},{baseUrl:u},t);return h.stringFormat(e,t)}function v(e){return e?{headers:{Authorization:"Bearer "+e}}:{}}function g(){if(!r){var e=t.loginInfo;if(e&&e.url&&(e.username||e.password)){r=f({url:e.url,type:E,data:{grant_type:"password",username:e.username,password:e.password},dataType:"json",contentType:i}).then(function(e){return e.access_token})}else{r=Promise.resolve()}}return r}return{_urlFromTemplate:p,registerClient:function(r){return g().then(function(e){var t=h.extend(v(e),r,{type:E,url:p("{baseUrl}/clients"),dataType:"json",data:JSON.stringify({timeStamp:Date.now()})});return f(t)}).then(function(e){if(e.Message){throw e.Message}return e.clientId})},unregisterClient:function(r,n){d(r);return g().then(function(e){var t=h.extend(v(e),n,{type:o,url:p("{baseUrl}/clients/{clientID}",{clientID:r})});return f(t)})},getParameters:function(r,n,i,a){d(r);return g().then(function(e){var t=h.extend(v(e),a,{type:E,url:p("{baseUrl}/clients/{clientID}/parameters",{clientID:r}),contentType:m,dataType:"json",data:JSON.stringify({report:n,parameterValues:i})});return f(t)})},createReportInstance:function(r,n,i,a){d(r);return g().then(function(e){var t=h.extend(v(e),a,{type:E,url:p("{baseUrl}/clients/{clientID}/instances",{clientID:r}),contentType:m,dataType:"json",data:JSON.stringify({report:n,parameterValues:i})});return f(t)}).then(function(e){return e.instanceId})},deleteReportInstance:function(r,n,i){d(r);return g().then(function(e){var t=h.extend(v(e),i,{type:o,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}",{clientID:r,instanceID:n})});return f(t)})},createReportDocument:function(r,n,i,a,o,l,s,c){d(r);return g().then(function(e){a=a||{};a["BasePath"]=u;var t=h.extend(v(e),c,{type:E,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents",{clientID:r,instanceID:n}),contentType:m,dataType:"json",data:JSON.stringify({format:i,deviceInfo:a,useCache:o,baseDocumentID:l,actionID:s})});return f(t)}).then(function(e){return e.documentId})},sendDocument:function(r,n,i,a,o){d(r);return g().then(function(e){var t=h.extend(v(e),o,{type:E,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}/send",{clientID:r,instanceID:n,documentID:i}),contentType:m,data:JSON.stringify({from:a.from,to:a.to,cc:a.cc,subject:a.subject,body:a.body})});return f(t)})},deleteReportDocument:function(r,n,i,a){d(r);return g().then(function(e){var t=h.extend(v(e),a,{type:o,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}",{clientID:r,instanceID:n,documentID:i})});return f(t)})},getDocumentInfo:function(r,n,i,a){d(r);return g().then(function(e){var t=h.extend(v(e),a,{type:l,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}/info",{clientID:r,instanceID:n,documentID:i}),dataType:"json"});return f(t)})},getPage:function(r,n,i,a,o){d(r);return g().then(function(e){var t=h.extend(v(e),o,{type:l,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}/pages/{pageNumber}",{clientID:r,instanceID:n,documentID:i,pageNumber:a}),dataType:"json"});return f(t)})},get:function(e){var t={type:l,url:e};return f(t)},formatDocumentUrl:function(e,t,r,n){var i=p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}",{clientID:e,instanceID:t,documentID:r});if(n){i+="?"+n}return i},getDocumentFormats:function(r){return g().then(function(e){var t=h.extend(v(e),r,{type:l,url:p("{baseUrl}/formats"),dataType:"json"});return f(t)})},getResource:function(r,n,i,a,o){d(r);return g().then(function(e){var t=h.extend(v(e),o,{type:l,url:p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}/resources/{resourceID}",{clientID:r,instanceID:n,documentID:i,resourceID:a}),dataType:"json"});return f(t)})},getSearchResults:function(e,t,r,n,i,a,o,l){d(e);var s=p("{baseUrl}/clients/{clientID}/instances/{instanceID}/documents/{documentID}/search",{clientID:e,instanceID:t,documentID:r});return g().then(function(e){var t=h.extend(v(e),l,{type:E,url:s,contentType:m,dataType:"json",data:JSON.stringify({searchToken:n,matchCase:i,matchWholeWord:a,useRegularExpressions:o})});return f(t)})},setAccessToken:function(e){r=Promise.resolve(e)},login:g}}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery);(function(Ae,Me,e,Ne){"use strict";var De=Ae.sr;if(!De){throw"Missing telerikReportViewer.sr"}var ye=Ae.utils;if(!ye){throw"Missing telerikReportViewer.utils"}var ke=Ae.printManager;if(!ke){throw"Missing telerikReportViewer.printManager"}Ae.ViewModes={INTERACTIVE:"INTERACTIVE",PRINT_PREVIEW:"PRINT_PREVIEW"};Ae.PrintModes={AUTO_SELECT:"AUTO_SELECT",FORCE_PDF_PLUGIN:"FORCE_PDF_PLUGIN",FORCE_PDF_FILE:"FORCE_PDF_FILE"};Ae.PageModes={SINGLE_PAGE:"SINGLE_PAGE",CONTINUOUS_SCROLL:"CONTINUOUS_SCROLL"};Ae.ParameterEditorTypes={COMBO_BOX:"COMBO_BOX",LIST_VIEW:"LIST_VIEW"};Ae.ParametersAreaPositions={RIGHT:"RIGHT",LEFT:"LEFT",TOP:"TOP",BOTTOM:"BOTTOM"};Ae.DocumentMapAreaPositions={RIGHT:"RIGHT",LEFT:"LEFT"};var Oe={pagePollIntervalMs:500,documentInfoPollIntervalMs:2e3};function t(E){var o={},b,T,P,e,t,r,l,n,i,w,a=Ae.ViewModes.INTERACTIVE,s=Ae.PageModes.CONTINUOUS_SCROLL,c,u=Ae.PrintModes.AUTO_SELECT,f,d=false,p=Ae.parameterValidators,v=new ce;m();E=ye.extend({},Oe,E);if(E.settings.printMode){u=E.settings.printMode()}var _=E.serviceClient;if(!_){throw De.noServiceClient}b=E.settings.clientId();function g(e){b=e;E.settings.clientId(b)}function h(){b=null;e=null;E.settings.clientId(null)}function R(){if(a===Ae.ViewModes.PRINT_PREVIEW){return"HTML5"}return"HTML5Interactive"}function I(e,t,r){var n="";if(!e.xhr){n=e;N(G({responseText:n},null,null,null));throw n}if(ye.isInvalidClientException(e.xhr)){Re()}N(G(e.xhr,e.status,e.error,t));if(!r){n=e.xhr.responseJSON&&e.xhr.responseJSON.exceptionMessage?e.xhr.responseJSON.exceptionMessage:De.promisesChainStopError;throw n}}function S(){if(!e){e=_.registerClient().catch(function(e){I(e,De.errorRegisteringViewer)}).then(function(e){g(e)}).catch(h)}return e}function C(){if(!t){t=D(l,n).then(function(e){T=e});t.catch(function(e){t=null})}return t}function m(){P=null;T=null;t=null;A()}function A(){i=w=0}function M(e){var t=e.length;if(t===1){return e[0]}if(t>1){return ye.stringFormat(e[0],Array.prototype.slice.call(e,1))}return""}function N(){var e=M(arguments);o.error(e)}function D(t,e){ne();return _.createReportInstance(b,t,e).catch(function(e){I(e,ye.stringFormat(De.errorCreatingReportInstance,[ye.escapeHtml(t)]))})}function y(t,e,r,n,i){ne();ie();return _.createReportDocument(b,T,t,e,r,n,i).catch(function(e){I(e,ye.stringFormat(De.errorCreatingReportDocument,[ye.escapeHtml(l),ye.escapeHtml(t)]))})}function k(e,t){ne();ie();return _.sendDocument(b,T,e,t).catch(function(e){I(e,ye.stringFormat(De.errorSendingDocument,[ye.escapeHtml(l)]))})}function O(t,r,n,i){if(!i.isCanceled&&r===T){return _.getDocumentInfo(t,r,n).catch(I).then(function(e){if(e&&e.documentReady){return e}else{e["promise"]=new Promise(function(e,t){Me.setTimeout(e,i.documentInfoPollIntervalMs)}).then(function(){return O(t,r,n,i)});return e}})}else{return Promise.reject()}}function V(t,r,n,i){var a={};function o(e){if(t){P=e;s();f()}}function l(e){a.documentInfoPollIntervalMs=E.pagePollIntervalMs;if(t){t.beforeLoadReport(e)}}function s(){if(t){t.beginLoadReport()}}function c(e){if(t){t.onReportLoadComplete(e)}}function u(e){if(t){w=e.pageCount;t.reportLoadProgress(e)}}function f(){ne();ie();ae();d(O(b,T,P,a))}function d(e){e.then(function(e){if(e.documentReady){c(e)}else{u(e);d(e.promise)}})}function e(){if(t){t.raiseError.apply(this,arguments)}}function p(n){return new Promise(function(t,e){var r=function(){_.getPage(b,T,P,n).catch(I).then(function(e){if(e.pageReady){t(e)}else{Me.setTimeout(r,E.pagePollIntervalMs)}})};r()})}function v(e){if(t){t.beginLoadPage(e)}}var g;function h(){if(!g){var e=R();var t=m();l({deviceInfo:t});g=S().then(C).then(function(){return y(e,t,r,n,i)}).then(o)}return g}function m(){var e=L();e.ContentOnly=true;e.UseSVG=ye.isSvgSupported();return e}return{beginLoad:function(){h()},beginGetPage:function(e){ne();h().then(function(){v(e);return p(e)}).then(function(e){a.documentInfoPollIntervalMs=E.documentInfoPollIntervalMs;if(t){t.pageReady(e)}})},getPageData:function(e){ne();return h().then(function(){return p(e)})},dispose:function(){t=null},cancel:function(){a.isCanceled=true}}}function L(){var e={};if(E.settings.enableAccessibility()){e.enableAccessibility=true;e.contentTabIndex=E.settings.contentTabIndex}var t={};o.getSearchDialogState(t);var r=t.visible;var n=E.settings.searchMetadataOnDemand();var i=!n||r;e.enableSearch=i;return e}function x(e){switch(e){case"Telerik.Reporting.Services.Engine.InvalidParameterException":return De.missingOrInvalidParameter;default:return""}}function G(e,t,r,n){var i=ye.parseJSON(e.responseText);var a="";if(i){var o=x(i.exceptionType||i.error);if(o){return o}a=ye.escapeHtml(i.message);var l=ye.escapeHtml(i.exceptionMessage||i.error_description);if(l){if(a){a+="<br/>"+l}else{a=l}}}else{a=ye.escapeHtml(e.responseText)}if(n||r){if(a){a="<br/>"+a}a=ye.escapeHtml(n?n:r)+a}if(ye.isInvalidClientException(e)){a+="<br />"+De.clientExpired}return a}function F(e){if(c){c.beginGetPage(e)}}function U(e,t,r){if(!l){N(De.noReport);return}if(c){c.dispose();c=null}m();c=new V(o,!e,t,r);c.beginLoad()}function B(e){o.exportStarted(e)}function $(e){o.exportReady(e)}function H(e){o.sendEmailStarted(e)}function W(e){o.sendEmailReady(e)}function z(e){o.printStarted(e)}function j(e){o.printReady(e)}function Y(e){o.showNotification(e)}function K(e){o.hideNotification(e)}function X(e){o.setUIState(e)}function Q(){oe();var e={ImmediatePrint:true},t={deviceInfo:e,handled:false};z(t);if(!t.handled){X({operationName:"PrintInProgress",inProgress:true});Y({stringResources:"preparingPrint"});var r=q(),n=r?"inline":"attachment",i="response-content-disposition="+n;ee("PDF",e).then(function(e){var t=_.formatDocumentUrl(e.clientId,e.instanceId,e.documentId,i),r={url:t,handled:false};j(r);K();X({operationName:"PrintInProgress",inProgress:false});if(!r.handled){ke.print(t)}})}}function q(){switch(u){case Ae.PrintModes.FORCE_PDF_FILE:case false:return false;case Ae.PrintModes.FORCE_PDF_PLUGIN:case true:return true;default:return ke.getDirectPrintState()}}function J(n,e){oe();if(!e){e=L()}var t={format:n,deviceInfo:e,handled:false};B(t);if(!t.handled){var i="response-content-disposition=attachment";X({operationName:"ExportInProgress",inProgress:true});Y({stringResources:"preparingDownload"});ee(n,t.deviceInfo).then(function(e){var t=_.formatDocumentUrl(e.clientId,e.instanceId,e.documentId,i),r={url:t,format:n,handled:false,windowOpenTarget:"_self"};$(r);K();X({operationName:"ExportInProgress",inProgress:false});if(!r.handled){Me.open(t,r.windowOpenTarget)}})}}function Z(r){oe();if(!r.deviceInfo){r.deviceInfo=L()}var e={deviceInfo:r.deviceInfo,handled:false,format:r.format};H(e);var n="response-content-disposition=attachment";if(!e.handled){ee(r.format,r.deviceInfo).then(function(e){var t=_.formatDocumentUrl(e.clientId,e.instanceId,e.documentId,n);r["url"]=t;r["handled"]=false;W(r);delete r.deviceInfo;if(!r.handled){k(e.documentId,r)}})}}function ee(e,t){oe();return S().then(C).then(function(){return y(e,t,true,P)}).then(function(e){return te(b,T,e,E)})}function te(n,i,a,o){return new Promise(function(t,e){var r=function(e){e.then(function(e){if(e.documentReady){t({clientId:n,instanceId:i,documentId:a})}else{r(e.promise)}})};r(O(n,i,a,o))})}function re(e){oe();ie();ae();Te();o.refreshReportCore(false,P,e)}function ne(){if(!b){throw De.controllerNotInitialized}}function ie(){if(!T){throw De.noReportInstance}}function ae(){if(!P){throw De.noReportDocument}}function oe(){if(!l){throw De.noReport}}function le(e){var t;if(e&&e.length){t=e[0]}if(typeof t==="function"){return t}return null}function se(e,t){var r=le(t);if(r){v.on(e,r)}else{v.trigger(e,t)}return o}function ce(){var r={};function n(e){var t=r[e];if(!t){r[e]=t=new i(e)}return t}return{on:function(e,t){n(e).on(t)},trigger:function(e,t){n(e).trigger(t)}};function i(n){var i=[];var e={on:function(e){i.push(e)},trigger:function(e){var t=[].slice.call(e);t.unshift(n);for(var r=0;r<i.length;r++){i[r].apply(o,t)}}};return e}}function ue(e,t){return S().then(function(){return _.getParameters(b,e,t||n||{}).catch(function(e){I(e,De.unableToGetReportParameters)})})}function fe(){if(!r){r=_.getDocumentFormats().catch(I)}return r}function de(e,t){if(e){for(var r=0,n=e.length;r<n;r++){var i=e[r];if(i.id===t){return i.page}else{var a=de(i.items,t);if(a){return a}}}}return null}function pe(e){var t={};if(Array.isArray(e)){e.forEach(function(e){t[e.Key]=e.Value})}return t}function ve(e){ge(e);o.reportSourceChanged()}function ge(e){if(E.settings.reportSource){E.settings.reportSource(e)}}function he(e){E.settings.pageNumber(e);o.currentPageChanged()}var me={sorting:function(e){re(e.Id)},toggleVisibility:function(e){re(e.Id)},navigateToReport:function(e){var t=e.Value;Te();o.reportSource({report:t.Report,parameters:pe(t.ParameterValues)});o.refreshReport(false)},navigateToUrl:function(e){var t=e.Value;Me.open(t.Url,t.Target)},navigateToBookmark:function(e){var t=e.Value,r=de(f,t);o.navigateToPage(r,{type:"bookmark",id:t})},customAction:function(e){}};function Ee(e){o.interactiveActionExecuting(e)}function be(e){var t=e.action;var r=me[t.Type];if(typeof r==="function"){Me.setTimeout(function(){Ee(e);if(!e.cancel){r(t)}},0)}}function Te(){o.serverActionStarted()}function Pe(e){o.interactiveActionEnter({action:e.action,element:e.element})}function we(e){o.interactiveActionLeave({action:e.action,element:e.element})}function _e(){return se(o.Events.CLIENT_EXPIRED,arguments)}function Re(){d=true;o.clientExpired()}function Ie(e){o.toolTipOpening(e)}function Se(e){if(!e.searchToken||e.searchToken===""){return Promise.resolve(null)}return _.getSearchResults(b,T,P,e.searchToken,e.matchCase,e.matchWholeWord,e.useRegex).catch(Ce)}function Ce(e){if(!ye.isExceptionOfType(e.xhr,"System.ArgumentException")){I(e,null,true);throw null}var t=ye.parseJSON(e.xhr.responseText);throw t.exceptionMessage}o.Events={ERROR:"trv.ERROR",EXPORT_STARTED:"trv.EXPORT_STARTED",EXPORT_DOCUMENT_READY:"trv.EXPORT_DOCUMENT_READY",PRINT_STARTED:"trv.PRINT_STARTED",PRINT_DOCUMENT_READY:"trv.PRINT_DOCUMENT_READY",BEFORE_LOAD_PARAMETERS:"trv.BEFORE_LOAD_PARAMETERS",ON_LOADED_REPORT_CHANGE:"trv.ON_LOADED_REPORT_CHANGE",BEFORE_LOAD_REPORT:"trv.BEFORE_LOAD_REPORT",BEGIN_LOAD_REPORT:"trv.BEGIN_LOAD_REPORT",REPORT_LOAD_COMPLETE:"trv.REPORT_LOAD_COMPLETE",REPORT_LOAD_PROGRESS:"trv.REPORT_LOAD_PROGRESS",REPORT_LOAD_FAIL:"trv.REPORT_LOAD_FAIL",BEGIN_LOAD_PAGE:"trv.BEGIN_LOAD_PAGE",PAGE_READY:"trv.PAGE_READY",VIEW_MODE_CHANGED:"trv.VIEW_MODE_CHANGED",PAGE_MODE_CHANGED:"trv.PAGE_MODE_CHANGED",PRINT_MODE_CHANGED:"trv.PRINT_MODE_CHANGED",REPORT_SOURCE_CHANGED:"trv.REPORT_SOURCE_CHANGED",NAVIGATE_TO_PAGE:"trv.NAVIGATE_TO_PAGE",CURRENT_PAGE_CHANGED:"trv.CURRENT_PAGE_CHANGED",GET_DOCUMENT_MAP_STATE:"trv.GET_DOCUMENT_MAP_STATE",SET_DOCUMENT_MAP_VISIBLE:"trv.SET_DOCUMENT_MAP_VISIBLE",GET_PARAMETER_AREA_STATE:"trv.GET_PARAMETER_AREA_STATE",SET_PARAMETER_AREA_VISIBLE:"trv.SET_PARAMETER_AREA_VISIBLE",PAGE_SCALE:"trv.PAGE_SCALE",GET_PAGE_SCALE:"trv.GET_PAGE_SCALE",SERVER_ACTION_STARTED:"trv.SERVER_ACTION_STARTED",SET_TOGGLE_SIDE_MENU:"trv.SET_TOGGLE_SIDE_MENU",GET_TOGGLE_SIDE_MENU:"trv.GET_TOGGLE_SIDE_MENU",UPDATE_UI:"trv.UPDATE_UI",CSS_LOADED:"trv.CSS_LOADED",RELOAD_PARAMETERS:"trv.RELOAD_PARAMETERS",INTERACTIVE_ACTION_EXECUTING:"trv.INTERACTIVE_ACTION_EXECUTING",INTERACTIVE_ACTION_ENTER:"trv.INTERACTIVE_ACTION_ENTER",INTERACTIVE_ACTION_LEAVE:"trv.INTERACTIVE_ACTION_LEAVE",UPDATE_UI_INTERNAL:"trv.UPDATE_UI_INTERNAL",CLIENT_EXPIRED:"trv.CLIENT_EXPIRED",TOOLTIP_OPENING:"trv.TOOLTIP_OPENING",PAGE_NUMBER:"trv.PAGE_NUMBER",PAGE_COUNT:"trv.PAGE_COUNT",GET_SEARCH_DIALOG_STATE:"trv.GET_SEARCH_DIALOG_STATE",SET_SEARCH_DIALOG_VISIBLE:"trv.SET_SEARCH_DIALOG_VISIBLE",SET_SEND_EMAIL_DIALOG_VISIBLE:"trv.SET_SEND_EMAIL_DIALOG_VISIBLE",SEND_EMAIL_STARTED:"trv.SEND_EMAIL_STARTED",SEND_EMAIL_READY:"trv.SEND_EMAIL_READY",SHOW_NOTIFICATION:"trv.SHOW_NOTIFICATION",HIDE_NOTIFICATION:"trv.HIDE_NOTIFICATION",UI_STATE:"trv.UI_STATE",SCROLL_PAGE_READY:"trv.SCROLL_PAGE_READY",UPDATE_SCROLL_PAGE_DIMENSIONS_READY:"trv.UPDATE_SCROLL_PAGE_DIMENSIONS_READY",MISSING_OR_INVALID_PARAMETERS:"trv.MISSING_OR_INVALID_PARAMETERS",RENDERING_STOPPED:"trv.RENDERING_STOPPED"};ye.extend(o,{getPageData:function(e){if(c){return c.getPageData(e)}return},reportSource:function(e){if(null===e){l=n=null;m();ve(e);return this}else if(e){l=e.report;n=e.parameters;ve(e);return this}else{if(l===null){return null}return{report:l,parameters:ye.extend({},n)}}},updateSettings:function(e){E.settings=ye.extend({},e,E.settings)},clearReportSource:function(){l=n=null;m();ve(Ne);return this},reportDocumentIdExposed:function(){return P},setParameters:function(e){n=e},pageCount:function(){return w},currentPageNumber:function(e){if(e===Ne)return i;var t=ye.tryParseInt(e);if(t!==i){i=t;he(t)}return this},viewMode:function(e){var t=o.setViewMode(e);if(typeof t==="string"){return t}if(l){o.refreshReportCore(false,P)}return o},setViewMode:function(e){if(!e){return a}if(a!==e){a=e;o.viewModeChanged(e)}return o},pageMode:function(e){var t=o.setPageMode(e);if(typeof t==="string"){return t}if(l){o.refreshReportCore(false,P)}return o},setPageMode:function(e){if(!e){return s}if(s!==e){s=e;o.pageModeChanged(e)}return o},printMode:function(e){if(!e){return u}if(u!==e){u=e;o.printModeChanged(e)}return o},previewReport:function(e,t,r){o.onLoadedReportChange();o.refreshReportCore(e,t,r)},refreshReportCore:function(e,t,r){U(e,t,r)},stopRendering:function(){oe();ie();ae();_.deleteReportDocument(b,T,P).catch(I).then(function(){if(c){c.cancel()}A();o.renderingStopped()})},refreshReport:function(n,i,a){o.onLoadedReportChange();if(d){d=false;h()}if(!l){N(De.noReport);return}var e=o.loadParameters(null);e.then(function(e){var t={};var r=false;ye.each(e||[],function(){try{t[this.id]=p.validate(this,this.value)}catch(e){r=true;return}});if(r){N(De.missingOrInvalidParameter);o.missingOrInvalidParameters()}else{o.setParameters(t);o.refreshReportCore(n,i,a)}});o.reloadParameters(e)},exportReport:function(e,t){J(e,t)},sendReport:function(e){Z(e)},printReport:function(){Q()},getReportPage:function(e){F(e)},executeReportAction:function(e){be(e)},reportActionEnter:function(e){Pe(e)},reportActionLeave:function(e){we(e)},reportToolTipOpening:function(e){Ie(e)},loadParameters:function(e){if(l===null){return{}}o.beforeLoadParameters(e===null);return ue(l,e)},getDocumentFormats:function(){return fe()},setAuthenticationToken:function(e){_.setAccessToken(e)},clientId:function(){return b},onReportLoadComplete:function(e){w=e.pageCount;f=e.bookmarkNodes;ge(o.reportSource());o.reportLoadComplete(e)},raiseError:N,getSearchResults:function(e,t){return Se(e,t)},on:v.on,showNotification:function(){return se(o.Events.SHOW_NOTIFICATION,arguments)},hideNotification:function(){return se(o.Events.HIDE_NOTIFICATION,arguments)},setUIState:function(){return se(o.Events.UI_STATE,arguments)},error:function(){return se(o.Events.ERROR,arguments)},reloadParameters:function(){return se(o.Events.RELOAD_PARAMETERS,arguments)},exportStarted:function(){return se(o.Events.EXPORT_STARTED,arguments)},exportReady:function(){return se(o.Events.EXPORT_DOCUMENT_READY,arguments)},sendEmailStarted:function(){return se(o.Events.SEND_EMAIL_STARTED,arguments)},sendEmailReady:function(){return se(o.Events.SEND_EMAIL_READY,arguments)},printStarted:function(){return se(o.Events.PRINT_STARTED,arguments)},printReady:function(){return se(o.Events.PRINT_DOCUMENT_READY,arguments)},beforeLoadParameters:function(){return se(o.Events.BEFORE_LOAD_PARAMETERS,arguments)},onLoadedReportChange:function(){return se(o.Events.ON_LOADED_REPORT_CHANGE,arguments)},beforeLoadReport:function(){return se(o.Events.BEFORE_LOAD_REPORT,arguments)},beginLoadReport:function(){return se(o.Events.BEGIN_LOAD_REPORT,arguments)},reportLoadComplete:function(){return se(o.Events.REPORT_LOAD_COMPLETE,arguments)},reportLoadProgress:function(){return se(o.Events.REPORT_LOAD_PROGRESS,arguments)},reportLoadFail:function(){return se(o.Events.REPORT_LOAD_FAIL,arguments)},beginLoadPage:function(){return se(o.Events.BEGIN_LOAD_PAGE,arguments)},pageReady:function(){return se(o.Events.PAGE_READY,arguments)},viewModeChanged:function(){return se(o.Events.VIEW_MODE_CHANGED,arguments)},pageModeChanged:function(){return se(o.Events.PAGE_MODE_CHANGED,arguments)},printModeChanged:function(){return se(o.Events.PRINT_MODE_CHANGED,arguments)},reportSourceChanged:function(){return se(o.Events.REPORT_SOURCE_CHANGED,arguments)},navigateToPage:function(){return se(o.Events.NAVIGATE_TO_PAGE,arguments)},currentPageChanged:function(){return se(o.Events.CURRENT_PAGE_CHANGED,arguments)},getDocumentMapState:function(){return se(o.Events.GET_DOCUMENT_MAP_STATE,arguments)},setDocumentMapVisible:function(){return se(o.Events.SET_DOCUMENT_MAP_VISIBLE,arguments)},getParametersAreaState:function(){return se(o.Events.GET_PARAMETER_AREA_STATE,arguments)},setParametersAreaVisible:function(){return se(o.Events.SET_PARAMETER_AREA_VISIBLE,arguments)},setSideMenuVisible:function(){return se(o.Events.SET_TOGGLE_SIDE_MENU,arguments)},getSideMenuVisible:function(){return se(o.Events.GET_TOGGLE_SIDE_MENU,arguments)},scale:function(){return se(o.Events.PAGE_SCALE,arguments)},getScale:function(){return se(o.Events.GET_PAGE_SCALE,arguments)},serverActionStarted:function(){return se(o.Events.SERVER_ACTION_STARTED,arguments)},cssLoaded:function(){return se(o.Events.CSS_LOADED,arguments)},interactiveActionExecuting:function(){return se(o.Events.INTERACTIVE_ACTION_EXECUTING,arguments)},interactiveActionEnter:function(){return se(o.Events.INTERACTIVE_ACTION_ENTER,arguments)},interactiveActionLeave:function(){return se(o.Events.INTERACTIVE_ACTION_LEAVE,arguments)},updateUI:function(){return se(o.Events.UPDATE_UI,arguments)},updateUIInternal:function(){return se(o.Events.UPDATE_UI_INTERNAL,arguments)},toolTipOpening:function(){return se(o.Events.TOOLTIP_OPENING,arguments)},pageNumberChange:function(){return se(o.Events.PAGE_NUMBER,arguments)},pageCountChange:function(){return se(o.Events.PAGE_COUNT,arguments)},getSearchDialogState:function(){return se(o.Events.GET_SEARCH_DIALOG_STATE,arguments)},getSendEmailDialogState:function(){return se(o.Events.GET_SEND_EMAIL_DIALOG_STATE,arguments)},setSearchDialogVisible:function(){return se(o.Events.SET_SEARCH_DIALOG_VISIBLE,arguments)},setSendEmailDialogVisible:function(){return se(o.Events.SET_SEND_EMAIL_DIALOG_VISIBLE,arguments)},scrollPageReady:function(){return se(o.Events.SCROLL_PAGE_READY,arguments)},updatePageDimensionsReady:function(){return se(o.Events.UPDATE_SCROLL_PAGE_DIMENSIONS_READY,arguments)},missingOrInvalidParameters:function(){return se(o.Events.MISSING_OR_INVALID_PARAMETERS,arguments)},renderingStopped:function(){return se(o.Events.RENDERING_STOPPED,arguments)},clientExpired:_e});return o}Ae.ReportViewerController=t})(window.telerikReportViewer=window.telerikReportViewer||{},window,document);(function(e,d,t,r,n){"use strict";e.touchBehavior=function(e,n){var r,t;i(e);function i(e){if(typeof d.fn.kendoTouch==="function"){d(e).mousedown(function(){t=true}).mouseup(function(){t=false}).kendoTouch({multiTouch:true,enableSwipe:true,swipe:function(e){if(!t){c(e)}},gesturestart:function(e){if(!t){o(e)}},gestureend:function(e){if(!t){l(e)}},gesturechange:function(e){if(!t){s(e)}},doubletap:function(e){if(!t){a(e)}},touchstart:function(e){if(!t){f("touchstart")}}})}}function a(e){f("doubletap",e)}function o(e){r=kendo.touchDelta(e.touches[0],e.touches[1]).distance}function l(e){}function s(e){var t=kendo.touchDelta(e.touches[0],e.touches[1]).distance;u({distance:t,lastDistance:r});r=t}function c(e){f("swipe",e)}function u(e){f("pinch",e)}function f(e,t){var r=n[e];if(typeof r==="function"){r(t)}}}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(ve,ge,he,e,me){"use strict";var Ee=ve.sr;if(!Ee){throw"Missing telerikReportViewer.sr"}var be=ve.utils;if(!be){throw"Missing telerikReportViewer.utils"}var Te=ve.domUtils;var Pe=ve.touchBehavior;if(!Pe){throw"Missing telerikReportViewer.touch"}var we={};var _e=ve.ScaleModes={FIT_PAGE_WIDTH:"FIT_PAGE_WIDTH",FIT_PAGE:"FIT_PAGE",SPECIFIC:"SPECIFIC"};function r(e,s,t){s=ge.extend({},we,s,t);var c=s.controller;if(!c)throw"No controller (telerikReportViewer.reportViewerController) has been specified.";var u=ge(e),f=u.find(".trv-page-container"),b=f[0],o=u.find(".trv-page-wrapper"),i=o[0],r=u.find(".trv-error-message"),l,d,p=_e.SPECIFIC,v=1,n=.1,a=8,g=true,h,m,E,T,P=false,w=false,_='.trv-page-container {background: #ffffff url("{0}") no-repeat center 50px}',R="trv-initial-image-styles",I=be.extend({},ve.scroll,{}),S=null;C();if(I){I.init(e,s)}function C(){A(u)}function A(e){e.attr("aria-label",Ee[e.attr("aria-label")])}ge(he).on("resize",function(e,t){if(V()){L()}});O(u);function M(){if(T){he.clearTimeout(T)}}function N(){var e=U(h);if(e){K(e,-1)}}function D(e,t){if(e&&e<=t){B(e,m)}}function y(e,t){if(e){e=Math.min(e,t);B(e,m)}}function k(){G(E);E=false}c.pageModeChanged(function(){if(c.pageMode()===ve.PageModes.CONTINUOUS_SCROLL){I.enable()}else{I.disable()}c.refreshReport(true)}).reportSourceChanged(function(){E=true;h=null;m=null;g=false}).beforeLoadParameters(function(e,t){if(t){Y(Ee.loadingReport)}}).beforeLoadReport(function(){g=false;if(!h){h=1}M();G();j(true);Y(Ee.loadingReport)}).beginLoadReport(function(e,t){g=true;N()}).reportLoadProgress(function(e,t){D(h,t.pageCount);Y(be.stringFormat(Ee.loadingReportPagesInProgress,[t.pageCount]))}).reportLoadComplete(function(e,t){if(0===t.pageCount){k();Y(Ee.noPageToDisplay)}else{y(h,t.pageCount);Y(be.stringFormat(Ee.loadedReportPagesComplete,[t.pageCount]));T=he.setTimeout(Y,2e3);q()}if(t.containsFrozenContent&&!S){S=be.extend({},ve.uiFreezeCoordinator,{});if(c.viewMode()===ve.ViewModes.INTERACTIVE){S.init(u)}}}).navigateToPage(function(e,t,r){B(t,r)}).pageReady(function(e,t){if(c.pageMode()===ve.PageModes.SINGLE_PAGE){if(I.isEnabled()){I.disable()}ue(t)}else{I.renderPage(t)}if(!w){w=true}if(P){pe()}if(c.viewMode()===ve.ViewModes.INTERACTIVE&&S){S.init(u)}j(false)}).error(function(e,t){j(false);k();Y(t)}).showNotification(function(e,t){Y(Ee[t.stringResources])}).hideNotification(function(e,t){Y()}).scale(function(e,t){x(t)}).getScale(function(e,t){var r=F();var n=ge(r).data("pageScale")||v;t.scale=n;t.scaleMode=p}).setDocumentMapVisible(function(){if(V()){setTimeout(function(){L()})}}).setParametersAreaVisible(function(){if(V()){setTimeout(function(){L()})}}).serverActionStarted(function(){j(true);Y(Ee.loadingReport)}).scrollPageReady(function(e,t){fe(t)}).missingOrInvalidParameters(function(e,t){if(s.initialPageAreaImageUrl&&!w){k();de()}}).renderingStopped(function(){G(true);Y(Ee.renderingCanceled)});function O(e){var r,n;Pe(e,{swipe:function(e){var t=c.currentPageNumber();if(r&&e.direction==="left"){if(t<c.pageCount()){c.navigateToPage(t+1)}}else if(n&&e.direction==="right"){if(t>1){c.navigateToPage(t-1)}}},pinch:function(e){var t=F();var r=ge(t).data("pageScale")||v;var n=e.distance/e.lastDistance;x({scale:r*n,scaleMode:ve.ScaleModes.SPECIFIC})},doubletap:function(e){s.commands.toggleZoomMode.exec()},touchstart:function(e){var t=i;n=0===t.scrollLeft;r=t.scrollWidth-t.offsetWidth===t.scrollLeft}})}function V(){return-1!==[_e.FIT_PAGE,_e.FIT_PAGE_WIDTH].indexOf(p)}function L(){for(var e=0,t=f.find(".trv-report-page"),r=t.length;e<r;e++){var n=parseInt(ge(t[e]).attr("data-page"));Q(t[e],p,v,n)}c.updatePageDimensionsReady()}function x(e){p=e.scaleMode||p;var t=v;if("scale"in e){t=e.scale}v=Math.max(n,Math.min(a,t));L()}function G(e){J();d=me;if(e){o.empty()}Y()}function F(){return U(c.currentPageNumber())}function U(n){var i;if(c.pageMode()===ve.PageModes.SINGLE_PAGE){be.each(f.find(".trv-report-page"),function(e,t){if(K(t)===n){i=t}return!i})}else{var e=f.find(".trv-report-page");ge.each(e,function(e,t){var r=parseInt(ge(t).attr("data-page"));if(r===n){i=t}})}return i}function B(e,t){if(g){H(e,t)}else{$(e,t)}}function $(e,t){h=e;m=t}function H(e,t){var r=U(e);if(r){if(t){W(t,e)}if(I.isEnabled()&&!t){I.navigateToElement(r.offsetTop,e)}}else{d=t;X(e)}}function W(e,t){if(e){var r=f.find("[data-"+e.type+"-id="+e.id+"]")[0];if(r){if(s.enableAccessibility){var n=z(ge(r));if(n){n.focus()}}var i=f[0],a=0,o=0;while(r&&r!==i){if(ge(r).is(".trv-page-wrapper")){var l=ge(r).data("pageScale");if(typeof l==="number"){a*=l;o*=l}}a+=r.offsetTop;o+=r.offsetLeft;r=r.offsetParent}if(I.isEnabled()&&t){I.navigateToElement(a,t)}else{i.scrollTop=a;i.scrollLeft=o}}else{if(I.isEnabled()&&t){I.navigateToElement(u.find('[data-page="'+t+'"]')[0].offsetTop,t)}}}}function z(e){if(!e||e.length===0){return null}var t=be.tryParseInt(e.attr("tabindex"));if(!isNaN(t)&&t>-1){return e}return z(e.next())}function j(e){(e?ge.fn.addClass:ge.fn.removeClass).call(u,"trv-loading")}function Y(e){r.html(e);(e?ge.fn.addClass:ge.fn.removeClass).call(u,"trv-error")}function K(e,t){var r=e.$?e:ge(e),n="pageNumber";if(t===me){return r.data(n)}r.data(n,t);return e}function X(e){j(true);he.setTimeout(c.getReportPage.bind(c,e),1);h=null}function Q(e,t,r,n){var i=ge(e),a=n?i:i.find("div.trv-report-page"),o=a.find("div.sheet"),l=a.find("div.trv-skeleton-wrapper"),s=o[0]||l[0],c=l[0];if(!s)return;var u,f,d=i.data("box");if(!d){var p=Te.getMargins(i),v=Te.getBorderWidth(a),g=Te.getPadding(a);d={padLeft:p.left+v.left+g.left,padRight:p.right+v.right+g.right,padTop:p.top+v.top+g.top,padBottom:p.bottom+v.bottom+g.bottom};i.data("box",d)}if(i.data("pageWidth")===me){u=s.offsetWidth;f=s.offsetHeight;i.data("pageWidth",u);i.data("pageHeight",f)}else{u=i.data("pageWidth");f=i.data("pageHeight")}var h=f>u&&t===_e.FIT_PAGE_WIDTH?20:0,m=(b.clientWidth-h-d.padLeft-d.padRight)/u,E=(b.clientHeight-1-d.padTop-d.padBottom)/f;if(t===_e.FIT_PAGE_WIDTH){r=m}else if(!r||t===_e.FIT_PAGE){r=Math.min(m,E)}i.data("pageScale",r);if(!c){Te.scale(o,r,r)}a.css({height:r*f,width:r*u})}function q(){f.on("click","[data-reporting-action]",Z);f.on("mouseenter","[data-reporting-action]",te);f.on("mouseleave","[data-reporting-action]",re);f.on("mouseenter","[data-tooltip-title],[data-tooltip-text]",ie);f.on("mouseleave","[data-tooltip-title],[data-tooltip-text]",se)}function J(){f.off("click","[data-reporting-action]",Z);f.off("mouseenter","[data-reporting-action]",te);f.off("mouseleave","[data-reporting-action]",re);f.off("mouseenter","[data-tooltip-title],[data-tooltip-text]",ie);f.off("mouseleave","[data-tooltip-title],[data-tooltip-text]",se)}function Z(e){var t=ge(this);var r=t.attr("data-reporting-action"),n=ne(r);if(n){h=ee(e,n.Type);c.executeReportAction({element:e.currentTarget,action:n,cancel:false})}e.stopPropagation()}function ee(e,t){if(I.isEnabled()&&(t==="sorting"||t==="toggleVisibility")){return ge(e.target).closest(".trv-report-page").attr("data-page")||c.currentPageNumber()}return c.currentPageNumber()}function te(e){var t=ge(this);var r=t.attr("data-reporting-action");var n=ne(r);if(n!==null&&e.currentTarget===this){c.reportActionEnter({element:e.currentTarget,action:n})}}function re(e){var t=ge(this);var r=t.attr("data-reporting-action");var n=ne(r);if(n!==null&&e.currentTarget===this){c.reportActionLeave({element:e.currentTarget,action:n})}}function ne(e){if(l){var t;be.each(l,function(){if(this.Id===e){t=this}return t===me});return t}return null}function ie(e){var t=ge(this);var r=t.attr("data-tooltip-title");var n=t.attr("data-tooltip-text");if(!r&&!n){return}var i={element:e.currentTarget,toolTip:{title:r||"",text:n||""},cancel:false};c.reportToolTipOpening(i);if(i.cancel){return}var a=ae(i);var o=e.currentTarget.viewportElement;var l=le(t,a);l.show(t);if(o&&o.nodeName==="svg"){oe(l,e)}}function ae(e){var t=s.templates["trv-pages-area-kendo-tooltip"];var r=ge(t);var n=r.find(".trv-pages-area-kendo-tooltip-title");var i=r.find(".trv-pages-area-kendo-tooltip-text");n.text(e.toolTip.title);i.text(e.toolTip.text);return r.clone().wrap("<p>").parent().html()}function oe(e,t){var r=t.pageX;var n=t.pageY;e.popup.element.parent().css({left:r+10,top:n+5})}function le(e,t){var r=e.data("kendoTooltip");if(!r){r=e.kendoTooltip({content:t,autohide:true,callout:false}).data("kendoTooltip")}return r}function se(e){var t=ge(this);var r=t.data("kendoTooltip");if(r){r.hide()}}function ce(e){var t="trv-"+c.clientId()+"-styles";ge("#"+t).remove();var r=ge("<style id="+t+"></style>");r.append(e.pageStyles);r.appendTo("head")}function ue(e){l=JSON.parse(e.pageActions);ce(e);var t=e.pageNumber,r=ge(ge.parseHTML(e.pageContent)),n=r.find("div.sheet"),i=ge('<div class="trv-report-page" data-page="'+t+'"></div>');n.css("margin",0);i.append(n).append(ge('<div class="trv-page-overlay"></div>'));var a=o.empty().removeData().data("pageNumber",t).append(i);c.currentPageNumber(t);if(c.viewMode()===ve.ViewModes.INTERACTIVE){u.removeClass("printpreview");u.addClass("interactive")}else{u.removeClass("interactive");u.addClass("printpreview")}Q(a,p,v);f.scrollTop(0);f.scrollLeft(0);W(d)}function fe(e){var t=JSON.parse(e.page.pageActions);if(!l){l=t}else{l=l.concat(t)}if(c.viewMode()===ve.ViewModes.INTERACTIVE){u.removeClass("printpreview");u.addClass("interactive")}else{u.removeClass("interactive");u.addClass("printpreview")}Q(e.target,p,v,e.page.pageNumber)}function de(){var e=ge("<style id="+R+"></style>");pe();e.append(be.stringFormat(_,[s.initialPageAreaImageUrl]));e.appendTo("head");P=true}function pe(){ge("#"+R).remove()}}var n="telerik_ReportViewer_PagesArea";ge.fn[n]=function(e,t){return be.each(this,function(){if(!ge.data(this,n)){ge.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(R,I,S,e,t){"use strict";var C={};var A=R.sr;if(!A){throw"Missing telerikReportViewer.sr"}var M=R.utils;if(!M){throw"Missing telerikReportViewer.utils"}function r(e,n,t){n=I.extend({},C,n,t);var i=n.controller;if(!i){throw"No controller (telerikReporting.reportViewerController) has been specified."}var a=I(e),o;var r=n.documentMapVisible!==false;var l=n.enableAccessibility;var s=null;var c=false;u();function u(){o=I('<div id="'+n.viewerSelector+'-documentMap"></div>');o.appendTo(e);w();_(a)}function f(e){var t=this.dataItem(e.node),r=t.page,n=t.id;i.navigateToPage(r,{type:"bookmark",id:n})}function d(e){if(l){S.setTimeout(function(){p(e.node)},100)}}function p(e){var t=I(e).find("li");M.each(t,function(){var e=I(this);e.attr("aria-label",e[0].innerText)})}function v(){g([])}function g(e){var t=e&&!I.isEmptyObject(e);var r=o.data("kendoTreeView");if(!r){o.kendoTreeView({dataTextField:"text",select:f});r=o.data("kendoTreeView")}r.setDataSource(e);if(l){h(r)}P(t)}function h(e){e.bind("expand",d);e.element.attr("aria-label",A.ariaLabelDocumentMap);var t=e.element.find("ul");M.each(t,function(){p(this)});if(c){m()}}function m(){var e=a.next();if(n.documentMapAreaPosition===R.DocumentMapAreaPositions.RIGHT){e=a.prev()}e.attr("aria-label",A.ariaLabelDocumentMapSplitter)}function E(){var e={};i.getDocumentMapState(e);return e.visible}function b(){a.addClass("trv-loading")}function T(){a.removeClass("trv-loading")}function P(e){var t=R[n.viewerSelector+"-document-map-splitter"],r=a.next();if(n.documentMapAreaPosition===R.DocumentMapAreaPositions.RIGHT){r=a.prev()}if(t){(c?I.fn.removeClass:I.fn.addClass).call(r,"trv-hidden");t.toggle(".trv-document-map",e)}}function w(){i.beginLoadReport(function(){b();var e=i.reportSource().report;var t=s!==e||!E();s=e;if(t){v()}}).reportLoadComplete(function(e,t){if(t.documentMapAvailable){c=true;g(t.documentMapNodes);i.setDocumentMapVisible({enabled:true,visible:r})}else{c=false;P(c)}T()}).error(function(e,t){T();v()}).getDocumentMapState(function(e,t){t.enabled=c;t.visible=r}).setDocumentMapVisible(function(e,t){r=t.visible;P(r&&c)}).renderingStopped(function(){c=false;P(false)})}function _(e){var t=e.find(".trv-document-map-overlay");if(!t){return}t.attr("aria-label",A[t.attr("aria-label")])}}var n="telerik_ReportViewer_DocumentMapArea";I.fn[n]=function(e,t){return M.each(this,function(){if(!I.data(this,n)){I.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(l,R,I,e,t){"use strict";l.ParameterTypes={INTEGER:"System.Int64",FLOAT:"System.Double",STRING:"System.String",DATETIME:"System.DateTime",BOOLEAN:"System.Boolean"};l.parameterEditorsMatch={MultiSelect:function(e,t){return Boolean(e.availableValues)&&e.multivalue&&(!t||!t.multiSelect||t.multiSelect!==l.ParameterEditorTypes.COMBO_BOX)},MultiSelectCombo:function(e,t){return Boolean(e.availableValues)&&e.multivalue&&(t&&t.multiSelect&&t.multiSelect===l.ParameterEditorTypes.COMBO_BOX)},SingleSelect:function(e,t){return Boolean(e.availableValues)&&!e.multivalue&&(!t||!t.singleSelect||t.singleSelect!==l.ParameterEditorTypes.COMBO_BOX)},SingleSelectCombo:function(e,t){return Boolean(e.availableValues)&&!e.multivalue&&(t&&t.singleSelect&&t.singleSelect===l.ParameterEditorTypes.COMBO_BOX)},MultiValue:function(e){return Boolean(e.multivalue)},DateTime:function(e){return e.type===l.ParameterTypes.DATETIME},String:function(e){return e.type===l.ParameterTypes.STRING},Number:function(e){switch(e.type){case l.ParameterTypes.INTEGER:case l.ParameterTypes.FLOAT:return true;default:return false}},Boolean:function(e){return e.type===l.ParameterTypes.BOOLEAN},Default:function(e){return true}};var S=l.sr,C=l.utils;var o=function(){var r="\n";return{formatValue:function(e){var t="";if(e){[].concat(e).forEach(function(e){if(t.length>0){t+=r}t+=e})}return t},parseValues:function(e){return(""+e).split(r)}}}();function s(t){function r(e){return/^(\-|\+)?([0-9]*)$/.test(e)}function n(e){if(C.isSpecialKey(e.keyCode)){return true}return r(R(t).val()+String.fromCharCode(e.charCode))}function i(e){}function e(e){R(e).on("keypress",n).on("paste",i)}function a(e){R(e).off("keypress",n).off("paste",i)}e(t);return{dispose:function(){a(t)}}}function c(t){function r(e){return/^(\-|\+)?([0-9]*(\.[0-9]*)?)$/.test(e)}function n(e){if(C.isSpecialKey(e.keyCode)){return true}return r(R(t).val()+String.fromCharCode(e.charCode))}function i(e){}function e(e){R(e).on("keypress",n).on("paste",i)}function a(e){R(e).off("keypress",n).off("paste",i)}e(t);return{dispose:function(){a(t)}}}function r(e,t,r){var n=e?R.fn.addClass:R.fn.removeClass;n.call(r,t)}function A(e,t){r(!t,"k-state-disabled",e)}function M(e,t){r(t,"k-state-selected",e);e.attr("aria-selected",t)}function N(e,t,r,n,i){if(!n){n=""}var a=C.stringFormat("{0}. {1} {2}. {3}",[r,t,S.ariaLabelParameter,n]);e.attr("aria-label",a);y(e,i)}var u;var f=0;function D(e){if(!u){var t=R("div.trv-parameters-area-content");if(t.length>0){var r=t.attr("tabIndex");if(r){u=C.tryParseInt(r)}}if(!u||isNaN(u)){u=300}}var n=e.closest(".trv-parameter-value"),i=n.find(".trv-select-all"),a=n.find(".trv-select-none"),o=e.closest(".k-widget"),l=o.find(".k-input"),s=l&&l.length;if(i&&i.length){i.attr("tabindex",u+ ++f)}if(a&&a.length){a.attr("tabindex",u+ ++f)}if(s){l.attr("tabindex",u+ ++f)}else{e.attr("tabindex",u+ ++f)}}function y(e,t){var r=C.stringFormat(" {0}:",[S.ariaLabelErrorMessage]);var n=e.attr("aria-label");if(!n){return}var i=n.indexOf(r);if(i>-1){n=n.substring(0,i)}if(t&&t!==""){e.attr("aria-required",true);e.attr("aria-invalid",true);n+=r+t}else{e.removeAttr("aria-invalid")}e.attr("aria-label",n)}function k(e){return kendo.version>="2017.3.1018"||e}l.parameterEditors=[{match:l.parameterEditorsMatch.MultiSelect,createEditor:function(e,t){var r=R(e);var n=true;r.html(t.templates["trv-parameter-editor-available-values-multiselect"]);var i=r.find(".trv-list"),a=r.find(".trv-select-all"),o=r.find(".trv-select-none"),l,s,c,u=t.parameterChanged,f;a.text(S[a.text()]);a.click(function(e){e.preventDefault();if(!n)return;w(s.availableValues.map(function(e){return e.value}))});o.text(S[o.text()]);o.click(function(e){e.preventDefault();if(!n)return;w([])});function d(e){if(f){p(e);v(e)}}function p(r){var e=i.find(".trv-listviewitem");C.each(e,function(){var e=R(this);var t=r.filter(e).length>0;e.attr("aria-selected",t)})}function v(e){var t=s.availableValues,r=R.map(e,function(e){return t[R(e).index()].value});g();var n=!s.autoRefresh&&!s.childParameters;c=I.setTimeout(function(){if(!C.areEqualArrays(s.value,r)){u(s,r)}c=null},n?0:1e3)}function g(){if(c){I.clearTimeout(c)}}function h(){return R(l.element).find(".k-state-selected")}function m(e){if(!n)return;var t=R(e.target);var r=l.select();if(t.hasClass("k-state-selected")){r.splice(R.inArray(t[0],r),1)}else{r.push(t)}l.unbind("change");l.clearSelection();l.bind("change",E);l.select(r)}function E(e){d(h())}function b(e){if(!n)return;if(e.which!==32){return}var t=l.element.find(".k-state-focused");if(t.length>0){t.toggleClass("k-state-selected");d(h());e.preventDefault()}}function T(){D(i);w(s.value);l.element.off().on("touch click",".trv-listviewitem",m);l.element.on("keydown",b);f=true}function P(){f=false;if(l){l.element.off("touch click",".trv-listviewitem",m);l.element.off("keydown",b)}}function w(e){_(e);d(h())}function _(t){if(!Array.isArray(t)){t=[t]}var r=i.find(".trv-listviewitem");C.each(s.availableValues,function(e,n){var i=false;C.each(t,function(e,t){var r=n.value;if(t instanceof Date){r=C.parseToLocalDate(n.value)}i=C.areEqual(t,r);return!i});M(R(r[e]),i)})}return{beginEdit:function(e){P();s=e;l=i.kendoListView({template:kendo.template('<div class="trv-listviewitem" style="cursor: pointer">${name}</div>'),dataSource:{data:s.availableValues},selectable:"MULTIPLE",navigatable:k(t.enableAccessibility),change:E}).data("kendoListView");T()},enable:function(e){n=e;A(i,n)},clearPendingChange:g,addAccessibility:function(e){var t=C.stringFormat(S.ariaLabelParameterInfo,[e.availableValues.length]);N(i,S.ariaLabelMultiSelect,e.text,t,e.Error);i.attr("aria-multiselectable","true");var r=i.find(".trv-listviewitem");C.each(r,function(){R(this).attr("aria-label",this.innerText)})},setAccessibilityErrorState:function(e){y(i,e.Error)},destroy:function(){l.destroy()}}}},{match:l.parameterEditorsMatch.MultiSelectCombo,createEditor:function(e,t){var r=R(e),n=true,i=".trv-combo",a="trv-parameter-editor-available-values-multiselect-combo",o=t.parameterChanged,l,s,c,u,f,d=true,p;r.html(t.templates[a]);l=r.find(i);s=r.find(".trv-select-none");if(s){s.text(S[s.text()]);s.click(function(e){e.preventDefault();u.value([]);u.trigger("change")})}c=r.find(".trv-select-all");if(c){c.text(S[c.text()]);c.click(function(e){e.preventDefault();if(!n)return;var t=R.map(p.availableValues,function(e){return e.value});u.value(t);u.trigger("change")})}function v(e){g(e)}function g(e){h();var t=!p.autoRefresh&&!p.childParameters;f=I.setTimeout(function(){if(!C.areEqualArrays(p.value,e)){o(p,e)}f=null},t?0:1e3)}function h(){if(f){I.clearTimeout(f)}}function m(){return u.value()}function E(){if(d){v(m())}}function b(){D(l);u.bind("change",E)}function T(){if(u){u.unbind("change",E)}}return{beginEdit:function(e){T();p=e;l.kendoMultiSelect({itemTemplate:'<div class="trv-editoritem">${name}</div>',dataSource:p.availableValues,dataTextField:"name",dataValueField:"value",value:p.value,filter:"contains",autoClose:false,open:function(){d=false},close:function(e){d=true;E()},autoWidth:true,clearButton:false});u=l.data("kendoMultiSelect");b(l)},enable:function(e){n=e;u.enable(e)},clearPendingChange:h,addAccessibility:function(e){var t=u.input;var r=C.stringFormat(S.ariaLabelParameterInfo,[e.availableValues.length]);N(t,S.ariaLabelMultiSelect,e.text,r,e.Error);var n=u.items();C.each(n,function(){R(this).attr("aria-label",this.innerText)})},setAccessibilityErrorState:function(e){y(l,e.Error)},destroy:function(){u.destroy()}}}},{match:l.parameterEditorsMatch.SingleSelect,createEditor:function(e,t){var r=R(e);var n=true;r.html(t.templates["trv-parameter-editor-available-values"]);var a=r.find(".trv-list"),i=r.find(".trv-select-none"),o,l,s=t.parameterChanged;if(i){i.text(S[i.text()]);i.click(function(e){e.preventDefault();o.clearSelection()})}function c(e){u(e)}function u(e){var t=l.availableValues,r=R.map(e,function(e){return t[R(e).index()].value});if(Array.isArray(r)){r=r[0]}s(l,r)}function f(){return o.select()}function d(){c(f())}function p(){D(a);g(l.value);o.bind("change",d)}function v(){if(o){o.unbind("change",d)}}function g(n){var i=a.find(".trv-listviewitem");C.each(l.availableValues,function(e,t){var r=t.value;if(n instanceof Date){r=C.parseToLocalDate(t.value)}if(C.areEqual(n,r)){o.select(i[e]);return false}return true})}return{beginEdit:function(e){v();l=e;a.kendoListView({template:'<div class="trv-listviewitem">${name}</div>',dataSource:{data:l.availableValues},selectable:true,navigatable:k(t.enableAccessibility)});o=a.data("kendoListView");p(a)},enable:function(e){n=e;A(a,n);if(n){o.bind("change",d);a.addClass("k-selectable")}else{o.unbind("change",d);a.removeClass("k-selectable")}},addAccessibility:function(e){var t=C.stringFormat(S.ariaLabelParameterInfo,[e.availableValues.length]);N(a,S.ariaLabelSingleValue,e.text,t,e.Error);var r=a.find(".trv-listviewitem");C.each(r,function(){R(this).attr("aria-label",this.innerText)})},setAccessibilityErrorState:function(e){y(a,e.Error)},destroy:function(){o.destroy()}}}},{match:l.parameterEditorsMatch.SingleSelectCombo,createEditor:function(e,t){var r=R(e),n=true,i=".trv-combo",a="trv-parameter-editor-available-values-combo",o=t.parameterChanged,l,s,c,u;r.html(t.templates[a]);l=r.find(i);s=r.find(".trv-select-none");if(s){s.text(S[s.text()]);s.click(function(e){e.preventDefault();c.value("");c.trigger("change")})}function f(e,t){d(e,t)}function d(e,t){var r=t||"",n;if(!t&&e>=0){n=u.availableValues;r=n[e].value}o(u,r)}function p(){return c.select()}function v(e){f(p(),this.value())}function g(){D(l);c.bind("change",v)}function h(){if(c){c.unbind("change",v)}}return{beginEdit:function(e){h();u=e;l.kendoComboBox({template:'<div class="trv-editoritem">${name}</div>',dataSource:u.availableValues,dataTextField:"name",dataValueField:"value",value:u.value,filter:"contains",suggest:true,clearButton:false});c=l.data("kendoComboBox");g(l)},enable:function(e){n=e;c.enable(e)},addAccessibility:function(e){var t=c.input;var r=C.stringFormat(S.ariaLabelParameterInfo,[e.availableValues.length]);N(t,S.ariaLabelSingleValue,e.text,r,e.Error);var n=c.items();C.each(n,function(){R(this).attr("aria-label",this.innerText)})},setAccessibilityErrorState:function(e){y(l,e.Error)},destroy:function(){c.destroy()}}}},{match:l.parameterEditorsMatch.MultiValue,createEditor:function(e,t){var r=R(e),n;r.html(t.templates["trv-parameter-editor-multivalue"]);var i=r.find("textarea").on("change",function(){if(t.parameterChanged){t.parameterChanged(n,o.parseValues(this.value))}});function a(e){n.value=e;i.val(o.formatValue(e))}return{beginEdit:function(e){n=e;a(e.value);D(i)},enable:function(e){A(i,e);i.prop("disabled",!e)},addAccessibility:function(e){N(i,S.ariaLabelMultiValue,e.text,null,e.Error)},setAccessibilityErrorState:function(e){y(i,e.Error)}}}},{match:l.parameterEditorsMatch.DateTime,createEditor:function(e,r){var t=R(e),n;t.html(r.templates["trv-parameter-editor-datetime"]);var i=t.find("input[type=datetime]").kendoDatePicker({change:function(){var e=r.parameterChanged;if(e){var t=this.value();if(null!==t){t=C.adjustTimezone(t)}e(n,t)}}});var a=i.data("kendoDatePicker");function o(e){n.value=e;var t=null;try{if(e){t=C.unadjustTimezone(e)}}catch(e){t=null}a.value(t)}return{beginEdit:function(e){n=e;o(e.value);D(i)},enable:function(e){a.enable(e);A(i,e)},addAccessibility:function(e){N(i,S.ariaLabelParameterDateTime,e.text,null,e.Error);i.attr("aria-live","assertive")},setAccessibilityErrorState:function(e){y(i,e.Error)},destroy:function(){a.destroy()}}}},{match:l.parameterEditorsMatch.String,createEditor:function(e,t){var r=R(e),n;r.html(t.templates["trv-parameter-editor-text"]);var i=r.find('input[type="text"]').change(function(){if(t.parameterChanged){t.parameterChanged(n,i.val())}});function a(e){n.value=e;i.val(e)}return{beginEdit:function(e){n=e;a(e.value);D(i)},enable:function(e){i.prop("disabled",!e);A(i,e)},addAccessibility:function(e){N(i,S.ariaLabelParameterString,e.text,null,e.Error);i.attr("aria-live","assertive")},setAccessibilityErrorState:function(e){y(i,e.Error)}}}},{match:l.parameterEditorsMatch.Number,createEditor:function(e,t){var r=R(e),n,i;r.html(t.templates["trv-parameter-editor-number"]);var a=r.find("input[type=number]").on("change",function(){if(t.parameterChanged){t.parameterChanged(n,a.val())}});function o(e){n.value=e;a.val(e)}return{beginEdit:function(e){if(i){i.dispose()}n=e;a.val(n.value);if(n.type===l.ParameterTypes.INTEGER){i=s(a)}else{i=c(a)}D(a)},enable:function(e){a.prop("disabled",!e);A(a,e)},addAccessibility:function(e){N(a,S.ariaLabelParameterNumerical,e.text,null,e.Error);a.attr("aria-live","assertive")},setAccessibilityErrorState:function(e){y(a,e.Error)}}}},{match:l.parameterEditorsMatch.Boolean,createEditor:function(e,t){var r=R(e),n;r.html(t.templates["trv-parameter-editor-boolean"]);var i=r.find("input[type=checkbox]").on("change",function(){if(t.parameterChanged){t.parameterChanged(n,this.checked)}});function a(e){n.value=e;i[0].checked=e===true}return{beginEdit:function(e){n=e;a(e.value);D(i)},enable:function(e){A(i,e);i.attr("disabled",!e)},addAccessibility:function(e){N(i,S.ariaLabelParameterBoolean,e.text,null,e.Error);i.attr("aria-live","assertive")},setAccessibilityErrorState:function(e){y(i,e.Error)}}}},{match:l.parameterEditorsMatch.Default,createEditor:function(e,t){var r=R(e);r.html('<div class="trv-parameter-editor-generic"></div>');return{beginEdit:function(e){r.find(".trv-parameter-editor-generic").html(e.Error?"(error)":e.value)},enable:function(e){}}}}]})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,t,r,n,l){"use strict";var s=e.sr,c=e.utils;e.parameterValidators=function(){var n={};function i(t,e,r,n){var i=[].concat(e).map(function(e){return o(t,r(e),n)});if(t.multivalue){if(e==null||e.length==0){if(t.allowNull){return e}else{throw s.invalidParameter}}return i}return i[0]}function a(e,t){return e.allowNull&&-1!=[null,"",l].indexOf(t)}function o(e,r,n){if(e.availableValues){var i=false;c.each(e.availableValues,function(e,t){i=n(r,t.value);return!i});if(!i){if(e.allowNull&&!r){return r}throw s.invalidParameter}}return r}n[e.ParameterTypes.STRING]={validate:function(t,e){return i(t,e,function(e){if(!e){if(t.allowNull){return null}if(t.allowBlank){return""}throw s.parameterIsEmpty}return e},function(e,t){return e==t})}};n[e.ParameterTypes.FLOAT]={validate:function(r,e){return i(r,e,function(e){var t=c.tryParseFloat(e);if(isNaN(t)){if(a(r,e)){return null}throw s.parameterIsEmpty}return t},function(e,t){return c.tryParseFloat(e)==c.tryParseFloat(t)})}};n[e.ParameterTypes.INTEGER]={validate:function(r,e){return i(r,e,function(e){var t=c.tryParseInt(e);if(isNaN(t)){if(a(r,e)){return null}throw s.parameterIsEmpty}return t},function(e,t){return c.tryParseInt(e)==c.tryParseFloat(t)})}};n[e.ParameterTypes.DATETIME]={validate:function(t,e){return i(t,e,function(e){if(t.allowNull&&(e===null||e===""||e===l)){return null}if(!isNaN(Date.parse(e))){if(t.availableValues){return e}return c.parseToLocalDate(e)}throw s.invalidDateTimeValue},function(e,t){e=c.parseToLocalDate(e);t=c.parseToLocalDate(t);return e.getTime()==t.getTime()})}};n[e.ParameterTypes.BOOLEAN]={validate:function(t,e){return i(t,e,function(e){if(-1!=["true","false"].indexOf((""+e).toLowerCase())){return Boolean(e)}if(a(t,e)){return null}throw s.parameterIsEmpty},function(e,t){return Boolean(e)==Boolean(t)})}};return{validate:function(e,t){var r=n[e.type];if(!r){throw c.stringFormat(s.cannotValidateType,e)}return r.validate(e,t)}}}()})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(te,re,e,t,ne){"use strict";var ie=te.sr,ae=te.utils,oe=te.parameterValidators;var le={};var se={PARAMETERS_READY:"pa.parametersReady",ERROR:"pa.Error"};function r(e,p,t){p=re.extend({},le,p,t);var r={};var n=re(r);var v={};var i=p.controller;if(!i){throw"No controller (telerikReporting.reportViewerController) has been specified."}var a=[].concat(p.parameterEditors,te.parameterEditors);var g,o,h=ne;var l=re(e),s=l.find(".trv-parameters-area-content"),c=l.find(".trv-error-message"),u=l.find(".trv-parameters-area-preview-button");u.text(ie[u.text()]);u.attr("aria-label",ie[u.attr("aria-label")]);u.on("click",function(e){e.preventDefault();if(A()){D()}});var f=p.templates["trv-parameter"];var d=p.parametersAreaVisible!==false;var m=p.enableAccessibility;E();function E(){b(l)}function b(e){var t=e.find(".trv-parameters-area-preview-button");if(!t){return}t.attr("aria-label",ie[t.attr("aria-label")]);t.text(ie[t.text()])}function T(){return re(f)}function P(t){var e=T(),r=e.find(".trv-parameter-value"),n=e.find(".trv-parameter-title"),i=e.find(".trv-parameter-error"),a=e.find(".trv-parameter-error-message"),o=e.find(".trv-parameter-use-default input"),l=p.parameters&&p.parameters.editors?p.parameters.editors:null,s=R(t,l);var c=t.text;var u=!t.isVisible;if(u){c+=" [<b>hidden</b>]"}n.html(c).attr("title",c);a.html(t.Error);(t.Error?re.fn.show:re.fn.hide).call(i);var f=s.createEditor(r,{templates:p.templates,parameterChanged:function(t,e){try{e=oe.validate(t,e);i.hide();x(t,e)}catch(e){t.Error=e;t.value=[];a.html(e);i.show();_(false)}finally{w(t)}},enableAccessibility:m});v[t.id]=f;f.beginEdit(t);if(m&&!u){f.addAccessibility(t)}if(o.length>0){o.on("click",function(){var e=re(this).is(":checked");if(e){delete g[t.id];delete h[t.id];V(t);j(Q)}else{g[t.id]=t.value;h[t.id]=t.value}f.enable(!e);k()});var d=h!==null;if(d){if(!(t.id in h)){o.prop("checked",true);f.enable(false)}}else if(u){o.prop("checked",true);f.enable(false)}}return e}function w(e){var t=v[e.id];if(!t||!m){return}t.setAccessibilityErrorState(e)}function _(e){if(e){u.prop("disabled",false);u.removeClass("k-state-disabled")}else{u.prop("disabled",true);u.addClass("k-state-disabled")}}function R(e,t){var r;ae.each(a,function(){if(this&&this.match(e,t)){r=this}return!r});return r}function I(e){c.html(e);(e?re.fn.addClass:re.fn.removeClass).call(l,"trv-error")}function S(){(C(o)?re.fn.removeClass:re.fn.addClass).call(l,"preview")}function C(){var e=true;ae.each(o,function(){return e=!this.isVisible||this.autoRefresh});return e}function A(){var e=true;ae.each(o,function(){return e=!this.Error});return e}function M(){ae.each(v,function(){if(this.hasOwnProperty("destroy")){this.destroy()}});v={}}function N(e){g={};o=e||[];M();var r,n=re("<div></div>");ae.each(o,function(){try{this.value=oe.validate(this,this.value)}catch(e){this.Error=this.Error||e}var e=Boolean(this.Error),t=!e;if(t){g[this.id]=this.value}else{this.Error=ie.invalidParameter}if(this.isVisible||p.showHiddenParameters){r=P(this);if(r){n.append(r)}}});if(h!==ne){if(null===h){h={};ae.each(o,function(){if(this.isVisible){h[this.id]=this.value}else{delete g[this.id]}})}else{ae.each(o,function(){if(!(this.id in h)){delete g[this.id]}})}}s.empty();if(o.length>0){s.append(n.children());if(m){s.attr("aria-label","Parameters area. Contains "+o.length+" parameters.")}}S(o);var t=A();_(t)}function D(){i.setParameters(re.extend({},g));i.previewReport(false)}function y(){var e=true;for(var t=o.length-1;e&&t>=0;t--){var r=o[t];e=r.id in g&&(Boolean(r.autoRefresh)||!r.isVisible)}return e}function k(){r.parametersReady(g)}function O(){k();if(y()){D()}}function V(e){if(e.childParameters){ae.each(e.childParameters,function(e,t){var r=G(t);if(r){V(r)}delete g[t];L(t)})}}function L(e){if(v){var t=v[e];if(t&&typeof t.clearPendingChange==="function"){t.clearPendingChange()}}}function x(e,t){delete e["Error"];e.value=t;g[e.id]=t;if(h!==ne){if(e.id in h){g[e.id]=t}}else{g[e.id]=t}V(e);if(e.childParameters){j(O)}else{var r=A();_(r);if(r){O()}}}function G(e){if(o){for(var t=0;t<o.length;t++){var r=o[t];if(r.id===e){return r}}}return null}function F(e){i.setParametersAreaVisible({visible:e})}function U(e){if(!e||null===e){return false}var t=false;ae.each(e,function(){t=this.isVisible;return!t});return t}var B=0;function $(){B++;l.addClass("trv-loading")}function H(){if(B>0){if(0===--B){l.removeClass("trv-loading")}}}var W=false;function z(e,t){W=U(e);if(!W){q(false)}N(e);I("");if(W&&d){q(true);if(m){J()}}i.updateUIInternal();if(typeof t==="function"){t()}H()}function j(e){Y(i.loadParameters(g),e)}function Y(e,t){$();e.then(function(e){z(e,t)}).catch(function(e){H();ee();if(!l.hasClass("trv-hidden")){I(e)}r.error(e)})}function K(e){var t;if(e&&e.length){t=e[0]}if(typeof t==="function"){return t}return null}function X(e,t){var r=K(t);if(r){n.on(e,r)}else{n.trigger(e,t)}return i}function Q(){if(h===null){h=re.extend({},g)}k()}function q(e){var t=te[p.viewerSelector+"-parameters-splitter"],r=l.prev();if(p.parametersAreaPosition===te.ParametersAreaPositions.TOP||p.parametersAreaPosition===te.ParametersAreaPositions.LEFT){r=l.next()}if(t){(W?re.fn.removeClass:re.fn.addClass).call(r,"trv-hidden");t.toggle(".trv-parameters-area",e)}}function J(){var e=l.prev();var t=l.find(".trv-parameters-area-content").attr("tabIndex");if(p.parametersAreaPosition===te.ParametersAreaPositions.TOP||p.parametersAreaPosition===te.ParametersAreaPositions.LEFT){e=l.next()}e.attr("aria-label",ie.ariaLabelParametersAreaSplitter);e.attr("tabIndex",t)}function Z(e,t){I();s.empty();Y(t,Q)}i.reloadParameters(Z).getParametersAreaState(function(e,t){var r=false;if(o){r=U(o)}t.enabled=r;t.visible=d}).setParametersAreaVisible(function(e,t){d=t.visible;q(t.visible&&U(o))}).beforeLoadReport(function(){B=0;$()}).error(H).pageReady(function(){H()});function ee(){N([])}re.extend(r,{allParametersValid:function(){return A()},clear:function(){ee()},error:function(){return X(se.ERROR,arguments)},parametersReady:function(){return X(se.PARAMETERS_READY,arguments)},setParameters:function(e){h=null===e?null:re.extend({},e)}});return r}var n="telerik_ReportViewer_ParametersArea";re.fn[n]=function(e,t){return ae.each(this,function(){if(!re.data(this,n)){re.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(P,e,l,t,r){"use strict";var n=P.utils;if(!n){throw"Missing telerikReporting.utils"}function i(e){var d={ExportInProgress:1<<0,PrintInProgress:1<<1,RenderInProgress:1<<2};function p(e){return(n&e)!=0}function r(e,t){if(t){n|=e}else{n&=~e}}var v=e.controller,g=e.history,n=0,t,h=e.commands;if(!v){throw"No controller (telerikReporting.ReportViewerController) has been specified."}function m(){var e={};v.getDocumentMapState(e);return e}function E(){var e={};v.getParametersAreaState(e);return e}function b(){var e={};v.getSearchDialogState(e);return e}function T(){var e={};v.getSendEmailDialogState(e);return e}function i(){if(!t){t=true;l.setTimeout(function(){try{a()}finally{t=false}},10)}}function a(){var e=v.reportSource();var t=v.pageCount();var r=v.currentPageNumber();var n=e&&e.report;var i=n&&t>0;var a=i&&r<t;var o=i&&r>1;var l=i&&r;var s=m();var c=E();var u=b();var f=T();h.goToFirstPage.enabled(o);h.goToPrevPage.enabled(o);h.stopRendering.enabled(n&&p(d.RenderInProgress));h.goToLastPage.enabled(a);h.goToNextPage.enabled(a);h.goToPage.enabled(i);h.print.enabled(i&&!p(d.PrintInProgress));h.export.enabled(i&&!p(d.ExportInProgress));h.refresh.enabled(n);h.historyBack.enabled(g&&g.canMoveBack());h.historyForward.enabled(g&&g.canMoveForward());h.toggleDocumentMap.enabled(n&&s.enabled).checked(s.enabled&&s.visible);h.toggleParametersArea.enabled(n&&c.enabled).checked(c.enabled&&c.visible);h.togglePrintPreview.enabled(i).checked(v.viewMode()==P.ViewModes.PRINT_PREVIEW);h.pageMode.enabled(i).checked(v.pageMode()==P.PageModes.CONTINUOUS_SCROLL);h.zoom.enabled(l);h.zoomIn.enabled(l);h.zoomOut.enabled(l);h.toggleZoomMode.enabled(l);h.toggleSearchDialog.enabled(i).checked(u.visible);h.toggleSendEmailDialog.enabled(i).checked(f.visible);v.updateUI(null);v.pageNumberChange(r);v.pageCountChange(t)}function o(){var e={};v.getScale(e);return e.scaleMode}v.scale(function(e,t){h.toggleZoomMode.checked(t.scaleMode===P.ScaleModes.FIT_PAGE||t.scaleMode===P.ScaleModes.FIT_PAGE_WIDTH)});v.currentPageChanged(i);v.beforeLoadReport(function(){r(d.RenderInProgress,true);i()});v.reportLoadProgress(i);v.reportLoadComplete(function(){r(d.RenderInProgress,false);i()});v.reportSourceChanged(i);v.viewModeChanged(i);v.pageModeChanged(function(){i()});v.setParametersAreaVisible(i);v.setDocumentMapVisible(i);v.setUIState(function(e,t){r(d[t.operationName],t.inProgress);i()});v.error(function(){r(d.ExportInProgress,false);r(d.PrintInProgress,false);r(d.RenderInProgress,false);i()});v.updateUIInternal(i);v.setSearchDialogVisible(i);v.setSendEmailDialogVisible(i);v.renderingStopped(function(){r(d.RenderInProgress,false);i()});i()}P.uiController=i})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,t,r,n){"use strict";var v=e.utils;if(!v){throw"Missing telerikReporting.utils"}e.HistoryManager=function(e){var n=e.controller;var r=false;if(!n){throw"No controller (telerikReporting.reportViewerController) has been specified."}var t=e.settings,i=t.history()||{records:[],position:-1};n.onLoadedReportChange(function(){r=false;c(true)}).currentPageChanged(function(){s()}).reportLoadComplete(function(e,t){c(false)}).clientExpired(function(){r=true;var e=i.records;for(var t=0;t<e.length;t++){e[t].reportDocumentId=null}});function a(){var e=i.records;if(e.length>0){return e[i.position]}return null}function o(e){var t=i.records,r=i.position;t=Array.prototype.slice.call(t,0,r+1);t.push(e);i.records=t;i.position=t.length-1;l()}function l(){t.history(i)}function s(){var e=a();if(e){e.pageNumber=n.currentPageNumber();e.viewMode=n.viewMode();e.reportDocumentId=n.reportDocumentIdExposed();l()}}function c(e){p();var t=a();var r=n.reportSource();if(!t||!v.reportSourcesAreEqual(t.reportSource,r)){o({reportSource:r,pageNumber:1,temp:e})}}function u(e){n.setViewMode(e.viewMode);n.reportSource(e.reportSource);n.refreshReport(false,e.reportDocumentId);n.navigateToPage(e.pageNumber)}function f(e){var t=i.position,r=i.records.length,n=t+e;return 0<=n&&n<r}function d(e){var t=i.position,r=i.records.length,n=t+e;if(n<0){n=0}else if(n>=r){n=r-1}if(n!=t){i.position=n;l();u(a())}}function p(){var e=i.records.length-1;while(e>=0){if(i.records[e].temp===true){i.records.splice(e,1);if(i.position>=e){i.position--}}else{break}e--}}return{back:function(){d(-1)},forward:function(){d(+1)},canMoveBack:function(){return f(-1)},canMoveForward:function(){return f(1)},loadCurrent:function(){var e=a();if(e){u(e)}return Boolean(e)}}}})(window.telerikReportViewer=window.telerikReportViewer||{},window,document);(function(u,a,e,t,r){"use strict";var n=u.utils;if(!n){throw"Missing telerikReporting.utils"}var f={};f[u.ScaleModes.FIT_PAGE]={scaleMode:u.ScaleModes.FIT_PAGE_WIDTH};f[u.ScaleModes.FIT_PAGE_WIDTH]={scaleMode:u.ScaleModes.SPECIFIC,scale:1};f[u.ScaleModes.SPECIFIC]={scaleMode:u.ScaleModes.FIT_PAGE};var d=[.1,.25,.5,.75,1,1.5,2,4,8];function i(e){var r=e.controller;if(!r){throw"No options.controller."}var t=e.history;if(!t){throw"No options.history."}function n(){var e={};r.getDocumentMapState(e);return Boolean(e.visible)}function i(){var e={};r.getParametersAreaState(e);return Boolean(e.visible)}function a(){var e={};r.getSideMenuVisible(e);return Boolean(e.visible)}function o(){var e={};r.getSearchDialogState(e);return Boolean(e.visible)}function l(){var e={};r.getSendEmailDialogState(e);return Boolean(e.visible)}return{historyBack:new p(function(){t.back()}),historyForward:new p(function(){t.forward()}),stopRendering:new p(function(){r.stopRendering()}),goToPrevPage:new p(function(){r.navigateToPage(r.currentPageNumber()-1)}),goToNextPage:new p(function(){r.navigateToPage(r.currentPageNumber()+1)}),goToFirstPage:new p(function(){r.navigateToPage(1)}),goToLastPage:new p(function(){r.navigateToPage(r.pageCount())}),goToPage:new p(function(e){if(!isNaN(e)){var t=r.pageCount();if(e>t){e=t}else if(e<1){e=1}r.navigateToPage(e);return e}}),refresh:new p(function(){r.refreshReport(true)}),export:new p(function(e){if(e){r.exportReport(e)}}),print:new p(function(){r.printReport()}),pageMode:new p(function(){r.pageMode(r.pageMode()===u.PageModes.SINGLE_PAGE?u.PageModes.CONTINUOUS_SCROLL:u.PageModes.SINGLE_PAGE)}),togglePrintPreview:new p(function(){r.viewMode(r.viewMode()===u.ViewModes.PRINT_PREVIEW?u.ViewModes.INTERACTIVE:u.ViewModes.PRINT_PREVIEW)}),toggleDocumentMap:new p(function(){r.setDocumentMapVisible({visible:!n()})}),toggleParametersArea:new p(function(){r.setParametersAreaVisible({visible:!i()})}),zoom:new p(function(e){var t={};t.scale=e;t.scaleMode=u.ScaleModes.SPECIFIC;r.scale(t)}),zoomIn:new p(function(){s(1)}),zoomOut:new p(function(){s(-1)}),toggleSideMenu:new p(function(){r.setSideMenuVisible({visible:!a()})}),toggleZoomMode:new p(function(e){var t={};r.getScale(t);r.scale(f[t.scaleMode])}),toggleSearchDialog:new p(function(){r.setSearchDialogVisible({visible:!o()})}),toggleSendEmailDialog:new p(function(){r.setSendEmailDialogVisible({visible:!l()})})};function s(e){var t={};r.getScale(t);t.scale=c(t.scale,e);t.scaleMode=u.ScaleModes.SPECIFIC;r.scale(t)}function c(e,t){var r=-1,n=d.length;for(var i=0;i<n;i++){var a=d[i];if(e<a){r=i-.5;break}if(e===a){r=i;break}}r=r+t;if(t>=0){r=Math.round(r-.49)}else{r=Math.round(r+.49)}if(r<0){r=0}else if(r>n-1){r=n-1}return d[r]}}u.CommandSet=i;function p(e){var r=true;var n=false;var i={enabled:function(e){if(arguments.length===0){return r}var t=Boolean(e);r=t;a(this).trigger("enabledChanged");return i},checked:function(e){if(arguments.length===0){return n}var t=Boolean(e);n=t;a(this).trigger("checkedChanged");return i},exec:e};return i}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,M,N,D,y){"use strict";var k=e.sr;if(!k){throw"Missing telerikReportViewer.sr"}var O=e.utils;if(!O){throw"Missing telerikReporting.utils"}var V,L;function r(e,t,r){var n=M.extend({},t,r),a=M(e).data("kendoMenu"),o=e.childNodes,i=n.controller,l=n.enableAccessibility;if(!i){throw"No controller (telerikReporting.ReportViewerController) has been specified."}if(!a){s()}i.reportLoadComplete(function(e,t){if(!l){if(a&&a._oldHoverItem){a._oldHoverItem.toggleClass("k-state-focused")}}});function s(){a=M(e).kendoMenu().data("kendoMenu"),a.bind("open",f);a.bind("activate",g);a.bind("deactivate",h);a.element.off("keydown",E);a.element.on("keydown",E);if(n.enableAccessibility){c()}C()}function c(){var e=M.find('[data-role="telerik_ReportViewer_MainMenu"]');O.each(e,function(){var e=M(this);var t=e.find("li");var r=0;var n=e.attr("tabIndex");if(n){r=O.tryParseInt(n);if(!r||isNaN(r)){r=0}}u(t,r);var i=t.find('input[data-role="telerik_ReportViewer_PageNumberInput"]');if(i.length>0){i.attr("tabindex",r)}})}function u(e,n){O.each(e,function(){var t=M(this);t.attr("tabindex",n);t.focus(function(){t.addClass("k-state-focused")});t.blur(function(){t.removeClass("k-state-focused")});var e=t.children("a");if(e.length>0){var r=M(e);r.attr("tabindex",-1);t.attr("title",r.attr("title"))}t.off("keydown");t.on("keydown",function(e){if(e.which==kendo.keys.ENTER){v(t);V=t}})})}function f(e){var t=M(e.item);if(t.children("ul[data-command-list=export-format-list]").length>0){a.unbind("open",f);a.append({text:k.loadingFormats,spriteCssClass:"k-icon k-loading"},t);i.getDocumentFormats().then(d).then(function(){a.open(t)})}}function d(t){O.each(M(e).find("ul[data-command-list=export-format-list]"),function(){var e=M(this),n=e.parents("li");a.remove(e.children("li"));var i=l?n.attr("tabindex"):-1;if(!i){i=1}O.each(t,function(){var e=this;var t=l?O.stringFormat('aria-label="{localizedName}" ',e):" ";var r="<li "+t+O.stringFormat('tabindex="'+i+'"><a tabindex="-1" href="#" data-command="telerik_ReportViewer_export" data-command-parameter="{name}"><span>{localizedName}</span></a></li>',e);a.append(r,n)});if(l){p(n.find("li"))}})}function p(e){O.each(e,function(){var n=M(this);n.off("keydown");n.on("keydown",function(e){switch(e.which){case kendo.keys.ENTER:v(n);break;case kendo.keys.UP:var t=n.prev();if(t.length>0){t.focus()}else{n.parents("li").focus()}break;case kendo.keys.DOWN:var r=n.next();if(r.length>0){r.focus()}else{n.parent().children("li").first().focus()}break}})})}function v(e){if(e&&e.length>0){var t=e.children("a");if(t.length>0){t.click()}}}function g(e){var t=M(e.item);m(t)}function h(e){L=y}function m(t){if(V&&V.is(t)){N.setTimeout(function(){var e=t.find("li");if(e.length>0){e[0].focus()}},100)}}function E(e){switch(e.which){case kendo.keys.ENTER:if(!l){var t=b();if(t.length>0){if(S(t)&&L){t=L}v(t)}}break;case kendo.keys.RIGHT:l?T():w();break;case kendo.keys.LEFT:l?_():R();break;case kendo.keys.DOWN:case kendo.keys.UP:if(!l){L=I()}}}function b(){var e;var t=D.activeElement;if(t&&t.localName=="li"){var r=M(o).filter("li.k-item");for(var n=0;n<r.length;n++){var i=r[n];if(t===i){e=M(i);break}}}else if(t&&t.localName=="input"){e=M(t).closest("li.k-item")}else{e=a.element.children("li.k-item.k-state-focused");if(e.length===0){e=a.element.children("li.k-item").first()}}return e}function T(){var e=b();if(!e||!e.length>0){return}var t=e.next();if(!t.length>0){t=M(o).filter("li.k-item").first()}t.focus()}var P;function w(){var e=a.element.children("li.k-item");var t=e.filter(".k-state-focused");if(kendo.version>="2017.3.913"){P=t;return}if(t.hasClass("k-state-disabled")){if(!P||t.is(P)){var r=t.next();if(!r.length>0){r=e.first()}t.toggleClass("k-state-focused");r.toggleClass("k-state-focused");P=r;a._oldHoverItem=r}else{P=t}}else{a._oldHoverItem=t;P=t}}function _(){var e=b();if(!e||!e.length>0){return}var t=e.prev();if(!t.length>0){t=M(o).filter("li.k-item").last()}t.focus()}function R(){var e=a.element.children("li.k-item.k-state-focused");P=e}function I(){var e=a.element.find('li.k-item.k-state-focused [data-command="telerik_ReportViewer_export"]');if(e.length===1){return e.parent("li")}return y}function S(e){if(e.length===0){return}var t=e.attr("id");return t=="trv-main-menu-export-command"||t=="trv-side-menu-export-command"}function C(){var e=A();if(!e){return}O.each(e,function(){var e=M(this),t=e.children("li.k-item");e.attr("aria-label",k[e.attr("aria-label")]);O.each(t,function(){var e=M(this);e.attr("aria-label",k[e.attr("aria-label")]);if(!e.hasClass("trv-report-pager")){var t=e.find("a");if(t){t.attr("title",k[t.attr("title")])}}else{e.attr("title",k[e.attr("title")])}})})}function A(){return O.findElement("ul[data-role=telerik_ReportViewer_MainMenu]")}}var n="telerik_ReportViewer_MainMenu";M.fn[n]=function(e,t){return O.each(this,function(){if(!M.data(this,n)){M.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,I,S,C,t){"use strict";var A=e.sr;if(!A){throw"Missing telerikReportViewer.sr"}var M=e.utils;if(!M){throw"Missing telerikReporting.utils"}function r(e,t,r){var n=I.extend({},t,r),i=I(e).data("kendoMenu"),o=n.enableAccessibility,a,l=3,s,c,u=false,f=n.controller;if(!f){throw"No controller (telerikReporting.ReportViewerController) has been specified."}if(!i){d(e)}function d(t){var e=I(t);s=e.children("ul").kendoPanelBar().data("kendoPanelBar");s.bind("expand",v);s.element.off("keydown",T);s.element.on("keydown",T);E(e);m(e);e.click(function(e){if(e.target==t){f.setSideMenuVisible({visible:!u})}});_()}f.setSideMenuVisible(function(e,t){p();if(o){s.element.focus()}u=t.visible}).getSideMenuVisible(function(e,t){t.visible=u});function p(){var e=s.element.parent();var t=e.position().left<0||!e.is(":visible");if(t){e.show()}else{S.setTimeout(function(){e.hide()},500)}}function v(e){var t=I(e.item);if(t.children("ul[data-command-list=export-format-list]").length>0){s.unbind("expand",v);s.append({text:A.loadingFormats,spriteCssClass:"k-icon k-loading"},t);n.controller.getDocumentFormats().then(g).then(function(){s.expand(t)})}}function g(t){M.each(I(e).find("ul[data-command-list=export-format-list]"),function(){var e=I(this),i=e.parents("li");s.remove(e.children("li"));var a=i.attr("tabindex");if(!a){a=l}M.each(t,function(e){var t=this;var r=o?M.stringFormat('aria-label="{localizedName}" ',t):" ";var n="<li "+r+M.stringFormat('tabindex="'+a+'"><a tabindex="-1" href="#" data-command="telerik_ReportViewer_export" data-command-parameter="{name}"><span>{localizedName}</span></a></li>',t);s.append(n,i)});b(i.find("li"),a);m(i)})}function h(t){if(a&&a.is(t)){S.setTimeout(function(){var e=t.find("li");if(e.length>0){e[0].focus()}},100)}}function m(e){M.each(e.find("li"),function(){var e=I(this).children("ul").length===0;if(e){I(this).children("a").click(function(){f.setSideMenuVisible({visible:!u})})}})}function E(e){if(!e){return}var t=e.children("ul");var r=e.attr("tabindex");var n=r?r:l;b(t,n)}function b(e,n){e.attr("tabindex",n);var t=e.find("li");M.each(t,function(){var t=I(this);t.attr("tabindex",n);var e=t.children("a");if(e.length>0){var r=I(e);r.attr("tabindex",-1)}t.focus(function(){var e=t.children("a");if(e.length>0){e.addClass("k-state-focused")}});t.blur(function(){var e=t.children("a");if(e.length>0){e.removeClass("k-state-focused")}});t.off("keydown",P);t.on("keydown",P)})}function T(e){if(e.which==kendo.keys.ENTER){var t;var r=false;var n=C.activeElement;if(n&&n.localName=="li"){var i=s.element.find("li.k-item");for(var a=0;a<i.length;a++){var o=i[a];if(n===o){t=I(o);r=true;break}}}else{t=s.select()}if(!t||!t.length>0){return}w(t,r)}}function P(e){if(e.which==kendo.keys.ENTER){w(I(e.target),false)}}function w(e,t){if(!e.length>0){return}a=e;var r=e.children("ul").length===0;if(!r){if(t){if(e.hasClass("k-state-active")){s.collapse(e)}else{s.expand(e)}}}else{var n=e.find("a");if(n.length>0){n[0].click()}}}function _(){var e=R();if(!e){return}M.each(e,function(){var e=I(this),t=e.children("li.k-item");e.attr("aria-label",A[e.attr("aria-label")]);M.each(t,function(){var e=I(this),t=e.find("a");e.attr("aria-label",A[e.attr("aria-label")]);if(t){var r=t.find("span:not(.k-icon)");t.attr("title",A[t.attr("title")]);if(r){r.text(A[r.text()])}}})})}function R(){return M.findElement("div[data-role=telerik_ReportViewer_SideMenu] > ul")}}var n="telerik_ReportViewer_SideMenu";I.fn[n]=function(e,t){return M.each(this,function(){if(!I.data(this,n)){I.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(s,c,e,u,t){"use strict";var f=s.utils;if(!f){throw"Missing telerikReportViewer.utils"}s.binder={bind:function(e){var r=Array.prototype.slice.call(arguments,1);n(e,r);var t=f.selector('[data-role^="telerik_ReportViewer_"]');f.each(t,function(){var e=c(this),t=c.fn[e.attr("data-role")];if(typeof t==="function"){t.apply(e,r)}})}};function n(r,e){var i=e[0].commands,n=e[1],t='[data-command^="telerik_ReportViewer_"]',a="[data-target-report-viewer]"+t;r.on("click",t,o);if(!s.GlobalSettings.CommandHandlerAttached){c(u.body).on("click",a,l);s.GlobalSettings.CommandHandlerAttached=true}f.each(i,function(e,t){d(e,t,n,r)});function o(e){var t=c(this).attr("data-command");if(t){var r=t.substring("telerik_ReportViewer_".length),n=i[r];if(n&&n.enabled()){n.exec(c(this).attr("data-command-parameter"))}e.preventDefault()}}function l(e){var t=c(this),r=t.attr("data-command"),n=t.attr("data-target-report-viewer");if(r&&n){var i=r.substring("telerik_ReportViewer_".length),a=c(n).data("telerik_ReportViewer"),o=a.commands[i];if(o.enabled()){o.exec(c(this).attr("data-command-parameter"))}e.preventDefault()}}}function d(e,t,r,n){if(t){var i='[data-command="telerik_ReportViewer_'+e+'"]',a='[data-target-report-viewer="'+r.selector+'"]'+i,o=n.find(i),l=c(a);c(t).on("enabledChanged",function(e){(t.enabled()?c.fn.removeClass:c.fn.addClass).call(o.parent("li"),"k-state-disabled");(t.enabled()?c.fn.removeClass:c.fn.addClass).call(l,r.disabledButtonClass)}).on("checkedChanged",function(e){(t.checked()?c.fn.addClass:c.fn.removeClass).call(o.parent("li"),"k-state-selected");(t.checked()?c.fn.addClass:c.fn.removeClass).call(l,r.checkedButtonClass)})}}function r(e,t){var r,n=c(e),i=n.attr("data-command");if(i){r=t.commands[i]}if(r){n.click(function(e){if(r.enabled()){r.exec(c(this).attr("data-command-parameter"))}else{e.preventDefault()}});c(r).on("enabledChanged",function(e){(r.enabled()?c.fn.removeClass:c.fn.addClass).call(n,"disabled")}).on("checkedChanged",function(e){(r.checked()?c.fn.addClass:c.fn.removeClass).call(n,"checked")})}}var i="telerik_ReportViewer_LinkButton";c.fn[i]=function(e){return f.each(this,function(){if(!c.data(this,i)){c.data(this,i,new r(this,e))}})};function a(e,t){var r=c(e),n=0,i=t.commands["goToPage"];function a(e){if(n!==e||!r.is(":focus")){r.val(e);n=e}}t.controller.pageNumberChange(function(e,t){a(t)});r.change(function(){var e=c(this).val();var t=f.tryParseInt(e);if(!isNaN(t)){var r=i.exec(t);a(r)}});r.keydown(function(e){if(e.which==13){c(this).change();return e.preventDefault()}});function o(e){return/^([0-9]+)$/.test(e)}r.keypress(function(e){if(f.isSpecialKey(e.keyCode)){return true}var t=r.val()+String.fromCharCode(e.charCode);return o(t)}).on("paste",function(e){})}var o="telerik_ReportViewer_PageNumberInput";c.fn[o]=function(e){return f.each(this,function(){if(!c.data(this,o)){c.data(this,o,new a(this,e))}})};function l(e,t){var r=c(e);t.controller.pageCountChange(function(e,t){r.html(t)})}var p="telerik_ReportViewer_PageCountLabel";c.fn[p]=function(e){return f.each(this,function(){if(!c.data(this,p)){c.data(this,p,new l(this,e))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(e,P,t,w){"use strict";e.PerspectiveManager=function(e,r){var n=e.querySelectorAll?e.querySelectorAll(".trv-menu-small")[0]:false,i={small:{documentMapVisible:false,parametersAreaVisible:false,onDocumentMapVisibleChanged:function(e,t){if(t.visible){r.setParametersAreaVisible({visible:false})}},onParameterAreaVisibleChanged:function(e,t){if(t.visible){r.setDocumentMapVisible({visible:false})}},onBeforeLoadReport:function(){r.setParametersAreaVisible({visible:false});r.setDocumentMapVisible({visible:false})},onNavigateToPage:function(){r.setParametersAreaVisible({visible:false});r.setDocumentMapVisible({visible:false})}},large:{documentMapVisible:true,parametersAreaVisible:true}},a;function t(){a=h();m(i["large"])}function o(e){var t=h();if(t!==a){var r=i[a];var n=i[t];a=t;if(e){e.call(w,r,n)}E(n)}}function l(e,t){v("onDocumentMapVisibleChanged",arguments)}function s(e,t){v("onParameterAreaVisibleChanged",arguments)}function c(){v("onBeforeLoadReport",arguments)}function u(){v("onNavigateToPage",arguments)}function f(){v("onReportLoadComplete",arguments)}function d(){o(function(e,t){m(e)})}function p(){o(null)}function v(e,t){var r=i[a];var n=r[e];if(typeof n==="function"){n.apply(r,t)}}function g(){P.addEventListener("resize",d);r.setDocumentMapVisible(l);r.setParametersAreaVisible(s);r.beforeLoadReport(c);r.navigateToPage(u);r.reportLoadComplete(f);r.cssLoaded(p)}function h(){var e=$(P).width()/parseFloat($("body").css("font-size")),t=40.5;return n&&e<=t?"small":"large"}function m(e){e.documentMapVisible=b();e.parametersAreaVisible=T()}function E(e){b(e.documentMapVisible);T(e.parametersAreaVisible)}function b(){if(arguments.length===0){var e={};r.getDocumentMapState(e);return e.visible}r.setDocumentMapVisible({visible:Boolean(arguments[0])});return this}function T(){if(arguments.length===0){var e={};r.getParametersAreaState(e);return e.visible}r.setParametersAreaVisible({visible:Boolean(arguments[0])});return this}t();return{attach:g}}})(window.telerikReportViewer=window.telerikReportViewer||{},window,document);(function(e,_,R,I,S){"use strict";var C=e.sr;if(!C){throw"Missing telerikReportViewer.sr"}var A=e.utils;if(!A){throw"Missing telerikReportViewer.utils"}var M={};function t(e){var t,r=false,n,i,a={CONFIRM_KEY:13,CONTENT_AREA_KEY:67,DOCUMENT_MAP_AREA_KEY:68,MENU_AREA_KEY:77,PARAMETERS_AREA_KEY:80};e=_.extend({},M,e);t=e.controller;if(!t){throw"No controller (telerikReporting.ReportViewerController) has been specified."}t.reportLoadComplete(o).pageReady(function(e,t){c(t);r=true}).error(function(e,t){s();R.setTimeout(l,500)}).updateUI(function(e){if(r){u();T()}});function o(e,t){l();var r=d();if(r.length>0){r.focus()}}function l(){if(!n){f();_(I.body).off("keydown",h);_(I.body).on("keydown",h)}}function s(){var e=["div.trv-pages-area","div.trv-error-message"];var t=A.findElement(e);if(t.length===0){return}t.attr("tabIndex",0);t.focus()}function c(e){if(!e){return}l();var t=n[a.CONTENT_AREA_KEY];b(t)}function u(){var e=_(".trv-report-pager");if(e.length>0){var r=t.currentPageNumber();var n=t.pageCount();A.each(e,function(){var e=_(this);e.attr("aria-label",A.stringFormat(C.ariaLabelPageNumberSelector,[r,n]));var t=e.find("input[data-role=telerik_ReportViewer_PageNumberInput]");if(t.length>0){A.each(t,function(){var e=_(this);e.attr("aria-label",C.ariaLabelPageNumberEditor);e.attr("min","1");e.attr("max",""+n)})}})}}function f(){n={};n[a.DOCUMENT_MAP_AREA_KEY]=p();n[a.MENU_AREA_KEY]=v();n[a.CONTENT_AREA_KEY]=d();var e=g();if(e){n[a.PARAMETERS_AREA_KEY]=e;m(e)}}function d(){return A.findElement(["div[data-role=telerik_ReportViewer_PagesArea]"])}function p(){return A.findElement(["div[data-role=telerik_ReportViewer_DocumentMapArea]","div[data-role=treeview]"])}function v(){return A.findElement("ul[data-role=telerik_ReportViewer_MainMenu]")}function g(){return A.findElement(["div[data-role=telerik_ReportViewer_ParametersArea]","div.trv-parameters-area-content"])}function h(e){if(!n){return}if(!(e.altKey&&e.ctrlKey)){return}var t=n[e.which];if(!t){return}if(!E(t.parent())){return}var r="k-state-focused";if(i){i.removeClass(r)}t.addClass(r);t.focus();i=t;e.preventDefault()}function m(e){if(e.length===0){return}var r=e.parent("div[data-role=telerik_ReportViewer_ParametersArea]");if(!E(r)){return}A.each(e.children(),function(){_(this).keydown(function(e){if(e.which==a.CONFIRM_KEY){var t=r.find("button.trv-parameters-area-preview-button");t.focus();e.preventDefault()}})})}function E(e){return e&&!(e.hasClass("k-state-collapsed")||e.hasClass("trv-hidden"))}function b(e){if(!e){return}var t=e.find("div [data-reporting-action]");if(!t.length>0){return}A.each(t,function(){var t=_(this);t.keydown(function(e){if(e.which==a.CONFIRM_KEY){t.click()}})})}function T(){var e=n[a.MENU_AREA_KEY];if(!e){return}A.each(e,function(){var e=_(this);var t=e.children("li.k-item");A.each(t,function(){var e=_(this);if(!e.hasClass("trv-report-pager")){var t=e.attr("aria-label");var r=A.stringFormat(". {0}",[C.ariaLabelExpandable]),n=e.find("ul").length>0&&t.indexOf(r)<0?r:"";var i=A.stringFormat(". {0}",[C.ariaLabelSelected]),a=e.hasClass("k-state-selected")&&t.indexOf(i)<0?i:"";var o=t+n+a;e.attr("aria-label",o);if(e.hasClass("k-state-disabled")){e.attr("aria-disabled","true")}else{e.removeAttr("aria-disabled")}}})})}function P(e){a=e;n=S}function w(){return a}return{getKeyMap:w,setKeyMap:P}}e.accessibility=t})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(ve,ge,he,e,t){"use strict";var me=ve.sr;if(!me){throw"Missing telerikReportViewer.sr"}var Ee=ve.utils;if(!Ee){throw"Missing telerikReportViewer.utils"}var be={};function r(e,t,r){t=ge.extend({},be,t);var n=t.controller,i=false,a=false,o,l,s,c,u,f,d,p,v,g,h,m,E,b,T,P,w=[],_,R,I,S,C,A=ge("[data-selector='"+r.viewerSelector+"']").find(".trv-report-viewer"),M="";var N={shadedClassName:"trv-search-dialog-shaded-result",highlightedClassName:"trv-search-dialog-highlighted-result",current:null,elements:[]};if(!n){throw"No controller (telerikReporting.ReportViewerController) has been specified."}n.getSearchDialogState(function(e,t){t.visible=a}).setSearchDialogVisible(function(e,t){y(t.visible)}).setSendEmailDialogVisible(function(e,t){if(t.visible&&a){k(!a)}}).pageReady(z).scrollPageReady(z).beginLoadReport(D).viewModeChanged(D);function D(){if(R){return}k(false);I=false}function y(e){a=e;if(e){var t=r.searchMetadataOnDemand;if(t&&!I){R=true;n.reportLoadComplete(function(){if(R){k(true);R=false}});n.refreshReport(true);return}}k(e)}function k(e){a=e;if(e){I=true;O();m.open();h.value("");q(null);se(false,null)}else{ae();if(m&&m.options.visible){m.close()}}}function O(){if(!i){o=ge(e);l=o.find(".trv-search-dialog-input-box");v=o.find(".trv-search-dialog-results-label");g=o.find(".trv-search-dialog-results-area");B();ce(o);s=o.find(".trv-search-dialog-search-options").kendoMenu();u=o.find(".trv-search-dialog-stopsearch-placeholder").kendoMenu();d=o.find(".trv-search-dialog-navigational-buttons").kendoMenu();c=s.data("kendoMenu");f=u.data("kendoMenu");p=d.data("kendoMenu");c.element.on("keydown",V);f.element.on("keydown",V);p.element.on("keydown",V);h=l.kendoComboBox({dataTextField:"value",dataValueField:"value",dataSource:w,change:Y,ignoreCase:false,filtering:j,filter:"startswith",delay:1e3,open:function(e){if(_){e.preventDefault()}},select:G}).data("kendoComboBox");m=A.find(".trv-search-window").kendoWindow({title:me.searchDialogTitle,height:390,width:310,minWidth:310,minHeight:390,maxHeight:700,scrollable:false,close:function(){L();M=""},open:function(){x()},deactivate:function(){n.setSearchDialogVisible({visible:false})},activate:function(){h.input.focus()}}).data("kendoWindow");m.wrapper.addClass("trv-search");U();i=true}}function V(e){var t=ge(e.target).find(".k-state-focused");if(e.keyCode===13&&t&&t.length>0){var r=t.children("a");if(r.length>0){r.click()}}}ge(he).resize(function(){if(m&&m.options.visible){L();x()}});function L(){var e=m.element.parent(".k-window");C=e.offset()}function x(){var e=ge(he).innerWidth(),t=ge(he).innerHeight(),r=m.wrapper,n=r.outerWidth(true),i=r.outerHeight(true),a=10;if(!C){var o=A[0].getBoundingClientRect();r.css({top:o.top+a,left:o.right-n-a});m.setOptions({position:{top:o.top+a,left:o.right-n-a}})}else{var l=C.left,s=C.top,c=l+n,u=s+i;if(c>e-a){l=Math.max(a,e-n-a);r.css({left:l});m.setOptions({position:{left:l}})}if(u>t-a){s=Math.max(a,t-i-a);r.css({top:s});m.setOptions({position:{top:s}})}}}function G(e){if(!(he.event||he.event.type)){return}var t=he.event;if(t.type==="keydown"){e.preventDefault();if(t.keyCode===40){oe(1)}else if(t.keyCode===38){oe(-1)}}}var F={matchCase:"searchDialog_MatchCase",matchWholeWord:"searchDialog_MatchWholeWord",useRegex:"searchDialog_UseRegex"};function U(){b={searchDialog_MatchCase:new pe(function(){H(this)}),searchDialog_MatchWholeWord:new pe(function(){H(this)}),searchDialog_UseRegex:new pe(function(){H(this)})};var e=ve.binder;e.bind(s,{controller:n,commands:b},r);E=new pe(function(){$()});e.bind(u,{controller:n,commands:{searchDialog_StopSearch:E}},r);T={searchDialog_NavigateUp:new pe(function(){oe(-1)}),searchDialog_NavigateDown:new pe(function(){oe(1)})};e.bind(d,{controller:n,commands:T},r)}function B(){g.kendoListView({selectable:true,navigatable:true,dataSource:{},template:"<div class='trv-search-dialog-results-row'><span>#: description #</span> <span class='trv-search-dialog-results-pageSpan'>"+me.searchDialogPageText+" #:page#</span></div>",change:function(){var e=this.select().index(),t=this.dataSource.view(),r=t[e];ne(r);ie(e,t.length)}})}function $(){W(false)}function H(e){e.checked(!e.checked());K()}function W(e){E.enabled(e)}function z(e,t){if(a){ee(P)}}function j(e){e.preventDefault();if(e.filter&&e.filter.value!==M){M=e.filter.value;X(M)}}function Y(e){var t=e.sender.dataItem()?e.sender.dataItem().value:null;if(t&&M!==t){M=t;X(M)}}function K(){if(h){X(h.value())}}function X(e){Q();J(e);n.getSearchResults({searchToken:e,matchCase:b.searchDialog_MatchCase.checked(),matchWholeWord:b.searchDialog_MatchWholeWord.checked(),useRegex:b.searchDialog_UseRegex.checked()}).then(function(e){q(e,null)}).catch(function(e){if(e){q(null,e)}})}function Q(){v.text(me.searchDialogSearchInProgress);ae();P=null;W(true);se(false,null)}function q(e,t){W(false);if(t){se(true,t)}Z(e);P=e;if(e&&e.length>0){ee(e);re()}else{ie(-1,0)}}function J(t){if(!t||t===""){return}var e=w.filter(function(e){return e.value===t});if(e&&e.length>0){return}w.unshift({value:t});if(w.length>10){w.pop()}_=true;h.dataSource.data(w);h.select(function(e){return e.value===t});_=false}function Z(e){var t=g.data("kendoListView");if(!e){e=[]}t.dataSource.data(e)}function ee(e){if(!e||e.length===0){return}var t=o.parent(),r=t.find(".trv-page-container"),n=r.find("[data-search-id]");Ee.each(e,function(){var e=n.filter("[data-search-id="+this.id+"]");if(e){e.addClass(N.shadedClassName);N.elements.push(e)}});te(S);S=null}function te(e){if(e){var t=e.id;var r=ge(N.elements.filter(function(e){return e.attr("data-search-id")===t})).first();if(r){N.current=r[0];if(N.current){var n=ge("[data-search-id='"+t+"']");n.removeClass(N.shadedClassName);n.addClass(N.highlightedClassName)}}}}function re(){var e=g.data("kendoListView");var t=e.element.children().first();e.select(t)}function ne(e){if(!e){return}if(N.current){N.current.removeClass(N.highlightedClassName);N.current.addClass(N.shadedClassName)}if(e.page===n.currentPageNumber()){te(e)}else{if(n.pageMode()!==ve.PageModes.CONTINUOUS_SCROLL){ae()}else{te(e)}}S=e;n.navigateToPage(e.page,{type:"search",id:e.id})}function ie(e,t){var r=t===0?me.searchDialogNoResultsLabel:Ee.stringFormat(me.searchDialogResultsFormatLabel,[e+1,t]);v.text(r);var n=e>0;var i=e<t-1;T.searchDialog_NavigateUp.enabled(n);T.searchDialog_NavigateDown.enabled(i)}function ae(){if(N.elements&&N.elements.length>0){Ee.each(N.elements,function(){this.removeClass(N.shadedClassName)})}if(N.current){N.current.removeClass(N.highlightedClassName)}N.elements=[];N.current=null}function oe(e){var t=g.data("kendoListView");var r=t.select();if(!r){r=t.element.children().first()}else{var n=t.select().index(),i=t.dataSource.view();var a=Math.min(i.length-1,Math.max(0,n+e));if(a!==n){var o=i[a];var l=t.element.find('[data-uid="'+o.uid+'"]');if(l){t.select(l);le(l[0],t.element[0])}}}}function le(e,t){if(e.offsetTop-e.clientHeight<t.scrollTop){e.scrollIntoView()}else{var r=e.offsetTop+e.offsetHeight;var n=t.scrollTop+t.offsetHeight;if(r>n){t.scrollTop=r-t.offsetHeight}}}function se(e,t){var r=s.find("i[data-role='telerik_ReportViewer_SearchDialog_Error']");if(!r||r.length===0){console.log(t);return}var n=s.data("kendoMenu").element.find("li").last();if(e){r[0].title=t;n.show()}else{n.hide()}}function ce(e){if(!e){return}var t=e.find(".trv-search-dialog-caption-label"),r=e.find(".trv-search-dialog-search-options"),n=e.find("a[data-command='telerik_ReportViewer_searchDialog_StopSearch']"),i=e.find("a[data-command='telerik_ReportViewer_searchDialog_MatchCase']"),a=e.find("a[data-command='telerik_ReportViewer_searchDialog_MatchWholeWord']"),o=e.find("a[data-command='telerik_ReportViewer_searchDialog_UseRegex']"),l=e.find("a[data-command='telerik_ReportViewer_searchDialog_NavigateUp']"),s=e.find("a[data-command='telerik_ReportViewer_searchDialog_NavigateDown']");de(e,"aria-label");de(r,"aria-label");fe(t);ue(n);ue(i);ue(a);ue(o);ue(l);ue(s)}function ue(e){de(e,"title");de(e,"aria-label")}function fe(e){if(e){e.text(me[e.text()])}}function de(e,t){if(e){e.attr(t,me[e.attr(t)])}}function pe(e){var r=true;var n=false;var i={enabled:function(e){if(arguments.length===0){return r}var t=Boolean(e);r=t;ge(this).trigger("enabledChanged");return i},checked:function(e){if(arguments.length===0){return n}var t=Boolean(e);n=t;ge(this).trigger("checkedChanged");return i},exec:e};return i}}var n="telerik_ReportViewer_SearchDialog";ge.fn[n]=function(e,t){return Ee.each(this,function(){if(!ge.data(this,n)){ge.data(this,n,new r(this,e,t))}})}})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);(function(trv,$,window,document,undefined){"use strict";if(!$){alert("jQuery is not loaded. Make sure that jQuery is included.")}if(!trv.GlobalSettings){trv.GlobalSettings={}}var utils=trv.utils;if(!utils){throw"Missing telerikReportViewer.utils"}var sr=trv.sr;if(!sr){throw"Missing telerikReportViewer.sr"}if(!trv.ServiceClient){throw"Missing telerikReportViewer.ServiceClient"}if(!trv.ReportViewerController){throw"Missing telerikReportViewer.ReportViewerController"}if(!trv.HistoryManager){throw"Missing telerikReportViewer.HistoryManager"}var binder=trv.binder;if(!binder){throw"Missing telerikReportViewer.Binder"}if(!trv.CommandSet){throw"Missing telerikReportViewer.commandSet"}if(!trv.uiController){throw"Missing telerikReportViewer.uiController"}trv.Events={EXPORT_BEGIN:"EXPORT_BEGIN",EXPORT_END:"EXPORT_END",PRINT_BEGIN:"PRINT_BEGIN",PRINT_END:"PRINT_END",RENDERING_BEGIN:"RENDERING_BEGIN",RENDERING_END:"RENDERING_END",PAGE_READY:"PAGE_READY",ERROR:"ERROR",UPDATE_UI:"UPDATE_UI",INTERACTIVE_ACTION_EXECUTING:"INTERACTIVE_ACTION_EXECUTING",INTERACTIVE_ACTION_ENTER:"INTERACTIVE_ACTION_ENTER",INTERACTIVE_ACTION_LEAVE:"INTERACTIVE_ACTION_LEAVE",VIEWER_TOOLTIP_OPENING:"VIEWER_TOOLTIP_OPENING",SEND_EMAIL_BEGIN:"SEND_EMAIL_BEGIN",SEND_EMAIL_END:"SEND_EMAIL_END"};var templateCache=function(){var n={};return{load:function(e,o,t){var r=n[e];if(!r){n[e]=r=t.get(e).then(function(e){var n={};var r=[];var i=[];var t=utils.rtrim(o,"\\/")+"/";e=utils.replaceAll(e,"{service}/",t);e=utils.replaceAll(e,"{service}",t);var a=$("<div></div>").html(e);utils.each(a.find("template"),function(e,t){var r=$(t);n[r.attr("id")]=utils.trim(r.html(),"\n \t")});utils.each(a.find("link"),function(e,t){r.push(utils.trim(t.outerHTML,"\n \t"))});r=utils.filterUniqueLastOccurance(r);utils.each(a.find("script"),function(e,t){i.push(utils.trim(t.innerHTML,"\n \t"))});return{templates:n,styleSheets:r,scripts:i}})}return r}}}();function MemStorage(){var r={};return{getItem:function(e){return r[e]},setItem:function(e,t){r[e]=t},removeItem:function(e){delete r[e]}}}function ReportViewerSettings(t,a,r){var o={};function l(e){var t=a.getItem(c(e));return t!==null&&t!==undefined?t:r[e]}function e(e,t){var r="state";var n=l(r);var i=typeof n==="string"?JSON.parse(n):{};if(t.length){if(i){var a=t[0];if(a===undefined){delete i[e]}else{i[e]=a}}s(r,JSON.stringify(i));return o}else{return i[e]}}function s(e,t){var r=c(e);a.setItem(r,t);if(a instanceof window.Storage){var n=a.getItem(r);var i=document.createEvent("StorageEvent");i.initStorageEvent("telerikReportingStorage",false,false,r,n,t,null,a);window.dispatchEvent(i)}}function c(e){return t+"_"+e}function n(e,t){if(t.length){s(e,t[0]);return o}else{return l(e)}}function i(e,t){if(t.length){s(e,t[0]);return o}else{return parseFloat(l(e))}}function u(e,t){if(t.length){s(e,JSON.stringify(t[0]));return o}else{var r=l(e);return typeof r==="string"?JSON.parse(r):null}}utils.extend(o,{viewMode:function(){return e("viewMode",arguments)},pageMode:function(){return e("pageMode",arguments)},printMode:function(){return e("printMode",arguments)},scale:function(){return e("scale",arguments)},scaleMode:function(){return e("scaleMode",arguments)},documentMapVisible:function(){return e("documentMapVisible",arguments)},parametersAreaVisible:function(){return e("parametersAreaVisible",arguments)},history:function(){return u("history",arguments)},clientId:function(){return n("clientId",arguments)},reportSource:function(){return e("reportSource",arguments)},pageNumber:function(){return e("pageNumber",arguments)},enableAccessibility:function(){return n("enableAccessibility",arguments)},accessibilityKeyMap:function(){return e("accessibilityKeyMap",arguments)},searchMetadataOnDemand:function(){return n("searchMetadataOnDemand",arguments)}});return o}function getDefaultOptions(e,t){return{id:null,serviceUrl:null,templateUrl:utils.rtrim(e,"\\/")+"/resources/templates/telerikReportViewerTemplate-"+t+".html/",reportSource:null,reportServer:null,authenticationToken:null,sendEmail:null,scale:1,scaleMode:trv.ScaleModes.FIT_PAGE,viewMode:trv.ViewModes.INTERACTIVE,pageMode:trv.PageModes.CONTINUOUS_SCROLL,parametersAreaPosition:trv.ParametersAreaPositions.RIGHT,documentMapAreaPosition:trv.DocumentMapAreaPositions.LEFT,parameters:{editors:{multiSelect:trv.ParameterEditorTypes.LIST_VIEW,singleSelect:trv.ParameterEditorTypes.LIST_VIEW}},persistSession:false,parameterEditors:[],disabledButtonClass:null,checkedButtonClass:null,parametersAreaVisible:true,documentMapVisible:true,enableAccessibility:false,searchMetadataOnDemand:false,initialPageAreaImageUrl:null}}function ReportViewer(dom,options){var svcApiUrl=options.serviceUrl,reportServerUrlSVCApiUrl="";if(options.reportServer){reportServerUrlSVCApiUrl=utils.rtrim(options.reportServer.url,"\\/");svcApiUrl=reportServerUrlSVCApiUrl+"/api/reports"}var $placeholder=$(dom),templates={},scripts={},persistanceKey=options.id||"#"+$placeholder.attr("id"),accessibility,settings={},client={},controller={},history={},commands={},viewer={},serviceClientOptions={},reportServerUrl="";options.viewerSelector="reportViewer-"+utils.generateGuidString();$placeholder.attr("data-selector",options.viewerSelector);if(!validateOptions(options)){return}var version="14.2.20.1021";options=utils.extend({},getDefaultOptions(svcApiUrl,version),options);settings=new ReportViewerSettings(persistanceKey,options.persistSession?window.sessionStorage:new MemStorage,{scale:options.scale,scaleMode:options.scaleMode,printMode:options.printMode?options.printMode:options.directPrint,enableAccessibility:options.enableAccessibility,searchMetadataOnDemand:options.searchMetadataOnDemand,sendEmail:options.sendEmail,parametersAreaPosition:options.parametersAreaPosition,documentMapAreaPosition:options.documentMapAreaPosition});if(options.reportServer){reportServerUrl=utils.rtrim(options.reportServer.url,"\\/");serviceClientOptions.serviceUrl=reportServerUrl+"/api/reports";serviceClientOptions.loginInfo={url:reportServerUrl+"/Token",username:options.reportServer.username,password:options.reportServer.password}}else{serviceClientOptions.serviceUrl=options.serviceUrl}client=new trv.ServiceClient(serviceClientOptions);controller=options.controller;if(!controller){controller=new trv.ReportViewerController({serviceClient:client,settings:settings})}else{controller.updateSettings(settings)}history=new trv.HistoryManager({controller:controller,settings:settings});commands=new trv.CommandSet({controller:controller,history:history});new trv.uiController({controller:controller,history:history,commands:commands});viewer={stringResources:sr,refreshReport:function(e){if(arguments.length===0){e=true}controller.refreshReport(e);return viewer},reportSource:function(e){if(e||e===null){controller.reportSource(e);controller.refreshReport(false);return viewer}return controller.reportSource()},clearReportSource:function(){controller.clearReportSource();return viewer},viewMode:function(e){if(e){controller.viewMode(e);return viewer}return controller.viewMode()},pageMode:function(e){if(e){controller.pageMode(e);return viewer}return controller.pageMode()},printMode:function(e){if(e){controller.printMode(e);return viewer}return controller.printMode()},scale:function(e){if(e){controller.scale(e);return viewer}e={};controller.getScale(e);return e},currentPage:function(){return controller.currentPageNumber()},pageCount:function(){return controller.pageCount()},parametersAreaVisible:function(e){controller.setParametersAreaVisible({visible:e})},authenticationToken:function(e){if(e){controller.setAuthenticationToken(e)}return viewer},bind:function(e,t){eventBinder(e,t,true)},unbind:function(e,t){eventBinder(e,t,false)},accessibilityKeyMap:function(e){if(accessibility){if(e){accessibility.setKeyMap(e);return viewer}return accessibility.getKeyMap()}return undefined},commands:commands};function validateOptions(e){if(!e){$placeholder.html("The report viewer configuration options are not initialized.");return false}if(e.reportServer){if(!e.reportServer.url){$placeholder.html("The report server URL is not specified.");return false}}else{if(!e.serviceUrl){$placeholder.html("The serviceUrl is not specified.");return false}}return true}function eventBinder(e,t,r){if(typeof t==="function"){if(r){$(viewer).on(e,{sender:viewer},t)}else{$(viewer).off(e,t)}}else if(!t&&!r){$(viewer).off(e)}}function attachEvents(){var e={EXPORT_BEGIN:controller.Events.EXPORT_STARTED,EXPORT_END:controller.Events.EXPORT_DOCUMENT_READY,PRINT_BEGIN:controller.Events.PRINT_STARTED,PRINT_END:controller.Events.PRINT_DOCUMENT_READY,RENDERING_BEGIN:controller.Events.BEFORE_LOAD_REPORT,RENDERING_END:controller.Events.REPORT_LOAD_COMPLETE,PAGE_READY:controller.Events.PAGE_READY,ERROR:controller.Events.ERROR,UPDATE_UI:controller.Events.UPDATE_UI,INTERACTIVE_ACTION_EXECUTING:controller.Events.INTERACTIVE_ACTION_EXECUTING,INTERACTIVE_ACTION_ENTER:controller.Events.INTERACTIVE_ACTION_ENTER,INTERACTIVE_ACTION_LEAVE:controller.Events.INTERACTIVE_ACTION_LEAVE,VIEWER_TOOLTIP_OPENING:controller.Events.TOOLTIP_OPENING,SEND_EMAIL_BEGIN:controller.Events.SEND_EMAIL_STARTED,SEND_EMAIL_END:controller.Events.SEND_EMAIL_READY},t=$(viewer);for(var r in e){var n=e[r];controller.on(n,function(r,n){return function(e,t){r.trigger({type:n,data:e.data},t)}}(t,r))}}function attachEventHandlers(){eventBinder(trv.Events.EXPORT_BEGIN,options.exportBegin,true);eventBinder(trv.Events.EXPORT_END,options.exportEnd,true);eventBinder(trv.Events.PRINT_BEGIN,options.printBegin,true);eventBinder(trv.Events.PRINT_END,options.printEnd,true);eventBinder(trv.Events.RENDERING_BEGIN,options.renderingBegin,true);eventBinder(trv.Events.RENDERING_END,options.renderingEnd,true);eventBinder(trv.Events.PAGE_READY,options.pageReady,true);eventBinder(trv.Events.ERROR,options.error,true);eventBinder(trv.Events.UPDATE_UI,options.updateUi,true);eventBinder(trv.Events.INTERACTIVE_ACTION_EXECUTING,options.interactiveActionExecuting,true);eventBinder(trv.Events.INTERACTIVE_ACTION_ENTER,options.interactiveActionEnter,true);eventBinder(trv.Events.INTERACTIVE_ACTION_LEAVE,options.interactiveActionLeave,true);eventBinder(trv.Events.VIEWER_TOOLTIP_OPENING,options.viewerToolTipOpening,true);eventBinder(trv.Events.SEND_EMAIL_BEGIN,options.sendEmailBegin,true);eventBinder(trv.Events.SEND_EMAIL_END,options.sendEmailEnd,true)}function init(){$placeholder.html(templates["trv-report-viewer"]);binder.bind($placeholder,{controller:controller,commands:commands,templates:templates},options);new trv.PerspectiveManager(dom,controller).attach();initSplitter();attachEvents();attachEventHandlers();initFromStorage();initAccessibility(options)}function initSplitter(){var e={max:"500px",min:"50px",size:"210px",collapsible:true},t=$placeholder.find(".trv-parameters-area"),r=[{}],n={max:"500px",min:"50px",size:"210px",collapsible:true,collapsed:true},i=$placeholder.find(".trv-document-map"),a=[{}],o="horizontal";if(options.documentMapAreaPosition===trv.DocumentMapAreaPositions.RIGHT){i.insertAfter($placeholder.find(".trv-pages-area"));a.push(n)}else{a.unshift(n)}if(options.parametersAreaPosition===trv.ParametersAreaPositions.TOP||options.parametersAreaPosition===trv.ParametersAreaPositions.BOTTOM){o="vertical";t.addClass("-vertical");e.size="130px"}if(options.parametersAreaPosition===trv.ParametersAreaPositions.LEFT||options.parametersAreaPosition===trv.ParametersAreaPositions.TOP){t.insertBefore($placeholder.find(".trv-document-map-splitter"));r.unshift(e)}else{r.push(e)}var l=$placeholder.find(".trv-document-map-splitter").kendoSplitter({panes:a,expand:function(e){setSplitterPaneVisibility(e.pane,true)},collapse:function(e){setSplitterPaneVisibility(e.pane,false)}}).data("kendoSplitter");var s=$placeholder.find(".trv-parameters-splitter").kendoSplitter({panes:r,orientation:o,expand:function(e){setSplitterPaneVisibility(e.pane,true)},collapse:function(e){setSplitterPaneVisibility(e.pane,false)}}).data("kendoSplitter");trv[options.viewerSelector+"-parameters-splitter"]=s;trv[options.viewerSelector+"-document-map-splitter"]=l}function setSplitterPaneVisibility(e,t){var r=$(e).attr("data-id");switch(r){case"trv-document-map":controller.setDocumentMapVisible({visible:t});break;case"trv-parameters-area":controller.setParametersAreaVisible({visible:t});break}}function initFromStorage(){var e=settings.viewMode();var t=settings.pageMode();var r=settings.printMode();var n=settings.scale();var i=settings.scaleMode();var a=settings.documentMapVisible();var o=settings.parametersAreaVisible();var l=settings.accessibilityKeyMap();controller.viewMode(e?e:options.viewMode);controller.pageMode(t?t:options.pageMode);controller.printMode(r?r:options.printMode);controller.scale({scale:n?n:options.scale,scaleMode:i?i:options.scaleMode});controller.setDocumentMapVisible({visible:a?a:options.documentMapVisible});controller.setParametersAreaVisible({visible:o?o:options.parametersAreaVisible});controller.printModeChanged(function(){settings.printMode(controller.printMode())});controller.viewModeChanged(function(){settings.viewMode(controller.viewMode())});controller.pageModeChanged(function(){settings.pageMode(controller.pageMode())});controller.scale(function(){var e={};controller.getScale(e);settings.scale(e.scale);settings.scaleMode(e.scaleMode)});controller.setSideMenuVisible(function(e,t){window.setTimeout(function(){(t.visible?$.fn.addClass:$.fn.removeClass).call($placeholder,"trv-side-menu-visible")},1)});controller.setDocumentMapVisible(function(){var e={};controller.getDocumentMapState(e);settings.documentMapVisible(e.visible)});controller.setParametersAreaVisible(function(){var e={};controller.getParametersAreaState(e);settings.parametersAreaVisible(e.visible)})}function initAccessibility(e){if(e.enableAccessibility){accessibility=new trv.accessibility({controller:controller,templates:templates});var t=e.accessibilityKeyMap;if(t){accessibility.setKeyMap(t)}settings.contentTabIndex=getTemplateContentTabIndex()}}function getTemplateContentTabIndex(){var e="div.trv-pages-area";try{var t=$placeholder.find(e);if(t.length===0){throw"Selector "+e+" did not return a result."}return parseInt(t.attr("tabindex"))}catch(e){if(console)console.log(e);return 0}}function start(){var pendingRefresh=false;init();controller.reportLoadComplete(function(){if(options.documentMapVisible===false){controller.setDocumentMapVisible({visible:false})}});var rs=settings.reportSource();if(rs!==undefined){controller.reportSource(rs);var pageNumber=settings.pageNumber();if(pageNumber!==undefined){controller.navigateToPage(pageNumber)}pendingRefresh=true}else{if(options.viewMode){controller.viewMode(options.viewMode)}if(options.pageMode){controller.pageMode(options.pageMode)}if(options.reportSource){controller.reportSource(options.reportSource);pendingRefresh=true}}for(var i=0;i<scripts.length;i++){try{eval(scripts[i])}catch(e){if(console)console.log(e)}}if(typeof options.ready==="function"){options.ready.call(viewer)}if(pendingRefresh){controller.refreshReport(false)}}function loadStyleSheets(e){if(!e)return Promise.resolve();var i=$("head");var t=i.find("link").map(function(e,t){return t.outerHTML}).toArray();var r=[];utils.each(e,function(e,n){if(-1===t.indexOf(n)){r.push(new Promise(function(e,t){var r=$(n);r.on("load",e);r.on("onerror",function(){utils.logError("error loading stylesheet "+n);e()});i.append(r)}))}});return Promise.all(r).then(controller.cssLoaded)}function browserSupportsAllFeatures(){return window.Promise}function ensureKendo(e){if(window.kendo){return Promise.resolve()}else{var t=utils.rtrim(svcApiUrl,"\\/")+"/resources/js/telerikReportViewer.kendo-"+e+".min.js/";return utils.loadScript(t).catch(function(e){utils.logError("Kendo could not be loaded automatically. Make sure 'options.serviceUrl' / 'options.reportServer.url' is correct and accessible. The error is: "+e.error)})}}function main(e){ensureKendo(e).then(function(){viewer.authenticationToken(options.authenticationToken);controller.getDocumentFormats().catch(function(){$placeholder.html(utils.stringFormat(sr.errorServiceUrl,[utils.escapeHtml(svcApiUrl)]));return Promise.reject()}).then(function(){templateCache.load(options.templateUrl,svcApiUrl,client).catch(function(){$placeholder.html(utils.stringFormat(sr.errorLoadingTemplates,[utils.escapeHtml(options.templateUrl)]));return Promise.reject()}).then(function(e){templates=e.templates;scripts=e.scripts;return loadStyleSheets(e.styleSheets)}).then(start)})})}if(browserSupportsAllFeatures()){main(version)}else{utils.loadScriptWithCallback("https://cdn.polyfill.io/v2/polyfill.min.js?features=Promise",main,version)}return viewer}var pluginName="telerik_ReportViewer";jQuery.fn[pluginName]=function(e){if(this.selector&&!e.selector){e.selector=this.selector}return utils.each(this,function(){if(!$.data(this,pluginName)){$.data(this,pluginName,new ReportViewer(this,e))}})};trv.ReportViewer=ReportViewer})(window.telerikReportViewer=window.telerikReportViewer||{},jQuery,window,document);
/* DO NOT MODIFY OR DELETE THIS LINE! UPGRADE WIZARD CHECKSUM 7010107DA4B90BD430C5075C1460F47A */