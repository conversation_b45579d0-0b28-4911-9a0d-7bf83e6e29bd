﻿// remove the last comma(',') and any leading whitespace of the string example: entity1, entity2, entity3,
// will be entity1, entity2, entity3
Object.defineProperty(String.prototype, 'removeLastComma', {
    value: function removeLastComma() {
        return this.replace(/,\s*$/, "")
    }
})

// remove the unwanted white space from first and last
Object.defineProperty(String.prototype, 'stripWhiteSpace', {
    value: function stripWhiteSpace() {
        return this.replace(/^\s*\s*$/, '');
    }
})

// remove the unwanted white space from first and last
Object.defineProperty(String.prototype, 'toCamelCase', {
    value: function toCamelCase() {
        return this.replace(/(?:^\w|[A-Z]|\b\w)/g, function (word, index) {
            return index == 0 ? word.toLowerCase() : word.toUpperCase();
        }).replace(/\s+/g, '');
    }
})


/*
 * Checks if the object isEmpty returns true if it is empty else false
 **/
Object.defineProperty(Object.prototype, 'isObjectEmpty', {
    value: function isObjectEmpty() {
        for (var key in this) {
            if (this.hasOwnProperty(key))
                return false;
        }
        return true;
    }
})


/*
 * Checks if the string is null or empty
 **/
Object.defineProperty(String.prototype, 'isNullOrEmpty', {
    value: function isNullOrEmpty() {
        return (this === null || this === undefined || this.trim() === "")
    }
})

/*
 * remove all the space characters from the string
 **/
Object.defineProperty(String.prototype, 'removeSpaces', {
    value: function removeSpaces() {
        return this.replace(/\s/g, '');
    }
})

/** Show full validation type by full Convert the 'Char' to string   */
Object.defineProperty(String.prototype, "convertToFullValidationRecordType", {
    value: function convertToFullValidationRecordType() {
        switch (this.toString()) {
            case 'A':
                return "Added";
            case 'M':
                return "Modified";
            case 'D':
                return "Deleted";
            case 'W':
                return "AddedThroughWorkflow";

        }
    }
});