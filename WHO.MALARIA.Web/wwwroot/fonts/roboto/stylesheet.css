@font-face {
    font-family: 'robotoblack';
    src: url('Roboto-Black-webfont.eot');
    src: url('Roboto-Black-webfont.eot?#iefix') format('embedded-opentype'),
         url('Roboto-Black-webfont.woff') format('woff');        
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'robotobold';
    src: url('Roboto-Bold-webfont.eot');
    src: url('Roboto-Bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('Roboto-Bold-webfont.woff') format('woff');        
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'roboto_condensedbold';
    src: url('RobotoCondensed-Bold-webfont.eot');
    src: url('RobotoCondensed-Bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('RobotoCondensed-Bold-webfont.woff') format('woff');        
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'roboto_condensedregular';
    src: url('RobotoCondensed-Regular-webfont.eot');
    src: url('RobotoCondensed-Regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('RobotoCondensed-Regular-webfont.woff') format('woff');        
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'robotolight';
    src: url('Roboto-Light-webfont.eot');
    src: url('Roboto-Light-webfont.eot?#iefix') format('embedded-opentype'),
         url('Roboto-Light-webfont.woff') format('woff');       
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'roboto_condensedlight';
    src: url('RobotoCondensed-Light-webfont.eot');
    src: url('RobotoCondensed-Light-webfont.eot?#iefix') format('embedded-opentype'),
         url('RobotoCondensed-Light-webfont.woff') format('woff');         
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'robotomedium';
    src: url('Roboto-Medium-webfont.eot');
    src: url('Roboto-Medium-webfont.eot?#iefix') format('embedded-opentype'),
         url('Roboto-Medium-webfont.woff') format('woff');         
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'robotoregular';
    src: url('Roboto-Regular-webfont.eot');
    src: url('Roboto-Regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('Roboto-Regular-webfont.woff') format('woff');        
    font-weight: normal;
    font-style: normal;

}



