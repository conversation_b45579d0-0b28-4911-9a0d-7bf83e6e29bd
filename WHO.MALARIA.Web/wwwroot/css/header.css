﻿/*// Main Level Dropdown Navigation CSS*/

.bd-navbar-nav .nav-item {
    padding-left: 0px;
    padding-right: 0px;
}

.navbar-nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    margin: 0px;
}

.navbar-nav .nav-item.dropdown {
    position: relative;
}

.sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: block;
    float: left;
    min-width: 10rem;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
}

.sub-menu.hidden {
    display: none;
}

.sub-menu {
    display: table;
    min-width: auto;
    margin: 0px;
    padding: 15px 10px;
}

.sub-menu .sub-menu-column {
    padding-left: 3px;
    min-width: 185px;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 7px 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: normal;
    background-color: transparent;
    border: 0;
    font-size: 14px;
}

.dropdown-item:focus, .dropdown-item:hover, .dropdown-item:active {
    background-color: #D4DBE9;
}

.sub-menu .dropdown-item.active, .sub-menu .dropdown-item:active {
    background-color: #f8f9fa;
    color: #212529;
}
/*// Main Level Dropdown Navigation CSS*/
/*// Main Level Dropdown Column CSS*/
/* one item */
div.column:first-child:nth-last-child(1) {
    width: 100%;
}

/* two items */
div.column:first-child:nth-last-child(2),
div.column:first-child:nth-last-child(2) ~ div.column {
    width: 50%;
}

/* three items */
div.column:first-child:nth-last-child(3),
div.column:first-child:nth-last-child(3) ~ div.column {
    width: 33.3333%;
}

/* four items */
div.column:first-child:nth-last-child(4),
div.column:first-child:nth-last-child(4) ~ div.column {
    width: 25%;
}

/*// Main Level Dropdown Column CSS*/

.navbar-toggler .icon-bar {
    display: block;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: #fff;
    margin-top: 6px;
}

/*Revision CSS for Navigation*/
.main-nav .bg-nav-primary:first-child {
    background: var(--body-background-color);
}
.main-nav .bg-nav-primary {   
    background: var(--bg-color);
    border-bottom: 1px solid var(--header-bg-border-color);  
    padding: 0px 1rem;
    box-shadow: none;
}

.main-nav .navbar-brand {
    display: block;
    padding: 5px 0px;
}

.main-nav .navbar-brand a {
    display: inline-block;
    padding: 0px 10px;
    position: relative;
    top: 5px;
}

.main-nav .navbar-brand a:first-child {
    position: relative;   
}

.main-nav .navbar-brand a:first-child img {
    width: 45px;
    position: relative;
    top: -15px;
}

.main-nav .navbar-brand a + a {
    border-left: 1px solid #CAD7DE;
}

.main-nav .navbar-brand .app-logo {
    color: var(--header-logo-color);
    font-family: 'robotobold';
    font-size: 28px;
    font-weight: bold;
    line-height: 32px;
    padding-top: 5px;
}

.main-nav .navbar-brand .app-logo-text {
    color: var(--header-logo-color);
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 10px;
    line-height: 14px;
    display: block;
}

/*// Main Level Dropdown Column CSS*/
.nav-item {
    position: relative;
}

.main-menu-navbar .nav-item.RegulatoryProcesses .sub-menu {
    min-width: auto;
}

.main-menu-navbar .nav-item.RegulatoryProcesses .sub-menu {
    display: block;
}

.main-menu-navbar .nav-item.InventoryResources .sub-menu .column {
    min-width: 220px;
}

.main-menu-navbar .nav-item.InventoryResources .sub-menu {
    left: auto;
    right: 0px;
    display: block;
}

.main-menu-navbar .nav-item.ReportsStatistics .sub-menu {
    left: auto;
    right: 0px;
    display: block;
}

.main-menu-navbar .nav-item.ReportsStatistics .sub-menu .column {
    width: 100%;
    float: none;
    display: table;
    min-width: 220px;
}

.main-menu-navbar .nav-item.Settings .sub-menu {
    min-width: auto;
    left: auto;
    right: 0px;
}

.main-menu-navbar .nav-item.Settings .sub-menu .column {
    width: 100%;
    float: none;
    display: table;
    min-width: 220px;
}

.column-1, .column-2, .column-3, .column-4 {
    width: 25% !important;
    vertical-align: top;
    float: left;
}

.main-menu-navbar .nav-item.Administration .column:first-child .sticky-column {
    top: 1px;
    position: sticky;
    overflow-y: hidden;
}

.main-menu-navbar .nav-item.Administration .column:nth-child(2) .sticky-column {
    top: 1px;
    position: sticky;
    overflow-y: hidden;
}

.main-menu-navbar .nav-item:nth-child(5) .column .sticky-column {
    top: 1px;
    position: sticky;
    overflow-y: hidden;
}

.main-menu-navbar .nav-item.Administration .sticky-column .dropdown-item-heading {
    top: 0px;
    position: sticky;
    overflow-y: hidden;
    background-color: #F7F9FA;
}

.main-menu-navbar.navbar-nav .nav-link {
    padding: 25px 10px;
    color: var(--font-color);
    font-size: 14px;
    font-family: 'robotoregular';
    line-height: 18px;
    text-align: center;
    text-transform: capitalize;
    text-decoration: none;
    cursor: pointer;
    border-bottom: 3px solid transparent;
}

.main-menu-navbar.navbar-nav .nav-link:hover, .main-menu-navbar.navbar-nav .nav-item.active .nav-link {
    background-color: transparent;
    color: var(--primary-color);
    border-bottom: 3px solid #346BCB;
}

.main-menu-navbar.navbar-nav .nav-link:hover, .main-menu-navbar.navbar-nav .nav-item .nav-link > div {
    display: inline-block;
}

.main-menu-navbar li > a:not(:only-child):after {
    color: var(--font-color);
    font-family: "FontAwesome";
    content: '\f107';
    display: inline-block;
    position: relative;
    left: 5px;
    font-weight: 900;
}

.main-menu-navbar li:hover > a:not(:only-child):after {
    color: var(--menu-active-color);
}

.main-menu-navbar li > a:after, .main-menu-navbar li.active > a:after, .main-menu-navbar li:hover > a {
    color: var(--menu-active-color);
}

.main-menu-navbar li:last-child a span {
    font-size: 18px;
    line-height: 16px;
}
.dropdown-section .nav-link {
    color: var(--font-color);
    padding: 0px 0px !important;
    margin-top: 0px;
    font-size: 16px;
    line-height: 16px;
    border-bottom: 3px solid transparent;
    cursor: pointer;
}

.top-navbar-nav .dropdown-section .top-nav-link span:after {
    font-family: "FontAwesome";
    content: '\f107';
    display: inline-block;
    position: relative;
    left: 5px;
    font-weight: 900;
}

.top-navbar-nav .dropdown-section .nav-link .top-nav-link:hover span:after {
    color: var(--primary-color) !important;
}

.main-menu-navbar .sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: block;
    float: left;
    min-width: 10rem;
    padding: 10px 10px;
    margin: .125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: var(--bg-color);
    background-clip: padding-box;
    border: 1px solid #C7D7DF;
    margin-top: 1px;
}

.main-menu-navbar .dropdown-item-heading {
    font-size: 16px !important;
    font-weight: bold !important;
    color: var(--menu-item-heading-color) !important;
}

.main-menu-navbar .dropdown-item {
    display: block;
    width: 100%;
    padding: 7px 1.5rem;
    clear: both;
    font-weight: 400;
    color: var(--font-color);
    text-align: inherit;
    white-space: normal;
    background-color: transparent;
    border: 0;
    font-size: 14px;
}

.main-menu-navbar .dropdown-item:focus, .main-menu-navbar .dropdown-item:hover, .main-menu-navbar .dropdown-item:active {  
    background-color: var(--list-hover-color);
    color: #545B6A;
    border-radius: 4px;
}

.dropdown-section .dropdown-menu-section {
    background-color: var(--bg-color);
    background-clip: padding-box;
    border: 1px solid #C7D7DF;
    box-shadow: none;
    margin-top: 1px;
    padding: 10px 20px;
    z-index: 1001;
    border-radius: 0px;
}

li.dropdown-section {
    list-style-image: url(data:0);
}

.dropdown-menu-section {
    list-style-image: url(data:0);
}

.dropdown-menu-section li:first-child {
    text-transform: capitalize;
    list-style-image: url(data:0);
    color: var(--font-color);
}

.dropdown-menu-section li {
    list-style-image: url(data:0);
}

.dropdown-menu-section .deployment-mode {
    text-align: center;
}

.dropdown-menu-section li a {
    color: var(--font-color);
    text-transform: initial;
    font-size: 14px;
    display: block;
    padding: 10px 10px;
    white-space: nowrap;
}

.dropdown-menu-section li a:hover, .dropdown-menu-section li a:active {
    background-color: var(--list-hover-color);
    color: var(--primary-color);
    border-radius: 4px;
}

.main-nav .navbar-toggler {
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.main-nav .navbar-toggler #navbar-close {
    padding: 5px 7px;
}

.main-nav .navbar-toggler .icon-bar {
    display: block;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--primary-color);
    margin-top: 6px;
}

.main-nav .dropdown-menu {
    border-radius: 0px;
}

.top-navbar-nav .nav-item .nav-link {
    padding: 0px;
}

#main-menu {
    width: 100%;
    justify-content: space-evenly;
}

#main-menu .nav-item {
    position: static;
}

.top-navbar-nav .nav-item .top-nav-link {
    padding: 26px 10px;
    font-size: 14px;
    line-height: 16px;   
}
.top-navbar-nav .nav-link {
    color: var(--font-color) !important;
}

.dropdown-section .nav-link:hover, .dropdown-section .nav-link.show {
    color: var(--menu-active-color) !important;
    border-bottom: 3px solid var(--menu-active-color);
}

@media (max-width: 575.98px) {
    .main-nav .navbar-brand a:first-child {
        display: none;
        margin-left: 0%;
    }

    .main-nav .navbar-brand a + a {
        border: none;
        padding: 0px;
        margin-left: 0%;
    }

    .main-nav .navbar-brand a:first-child img {
        width: 50px;
        left: -30%;
    }
}

@media (max-width: 768px) {   
    .bd-navbar-nav.navbar-nav .nav-link {
        text-align: left;
    }

    .navbar-nav .sub-menu {
        width: 100%;
        position: relative;
        min-width: auto;
    }

    .navbar-nav .sub-menu .column {
        width: 100% !important;
    }

    .main-level .navbar-collapse {
        border-top: 1px solid #CAD7DE;
        margin: 5px 0px;
    }
}

@media (max-width: 992px) {
    .main-nav .bg-nav-primary .container {
        max-width: 100%;
    }
}

@media (max-width: 1199.98px) {
    .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }

    .sub-menu {
        display: block;
        width: 100%;
        position: relative;
    }

    .sub-menu .column {
        display: block;
        width: 100% !important;
    }

    .bd-navbar-nav.navbar-nav .nav-link {
        padding: 22px 20px;
        text-align: left;
    }

    .navbar-collapse .dropdown-section {
        width: 100%;
    }

    .dropdown-section .nav-link .app-login {
        padding: 17px 10px;
    }

    .dropdown-section .nav-link:hover, .dropdown-section .nav-link.show {
        color: none;
        border-bottom: none;
    }

    .dropdown-section .nav-link:hover .app-login, .dropdown-section .nav-link.show .app-login {
        color: var(--primary-color) !important;
        border-bottom: 2px solid var(--primary-color);
    }

    .main-menu-navbar li > a:not(:only-child):after {
        content: '\f107';
    }

    .main-menu-navbar .sub-menu {
        margin-top: 0px;
    }

    .main-menu-navbar.navbar-nav .nav-link {
        padding: 15px 15px !important;
        border-top: 1px solid #CAD7DE;
    }

    .main-menu-navbar.navbar-nav .nav-item:first-child .nav-link {
        border-top: 0px;
    }

    .dropdown-menu-section .deployment-mode {
        padding: 10px 10px;
        text-align: left;
    }

    .main-menu-navbar.navbar-nav .nav-link:hover, .main-menu-navbar.navbar-nav .nav-item.active .nav-link {
        border-bottom: 2px solid var(--primary-color);
        display: block;
    }

    #main-menu .nav-item {
        position: relative;
    }

    .main-menu-navbar.navbar-nav .nav-link {
        border-bottom: 2px solid transparent;
    }
}

@media (min-width: 1201px) and (max-width: 1300px) {
    .main-menu-navbar .sub-menu {
        min-width: 10rem;
        padding: 0.733vw 0.733vw;
        margin: .125rem 0 0;
        font-size: 1.173vw;
        border: 1px solid #C7D7DF;
        margin-top: 1px;
        position: absolute;
        left: 2%;
        right: 2%;
    }
}

/*Target 4k monitors and bigger screens*/
/*Media query for larger screens*/
@media only screen and (min-width: 1281px) {
    .main-nav .navbar-brand a:first-child img {
        width: 3.023vw;
    }

    .main-nav .navbar-brand a {
        padding: 0px 0.733vw;
    }

    .main-nav .navbar-brand .app-logo {
        font-size: 2.048vw;
        line-height: 2.346vw;
        padding-top: 0px;
    }

    .main-nav .navbar-brand .app-logo-text {
        font-size: 0.733vw;
        line-height: 1.026vw;
    }

    .main-menu-navbar.navbar-nav .nav-link {
        padding: 1.828vw 0.733vw;
        font-size: 1.026vw;
        line-height: 1.319vw;
    }

    .main-menu-navbar li:last-child a span {
        font-size: 1.319vw;
        line-height: 1.173vw;
    }

    .top-navbar-nav .nav-item .top-nav-link {
        padding: 2vw 0.733vw;
        font-size: 1.019vw;
        line-height: 1.173vw;
    }

    .top-navbar-nav .nav-item:first-child .top-nav-link:after {
        content: "|";
        position: absolute;
        margin-left: 8px;
    }

    .dropdown-menu-section li a {
        font-size: 1.026vw;
        /*padding: 0.733vw 1.466vw;*/
    }

    .dropdown-menu-section li:first-child {
        line-height: 1.173vw;
        color: var(--font-color);
    }

    .dropdown-menu-section .deployment-mode {
        padding: 0vw 1.10vw 0.733vw;
        line-height: 1.173vw;
        border-bottom: 1px solid #C7D7DF;
        color: #545B6A;
        white-space: nowrap
    }

    .main-nav .dropdown-menu {
        font-size: 1.173vw;
    }

    .main-menu-navbar li > a:not(:only-child):after {
        left: 0.365vw;
        font-size: 1.319vw;
        line-height: 1vw;
    }

    .main-menu-navbar.navbar-nav .nav-link:hover, .main-menu-navbar.navbar-nav .nav-item.active .nav-link {       
        border-bottom: 0.219vw solid var(--primary-color);
        color: var(--menu-active-color);
    }

    .main-menu-navbar .sub-menu {
        min-width: 10rem;
        padding: 0.733vw 0.733vw;
        margin: .125rem 0 0;
        font-size: 1.173vw;
        border: 1px solid #C7D7DF;
        margin-top: 1px;
        position: absolute;
        left: 5%;
        right: 5%;
    }

    .main-menu-navbar .dropdown-item {
        padding: 0.512vw 1.759vw;
        font-size: 1.026vw;
    }

    .main-menu-navbar .dropdown-item-heading {
        font-size: 1.173vw !important;
    }

    .dropdown-section .dropdown-menu-section {
        padding: 0.733vw 1.466vw;
    }

    .scroll-wrapper > .scroll-content {
        max-height: 30.724vw !important;
    }

    .sub-menu .sub-menu-column {
        padding-left: 3px;       
    }

    .scrollbar-inner > .scroll-element .scroll-bar {
        height: 3.657vw !important;
    }
}

@media screen {
    @media (min-width: 0px) {
        .main-menu-navbar.navbar-nav .nav-link {
            padding: 1vw 1.5vw !important;
        }
    }
}
