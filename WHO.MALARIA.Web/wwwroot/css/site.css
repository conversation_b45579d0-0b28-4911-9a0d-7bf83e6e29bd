﻿/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification\ 
for details on configuring this project to bundle and minify static web assets. */
body {
    /*padding-top: 50px;
    padding-bottom: 20px;*/
    margin: 0;
    padding: 0;
    /* font-family: sans-serif; */
    font-family: 'Roboto', sans-serif !important;
}

/* Wrapping element */
/* Set some basic padding to keep content from hitting the edges */
.body-content {
    /*padding-left: 15px;
    padding-right: 15px;*/
}

/* Carousel */
.carousel-caption p {
    font-size: 20px;
    line-height: 1.4;
}

/* Make .svg files in the carousel display properly in older browsers */
.carousel-inner .item img[src$=".svg"] {
    width: 100%;
}

/* QR code generator */
#qrCode {
    margin: 15px;
}

/* Hide/rearrange for smaller screens */
@media screen and (max-width: 767px) {
    /* Hide captions */
    .carousel-caption {
        display: none;
    }
}


/* Login Page CSS */
.login-page {
    background-color: #5990FF;
    min-width: 1128px;
    min-height: 100vh;
    margin: 0;
    position: relative;
}

.login-wrapper {
    padding: 15px 0px;
    margin: 0 auto;
    border-radius: 10px;
    background-color: #FFFFFF;
    box-shadow: 0 11px 7px -4px rgba(0,0,0,0.25);

    position: absolute;
    top: 30%;
    left: 40%;
    width: 420px;
    max-width: 420px;
    min-height: 300px;
}

.login-page .login-wrapper-l {
    top: 20%;
}
.login-wrapper .nav-tabs li {
    width: 50%;
    text-align: center;
    margin-bottom: 0px;
    margin-bottom: -3px;
}
.login-wrapper .nav-tabs > li > a{
    border: 0px;
    color: #545B6A;
    font-size: 16px;
    padding: 15px 10px;
}
.login-wrapper .nav-tabs > li.active > a, .login-wrapper .nav-tabs > li.active > a:hover, .login-wrapper .nav-tabs > li.active > a:focus{
    border: 0px;
    background-color: #ffffff !important;
    color: #5990FF;
    border-bottom: 3px solid #5990ff;
    margin: 0px 35px;
}
.login-wrapper .nav-tabs > li > a:hover {
    background-color: #ffffff !important;
    border: 0px;
}
.login-wrapper .tab-content .tab-pane {
    padding: 35px 25px 20px;
}
.login-wrapper .tab-content .btn-group{
    margin-top: 40px;
    display: block;
    margin-bottom: 0px;
}
.login-wrapper .tab-content .form-group .control-label{
    color: #383D48;
    font-weight: 500;
    margin-top: 0px;
    text-align: left;
}
.login-wrapper .tab-content .form-group .form-control{
    border: 1px solid #979797;
}
#external-login .list-inline {
    padding: 25px 0px;
}

#external-login .list-inline li{
    margin: 0 auto;
    display: block;
    text-align: center;
}
.login-wrapper .loginTitleSection {
    position: relative;
    text-align: center;
}

.login-wrapper .loginTitleSection .registerTitle {
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 24px;
    color: #5990FF;
    display: inline-block;
    /*padding: 15px 0px;*/
}
.login-wrapper .loginContentSection{
    padding: 15px 25px;
}
.loginContentSection ol.list {
    padding-left: 30px;
}
.loginContentSection .QR-code {
    display: block;
}
.loginContentSection .recovery-codes{
    padding: 15px 0px;
}
.loginContentSection .btn-group .btn {
    margin: 10px 0px;
    display: inline-block;
    border-radius: 5px !important;
}
.login-wrapper .error-message-wrapper {
    margin-left: 30%;
}
.login-wrapper .error-message {
    color: #d44950;
}

.login-wrapper .pageLink {
    text-align: center;
    display: block;
    margin: 0px auto;
    color: #5990FF;
}
.login-wrapper .loginText-l {
    text-align: center;
    font-size: 18px;
    line-height: 22px;
    color: rgb(0, 0, 0);
    padding: 15px 0px;
}

.btn-mui {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    padding: 7px 15px;
    border: 0px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    min-width: 140px;
}
.btn-blue {   
    background-color: #5990FF;
    color: #fff !important;
}
.btn-grey {
    background-color: #D4DBE9;
    color: #545B6A !important;
}
.form-signin-heading {
    position: absolute;
    top: 24%;
    left: 40%;
    display: block;
    text-align: center;
    margin: 0 auto;
    color: #fff;
    width: 420px;
    margin-bottom: 20px;
    font-size: 40px;
    font-weight: 600;
}
.form-signin-heading.form-signin-heading-l{
    top: 15%;
}
/* Login Page CSS */

/* Error Page CSS */
.section-background-image {
    background-image: url(../images/BackgroundWithCircles.svg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #999;
    width: 100%;
    height: calc(100vh - 65px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.bg-primary {
    background-color: #196aaa !important;
    color: #fff;
}
.bg-primary .navbar *, .bg-primary .container *{
    color: #fff !important;
}
.section-background-image * {
    color: #fff;
}
.section-bg-content {
    text-align: center;
    width: 350px;
}
.section-bg-content h1.heading-bg {
    color: #d6f4ff;
}
.section-bg-content h1 {
    font-weight: bold;
    font-size: 96px;
    color: #35add8;
    position: relative;
}
.section-bg-content .content {
    font-size: 20px;
    color: #ffffff;
    margin-top: 40px;
    margin-bottom: 20px;
}
.section-bg-content .app-btn-secondary {
    border: 1px solid #fff !important;
    border-radius: 5px;
    color: #fff !important;
    min-width: 125px;
}