﻿
#reportViewer1 {
    /*position: absolute;
    left: 5px;
    right: 5px;*/
    /*position: fixed;*/
    position: relative;
    top: 0px;
    height: 100vh;
    margin-bottom: 20px;
    /*top: 160px;*/
    bottom: 5px;
    overflow: scroll;
    clear: both;
}

    #reportViewer1 .trv-report-viewer {
        width: 100%;
    }

    #reportViewer1 .trv-parameter-editor-available-values .k-combobox {
        width: 85%;
    }

    #reportViewer1 .k-menu .k-state-hover .k-link {
        background-color: #fff !important;
        font-size: 14px;
    }

        #reportViewer1 .k-menu .k-state-hover .k-link:before {
            background-color: -internal-light-dark(rgb(239, 239, 239), rgb(59, 59, 59));
            opacity: 0.12;
        }

    #reportViewer1 .k-menu .k-state-selected > .k-link {
        background-color: var(--grid-heading-color)
    }

    #reportViewer1 .k-widget {
        background-color: var(--bg-white-color);
    }

        #reportViewer1 .k-widget.k-menu-horizontal > .k-item {
            color: var(--font-color);
            z-index: 91 !important;
        }

            #reportViewer1 .k-widget.k-menu-horizontal > .k-item .k-link {
                padding: 10px 15px;
            }

    #reportViewer1 .trv-nav.k-widget {
        border-color: rgba(0,0,0,0.12);
        border-width: 1px;
        border-style: solid;
    }

    #reportViewer1 .trv-pages-area.interactive .trv-page-container .trv-page-wrapper .trv-report-page {
        /* background-color: var(--bg-color); */
        width: 97.5% !important;
        /*height: 90vh !important;*/
    }

    #reportViewer1 svg text, #reportViewer1 svg line {
        fill: var(--font-color);
    }

    #reportViewer1 .trv-pages-area .txtRegion {
        background-color: var(--grid-heading-color);
        color: #fff;
    }

    #reportViewer1 .trv-pages-area .s3-0c0c18ee680809fc375d9c {
        background-color: var(--grid-heading-color);
        color: #fff;
    }

    #reportViewer1 .trv-pages-area .Region.Details {
        /*background-color: var(--bg-color);*/
        background-color: #e0e0e0;
        /*color: var(--font-color);*/
    }

    #reportViewer1 .trv-pages-area .layer div {
        /*color: var(--font-color);*/
    }

    #reportViewer1 .k-tooltip-validation.k-widget {
        background-color: #ffd7d4;
        border: 0px;
        z-index: 9;
        color: #888;
        width: 100%;
    }

    #reportViewer1 .k-dropdown-wrap.k-state-hover, #reportViewer1 .k-numeric-wrap.k-state-hover, #reportViewer1 .k-picker-wrap.k-state-hover {
        background-color: var(--control-background-color) !important;
        background-image: none;
        border-color: none !important;
    }

    #reportViewer1 .k-dropdown-wrap {
        background-color: var(--control-background-color) !important;
        background-image: none;
        border-color: none !important;
        border-radius: 0px;
    }

        #reportViewer1 .k-dropdown-wrap .k-select {
            background-color: var(--primary-color);
            opacity: 1;
            color: var(--font-color);
            border-radius: 0
        }

            #reportViewer1 .k-dropdown-wrap .k-select .k-icon:before {
                top: -8px !important;
            }

        #reportViewer1 .k-dropdown-wrap input {
            color: var(--font-color);
            font-size: 14px;
        }

        #reportViewer1 .k-dropdown-wrap.k-state-active .k-select .k-icon:before {
            top: 10px !important;
        }

    #reportViewer1 .trv-parameters-area .k-content {
        background-color: var(--body-background-color);
    }

        #reportViewer1 .trv-parameters-area .k-content .k-header {
            background-color: var(--list-hover-color);
            color: var(--primary-color);
            border-radius: 0px;
            padding: 5px 2px;
        }

    #reportViewer1 .k-menu .k-animation-container {
        z-index: 95 !important;
    }

    #reportViewer1 .k-picker-wrap.k-state-default {
        width: 85%;
        background-color: var(--control-background-color) !important;
        background-image: none !important;
        border-radius: 0px;
    }

.k-dropdown-wrap .k-state-border-down .k-input {
    font-size: 14px;
    background: white;
}

#reportViewer1 .trv-report-viewer .k-i-calendar:before {
    color: var(--font-color);
}

#reportViewer1 .k-datepicker .k-state-default .k-select {
    background-color: var(--primary-color);
    opacity: 1;
    color: var(--font-color);
}

#reportViewer1 .k-picker-wrap .k-input {
    color: var(--font-color);
}

.trv-pages-area-kendo-tooltip {
    font-size: 1.2em;
}

.trv-pages-area-kendo-tooltip-title {
    font-weight: bold;
}

.trv-pages-area-kendo-tooltip-text {
    font-weight: normal;
}

.reportHeaderTextStyle {
    font-family: "robotoregular, Helvetica, Arial, sans-serif";
    font-size: 1.173vw;
}

.reportBodyTextStyle {
    font-family: "robotoregular, Helvetica, Arial, sans-serif";
    font-size: 1.026vw;
}


.k-list-container {
    background: white;
    font-family: "robotoregular, Helvetica, Arial, sans-serif";
    font-size: 14px;
    color: #191414;
}

.k-popup.k-calendar-container, .k-popup.k-list-container {
    width: 100%
}

#reportViewer1 .k-list .k-item:focus, #reportViewer1 .k-list .k-item.k-state-focused, .k-list-optionlabel:focus, .k-list-optionlabel.k-state-focused {
    background: white;
}

#reportViewer1 .k-list > .k-state-focused.k-state-selected, .k-listview > .k-state-focused.k-state-selected, .k-state-focused.k-state-selected, td.k-state-focused.k-state-selected {
    color: #191414;
    box-shadow: none;
}

#reportViewer1 .k-draghandle.k-state-selected:hover, .k-ghost-splitbar-horizontal, .k-ghost-splitbar-vertical, .k-list > .k-state-highlight, .k-list > .k-state-selected, #reportViewer1 .k-marquee-color, .k-panel > .k-state-selected, .k-scheduler .k-scheduler-toolbar .k-state-selected, .k-scheduler .k-today.k-state-selected, #reportViewer1 .k-state-selected, .k-state-selected:link, .k-state-selected:visited, .k-tool.k-state-selected {
    color: #545b6a !important;
    background: white;
    box-shadow: none;
}

#reportViewer1 .k-grid-header th.k-state-focused, .k-list > .k-state-focused, .k-listview > .k-state-focused, .k-state-focused, td.k-state-focused {
    box-shadow: none !important;
}

#reportViewer1 .k-fieldselector .k-list .k-item, .k-item.k-drag-clue, .k-list-optionlabel, .k-listbox .k-item, .k-popup .k-list .k-item, .k-popup > .k-group-header, .k-popup > .k-virtual-wrap > .k-group-header {
    font-family: "robotoregular, Helvetica, Arial, sans-serif";
    font-size: 14px;
    min-height: auto !important;
}
/*#reportViewer1 .k-item .k-state-selected .k-state-focused {
*/
