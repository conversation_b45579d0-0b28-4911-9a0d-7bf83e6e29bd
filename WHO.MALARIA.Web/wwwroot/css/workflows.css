/*// Close */
.close-conn {
    font-size: 0.7em;
    font-weight: 700;
    line-height: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    margin: 0 auto;
    padding: 0;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
    pointer-events: initial;
    color: #fff;
    border-radius: 7px;
    background-color: #000;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

/*// Diagram */
.canvas {
    overflow: hidden;
    width: 100%;
    height: 100%;    
    background-color: var(--bg-color);
    height: 100vh;
    border: 1px solid #D4DBE9;
}

    .canvas * {
        border: none !important;
    }

.container {
    position: relative;    
    transition: height 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms, width 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
}

.text-center {
    text-align: center;
}

.container:active {
    cursor: grabbing;
}

.panAndZoom {
    position: absolute;
    width: 100%;
    height: 100%;
    border: none !important;
}

.node-connector-wrapper .closeButton {
    display: block;
    margin: 0 auto;
    color: #325AA0;
}

.activity {
    position: absolute;
    white-space: nowrap;
    z-index: 9;
    background-color: #fff;
    color: var(--black-font-color);
    text-align: center;
    border: 1px solid #346BCB !important;
    border-radius: 5px;
    width: auto;
    height: auto;
    min-width: 150px;
    padding: 30px 35px;    
}

.activity-circle {
    border-radius: 50%;   
}

.activity-diamond {
    background-color: none;
    transform: rotate(45deg);   
    border: double 20px rgba(92,142,233,0);
    background: rgba(228,204,209,1);
    background-clip: padding-box;
    background-color: #fff;
    background-clip: content-box, padding-box;
    z-index: 0;
    padding: 0px;
    min-width: 150px;
    min-height: 150px;
}

    .activity-diamond .node-content {
        transform: rotate(-45deg);
        transform: rotate(-45deg);
        background-color: transparent;
        border: 0px !important;
        box-shadow: inherit;        
        padding: 10px 5px 10px 5px;
        min-height: 100px;
        margin-top: 25px;
    }

    .activity-diamond .node-anchor-disabled {
        transform: rotate(0deg);
    }

    .activity-diamond .node-anchor-enabled {
        transform: rotate(0deg);
    }

.activity-parallel {
    background-color: none;
    transform: rotate(45deg);  
    border: double 20px rgba(92,142,233,0);
    background: rgba(228,204,209,1);
    background-clip: padding-box;
    background-color: #fff;
    background-clip: content-box, padding-box;
    z-index: 0;
    padding: 0px;
    min-width: 150px;
    min-height: 150px;
}

.activity-parallel .node-content {
    transform: rotate(-45deg);
    transform: rotate(-45deg);
    background-color: transparent;
    border: 0px !important;
    box-shadow: inherit;       
    padding: 5px 5px;
    min-height: 120px;
    margin-top: 25px;
}

.activity-circle .node-content-header .closeButton {
    right: 10%;
}

.activity-circle .node-content-header .editButton {
    left: 10%;
}

.activity-circle .node-content {
    border-radius: 50%;
}

.node.activity-circle {
    border-radius: 50%;
}

.activity-parallel .node-content::after {
    content: " ";
    position: absolute;
    display: block;
    background-color: #eaeaea;
    height: 10px;
    margin-top: -10px;
    top: 50%;
    left: 5px;
    right: 0px;
    z-index: 9;
}

.activity-parallel .node-content::before {
    content: " ";
    position: absolute;
    display: block;
    background-color: #eaeaea;
    width: 10px;
    margin-left: 0px;
    left: 50%;
    top: -10px;
    bottom: 5px;
    z-index: 9;
}

.activity-parallel .node-content .node-content-description {
    display: none;
}

.activity .node-content .node-content-header {
    display: none;
}

.activity:hover .node-content-header {
    display: block;
}

.activity .node-content .node-content-description {
    position: relative;
    top: 0px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.activity span {
    display: block;
}
.activity button > span{
    display: flex;
}

.activity-child {
    position: absolute;
    white-space: nowrap;
    z-index: 9;
    background-color: #325AA0;
    text-align: center;
    border: 9px solid #ccc !important;
    border-radius: 5px;
    width: auto;
    height: auto;
}

    .activity-child .node-content {
        background-color: #325AA0;
        color: #fff;
    }

    .activity-child .node-content-header .editButton {
        color: #fff;
    }

    .activity-child .node-content-header .closeButton {
        color: #fff;
    }

.activity:hover {
    cursor: pointer;
    border: 1px solid #8e8e8e !important;
}

    .activity:hover:active {
        cursor: grab;
    }

.node-anchor-disabled {
    border-radius: 50%;
}

.node-anchor-enabled {
    pointer-events: initial;
}

.node-content {
    position: relative;
    z-index: 1;
    margin: 0 auto;
    cursor: default;    
    border-radius: 5px;
    background-color: transparent;
    background-clip: border-box;
}

.node-selected {
    box-shadow: 0 3px 6px rgba(0, 0, 255, 0.16), 0 3px 6px rgba(0, 0, 255, 0.23);
}

.canvas-border {
    border: 1px solid #D4DBE9;
}

.canvas-dashed-border {
    border: 2px dashed #5990FF;
}

.page-left-section .sidebar-section .list-group .list-group-item {
    position: relative;
    cursor: move;
}

    .page-left-section .sidebar-section .list-group .list-group-item .DragIndicatorIcon {
        position: absolute;
        right: 5px;
        color: #999EA7;
        top: 7px;
    }

#mydiv {
    position: absolute;
    z-index: 9;
    background-color: #f1f1f1;
    text-align: center;
    border: 1px solid #d3d3d3;
}

#mydivheader {
    padding: 10px;
    cursor: move;
    z-index: 10;
    background-color: #2196F3;
    color: #fff;
}

.node-content-header {
    position: absolute;
    z-index: 9;
    min-height: 50px;
    top: -23px;
    left: 50%;
}

    .node-content-header .closeButton {
        position: absolute !important;
        right: 0px;
        color: #5990FF;
    }

    .node-content-header .editButton {
        position: absolute !important;
        left: 0px;
        color: #5990FF;
    }

.control-disabled {
    cursor: not-allowed !important;
    pointer-events: inherit !important;
}

.canvas .jtk-connector path {
    stroke: #5990FF;
    stroke-width: 2;
}

    .canvas .jtk-connector path:hover {
        stroke: #5990FF;
    }

.jtk-endpoint svg circle {
    stroke: #5990FF;
}

/* Activity BG classes CSS*/
.activity-bckg .node-content {
    background-color: transparent;
}

.activity-bckg .node-content-header .closeButton {
    color: #ffffff;
}

.activity-bckg .node-content-header .editButton {
    color: #ffffff;
}

.activity-bckg .node-content .node-content-description {
    color: #ffffff;
}

.activity-bg-DF .node-content-header .closeButton, .activity-bg-SU .node-content-header .closeButton {
    color: #000000;
}

.activity-bg-DF .node-content-header .editButton, .activity-bg-SU .node-content-header .editButton {
    color: #000000;
}

.activity-bg-DF .node-content .node-content-description, .activity-bg-SU .node-content .node-content-description {
    color: #000000;
}

.activity-bg-DF {
    background-color: #ffcc66;
}

.activity-bg-SU {
    background-color: #ccff99;
}

.activity-bg-SRN {
    background-color: #6699CC;
}

.activity-bg-DOC {
    background-color: #99CCFF;
}

.activity-bg-SecRAN {
    background-color: #666666;
}

.activity-bg {
    background-color: transparent;
}


/*Target 4k monitors and bigger screens*/
/*Media query for larger screens*/
@media only screen and (min-width: 1281px) {

    .activity {
        min-width: 10.972vw;
        padding: 2.20vw 2.560vw;
    }
    .activity-diamond {
        padding: 0vw;
        min-width: 10.972vw;
        min-height: 10.972vw;
    }
    .activity-diamond .node-content {
        padding: 0.733vw 0.365vw 0.733vw 0.365vw;
        min-height: 7.315vw;        
        margin-top: 1.228vw;
        max-width: 11vw;
    }
    .activity-parallel {
        padding: 0vw;
        min-width: 10.972vw;
        min-height: 10.972vw;
    }
    .activity-parallel .node-content {
        padding: 0.365vw 0.365vw;
        min-height: 8.778vw;
        margin-top: 1.828vw;
    }
    .activity .node-content .node-content-description {
        max-width: 14.630vw;
        font-size: 1.173vw;
    }
    .activity-diamond .node-content .node-content-description {
        max-width: 10.63vw;
        font-size: 1.173vw;
    }
    .node-content-header {
        min-height: 3.657vw;
        top: -1.682vw;
        left: 50%;
    }
    .node-content-header .editButton {
        left: 0vw;
    }
    .node-content-header .closeButton{
        right: 0vw;
    }
    .page-left-section .sidebar-section .list-group .list-group-item .DragIndicatorIcon {
        position: absolute;
        right: 0.365vw;
        color: #999EA7;
        top: 0.512vw;
    }
}