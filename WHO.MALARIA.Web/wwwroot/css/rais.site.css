﻿/* Contains all the Global and Custom CSS added as per designs for RAIS*/
/*// Render the Global and Custom CSS Styles for RAIS*/

:root {
    --body-background-color: #fff;
    --control-background-color: #fff;
    --secondary-color: #3458b9;
    /*--font-color: #212529;*/
    --font-color: #545B6A;
    --bg-color: #F7F9FA;
    --bg-white-color: #ffffff;
    --designer-section-color: #ffffff;
    --modal-overlay: rgba(0,0,0,0.2);
    --stepper-step-color: rgba(0,0,0,0.38);
    --disable-control-color: rgba(0, 0, 0, 0.38);
    --sidemenu-color: rgba(84 91 106, 1);
    --header-bg-border-color: #CAD7DE;
    --label-color: rgba(0, 0, 0, 0.54);
    --heading-color: #292922;
    --white-color: #ffffff;
    --white-font-color: #000;
    --green-color-h: 180.4;
    --green-color-s: 98.8%;
    --green-color-l: 32.4%;
    --red-color-h: 2;
    --red-color-s: 50%;
    --red-color-l: 45%;
    --blue-color-h: 218;
    --blue-color-s: 59%;
    --blue-color-l: 50%;
    --brown-color-h: 18;
    --brown-color-s: 67.5%;
    --brown-color-l: 60.2%;
    --green-color: hsl(var(--green-color-h), var(--green-color-s), var(--green-color-l));
    --green-color-action: hsl(180,55.3%,83.3%);
    --green-color-action-text: hsl(180.4,98.8%,32.4%);
    --red-color: hsl(var(--red-color-h), var(--red-color-s), var(--red-color-l));
    --blue-color: hsl(var(--blue-color-h), var(--blue-color-s), var(--blue-color-l));
    --blue-color-action: hsl(219.4,69.6%,91%);
    --blue-color-action-text: hsl(218.1,59.2%,50%);
    --brown-color: hsl(var(--brown-color-h), var(--brown-color-s), var(--brown-color-l));
    --brown-color-action: hsl(18.4,80.3%,88%);
    --brown-color-action-text: var(--brown-color);
    /* App Theme / Brand */
    --select-dropdown-icon: rgba(0, 0, 0, 0.54);
    --primary-color: var(--blue-color);
    --blue-heading-color: var(--blue-color);
    --green-heading-color: var(--green-color);
    --red-heading-color: var(--red-color);
    --brown-heading-color: var(--brown-color);
    --grid-heading-color: var(--blue-heading-color);
    --blue-list-hover-color: hsl(219.4,69.6%,91%);
    --green-list-hover-color: hsl(180,53.8%,92.4%);
    --brown-list-hover-color: hsl(18.4,80.3%,88%);
    --list-hover-color: var(--blue-list-hover-color);
    --list-disable-color: hsl(220,17.6%,96.7%);
    --header-logo-color: var(--primary-color);
    --primary-action-color: var(--blue-color-action);
    --primary-action-text-color: var(--blue-color-action-text);
    --primary-disabled-color: var(--blue-color);
    --secondary-action-color: hsl(200deg 2% 92%); /*/hsl(200,23.1%,97.5%);*/
    --secondary-action-text-color: hsl(220.9,11.6%,37.3%);
    --primary-border-color: var(--blue-color-10);
    --primary-hover-color: var(--blue-color);
    --primary-hover-color: var(--blue-color);
    --page-heading-color: var(--blue-color);
    --page-control-label-color: var(--blue-color);
    --page-control-focus-color: var(--blue-color);
    --page-link-color: var(--blue-color);
    --menu-item-heading-color: var(--blue-color);
    --theme-color: var(--blue-color);
    --checkbox-color: var(--blue-color);
    --radiobox-color: var(--blue-color);
    --grid-hover-color: #F7F9FA;
    --menu-active-color: var(--blue-color);
}

    :root[theme='dark'] {
        --green-color-h: 180.4;
        --green-color-s: 98.8%;
        --green-color-l: 32.4%;
        --red-color-h: 2;
        --red-color-s: 50%;
        --red-color-l: 45%;
        --brown-color-h: 18;
        --brown-color-s: 67.5%;
        --brown-color-l: 60.2%;
        /*--blue-color-h: 218;
    --blue-color-s: 50%;
    --blue-color-l: 46%;*/
        --blue-color-h: 218.1;
        --blue-color-s: 59.2%;
        --blue-color-l: 50%;
        --green-color: hsl(var(--green-color-h), var(--green-color-s), var(--green-color-l));
        --green-color-action: var(--green-color);
        --green-color-action-text: var(--white-color);
        --brown-color: hsl(var(--brown-color-h), var(--brown-color-s), var(--brown-color-l));
        --brown-color-action: var(--brown-color);
        --brown-color-action-text: var(--white-color);
        --blue-color: hsl(var(--blue-color-h), var(--blue-color-s), var(--blue-color-l));
        --blue-color-action: var(--blue-color);
        --blue-color-action-text: var(--white-color);
        --red-color-action: var(--red-color);
        --red-color-action-text: var(--white-color);
        --blue-heading-color: hsl(218.6,23.1%,35.7%);
        --green-heading-color: hsl(180,23.1%,35.7%);
        --brown-heading-color: hsl(18,30%,51%);
        --grid-heading-color: var(--blue-heading-color);
        --blue-list-hover-color: hsl(219.4,69.6%,91%);
        --green-list-hover-color: hsl(180,53.8%,92.4%);
        --brown-list-hover-color: hsl(18.4,80.3%,88%);
        --list-hover-color: var(--blue-list-hover-color);
        --list-disable-color: var(--bg-color);
        --header-logo-color: var(--white-font-color);
        --body-background-color: #121212;
        --control-background-color: #424242;
        --primary-color: #3a3a3a;
        --secondary-color: #9cafeb;
        --font-color: #ffffff;
        --black-font-color: #000;
        --white-font-color: #fff;
        --bg-color: #424242;
        --bg-white-color: #424242;
        --designer-section-color: #ededed;
        --modal-overlay: rgba(0,0,0,0.8);
        --stepper-step-color: rgba(255,255,255,0.9);
        --disable-control-color: rgba(255, 255, 255, 0.38);
        --sidemenu-color: rgba(255,255,255,0.9);
        --header-bg-border-color: transparent;
        --label-color: #346BCB;
        --heading-color: #818cab;
        --select-dropdown-icon: rgba(255, 255, 255, 0.54);
        --primary-action-color: var(--blue-color-action);
        --primary-action-text-color: var(--blue-color-action-text);
        --secondary-action-color: #777272;
        --secondary-action-text-color: var(--font-color);
        --page-heading-color: var(--blue-color);
        --page-control-label-color: var(--blue-color);
        --page-control-focus-color: var(--blue-color);
        --page-link-color: var(--blue-color);
        --menu-item-heading-color: var(--blue-color);
        --theme-color: var(--blue-color);
        --checkbox-color: var(--blue-color);
        --radiobox-color: var(--blue-color);
        --grid-hover-color: #6c6f6f;
        --menu-active-color: var(--blue-color);
    }

body {
    background-color: var(--body-background-color);
    color: var(--font-color);
    font-family: robotoregular, Helvetica, Arial, sans-serif;
    font-size: 14px;
    margin: 0;
    padding: 0;
}

.underline {
    text-decoration: underline !important;
}

.header {
    position: sticky;
    z-index: 99;
    top: 0px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

.linkText {
    /*color: #346BCB !important;*/
    color: var(--primary-color) !important;
    cursor: pointer;
}

    .linkText button {
        color: var(--primary-color);
    }

a:hover {
    text-decoration: none;
    color: var(--primary-color);
}

.pull-right {
    float: right;
}

.flex-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
}

.menu li {
    list-style: none;
}

.show {
    opacity: 1;
}

.hidden {
    display: none !important;
    opacity: 0;
    transition: opacity 0.5s linear;
}

.disabled input {
    color: var(--disable-control-color);
    cursor: default;
}

.disabled {
    color: var(--disable-control-color);
    cursor: not-allowed;
}

    .disabled .page-link {
        color: var(--disable-control-color) !important;
        cursor: not-allowed !important;
    }

input[readonly] {
    color: #9e9e9e;
}

.form-wrapper .form-section h6 {
    color: #212529;
}
/* copied from site.css */
/* Carousel */
.carousel-caption p {
    font-size: 20px;
    line-height: 1.4;
}

/* Make .svg files in the carousel display properly in older browsers */
.carousel-inner .item img[src$=".svg"] {
    width: 100%;
}

/* QR code generator */
#qrCode {
    margin: 15px;
}

/* copied from site.css */
.btn-theme {
    color: #fff !important;
    box-shadow: none !important;
    border-radius: 0px !important;
    margin-right: 10px !important;
    min-height: 30px;
}

.blue {
    background-color: #346BCB !important;
}

.green {
    background-color: #01A3A4 !important;
}

.brown {
    background-color: #DE7E55 !important;
}

.red {
    background-color: #f44336 !important;
}

.form-wrapper .form-group .col-form-control label, .col-form-control label {
    color: var(--page-control-label-color);
}

.form-wrapper .form-group .col-form-control.rais-error .Mui-error {
    color: #f44336 !important;
}

.col-form-control.col-radiobox-control label {
    color: var(--font-color) !important;
}

.form-wrapper .form-group .col-radiobox-control .radiobox-control span:first-child {
    color: var(--radiobox-color);
}

.radiobox-control span:first-child {
    color: var(--radiobox-color);
}

.checkbox-control span:first-child {
    color: var(--radiobox-color);
}

.form-wrapper .form-group .col-checkbox-control .checkbox-control span {
    color: var(--checkbox-color);
}

.MuiSelect-select:not([multiple]) option, .MuiSelect-select:not([multiple]) optgroup {
    background-color: var(--control-background-color) !important;
    color: var(--font-color);
}

.form-wrapper .form-group input:-internal-autofill-selected {
    appearance: menulist-button;
    background-color: var(--control-background-color) !important;
    background-image: none !important;
    color: -internal-light-dark(black, white) !important;
}

/*// Button CSS*/
.btn-top-group {
    margin-bottom: 15px;
}

.login .btn-blue {
    background-color: #346BCB !important;
    color: #fff !important;
    text-transform: uppercase;
}

    .login .btn-blue:hover {
        background-color: #346BCB !important;
    }

.login .btn-grey {
    background-color: #D4DBE9 !important;
    color: #545B6A !important;
    text-transform: uppercase;
}

    .login .btn-grey:hover {
        background-color: #D4DBE9 !important;
    }

.btn-blue {
    font-family: robotomedium, Helvetica, Arial, sans-serif;
    font-size: 14px;
    background-color: var(--primary-action-color) !important;
    color: var(--primary-action-text-color) !important;
    padding: 7px 15px;
    border: 0px;
    border-radius: 2px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    text-transform: initial;
}

    .btn-blue > a {
        color: #fff;
        font-weight: 500;
        text-decoration: none;
    }

    .btn-blue:hover {
        background-color: var(--primary-action-color) !important;
        opacity: 0.8;
    }

/* Ripple effect */
.ripple {
    background-position: center;
    transition: background 0.8s;
}

    .ripple:hover {
        background: #c7d7f5 radial-gradient(circle, transparent 1%, #c7d7f5 1%) center/15000%;
    }

    .ripple:active {
        background-color: #D8E3F8;
        background-size: 100%;
        transition: background 0s;
    }

.btn-grey {
    font-family: robotomedium, Helvetica, Arial, sans-serif;
    font-size: 14px;
    background-color: var(--secondary-action-color) !important;
    color: var(--secondary-action-text-color) !important;
    padding: 7px 15px;
    border: 0px;
    border-radius: 2px;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    text-transform: initial;
}

    .btn-grey:hover {
        background-color: var(--secondary-action-color) !important;
        opacity: 0.8;
    }

button:focus {
    outline: auto !important;
}

button.btn-blue:disabled {
    cursor: not-allowed;
    background-color: var(--primary-disabled-color) !important;
    opacity: 0.5;
    color: #fff !important;
}

button.btn-grey:disabled {
    cursor: not-allowed;
    background-color: #F7F9FA !important;
    color: #b6bcc7 !important;
}

.info-block {
    color: #03a9f4;
}
/*// Button CSS*/

/*Login Screens CSS*/
.login,
.image {
    min-height: 100vh;
}

.bg-login-image {
    background-image: url('../Login-bg-new.jpg');
    background-size: cover;
    background-position: 70% 50%;
}

.bg-login-message {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    position: relative;
    padding: 0.5em;
    left: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

    .bg-login-message .bg-heading {
        color: #FFFFFF;
        font-size: 46px;
        font-weight: 700;
        margin-bottom: 20px;
        line-height: 50px;
        position: absolute;
        top: 30%;
        left: 0vw;
    }

    .bg-login-message .bg-message {
        /*color: #A3C2FF;*/
        color: #ffffff;
        font-size: 32px;
        font-weight: 500;
    }

.md-form.md-outline input[type="text"] {
    color: #858B97 !important;
    background-color: #fff;
    border-radius: 2px;
}

    .md-form.md-outline input[type="text"].valid, .md-form.md-outline input[type="text"]:focus.valid, .md-form.md-outline input[type="password"].valid, .md-form.md-outline input[type="password"]:focus.valid {
        border-color: #B7BBC2 !important;
        box-shadow: none;
        font-size: 16px !important;
        line-height: 30px !important;
        color: #858B97 !important;
        border-radius: 2px;
    }

.md-form.md-outline.form-lg label {
    font-size: 1rem;
    -webkit-transform: translateY(12px);
    transform: translateY(12px);
    color: #858B97 !important;
}

.md-form.md-outline label.active {
    color: #B7BBC2 !important;
}

.md-form .input-prefix {
    color: #B7BBC2 !important;
    font-size: 20px;
    top: -10px;
}

.md-form.md-outline.form-lg .form-control.form-control-lg {
    background-color: #ffffff;
}

.md-form .fa:before {
    position: absolute;
    top: 25px;
    right: 0px;
}

.login .btn-blue:not([disabled]):not(.disabled):active, .login .btn-blue:not([disabled]):not(.disabled).active {
    background-color: #346BCB !important;
}

/* Change the white to any color ;) */
.login-wrapper input:-webkit-autofill,
.login-wrapper input:-webkit-autofill:hover,
.login-wrapper input:-webkit-autofill:focus,
.login-wrapper input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important;
}

/*.btn-blue:not([disabled]):not(.disabled):active, .btn-blue:not([disabled]):not(.disabled).active, .show > .btn-blue.dropdown-toggle {
    background-color: #346BCB !important;
}*/
/*Login Screens CSS*/

/* Login Page CSS */
.login-wrapper {
    padding: 0px 0px;
    margin: 0 auto;
    width: 300px;
    max-width: 300px;
    min-height: 300px;
}

    .login-wrapper .nav-tabs {
        padding: 10px 10px;
    }

    .login-wrapper .tab-content {
        min-height: 300px;
    }

    .login-wrapper .nav-tabs li {
        width: 50%;
        text-align: center;
        margin-bottom: 0px;
        margin-bottom: -2px;
    }

        .login-wrapper .nav-tabs li:only-child {
            width: 100%;
        }

    .login-wrapper .nav-tabs > li > a {
        border: 0px;
        color: #858B97;
        font-family: 'robotomedium';
        font-size: 18px;
        padding: 10px 10px;
    }

        .login-wrapper .nav-tabs > li > a.active, .login-wrapper .nav-tabs > li > a.active:hover {
            border: 0px;
            color: #346BCB;
            border-bottom: 3px solid #346BCB;
        }

    .login-wrapper .tab-content .tab-pane {
        padding: 10px 0px;
    }

    .login-wrapper .tab-content .form-group .control-label {
        color: #383D48;
        font-weight: 500;
        margin-top: 0px;
        text-align: left;
    }

    .login-wrapper .tab-content .form-group .form-control {
        border: 1px solid #979797;
    }

#external-login .list-inline {
    padding: 25px 0px;
}

    #external-login .list-inline li {
        margin: 0 auto;
        display: block;
        text-align: center;
    }

.login-wrapper .loginTitleSection {
    position: relative;
}

.login-wrapper .loginTitleSection {
    border-bottom: 1px solid #dddddd;
    margin-bottom: 15px;
    padding: 7px 0px;
}

.login-wrapper .login-content-section {
    margin: 10px 0px;
}

.login-wrapper .loginTitleSection .registerTitle {
    font-style: normal;
    font-weight: bold;
    line-height: normal;
    font-size: 24px;
    color: #346BCB;
    display: inline-block;
}

.login-wrapper .loginTitleSection span {
    display: block;
    color: #858B97;
}

.enable-authenticator .authenticator-wrapper {
    position: relative;
    top: -7%;
}

.authenticator-wrapper .loginContentSection {
    color: #858B97;
}

.loginContentSection .recovery-codes {
    display: block;
    color: #346BCB;
    background-color: #CAD7DE;
    margin-bottom: 15px;
    padding: 10px 15px;
}

    .loginContentSection .recovery-codes code {
        color: #346BCB;
        margin: 0px 3px;
    }

.loginContentSection ol.list {
    padding-left: 30px;
}

.loginContentSection .QR-code {
    display: block;
    color: #346BCB;
    background-color: #CAD7DE;
}

.login-wrapper .error-message-wrapper {
    margin: 15px 0px;
}

.login-wrapper .error-message {
    color: #c7254e;
}

.login-wrapper .loginText-l {
    text-align: left;
    font-size: 18px;
    line-height: 22px;
    color: #858B97;
    padding: 15px 0px;
}

.input-validation-error {
    border: 1px solid #c7254e !important;
}

.btn-mui {
    font-family: 'robotobold';
    font-size: 14px;
    padding: 15px 15px;
    border: 0px;
    border-radius: 2px;
    text-align: center;
    cursor: pointer;
    font-weight: 700;
    text-decoration: none;
    display: block;
    width: 100%;
    margin: 0px;
    text-transform: uppercase;
    box-shadow: none !important;
    transition: none !important;
}

.btn-grey:disabled {
    cursor: not-allowed;
}

.login-heading {
    position: relative;
    top: -30%;
    margin: 20px auto;
    width: 300px;
}

    .login-heading img {
        width: 60px;
        position: absolute;
        left: 0px;
        top: 8px;
    }

    .login-heading .app-logo {
        display: block;
        text-align: left;
        margin: 0 auto;
        color: #346BCB;
        font-size: 32px;
        font-weight: 700;
    }

    .login-heading .app-logo-text {
        display: block;
        text-align: left;
        margin: 0 auto;
        color: #346BCB;
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 12px;
        line-height: 14px;
        display: block;
    }

.form-signin-heading.form-signin-heading-l {
    top: 15%;
}
/* Login Page CSS */

/*// Page Breadcrumb CSS*/
.page-breadcrumb .breadcrumb {
    color: #5990FF;
    background-color: var(--body-background-color);
    padding: 12px 0px 6px;
    font-size: 14px;
    margin-bottom: 0px;
    min-height: 30px;
}

    .page-breadcrumb .breadcrumb .breadcrumb-separator {
        color: #5990FF;
    }

    .page-breadcrumb .breadcrumb .breadcrumb-item {
        color: #6c757d;
        font-size: 14px;
        min-height: 20px;
    }

        .page-breadcrumb .breadcrumb .breadcrumb-item.active {
            color: var(--font-color);
        }

        .page-breadcrumb .breadcrumb .breadcrumb-item a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
/*// Page Breadcrumb CSS*/

/*// Page Title Section CSS*/
.page-title-section {
    padding: 10px 0px;
}

    .page-title-section .page-title-left h2 {
        color: #383D48;
        font-size: 18px;
        font-weight: 500;
    }

    .page-title-section .page-title-right {
        margin: 15px 0px;
    }

/*// Page Title Section CSS*/

.document-title-section {
    margin: 15px 0px;
}

    .document-title-section .heading-title {
        color: var(--page-heading-color);
        font-family: robotomedium;
        font-size: 22px;
        margin-bottom: 0px;
    }

.form-title-section .heading-title {
    font-size: 18px;
    margin-bottom: 25px;
    color: #383D48;
}

/*// LHS Sidebar Section*/
.page-left-section .sidebar-section .list-group-title {
    padding: 10px 0px;
    border-bottom: 1px solid #CAD7DE;
}

    .page-left-section .sidebar-section .list-group-title h4 {
        margin: 0px;
        color: var(--font-color);
        font-size: 18px;
        font-weight: 500;
    }

.page-left-section .sidebar-section .list-group {
    list-style: none;
    padding-left: 0px;
    margin: 0px 0px;
    padding: 0px;
}

.page-left-section .sidebar-section .list-group-button {
    padding: 12px 25px;
}

.page-left-section .sidebar-section .list-group .list-group-item {
    padding: 0px;
    cursor: pointer;
    border: none;
    text-overflow: ellipsis;
    overflow: hidden;
    background-color: var(--body-background-color);
}

    .page-left-section .sidebar-section .list-group .list-group-item > a {
        text-overflow: ellipsis;
        overflow: hidden
    }

    .page-left-section .sidebar-section .list-group .list-group-item a {
        font-size: 16px;
        color: var(--sidemenu-color);
        padding: 7px 15px;
        display: block;
        width: 100%;
    }

        .page-left-section .sidebar-section .list-group .list-group-item a span {
            width: 78%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            vertical-align: bottom;
        }

        .page-left-section .sidebar-section .list-group .list-group-item a:hover {
            color: var(--primary-color);
        }

    .page-left-section .sidebar-section .list-group .list-group-item:hover, .page-left-section .sidebar-section .list-group .list-group-item.active, .page-left-section .sidebar-section .list-group .list-group-item.active a {
        background-color: var(--list-hover-color);
        color: var(--primary-color);
        border-radius: 2px;
    }

    .page-left-section .sidebar-section .list-group .list-group-item button {
        padding: 7px 7px;
    }
/*// LHS Sidebar Section*/

/* Kendo Grid CSS */
.k-grid-header-wrap table {
    width: 100% !important;
}

.k-grid-content table, .k-grid-content-locked table {
    width: 100% !important;
}

.kendo-grid-wrapper .k-grid .k-grid-header {
    background-color: var(--grid-heading-color);
    color: #fff;
    padding: 0px !important;
    border-bottom: 0px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    min-height: 50px;
}

.kendo-grid-wrapper .k-grid.rais-grid .k-grid-header {
    position: sticky;
    z-index: 11;
    top: 0px;
}

.kendo-grid-wrapper .k-grid.rais-grid .k-grid-content {
    overflow: hidden;
}

.collapsible-content-section.kendo-grid-wrapper .k-grid.rais-grid .k-grid-content {
    /*overflow: hidden;*/
    overflow-x: scroll;
}

#file-library .kendo-grid-wrapper .k-grid.rais-grid .k-grid-content {
    overflow-x: scroll;
}

.kendo-grid-wrapper .tab-wrapper .rais-tab-panel .k-grid .k-grid-header {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

.kendo-grid-wrapper .k-grid .k-grid-header .k-grid-header-wrap {
    border: none;
}

.kendo-grid-wrapper .k-grid table thead tr:hover {
    background-color: transparent;
}

.kendo-grid-wrapper .k-grid-header .k-header {
    text-transform: capitalize;
    background-color: var(--grid-heading-color);
    padding: 15px 24px;
    font-weight: 400;
    font-size: 16px;
    border-bottom: 0px !important;
    border-left: 1px solid rgba(255,255,255,0.5) !important;
    border: none;
}

.k-filter-row .k-textbox, .k-filter-row .k-textarea, .k-filter-row .k-input.k-textbox, .k-filter-row .k-textbox:hover {
    border-color: rgba(255,255,255,0.5) !important;
    color: #fff;
}

.k-filter-row .k-button {
    color: #fff;
    opacity: 0.7;
}

.k-grid-header .k-header .k-link:first-child {
    border-top-left-radius: 10px;
}

.kendo-grid-wrapper .k-grid-header .k-header .k-link > span {
    color: #fff;
}

.kendo-grid-wrapper .k-grid-header .k-header .k-link {
    font-size: 16px;
    color: #fff;
    padding: 10px 24px;
    position: relative;
}

    .kendo-grid-wrapper .k-grid-header .k-header .k-link .k-icon:before {
        color: #fff;
    }

    .kendo-grid-wrapper .k-grid-header .k-header .k-link .grid-icon {
        position: absolute;
        right: 0px;
        top: 2px;
        opacity: 0.7;
    }

        .kendo-grid-wrapper .k-grid-header .k-header .k-link .grid-icon .fa-filter {
            font-size: 20px;
            margin-top: 3px;
        }

        .kendo-grid-wrapper .k-grid-header .k-header .k-link .grid-icon button {
            color: #fff;
        }

.k-grid-header .k-filter-row .k-button {
    outline: none !important;
}

.kendo-grid-wrapper .k-grid-header .k-header {
    width: 0.2em !important;
}

.kendo-grid-wrapper .k-grid-header .k-header .k-link .k-icon {
    position: relative;
    top: 3px;
}

.kendo-grid-wrapper .k-grid-container .k-grid-content tbody tr {
    background-color: var(--bg-white-color);
    cursor: pointer;
    border-top: 1px solid #CAD7DE;
}

    .kendo-grid-wrapper .k-grid-container .k-grid-content tbody tr:first-child {
        border-top: none;
    }

    .kendo-grid-wrapper .k-grid-container .k-grid-content tbody tr.k-alt {
        background-color: var(--bg-white-color);
    }

    .kendo-grid-wrapper .k-grid-container .k-grid-content tbody tr:hover {
        background-color: var(--grid-hover-color);
    }

    .kendo-grid-wrapper .k-grid-container .k-grid-content tbody tr td .col-radiobox-control {
        color: var(--primary-color);
        padding: 4px;
    }

.kendo-grid-wrapper .k-grid tbody td {
    color: var(--font-color);
}

.kendo-grid-wrapper .k-grid {
    border: 0px;
}

.k-filtercell-operator span.k-dropdown-operator {
    display: none;
}

.k-filtercell-operator span.k-dropdown-wrap.k-state-default {
    display: none;
}

th span.k-icon.k-i-filter, th span.k-icon.k-i-x {
    font-size: 22px;
}

.k-grid th {
    border-bottom: 1px solid #c1c1c1 !important;
}

button.k-button.k-button-icon.k-clear-button-visible {
    background: transparent;
    font-size: 22px;
    border-radius: 50%;
}

.kendo-grid-wrapper .k-grid .k-grid-table {
    border: 1px solid #CAD7DE;
    box-sizing: inherit;
    background-color: var(--bg-white-color);
}

.kendo-grid-wrapper .k-grid .k-grid-content {
    overflow: auto;
}

    .kendo-grid-wrapper .k-grid .k-grid-content .k-grid-norecords {
        width: 100%;
        display: contents;
        font-weight: bold;
    }

        .kendo-grid-wrapper .k-grid .k-grid-content .k-grid-norecords td {
            width: 100% !important;
        }

    .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 8px 24px;
    }

        .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td svg {
            font-size: 1.2rem;
        }

.k-i-sort-asc-sm::before, .k-i-sort-desc-sm::before {
    content: "\e127";
    color: rgba(0, 0, 0, 0.54);
    font-size: 18px;
    font-weight: 900;
}

.kendo-grid-wrapper .k-grid-header .k-filter-row th {
    padding: 6px 0px 6px 24px;
    border-left: 1px solid rgba(255,255,255,0.5);
    border-top: 1px solid rgba(255,255,255,0.5);
}

.kendo-grid-wrapper .k-filtercell .k-filtercell-wrapper > .k-textbox {
    font-size: 14px;
    font-weight: 500;
}

.kendo-grid-wrapper .k-checkbox:checked + .k-checkbox-label::after, .kendo-grid-wrapper .k-checkbox:indeterminate + .k-checkbox-label::after {
    background-color: var(--checkbox-color);
    border-color: var(--checkbox-color);
    cursor: pointer;
}

.k-checkbox:checked + .k-checkbox-label::before {
    background-color: var(--checkbox-color) !important;
    border-color: var(--checkbox-color) !important;
}

.kendo-grid-wrapper .k-checkbox-label {
    flex: 0 0 auto;
    color: rgba(0, 0, 0, 0.54);
    padding: 12px;
    overflow: visible;
    font-size: 1.5rem;
    text-align: center;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 50%;
    cursor: pointer;
}

    .kendo-grid-wrapper .k-checkbox-label::before {
        border-color: var(--checkbox-color) !important;
    }

.kendo-grid-wrapper .k-pager-numbers .k-state-selected {
    background-color: var(--primary-color);
    color: rgb(255, 255, 255) !important;
    text-decoration: none;
    border-color: rgb(0, 123, 255);
    border-radius: 50%;
}

.kendo-grid-wrapper .k-pager-numbers .k-link {
    position: relative;
    line-height: 1.25;
    color: var(--primary-color);
    padding: 0.4rem 0.75rem;
    margin: 0px 3px;
}

.kendo-grid-wrapper .k-link .k-icon {
    color: var(--font-color);
}

.kendo-grid-wrapper .k-pager-numbers .k-link:hover, .kendo-grid-wrapper .k-pager-numbers .k-link.k-state-hover {
    background-color: var(--primary-color);
    color: rgb(255, 255, 255) !important;
    border-radius: 50%;
}

.kendo-grid-wrapper .k-pager-wrap {
    border-top: 0px;
    background-color: var(--bg-white-color);
}

    .kendo-grid-wrapper .k-pager-wrap .k-pager-info {
        margin-right: 0px;
        color: var(--font-color);
    }

.kendo-grid-wrapper .k-widget ::selection, .kendo-grid-wrapper .k-block ::selection, .kendo-grid-wrapper .k-panel ::selection {
    background-color: var(--primary-color);
    color: #fff;
}

#Grid-With-Radio .kendo-grid-wrapper .k-grid-header tr th:first-child, #Grid-With-Radio .kendo-grid-wrapper .k-grid-content tr td:first-child {
    width: 5%;
    text-overflow: inherit;
}

.kendo-grid-wrapper .k-checkbox-label::before {
    cursor: pointer;
}
/* Kendo Grid CSS */
.form-section-wrapper {
    padding: 15px 0px !important;
}

.page-content-section .form-wrapper {
    background-color: #F5F6F8;
    padding: 15px 30px;
    min-height: 450px;
}

.page-content-section .page-full-section-top {
    position: relative;
}

.page-content-section .page-right-section-top {
    position: relative;
    z-index: 9;
}

.page-content-section .page-right-section {
    position: relative;
}

.page-content-section .page-full-section-top .button-action-section {
    right: 0px;
    z-index: 11;
}

.page-content-section .page-right-section .button-action-section {
    right: 0px;
    z-index: 11;
    min-height: 35px;
}

.page-content-section .page-search-section {
    position: relative;
    z-index: 9;
}

    .page-content-section .page-search-section .button-action-section {
        position: relative;
        right: 0px;
        z-index: 11;
    }

.page-content-section .form-wrapper {
    background-color: var(--bg-color);
    border: 1px solid var(--header-bg-border-color);
    padding: 15px 30px;
    margin-bottom: 20px;
}

.page-content-section .page-full-section .form-wrapper {
    padding: 5% 15%;
}

.page-right-section .btn-top-group {
    padding: 20px 15px;
}

.page-content-section .form-wrapper .form-group-section .form-group-heading {
    margin-bottom: 15px;
    padding: 10px 30px;
    border-bottom: 1px solid #D4DBE9;
    font-size: 16px;
    font-family: robotomedium, Helvetica, Arial, sans-serif;
}

.page-content-section .form-wrapper .form-group-section img.logo-preview {
    padding: 5px 5px;
    -webkit-box-shadow: 0px 0px 10px 0px rgba(221,221,221,1);
    -moz-box-shadow: 0px 0px 10px 0px rgba(221,221,221,1);
    box-shadow: 0px 0px 10px 0px rgba(221,221,221,1);
    background-color: #fff;
}

img.logo-preview {
    width: 100%;
    height: auto;
}

#application-settings .form-wrapper, #ran-configuration .form-wrapper {
    padding: 5% 15%;
}

    #application-settings .form-wrapper .form-group-section {
        margin-bottom: 15px;
    }

        #application-settings .form-wrapper .form-group-section:last-child {
            margin-bottom: 0px;
        }

        #application-settings .form-wrapper .form-group-section .form-group-heading {
            padding: 10px 0px;
        }

    #application-settings .form-wrapper .form-group
    .form-wrapper .form-group-section .form-group .col-form-control {
        min-width: 100%;
        max-width: 100%;
    }
/* RHS form Section */
.page-right-section .form-wrapper {
    background-color: var(--bg-color);
    border: 1px solid var(--header-bg-border-color) !important;
    padding: 15px 30px;
    border: 1px solid #CAD7DE;
}

.page-right-section .form-wrapper-center, .page-content-section .form-wrapper-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.page-right-section .form-wrapper .form-group {
    margin-bottom: 15px;
}

.form-wrapper .form-group .col-form-label {
    display: block;
    font-weight: 500;
    font-size: 14px;
    font-family: 'robotomedium'
}

.form-group .col-form-label {
    display: block;
    font-weight: 500;
    font-size: 14px;
    font-family: 'robotomedium';
}

.form-wrapper .form-group .col-checkbox-control {
    margin-bottom: 0px;
}

.form-wrapper .form-group .col-form-control {
    min-width: 50%;
    max-width: 50%;
    display: inline-flex;
    flex-direction: column;
}

#datarole .rais-modal-content .form-wrapper .col-form-control, #functionalrole .rais-modal-content .form-wrapper .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

#permission-restriction .form-wrapper .form-group .col-form-control, #add-edit-functionalRolePermission .form-wrapper .form-group .col-form-control, #securityprofile .form-wrapper .form-group .col-form-control, #webservice_account .form-wrapper .form-group .col-form-control,
#add-entity .form-wrapper .form-group .col-form-control, #application-settings .form-wrapper .form-group .col-form-control,
#dashboardSettings .form-wrapper .form-group .col-form-control, #user-profile .form-wrapper .form-group .col-form-control,
#dashboard-template .form-wrapper .form-group .col-form-control, #email-template .form-wrapper .form-group .col-form-control,
#manageWorkflow .form-wrapper .form-group .col-form-control, #workflow-designer .form-wrapper .form-group .col-form-control,
#language-translation .form-wrapper .form-group .col-form-control, #errorLogs .form-wrapper .form-group .col-form-control,
#user .form-wrapper .form-group .col-form-control, #work-flow-form .form-wrapper .form-group .col-form-control,
.collapsible-content-section .form-wrapper .form-group .col-form-control, #dbPerformanceLogs .form-wrapper .form-group .col-form-control, #report-designer .form-wrapper .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

#add-entity .rais-modal-dialog .form-wrapper .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

#manageWorkflow .ran-form-wrapper .form-group .col-form-control, #add-entity .ran-form-wrapper .form-group .col-form-control {
    min-width: 82%;
    max-width: 82%;
}

#form-designer .form-wrapper .form-group .col-form-control {
    min-width: 80%;
    max-width: 80%;
}

#form-designer .form-wrapper .form-group .col-date-control {
    margin-top: 0px;
}

#form-designer .form-wrapper .form-group .col-form-control label {
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: normal;
}

#permission-restriction .form-wrapper .form-group, #add-edit-functionalRolePermission .form-wrapper .form-group, #securityprofile .form-wrapper .form-group, #webservice_account .form-wrapper .form-group {
    min-height: 65px;
    margin-bottom: 10px;
}

#user .form-wrapper .form-group {
    min-height: 50px;
    margin-bottom: 10px;
    position: relative;
}

#user .page-right-section .button-action-section {
    margin-bottom: 3%;
}

#permission-restriction .form-wrapper .form-group .col-radiobox-control .radiobox-control,
#add-edit-functionalRolePermission .form-wrapper .form-group .col-radiobox-control .radiobox-control,
#securityprofile .form-wrapper .form-group .col-radiobox-control .radiobox-control,
#webservice_account .form-wrapper .form-group .col-radiobox-control .radiobox-control {
    width: 50%;
    margin-bottom: 0px;
}

#language-translation .form-wrapper .form-group .col-radiobox-control .radiobox-control {
    width: 100%;
    margin-bottom: 0px;
}

#application-settings .form-wrapper .form-group .col-radiobox-control .radiobox-control, #dashboardSettings .form-wrapper .form-group .col-radiobox-control .radiobox-control {
    width: 47%;
    margin-bottom: 0px;
}

#user-profile .page-right-section .form-wrapper, #user .page-right-section .form-wrapper {
    padding: 5% 5%;
}

.rais-modal-pill .rais-modal-dialog .rais-modal-body .page-right-section .button-action-section {
    display: none;
}

.rais-modal-pill .rais-modal-dialog .rais-modal-body .page-right-section .form-wrapper {
    margin-bottom: 20px;
}

.rais-modal-dialog .rais-modal-body img.preview {
    height: 100%;
}

.rais-modal-dialog .form-wrapper .form-group {
    margin-bottom: 15px !important;
    min-height: inherit !important;
}

.form-section-wrapper .rais-modal-dialog .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

.page-right-section .page-full-section .document-title-section {
    display: none;
}

.full-layout .page-full-section .document-title-section {
    display: block;
}

.page-right-section .page-full-section .form-wrapper {
    padding: 5% 5%;
}

.full-layout .page-full-section .form-wrapper {
    padding: 5% 15%;
}

.page-right-section .form-group .col-date-control {
    margin-bottom: 0px;
    margin-top: 0px;
}

    .page-right-section .form-group .col-date-control > div {
        background-color: var(--bg-white-color);
    }

#permission-restriction .rais-modal-dialog .rais-modal-body .form-wrapper {
    margin-bottom: 5%;
}

#add-edit-functionalRolePermission .rais-modal-dialog .rais-modal-body .form-wrapper, #securityprofile .rais-modal-dialog .rais-modal-body .form-wrapper {
    margin-bottom: 5%;
    padding: 5% 5%;
}

.rais-modal-dialog .column-section-wrap .column-section .form-group-heading {
    border-bottom: 1px solid #D4DBE9;
}

.rais-modal-dialog .protection-setting-wrap {
    padding: 15px 20px !important;
}

#entity-details .page-full-section .rais-modal-dialog .form-wrapper {
    padding: 15px 30px;
}

.rais-modal-dialog .document-title-section {
    display: none;
}

.rais-modal-dialog .rais-modal-content .page-right-section, .rais-modal-dialog .rais-modal-content .page-full-section {
    position: relative;
}

.rais-modal-dialog .form-render-wrapper .form-section .renderer-section-1 {
    padding: 0px 15px;
}

.rais-modal-dialog .rais-modal-body .page-right-section .d-flex, .rais-modal-dialog .rais-modal-body .page-full-section
.d-flex {
    position: absolute;
    bottom: -70px;
    right: 0% !important;
    border-top: 0px !important;
}

.rais-modal-dialog .rais-modal-body .page-right-section .button-action-section, .rais-modal-dialog .rais-modal-body .page-full-section
.button-action-section {
    border-top: none;
    margin: 10px 0px;
}

.rais-modal-dialog .form-wrapper .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

.rais-modal-dialog .rais-modal-body .page-full-section .form-wrapper {
    padding: 5% 5%;
}

.col-form-doc .col-file-control {
    margin-bottom: 0px;
}

.col-form-doc .helper-text {
    display: block;
    color: #f44336;
    font-size: 12px;
    margin-left: 15px;
}

.full-screen .rais-modal-dialog .rais-modal-body .page-full-section .form-wrapper {
    padding: 5% 15%;
}

.rais-modal-dialog .rais-modal-body .page-right-section .form-wrapper, .rais-modal-dialog .rais-modal-body .page-full-section .form-wrapper {
    margin-bottom: 8%;
}

.full-screen .rais-modal-dialog .form-render-wrapper .form-section .renderer-section-1 {
    padding: 2% 25%;
}

.form-wrapper .form-group.form-group-checkbox {
    margin-bottom: 0px !important;
}

.form-render-wrapper .column-section .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

.form-wrapper.form-render-wrapper {
    min-height: auto;
}

.page-full-section .kendo-grid-wrapper {
    margin-bottom: 20px;
}

.kendo-editor-wrapper .k-toolbar {
    background-color: #fff;
}

    .kendo-editor-wrapper .k-toolbar .k-dropdown {
        width: 150px;
    }

.kendo-editor-wrapper.k-editor .k-editable-area {
    height: 320px;
    max-height: inherit;
    padding: 10px 15px !important;
}

.kendo-editor-wrapper .k-content {
    height: 100% !important;
}

#entity-form .pending-form {
    padding: 30px 30px !important;
}

    #entity-form .pending-form .record-renderer {
        align-content: center;
        flex-direction: row;
        justify-content: center;
    }

#entity-form .form-wrapper .pending-section {
    padding: 15px 0px;
}

#entity-form .form-section-wrapper .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

#entity-form .pending-section .pending-form {
    border: 0px;
    padding: 15px 0px !important;
}

#entity-form {
    position: relative;
}

    #entity-form .page-right-section {
        position: inherit;
    }

        #entity-form .page-right-section.pending-section .tab-wrapper .button-action-section {
            position: absolute;
            top: 0px;
            right: 0px;
        }

    #entity-form .rais-modal-dialog .rais-modal-content .button-action-section {
        position: relative !important;
    }

.pending-form .pending .column-section-controls .col-control-renderer {
    max-width: 100%;
    flex-basis: 100%;
}

#manageWorkflow .page-right-section .button-action-section, #querybuilderform .page-right-section .button-action-section /*#entity-form .page-right-section.pending-section .tab-wrapper .button-action-section*/ {
    position: absolute;
    top: -175px;
    right: 0%;
}

#manageWorkflow .page-right-section .form-wrapper {
    padding: 5% 5%;
}

#manageWorkflow .page-right-section .full-screen .rais-modal-dialog .form-wrapper, #add-entity .full-screen .rais-modal-dialog .ran-form-wrapper.form-wrapper {
    padding: 5% 15%;
}

#manageWorkflow .full-screen .ran-form-wrapper .form-group .col-form-control, #add-entity .full-screen .ran-form-wrapper .form-group .col-form-control {
    min-width: 90%;
    max-width: 90%;
}

#manageWorkflow .page-right-section .rais-modal-dialog .button-action-section {
    position: relative;
    top: inherit;
}

#manageWorkflow #wf-header .document-title-section {
    margin: inherit;
}

.icon-blue svg {
    color: var(--primary-color);
}

#language-translation .form-wrapper .form-group .page-link {
    padding: 7px 0px;
}

.translations-grid-wrapper .translations-grid-header {
    background-color: var(--grid-heading-color);
    color: #fff;
    padding: 0px 20px;
    border-bottom: 0px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}

.translations-grid-wrapper .translations-grid-content {
    border: 1px solid #CAD7DE;
    box-sizing: inherit;
    background-color: var(--bg-color);
    padding: 15px 20px;
}

    .translations-grid-wrapper .translations-grid-content .translations-grid-norecords {
        text-align: center;
    }

.translations-grid-header .sortButton {
    opacity: 0.7;
    color: #fff;
}

.translations-grid-header .col-form-label {
    padding: 2px 0px;
}

.translations-grid-wrapper .translations-grid-footer {
    padding: 10px 10px;
}

.translations-grid-footer .pagination {
    margin-bottom: 0px;
}

    .translations-grid-footer .pagination .page-item .page-link {
        position: relative;
        color: var(--primary-color);
        margin: 0px 3px;
        border: none;
        background-color: transparent;
        cursor: pointer;
    }

    .translations-grid-footer .pagination .disabled span {
        padding: 5px 5px;
        text-align: center;
        position: relative;
        display: block;
    }

    .translations-grid-footer .pagination .page-item.active .page-link, .translations-grid-footer .pagination .page-item:hover .page-link {
        background-color: var(--primary-color);
        color: rgb(255, 255, 255) !important;
        border-radius: 50%;
    }

.form-wrapper .multiselect-wrapper > div {
    top: 0px;
}

.form-wrapper .multiselect-wrapper.auto-complete-wrapper > .mul-dropdown {
    top: auto;
    padding: 0px;
}

.mul-dropdown {
    padding: 10px;
    width: 100%;
}

.multiselect-wrapper .mul-input {
    min-height: 34px;
    border-top: none;
    border-color: #B7BBC2;
}

#permission-restriction .page-right-section .form-wrapper, #add-edit-functionalRolePermission .page-right-section .form-wrapper {
    padding: 30px 40px;
}

#permission-restriction .form-wrapper .form-group .page-link > a, #add-edit-functionalRolePermission .form-wrapper .form-group .page-link > a {
    font-weight: 600;
}

.form-group-multiselect .note-message {
    font-size: 14px;
    padding: 7px 5px;
    line-height: 16px;
}

.material-textfield {
    position: relative;
}

    .material-textfield label.mul-label {
        position: absolute;
        font-size: 12px;
        left: 0px;
        top: 0%;
        transform: translateY(-50%);
        color: gray;
        padding: 0 0.3rem;
        margin: 0 0.5rem;
        transition: .1s ease-out;
        transform-origin: left top;
        pointer-events: none;
        z-index: 9;
        width: 100%;
    }

        .material-textfield label.mul-label:after {
            content: '';
            width: 78%;
            border-bottom: solid 1px #B7BBC2;
            position: absolute;
            left: 78px;
            top: 55%;
            z-index: 1;
        }

        .material-textfield label.mul-label:before {
            content: '';
            width: 2%;
            border-bottom: solid 1px #B7BBC2;
            position: absolute;
            left: -7px;
            top: 55%;
            z-index: 1;
        }

.mul-input {
    font-size: 1rem;
    outline: none;
    border: 1px solid gray;
    padding: 18px 0.7rem;
    color: gray;
    transition: 0.1s ease-out;
}

    .mul-input:focus {
        border-color: #6200EE;
    }

        .mul-input:focus + label {
            color: #6200EE;
            top: 0;
            transform: translateY(-50%) scale(.9);
        }

.mul-fieldset:focus .mul-input {
    border: 2px solid #346BCB !important;
    border-top: 0px !important;
}

.multiselect-wrapper .helper-text {
    color: #f44336;
    margin: 5px 14px;
    font-size: 0.75rem;
}

.multiselect-wrapper .mul-error .mul-label:before, .multiselect-wrapper .mul-error .mul-label:after {
    border-color: #f44336 !important;
}

.multiselect-wrapper .col-form-control.rais-error {
    border-color: #f44336 !important;
}

#permission-restriction .material-textfield.musi-error .mul-label:before, #permission-restriction .material-textfield.mui-error .mul-label:before {
    border-color: #f44336;
}

#entity-inventory-form .MuiStepper-horizontal {
    background-color: transparent;
}

#user .search-wrapper {
    min-height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

    #user .search-wrapper .form-group.autocomplete {
        flex-direction: column !important;
        display: flex;
        min-width: 70%;
    }

#user .user-form-container .form-render-wrapper {
    border: none !important;
    min-height: 20vw;
    padding: 0px !important;
}

    #user .user-form-container .form-render-wrapper .form-section {
        padding: 0px;
    }

#user .user-form-container .MuiStepper-horizontal {
    background-color: transparent;
}

#user .user-form-container .page-right-section .document-title-section {
    display: none;
}

#user .user-form-container .page-right-section .button-action-section .btn-grey {
    display: none;
}

#user .user-form-container .page-right-section {
    position: relative;
}

    #user .user-form-container .page-right-section .d-flex, #user .user-form-container .page-full-section .d-flex {
        position: absolute;
        bottom: -25px;
        right: 25% !important;
        border-top: 0px !important;
    }

#user .page-right-section .horozontal-wizard .stepper-wrapper .MuiStepper-horizontal {
    padding-top: 0px;
    background-color: var(--body-background-color);
}

#work-flow-form .form-render-wrapper {
    position: relative;
    margin-bottom: 40px;
}

    #work-flow-form .form-render-wrapper .button-action-section {
        position: absolute;
        bottom: 2%;
        right: 10px;
    }

    #work-flow-form .form-render-wrapper .rais-modal-dialog .button-action-section {
        position: relative;
        bottom: 5%;
        right: 0px;
    }

#work-flow-form .preview-list .attach-image {
    height: 200px;
}


.form-wrapper .form-group-items {
    margin-bottom: 0px;
}

.form-wrapper .table-section .table-row {
    display: flex;
}

.form-wrapper .table-section {
    background-color: var(--bg-white-color);
    border: 1px solid #B7BBC2;
}

    .form-wrapper .table-section .table-row .table-column {
        display: inline-flex;
        border-bottom: 1px solid rgba(224, 224, 224, 1);
        padding: 5px 15px;
        min-height: 38px;
        align-items: center;
    }

        .form-wrapper .table-section .table-row .table-column:first-child {
            width: 60%
        }

    .form-wrapper .table-section .table-row .table-column {
        width: 40%
    }

        .form-wrapper .table-section .table-row .table-column:first-child span {
            padding: 12px 10px;
        }

.form-group-close .closeButton {
    position: relative;
    top: -7px;
}

.form-group .col-form-control .col-control-scroll {
    padding: 10px 15px !important;
    background-color: #fff;
    max-height: 250px;
    overflow-y: scroll;
    position: relative;
}

    .form-group .col-form-control .col-control-scroll .scroll-wrapper > .scroll-content {
        max-height: 200px !important;
    }

/*Editor Control*/
span.editorWrapper div.DraftEditor-editorContainer {
    border: 1px solid #ddd;
    font-size: 14px;
    background: #fff;
    min-height: 150px;
}

#functionalrole .kendo-grid-wrapper .k-grid-header .k-header:first-child {
    width: 20%;
}

    #functionalrole .kendo-grid-wrapper .k-grid-header .k-header:first-child + .k-header {
        width: 20%;
    }

#functionalrole .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td:first-child {
    width: 20%;
}

    #functionalrole .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td:first-child + td {
        width: 20%;
    }

span.k-widget.k-autocomplete.k-header.col-form-control.k-state-disabled {
    border: 1px solid rgba(204,204,204,1);
    opacity: 1;
    cursor: default;
}

span.k-widget.k-autocomplete.k-header.col-form-control.k-state-focused {
    border: 1px solid #5990FF;
}

.k-autocomplete {
    border-width: 0 0 0px !important;
}

    .k-autocomplete input[placeholder] {
        font-size: 14px;
    }

    .k-autocomplete:hover {
        border-color: rgba(0,0,0,0.87);
        border-width: 0 0 0px;
    }

    .k-autocomplete .k-input {
        color: rgba(0,0,0,0.87);
        border: 1px solid #cdcdcd !important;
    }

span.k-widget.k-autocomplete.k-header.col-form-control.k-state-disabled input {
    color: rgba(0, 0, 0, 0.38);
}

.form-wrapper .form-group .col-form-control fieldset, .document-template .form-group .col-form-control fieldset {
    border-radius: 0px;
    border-color: #B7BBC2;
}

.col-form-control fieldset {
    border-color: #B7BBC2 !important;
    border-radius: 0px;
}

.col-form-control .Mui-focused {
    color: var(--page-control-label-color) !important;
}

.col-form-control select, .col-form-control input {
    color: var(--font-color);
}

.col-form-control div > svg.MuiSelect-icon {
    color: var(--font-color);
}

.col-form-control .Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: var(--page-control-focus-color) !important;
}

.form-wrapper .form-group .col-form-control > div {
    padding: 0px;
}

    .form-wrapper .form-group .col-form-control > div.MuiOutlinedInput-multiline {
        padding-top: 6px;
        padding-bottom: 0px;
        background-color: var(--control-background-color);
    }

.form-wrapper .form-group .col-form-control .MuiSelect-icon {
    color: var(--select-dropdown-icon);
}

.form-wrapper .form-group .col-form-control .Mui-disabled .MuiSelect-icon {
    color: rgba(0, 0, 0, 0.34);
}

.form-wrapper .form-group .col-form-control input, .form-wrapper .form-group .col-form-control textarea, .form-group .col-form-control select,
.document-template .form-group .col-form-control input {
    padding: 10.5px 14px;
    border-radius: 0px;
    background-color: var(--control-background-color);
    color: var(--font-color);
    font-size: 14px;
    line-height: 16px;
}

.form-wrapper .form-group .col-form-control .Mui-disabled input, .form-wrapper .form-group .col-form-control .Mui-disabled textarea, .form-group .col-form-control .Mui-disabled select {
    color: var(--disable-control-color);
}

.form-wrapper .form-group .col-form-control .Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: var(--page-control-focus-color);
}

.form-wrapper .form-group .col-checkbox-control .checkbox-control.Mui-checked {
    color: #346BCB;
}

.form-wrapper .form-group .col-checkbox-control .checkbox-control.Mui-disabled, .form-wrapper .form-group .col-radiobox-control .radiobox-control.Mui-disabled {
    color: var(--disable-control-color);
}

    .form-wrapper .form-group .col-checkbox-control .checkbox-control.Mui-disabled span, .form-wrapper .form-group .col-checkbox-control .checkbox-control.Mui-disabled + span, .form-wrapper .form-group .col-radiobox-control .radiobox-control.Mui-disabled span, .form-wrapper .form-group .col-radiobox-control .radiobox-control.Mui-disabled + span {
        color: var(--disable-control-color);
    }

.form-wrapper .form-group .col-checkbox-control .checkbox-control + span {
    font-size: 14px;
}

.form-wrapper .form-group .col-radiobox-control .radiobox-control span.Mui-checked:first-child {
    color: var(--radiobox-color);
}

.form-wrapper .form-group .page-link {
    padding: 5px 7px;
    display: block;
    font-size: 14px;
    color: var(--page-link-color);
    background-color: transparent;
    border: none;
    cursor: pointer;
}

#webservice_account .form-wrapper .form-group .page-link {
    padding: 0px 7px;
    margin: 5px 0px;
}

    #webservice_account .form-wrapper .form-group .page-link + .page-link {
        border-right: 1px solid #CAD7DE;
    }

#permission-restriction .form-wrapper .form-group .page-link {
    padding: 7px 5px;
}

.form-wrapper .form-group .page-link > a {
    font-size: 14px;
}

.col-radiobox-control {
    color: var(--font-color);
}
/* RHS form Section */
/* Button Action Section CSS */
.button-action-section {
    margin: 10px 0px 10px;
}

    .button-action-section .button-group button {
        margin-left: 15px;
        min-width: 100px;
    }

    .button-action-section .button-group .export-icon {
        min-width: auto;
        color: var(--primary-color);
        padding: 0px 5px;
    }

    .button-action-section .button-group .page-link {
        display: inline-block;
        border: none;
    }

        .button-action-section .button-group .page-link:hover {
            background-color: transparent;
        }

    .button-action-section .form-group .col-form-control {
        max-width: 250px;
        vertical-align: baseline;
    }

        .button-action-section .form-group .col-form-control .col-form-control > div {
            border-radius: 2px;
        }

    .button-action-section .multiselect-wrapper .col-form-control {
        padding: 6px 0.7rem;
    }

    .button-action-section .button-group .linkText {
        margin-left: 10px;
    }
/* Button Action Section CSS */

/* Toastify CSS */
.Toastify .Toastify__toast {
    position: relative;
}

    .Toastify .Toastify__toast .Toastify__toast-body {
        font-weight: bold;
    }

.Toastify .Toastify__close-button {
    position: absolute;
    z-index: 99;
    right: 15px;
    top: 35%;
    color: #ffffff;
    opacity: 1;
}

.Toastify__toast--success {
    background: #5dbb5f !important;
}
/* Toastify CSS */

/* error */
.error {
    border: 1px solid #c7254e !important;
}

.rais-error fieldset {
    border: 1px solid #f44336 !important;
}

.rais-error .col-file-control {
    border: 1px solid #f44336 !important;
}

.rais-error .helper-text {
    color: #f44336;
    margin: 0px 14px;
    font-size: 0.75rem;
}

.workflow-instance span {
    color: var(--font-color);
}

.workflow-instance .help-block {
    font-size: 16px;
    color: #c7254e;
}

p.help-block {
    color: #c7254e;
    font-size: 13px;
    margin-top: 5px;
}

.help-block {
    color: #c7254e;
    font-size: 13px;
    margin-top: 5px;
}

#message-success {
    top: 0;
    position: fixed;
    width: 30%;
    z-index: 9999;
    background-color: #d4edda;
    right: 0;
    padding: 15px 15px;
    border: 1px solid #c3e6cb;
    color: #155724;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    font-size: 15px;
}

#message-wrapper {
    top: 0;
    position: fixed;
    width: 30%;
    z-index: 9999;
    background-color: #f2dede;
    right: 0;
    padding: 15px 15px;
    border: 1px solid #ebccd1;
    color: #a94442;
    left: 0;
    right: 0;
    margin: 0 auto;
}

    #message-wrapper .error-header {
        font-weight: bold;
    }

    #message-wrapper .error-content {
        padding: 10px 0;
    }
/* Application Dialog CSS */
.application-dialog .dialog-head .closeButton {
    position: absolute;
    right: 10px;
    top: 10px;
}

.error-header span {
    font-size: 18px;
    cursor: pointer;
    margin-top: -1%;
}

.close-btn::after {
    display: inline-block;
    cursor: pointer;
    content: "\00d7"; /* This will render the 'X' */
}

.application-dialog .dialog-head .dialog-head-icon {
    position: relative;
    top: 5px;
    margin-right: 15px;
}

.application-dialog .dialog-content > div {
    padding: 16px 24px;
}

.application-dialog .dialog-action {
    padding: 16px 24px;
}

    .application-dialog .dialog-action button {
        text-transform: capitalize;
    }

.application-dialog .dialog-content .form-group .col-form-control {
    min-width: 70%;
    max-width: 70%;
}

    .application-dialog .dialog-content .form-group .col-form-control fieldset {
        border-radius: 0px;
    }

.application-dialog .dialog-content .form-wrapper .form-group .multiselect-wrapper .col-form-control {
    padding-right: 25px;
}

.application-dialog .dialog-content .form-wrapper .form-group .multiselect-wrapper .mul-dropdown {
    width: 100%;
}

.application-dialog .dialog-content .col-radiobox-control .radiobox-control span:first-child {
    color: #5990FF !important;
}

.dialog-column-properties .dialog-content {
    min-height: 60vh;
}

    .dialog-column-properties .dialog-content > div {
        border-bottom: 0px;
        overflow-y: inherit;
    }

.dialog-column-properties .dialog-action {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.dialog-content .preview-wrapper {
    border: 1px solid #ddd;
}

.dialog-content .page-link {
    padding: 5px 15px;
    display: inline-block;
    font-size: 14px;
    color: #5990FF !important;
}

.dialog-content .form-control {
    background-image: none !important;
    height: auto !important;
}

.application-dialog-xs .dialog-content .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

/* Application Dialog CSS */
.form-wrapper .form-group .css-1r4vtzz {
    width: 100%;
    border: 1px solid #ccc;
    padding: 3px 8px;
}

.form-wrapper .form-group .css-48ayfv {
    width: 100%;
    border: 1px solid #ccc;
    padding: 3px 8px;
}
/* Security profile Dialog CSS */
.form-wrapper.security-profile {
    min-height: 300px !important;
}

/* Multi select dropdown css starts*/
.form-wrapper .multi-select-dropdown > div {
    width: 51%;
    float: left;
}

    .form-wrapper .multi-select-dropdown > div > div {
        width: 100%;
    }

#userForm .selected-multiselect-item {
    position: relative;
}

.selected-multiselect-item {
    padding: 20px 15px;
    display: inline-block;
    width: 48%;
    margin-top: 20px;
    position: absolute;
}

    .selected-multiselect-item ul {
        padding-left: 0px;
        list-style: none;
    }

        .selected-multiselect-item ul li {
            padding: 5px 5px;
            display: inline-block;
            margin: 5px;
            border-radius: 5px;
        }

            .selected-multiselect-item ul li span {
                font-weight: bold;
                cursor: pointer;
                position: relative;
                z-index: 9;
            }

.tab-wrapper {
    margin-bottom: 20px;
}

    .tab-wrapper .rais-tabs {
        border-bottom: 1px solid #CAD7DE;
        margin-bottom: 20px;
    }

.kendo-grid-wrapper .tab-wrapper .rais-tab-panel {
    margin-top: 20px;
}

.tab-wrapper .rais-tabs .rais-tab {
    text-transform: initial;
    font-size: 16px;
    font-weight: 600;
    color: var(--font-color);
    flex-grow: inherit;
    flex-basis: inherit;
}

    .tab-wrapper .rais-tabs .rais-tab:hover {
        color: var(--theme-color);
    }

.tab-wrapper .rais-tab.Mui-selected {
    color: var(--theme-color);
    z-index: 9;
}

.rais-tabs .MuiTabs-indicator {
    background-color: var(--theme-color);
}

/* Content Tab CSS*/
/*/ Multi select dropdown css ends*/
.form-wrapper .form-group .css-1r4vtzz {
    width: 100%;
    border: 1px solid #ccc;
    padding: 3px 8px;
}

.form-wrapper .form-group .css-48ayfv {
    width: 100%;
    border: 1px solid #ccc;
    padding: 3px 8px;
}

.form-wrapper .multi-select-dropdown .css-1pcexqc-container {
    font-size: 14px;
}

.form-wrapper .multi-select-dropdown > div {
    width: 45%;
}

    .form-wrapper .multi-select-dropdown > div > div {
        width: 100%;
    }

.selected-multiselect-item {
    padding: 20px 15px;
    display: inline-block;
    width: 50%;
    margin-top: 0px;
    background-color: #F5F6F8;
    z-index: 10;
    max-height: 170px;
    overflow-y: auto;
    position: absolute;
    left: 45%;
}

    .selected-multiselect-item > div {
        font-size: 14px;
    }

    .selected-multiselect-item ul {
        padding-left: 0px;
        list-style: none;
        margin: 1em 0;
        cursor: pointer;
        font-weight: bold;
        font-size: 14px;
        line-height: 16px;
    }

        .selected-multiselect-item ul li {
            padding: 8px 10px;
            display: inline-block;
            margin: 5px;
            border-radius: 50px;
            font-size: 12px;
            background-color: #D4DBE9;
            color: #212529;
        }

.pill-box-close {
    font-weight: bold;
    cursor: pointer;
    position: relative;
    z-index: 9;
    background-color: #f9f9f9;
    padding: 2px 6px;
    border-radius: 50%;
    color: #545B6A;
    margin-left: 10px;
}

    .pill-box-close::after {
        display: inline-block;
        content: "\00d7";
    }

.selected-multiselect-item ul li span:hover {
    color: #5990FF;
}

.form-wrapper .multiselect-wrapper {
    position: relative;
}

    .form-wrapper .multiselect-wrapper .page-link {
        color: var(--primary-color);
    }

    .form-wrapper .multiselect-wrapper .tooltip-wrapper {
        position: absolute;
        left: 50%;
        top: -5px;
    }

fieldset.mul-active {
    border: 2px solid var(--page-control-focus-color) !important;
}

    fieldset.mul-active > legend {
        color: var(--page-control-label-color) !important;
    }

    fieldset.mul-active .mul-dropdown {
        border-top: 2px solid var(--page-control-focus-color) !important
    }

.form-wrapper .multiselect-wrapper .mul-input.col-form-control {
    max-height: 200px;
    overflow-y: auto;
    min-width: 100%;
}

.form-wrapper .refresh-wrapper .multiselect-wrapper .tooltip-wrapper {
    position: absolute;
    left: 100%;
    top: -5px;
}

.form-wrapper .refresh-wrapper .tooltip-wrapper {
    position: relative;
    top: -7px;
}

.selected-items-header {
    font-size: 12px !important;
    font-weight: bold;
}

.multi-select-dropdown > div > button {
    padding: 8px 8px 5px 8px;
    background-color: #FFF;
    border-radius: 2px;
    border-width: 0;
    box-shadow: none !important;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.36;
    -webkit-align-items: baseline;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    background: #FFF;
    box-sizing: border-box;
    border-color: hsl(0,0%,80%);
    color: #515151;
    cursor: pointer;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin: 0;
    max-width: 100%;
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    outline: currentcolor none medium !important;
    text-align: left;
    -webkit-text-decoration: none;
    text-decoration: none;
    -webkit-transition: background 0.1s ease-out 0s,box-shadow 0.15s cubic-bezier(0.47,0.03,0.49,1.38) 0s;
    transition: background 0.1s ease-out 0s,box-shadow 0.15s cubic-bezier(0.47,0.03,0.49,1.38) 0s;
    vertical-align: middle;
    white-space: nowrap;
    width: auto;
}

.selected-multiselect-item ul li .pill-box-close::after {
    display: inline-block;
    content: "\00d7";
}
/* Multi select dropdown css ends*/

/* Multi select dropdown checkbox CSS */
.css-1vr111p-option, css-1qprcsu-option {
    position: relative;
    margin: 1em 0;
    text-align: left;
    cursor: pointer;
    display: inline;
    line-height: 1.25em;
    vertical-align: top;
    clear: both;
    padding-left: 1px;
}

    .css-1vr111p-option input[type="checkbox"], .css-1qprcsu-option input[type="checkbox"] {
        outline: 0;
        visibility: hidden;
        width: 1.25em;
        margin: 0;
        display: block;
        float: left;
        font-size: inherit;
    }

*, *::before, *::after {
    box-sizing: border-box;
}

.css-1vr111p-option:not(:empty), .css-1qprcsu-option:not(:empty) {
    padding-left: 0.75em;
    position: relative;
    background-color: #fff !important;
    margin: 1em 0;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    line-height: 16px;
    white-space: nowrap;
}

.css-1vr111p-option::before input[type="checkbox"]:checked, .css-1qprcsu-option::before input[type="checkbox"]:checked {
    background: #337ab7;
    border: none;
}

.css-1vr111p-option::before, .css-1qprcsu-option::before {
    width: 1.25em;
    height: 1.25em;
    background: #fff;
    border: 2px solid #4B9BFF;
    border-radius: 0.125em;
    cursor: pointer;
    transition: background .3s;
}
/*.css-1vr111p-option:after input[type="checkbox"]:checked,*/
.css-1qprcsu-option::after /*input[type="checkbox"]:checked*/ {
    content: '✔' !important;
    transform: translate(0.25em, 0.3365384615em) rotate(0deg);
    width: 0.75em;
    height: 0.375em;
    border-top-style: none;
    border-right-style: none;
    color: #fff;
    left: 9px !important;
    top: 2px !important;
    font-weight: normal;
}

.css-1vr111p-option::before, .css-1qprcsu-option::before, .css-1vr111p-option::after, .css-1qprcsu-option::after {
    content: "";
    position: absolute;
    left: 10px;
    top: 6px;
}

.css-1qprcsu-option::before {
    background-color: #4B9BFF;
}
/* Multi select dropdown checkbox CSS */

/*Sticky CSS Sidebar*/
.page-left-section {
    z-index: 98;
}

    .page-left-section .sidebar-section .list-group-section {
        overflow-y: auto;
        margin-bottom: -5px;
    }

.sidebar-section .sidelink-action {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    color: #545B6A;
}

button.disabled {
    cursor: not-allowed !important;
    color: rgba(0, 0, 0, 0.38);
}

.page-left-section .sidebar-section .list-group-button button {
    text-transform: capitalize;
}
/*Sticky CSS Sidebar*/

/* Query Builder CSS*/
.active-query-builder-wrapper .qb-ui-layout__top {
    height: 100%;
}

.active-query-builder-wrapper .qb-widget-header {
    background-color: var(--grid-heading-color);
    color: #ffffff;
    display: block;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.active-query-builder-wrapper .qb-ui-layout {
    background-color: var(--bg-color);
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    border: 1px solid #dedede;
}

.active-query-builder-wrapper .qb-widget-content {
    border: 1px solid #dedede;
    box-shadow: none;
}

.active-query-builder-wrapper .qb-ui-tree-view {
    box-shadow: none;
}

.active-query-builder-wrapper .qb-ui-layout .qb-ui-layout__right {
    box-shadow: none;
}

.active-query-builder-wrapper .qb-ui-table-titlebar::after {
    background: hsla(0,0%,100%,0);
}

.active-query-builder-wrapper .qb-ui-canvas-navbar-union-panel {
    background-color: #D4DBE9;
}

.active-query-builder-wrapper .qb-ui-tree-item-filter-block {
    background-color: #D4DBE9;
}

.active-query-builder-wrapper .qb-ui-layout__left {
    min-width: 100%;
    max-width: 100%;
    width: 100% !important;
}

.qb-ui-layout__top .qb-ui-layout__right .qb-ui-canvas-navbar {
    background-color: var(--grid-heading-color);
    color: #545B6A;
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}

    .qb-ui-layout__top .qb-ui-layout__right .qb-ui-canvas-navbar:hover {
        background-color: var(--primary-color);
        color: #545B6A;
    }

.active-query-builder-wrapper .col-form-control, .active-query-builder-wrapper .col-form-control {
    padding: 6.5px 14px;
    border-radius: 0px;
    background-color: var(--body-background-color);
    font-size: 14px;
    width: 100%;
    color: var(--font-color);
    border-color: rgba(118, 118, 118, 0.3);
    border: 1px solid rgba(118, 118, 118, 0.3);
}

.active-query-builder-wrapper select.col-form-control {
    -webkit-appearance: menulist;
}

.active-query-builder-wrapper .form-group label {
    margin: 10px 0px;
    font-weight: 500;
    font-size: 14px;
}

.active-query-builder-wrapper .help-block {
    color: #f44336;
    font-size: 13px;
    margin-top: 5px;
    padding: 0px 15px;
    display: none;
}

.active-query-builder-wrapper .md-form.md-outline.form-error label {
    color: #f44336 !important;
}

.active-query-builder-wrapper .form-error .col-form-control, .active-query-builder-wrapper .form-error textarea {
    border: 1px solid #f44336;
}

.active-query-builder-wrapper .form-error textarea {
    height: 100% !important;
}

.display-aqb-error-block {
    display: block !important;
}

.active-query-builder-wrapper .ui-qb-grid-row-alias {
    min-width: 140px !important;
}

.active-query-builder-wrapper .qb-ui-structure-tabs__tab {
    display: block;
    margin-right: 0px;
}

    .active-query-builder-wrapper .qb-ui-structure-tabs__tab > label:after {
        width: auto;
    }
/*hide unnecessary icons & grid columns from AQB screen*/
.qb-ui-canvas-navbar-subquery-panel, .qb-ui-canvas-navbar-union-panel {
    display: none !important;
}
 
.qb-ui-layout__bottom, .qb-ui-editor textarea {
    height: 500px !important;
}

.active-query-builder-wrapper .qb-ui-editor-refresh-button {
    background-color: var(--primary-color) !important;
}
/* Query Builder CSS*/
/* pills */
.pills {
    display: inline-block;
    position: relative;
}

    .pills .pills-close {
        position: absolute;
    }

    .pills > div {
        display: inline-block;
    }

    .pills .pill {
        padding: 4px 10px;
        display: inline-block;
        margin: 5px;
        border-radius: 50px;
        font-size: 12px;
        line-height: 1.5;
        /*background-color: #D6DFF2;*/
        background-color: var(--list-hover-color);
        color: #212529;
        word-break: break-all;
        max-width: 250px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        padding-right: 30px;
    }

.close-pill-box {
    color: var(--primary-color);
    background-color: #ffffff;
    border-radius: 50%;
    font-size: 20px !important;
    position: absolute;
    top: 10px;
    right: 10px;
    white-space: normal;
    overflow: unset !important;
    text-overflow: inherit;
    float: right;
    cursor: pointer;
    width: 0.7em !important;
    height: 0.7em !important;
}

    .close-pill-box:hover {
        background-color: var(--primary-color);
        color: #ffffff;
        cursor: pointer;
    }

.multi-pill .close-pill-box {
    top: 6px;
    right: 6px;
}

.multi-pillbox .MuiInputBase-root {
    min-height: 32px;
    padding-left: 0px;
}

.multi-pillbox .MuiFormControl-root > div {
    min-height: 32px;
}

.multi-pillbox input {
    margin: 0px !important;
}

.form-group .multi-pillbox {
    background-color: #fff;
}

.mul-input {
    background-color: var(--bg-white-color);
    width: 50%;
    min-height: 32px;
    border: none;
    cursor: pointer;
    position: relative;
}

.mul-dropdown {
    background: var(--bg-white-color);
    padding: 10px;
    width: 50%;
    position: absolute;
    z-index: 999;
    max-height: 400px;
    overflow-y: scroll;
    border: 1px solid #ddd;
}

    .mul-dropdown .col-checkbox-control {
        margin-bottom: 0px;
    }

    .mul-dropdown .col-form-control {
        max-width: none !important;
    }

.down-arrow:after {
    position: absolute;
    top: 15px;
    right: 10px;
    width: 0;
    height: 0;
    padding: 0;
    content: '';
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.54);
    pointer-events: none;
}
/* kendo autocomplete*/
span.k-widget.k-autocomplete.k-header.col-form-control {
    padding-right: 0;
}

.multi-pillbox > div > div > div {
    padding-top: 0px !important;
    background-color: var(--control-background-color);
}

.MuiChip-deletable {
    background-color: #D4DBE9 !important;
}

.col-disabled.pills .pill .close-pill-box {
    cursor: not-allowed;
    pointer-events: none;
}

/* left-side navigation arrow */
.right-arrow {
    margin-right: 0px !important;
    color: #bdb9b9
}

/* data type multi-document/document */
.document-error-section {
    padding: 10px 0px;
}

.preview {
    height: 40px;
    width: 40px;
    vertical-align: middle;
}

.saved-preview-list {
    background-color: #F5F6F8;
}

.preview-list {
    /*padding: 4px 10px;*/
    padding: 4px 20px;
    border-bottom: 1px solid #ddd;
    background-color: var(--bg-white-color);
    min-height: 40px;
    position: relative;
    padding-right: 50px;
}

.preview-wrapper .preview-list:last-child {
    border-bottom: 0px;
}

.preview-list .file-preview-name {
    padding: 5px 5px;
    display: inline-block;
    color: var(--font-color) !important;
}

.preview-list .icon-section {
    position: absolute;
    top: 0px;
    right: 0px;
}

.preview-list .mui-icon {
    font-size: 1rem;
}

.file-name {
    font-size: 10px;
    padding-left: 10px;
    font-weight: 600;
    vertical-align: middle;
}

.document-upload {
    position: relative;
}

    .document-upload .allowed-format {
        padding: 5px 7px;
        display: block;
    }

.form-group .document-upload .col-file-control {
    padding: 0px 5px;
    border-radius: 0px;
    background-color: var(--control-background-color);
    font-size: 14px;
    line-height: 16px;
    border-color: #B7BBC2;
    border: 1px solid #b7bbc2;
    width: 100%;
    margin-bottom: 0px;
}

.col-file-control {
    padding: 0px 5px;
    border-radius: 0px;
    background-color: var(--control-background-color);
    font-size: 14px;
    line-height: 16px;
    border-color: #B7BBC2;
    border: 1px solid #b7bbc2;
    width: 100%;
    margin-bottom: 0px;
}

    .col-file-control a.page-link {
        color: var(--primary-color);
        background-color: var(--bg-white-color);
        border: none;
        cursor: pointer;
    }

    .col-file-control .page-link button {
        color: var(--font-color);
    }

.form-group .preview-wrapper {
    min-width: 100%;
    max-width: 100%;
    border: 1px solid #ddd;
    margin-bottom: 0px;
}

#work-flow-form .collapsible-tab .sticky-bottom-tabs {
    width: 75%;
    left: 12%;
    right: 15%;
    z-index: 99;
}

button.close {
    color: var(--font-color);
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
    font-size: 18px !important;
}

.collapsible-content-section .k-grid.rais-grid .k-grid-header {
    position: sticky;
    top: -15px;
}

/*Column Section Form Builder CSS*/
.form-section-wrapper {
    padding: 15px 0px !important;
}

.form-wrapper .form-section {
    border-bottom: 1px solid #D4DBE9;
    padding: 15px 30px;
}

#form-designer .form-wrapper .form-section {
    padding: 15px 0px;
}

#form-designer .form-wrapper .column-section-wrap {
    padding: 15px 15px;
}

#form-designer .form-wrapper .form-section .col-flex-1 {
    border: 1px solid #CAD7DE;
}

#form-designer .form-wrapper .form-section .form-group {
    margin-bottom: 15px;
}

#form-designer .form-wrapper-center {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #858B97;
}

#form-designer .column-section.dropabble .form-wrapper-center {
    min-height: 150px;
}

#form-designer .center-add-section {
    position: absolute;
    left: 45%;
    bottom: -35px;
}

#form-designer .rais-modal-dialog .form-wrapper .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
}

#form-designer .column-section.dropabble .col-control-actions {
    margin-top: -5px;
}

#form-designer .column-section .draggable .col-control-actions {
    cursor: move;
}

#form-designer .form-wrapper .form-group .lookup-control-renderer .col-form-control {
    min-width: 98%;
    max-width: 98%;
    margin-right: 10px;
}

#form-designer .design-section-1 .form-group .col-form-control {
    min-width: 94%;
    max-width: 94%;
}

#form-designer .form-wrapper .form-group .col-checkbox-control {
    margin-top: -5px;
}

#form-designer .page-left-section {
    height: 100%;
}

#form-designer .sidebar-section .list-group-item.non-draggable {
    /*background-color: #F5F6F8;
    color: #94969a;*/
    background-color: var(--list-disable-color);
    color: var(--font-color);
}

    #form-designer .sidebar-section .list-group-item.non-draggable .DragIndicatorIcon {
        display: none;
    }

    #form-designer .sidebar-section .list-group-item.non-draggable a {
        color: var(--font-color);
        opacity: 0.6;
    }

        #form-designer .sidebar-section .list-group-item.non-draggable a:hover {
            color: var(--font-color);
        }

#form-designer .page-right-section .form-wrapper {
    padding: 0px;
    border: 0px !important;
}

    #form-designer .page-right-section .form-wrapper > .form-section {
        background-color: var(--bg-white-color);
    }

#form-designer .section-wrapper {
    background-color: var(--bg-color);
    padding: 15px 30px;
    border: 1px solid #CAD7DE;
    border-top: 15px solid #fff;
    position: relative;
}

    #form-designer .section-wrapper .form-section {
        border-bottom: 0px;
    }

#form-designer .column-section-heading svg {
    font-size: 1.2rem;
}

#form-designer .form-wrapper .multiselect-wrapper {
    width: 100%;
}

    #form-designer .form-wrapper .multiselect-wrapper .col-form-control {
        min-width: 100%;
        max-width: 100%;
    }

.col-control-actions span {
    color: var(--primary-color);
}

#form-designer .sticky-sidebar {
    position: -webkit-sticky;
    position: sticky;
    top: 6rem;
    z-index: 90;
    height: calc(100vh - 6rem);
}

#form-designer .page-left-section .sidebar-workflow .list-group-section .scrollbar-inner {
    max-height: 75vh !important;
}

#form-designer .column-section-wrap .design-section-1 .col-control-renderer, #form-designer .column-section-wrap .design-section-2 .col-control-renderer, #form-designer .column-section-wrap .design-section-3 .col-control-renderer {
    max-width: 83.333333% !important;
    flex-basis: 83.333333% !important;
}

.form-wrapper .form-section .linkText {
    margin-right: 15px;
}

.form-section-wrapper .form-group .col-form-control {
    min-width: 80%;
    max-width: 100%;
}

.form-section-wrapper .column-section .form-group .col-form-control {
    max-width: 50%;
}

.form-section-wrapper .column-section .form-group .col-date-control {
    margin: 0px;
    background-color: #fff;
}

.form-render-wrapper .column-section .form-group .col-date-control {
    background-color: inherit;
}

.form-section-wrapper .column-section .form-group .col-date-control > div:before {
    border-bottom: none;
}

.form-section-wrapper .column-section .form-group .col-date-control > div:after {
    border-bottom: 2px solid #5990ff;
}

.column-section-wrap .column-section .col-lookup-control .lookup-text {
    position: absolute;
    right: 5%;
    top: -15px;
    margin-right: 0px;
}

.column-section-wrap .design-section-1 .column-section .lookup-text {
    position: absolute;
    right: 0%;
    top: -15px;
    margin-right: 0px;
}

.column-section-wrap .column-section .form-lookup-control {
    position: relative;
}

.form-wrapper .form-section:last-child {
    border-bottom: 0px;
}

.form-wrapper .form-section .form-group {
    position: relative;
    margin-bottom: 0px;
}

.column-section-wrap .column-section {
    /*background-color: #fff;*/
    /*background-color: var(--designer-section-color);*/
    padding: 15px 10px;
    min-height: 200px;
}

.form-wrapper .form-section .col-flex-1 {
    flex: 1;
    /*background: var(--designer-section-color);*/
    margin: 0px 2px;
    padding: 0px 5px;
}

#form-designer .section-wrapper .form-group .col-form-control input, #form-designer .section-wrapper .form-group .col-form-control select {
    /*background: var(--designer-section-color);*/
}

.select-column {
    padding: .25rem 0.50rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: .125rem;
    cursor: pointer;
    margin-right: 10px;
}

    .select-column .select-column-icon {
        display: inline-block;
        width: 1.5em;
        height: 1.0em;
        vertical-align: middle;
        content: "";
        background: no-repeat 50%;
        background-size: 100% 100%;
    }

    .select-column .icon-bar {
        display: inline-block;
        width: 100%;
        width: 6px;
        height: 100%;
        background-color: #ddd;
        margin: 0px 2px;
    }

    .select-column .icon-bar-selected {
        background-color: #5990ff;
        display: inline-block;
        width: 100%;
        width: 6px;
        height: 100%;
        margin: 0px 2px;
    }

.column-property-options {
    margin: 0 auto 30px;
    width: 100%;
    text-align: center;
}

.labelProperty .large,
.control-renderer .large {
    font-size: 20px;
}

.labelProperty .medium,
.control-renderer .medium {
    font-size: 16px;
}

.labelProperty .small,
.control-renderer .small {
    font-size: 12px;
}

.labelProperty .red,
.control-renderer .red {
    color: #f44336;
}

.labelProperty .blue,
.control-renderer .blue {
    color: #5990ff;
}

.labelProperty .black,
.control-renderer .black {
    color: var(--white-font-color);
}

.preview-text {
    word-break: break-all
}

.form-wrapper .form-section .column-section .form-group .col-form-label {
    white-space: normal;
    word-break: break-word;
}

.form-section-wrapper .column-section-wrap {
    margin-bottom: 20px;
}
/*Column Section Form Builder CSS*/

/*Form Designer Form Builder CSS*/
.form-section-wrapper .design-section-2 .col-control-label {
    max-width: 25%;
    flex-basis: 25%;
}

.form-section-wrapper .design-section-2 .col-control-renderer {
    max-width: 50%;
    flex-basis: 50%;
}

.form-section-wrapper .design-section-2 .col-control-actions {
    max-width: 25%;
    flex-basis: 25%;
}

.form-section-wrapper .design-section-2 .col-lookup-control .lookup-text {
    right: -15%;
}

.form-section-wrapper .column-section .form-group .control-renderer {
    position: relative;
}

    .form-section-wrapper .column-section .form-group .control-renderer .multiselect-wrapper .mul-fieldset {
        width: 80%;
    }

    .form-section-wrapper .column-section .form-group .control-renderer .mul-dropdown {
        min-width: 80%;
    }

.form-section-wrapper .lookup-control-renderer .col-form-control {
    min-width: 60%;
}
/*Form Designer Form Builder CSS*/
/*Form Render Form Builder CSS*/

.form-section-wrapper .design-section-3 .col-control-label {
    max-width: 100%;
    flex-basis: 100%;
}

.form-section-wrapper .design-section-3 .col-control-renderer {
    max-width: 58%;
    flex-basis: 58%;
    margin-top: 7px;
}

.form-section-wrapper .design-section-3 .column-section .lookup-text {
    position: absolute;
    left: -40%;
    top: -5px;
}

.form-render-wrapper .column-section .form-group .control-renderer .multiselect-wrapper .mul-fieldset {
    width: 100%;
}

.form-render-wrapper .form-section .col-flex-1 {
    background-color: var(--bg-color);
    margin: 0px;
}

.form-render-wrapper .form-section .form-group {
    margin-bottom: 15px;
}

.form-render-wrapper .column-section-wrap .column-section {
    background-color: var(--bg-color);
    padding: 0px;
    min-height: inherit;
}

.form-render-wrapper .column-section-wrap {
    margin-bottom: 0px;
}

.form-render-wrapper .column-section .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
    margin-right: 5px;
}

.form-render-wrapper .column-section .form-group .control-renderer .mul-dropdown {
    min-width: 100%;
    max-width: 100%;
}

.form-render-wrapper .column-section .form-group .preview-wrapper {
    min-width: 100%;
    max-width: 100%;
    border: 1px solid #ddd;
}

.form-render-wrapper .col-file-control {
    padding: 0px 5px;
    border-radius: 0px;
    background-color: #ffffff;
    font-size: 14px;
    line-height: 16px;
    border-color: #B7BBC2;
    border: 1px solid #b7bbc2;
    width: 100%;
    margin-bottom: 0px;
}

    .form-render-wrapper .col-file-control .page-link .attachment-icon {
        color: var(--font-color);
    }

    .form-render-wrapper .col-file-control.disabled .page-link .attachment-icon {
        cursor: not-allowed !important;
        color: var(--disable-control-color);
    }

.col-file-control .mui-icon {
    font-size: 1rem;
}

.form-render-wrapper .preview-list {
    background-color: var(--bg-white-color) !important;
}

.preview-list .preview {
    opacity: 0.5;
    font-size: 1rem;
    margin-right: 7px;
}

.preview-list .attach-image {
    width: 100%;
    height: 300px;
    opacity: 1;
}

img.preview.edit {
    object-fit: fill;
    height: auto;
    max-height: 280px;
}

.form-render-wrapper .form-section .column-section .form-group .col-form-label {
    padding: 5px 20px;
}

.form-render-wrapper .form-section .renderer-section-1 {
    padding: 2% 25%;
}

#entity-summary-form .form-render-wrapper .form-section .renderer-section-1 {
    padding: 2% 25%;
}

#entity-summary-form .column-section .column-section-controls {
    padding: 10px 0px;
}

.column-section-wrap .renderer-section-1 .col-control-renderer {
    max-width: 100%;
    flex-basis: 100%;
}

    .column-section-wrap .renderer-section-1 .col-control-renderer .multiselect-wrapper .mul-fieldset {
        width: 100% !important;
    }

.form-render-wrapper .renderer-section-1 .multiselect-wrapper .col-form-control {
    min-width: 100%;
}

.form-render-wrapper .renderer-section-1 .multiselect-wrapper .mul-dropdown {
    min-width: 100% !important;
}

.column-section-wrap .renderer-section-2 .col-control-label {
    max-width: 33.333333%;
    flex-basis: 33.333333%;
}

.column-section-wrap .renderer-section-2 .col-control-renderer {
    max-width: 97%;
    flex-basis: 97%;
}

    .column-section-wrap .renderer-section-2 .col-control-renderer .col-form-control {
        min-width: 100% !important;
    }

.form-render-wrapper .renderer-section-2 .multiselect-wrapper .col-form-control {
    min-width: 100% !important;
}

.column-section-wrap .renderer-section-2 .col-control-renderer .lookup-control-renderer .col-form-control {
    min-width: 98%;
}

.column-section-wrap .renderer-section-3 .col-control-label {
    max-width: 50%;
    flex-basis: 50%;
}

.column-section-wrap .renderer-section-3 .col-control-renderer {
    max-width: 100%;
    flex-basis: 100%;
}

    .column-section-wrap .renderer-section-3 .col-control-renderer .col-form-control {
        min-width: 96%;
    }

    .column-section-wrap .renderer-section-3 .col-control-renderer .lookup-control-renderer .col-form-control {
        min-width: 95%;
    }

.form-render-wrapper .renderer-section-1 .lookup-control-renderer {
    max-width: 100%;
}

    .form-render-wrapper .renderer-section-1 .lookup-control-renderer .col-form-control:first-child {
        min-width: 98% !important;
    }
/*.form-render-wrapper .renderer-section-1 .lookup-control-renderer .col-form-control*/
/*Form Render Form Builder CSS*/
/* collapsible tab CSS */
.page-right-section .collapsible-tab .sticky-bottom-tabs {
    position: fixed;
    bottom: 0;
    width: 50%;
    left: 35%;
    right: 15%;
    /*border: 1px solid #5b6170;*/
    border: 1px solid #B7BBC2;
    z-index: 9;
}

.collapsible-tab .sticky-bottom-tabs {
    position: fixed;
    bottom: 0;
    width: 56%;
    left: 22%;
    right: 22%;
}

.col-form-doc .col-form-control {
    min-width: 100% !important;
    max-width: 100% !important;
}

.application-dialog-sticky {
    z-index: 100 !important;
}

    .application-dialog-sticky .MuiDialog-container {
        height: 80% !important;
        outline: 0;
        width: 80%;
        margin: auto 12%;
        bottom: 0 !important;
        position: fixed;
        left: -2%;
    }

.MuiAppBar-positionFixed {
    top: 0;
    left: auto;
    right: 0;
    position: static !important;
}

.sticky-bottom-tabs .MuiAppBar-colorDefault, .application-dialog-sticky .MuiAppBar-colorDefault {
    color: rgba(0, 0, 0, 0.87);
    background-color: #fff;
    box-shadow: none;
}

    .sticky-bottom-tabs .MuiAppBar-colorDefault button, .application-dialog-sticky .MuiAppBar-colorDefault button {
        /*color: rgba(0, 0, 0, 0.87);*/
        color: var(--primary-color);
        background-color: var(--bg-white-color);
        box-shadow: none;
    }

.sticky-bottom-tabs .MuiTab-root, .sticky-bottom-tabs .MuiTabs-root, .application-dialog-sticky .MuiTab-root, .application-dialog-sticky .MuiTabs-root {
    min-height: 20px !important;
}

.sticky-bottom-tabs .MuiTab-root, .application-dialog-sticky .MuiTab-root {
    border-left: 1px solid #B7BBC2;
    color: #5b6170;
    text-transform: capitalize;
}

    .sticky-bottom-tabs .MuiTab-root:first-child, .application-dialog-sticky .MuiTab-root:first-child {
        border-left: none;
    }

    .application-dialog-sticky .MuiTab-root.Mui-selected {
        color: var(--primary-color);
        font-weight: 600;
        padding: 6px 10px;
    }

    .sticky-bottom-tabs .MuiTab-root.Mui-selected {
        /*color: rgba(0, 0, 0, 0.87);*/
        color: var(--primary-color);
        background-color: var(--bg-white-color);
        box-shadow: none;
    }

.application-dialog-sticky .MuiTabs-indicator, .sticky-bottom-tabs .MuiTabs-indicator {
    background: none;
}

.application-dialog-sticky .MuiPaper-root {
    padding: 15px 12px;
    background-color: var(--bg-white-color);
}

.documentList .document-link a {
    padding: 5px 3px;
    display: inline-block;
}

.documentList li.MuiListItem-root.MuiListItem-gutters:last-child, .generatedDocList li:last-child {
    border-bottom: none;
}

.documentList li.MuiListItem-root.MuiListItem-gutters, .generatedDocList li {
    border-bottom: 1px solid #ccc;
}

.collapsible-content-section.workflowTab .workflow-section {
    border-bottom: 1px solid #ccc;
    margin-bottom: 15px;
}

.collapsible-content-section.historyTab .col-form-control {
    color: var(--font-color);
    font-size: 14px;
}

    .collapsible-content-section.historyTab .col-form-control label {
        font-size: 14px;
    }

.documentList .document-link p {
    cursor: pointer;
    color: var(--primary-color);
}

.generatedDocList li {
    list-style: none;
    padding-top: 15px;
}

    .generatedDocList li p {
        margin-bottom: 0;
    }

.collapsible-content-section .form-wrapper .form-group .col-form-label {
    color: var(--font-color);
}

.collapsible-content-section .col-form-label, .collapsible-content-section label {
    color: var(--font-color);
}

.workflowTab .col-form-control {
    border-radius: 0px;
    background-color: var(--control-background-color);
    font-size: 14px;
    width: 60%;
    color: rgba(0, 0, 0, 0.87);
}

    .workflowTab .col-form-control input, .workflowTab .col-form-control select {
        padding: 7.5px 14px;
        border-radius: 0px;
        background-color: var(--control-background-color);
        font-size: 14px;
    }

.workflowTab p {
    margin-bottom: 0;
    margin-top: 20px;
}

.workflow-heading .col-form-label + .col-form-label {
    margin-left: 20px;
}

/* Document template CSS*/
.document-template-wrapper {
    border: 1px solid #CAD7DE;
    margin-bottom: 20px;
}

    .document-template-wrapper .form-group .col-form-control {
        min-width: 80%;
        max-width: 80%;
        background-color: #fff;
    }

    .document-template-wrapper label.btn-blue {
        margin: 0;
        /*padding: 12px;*/
        padding: 7px 10px;
        position: absolute;
        right: 1px;
        top: 1px;
    }

.document-template .document-template-heading {
    background-color: #F5F6F8;
    padding: 15px 30px;
    border: 1px solid #CAD7DE;
    border-bottom: 0px;
}

.document-template .form-group .col-form-control {
    min-width: 100%;
}

    .document-template .form-group .col-form-control fieldset:focus {
        border-color: #346BCB !important;
    }

    .document-template .form-group .col-form-control .Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #346BCB !important;
    }

.form-group-upload {
    position: relative;
}

    .form-group-upload .col-form-control input {
        padding: 10px 40% 10px 10px;
    }

    .form-group-upload .btn-blue {
        margin-left: 10px;
    }

.document-template .MuiOutlinedInput-input {
    padding: 10px 14px;
}

.document-template .select-alignment .align-left {
    display: inline-block;
    width: 23px;
    height: 7px;
    vertical-align: top;
    content: "";
    position: absolute;
    top: 1px;
    left: 0px;
    background: #dddddd;
}

.document-template .select-alignment {
    position: relative;
    width: 50px;
    height: 30px;
    margin: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
}

    .document-template .select-alignment .align-right {
        display: inline-block;
        width: 23px;
        height: 7px;
        vertical-align: top;
        content: "";
        position: absolute;
        top: 1px;
        right: 0px;
        background: #dddddd;
    }

    .document-template .select-alignment .align-center {
        display: inline-block;
        width: 23px;
        height: 7px;
        vertical-align: top;
        content: "";
        position: absolute;
        top: 1px;
        left: 25%;
        background: #dddddd;
    }

        .document-template .select-alignment .align-center.active, .document-template .select-alignment .align-left.active, .document-template .select-alignment .align-right.active {
            background-color: var(--primary-color);
        }

.document-section {
    background-color: #F5F6F8;
    padding: 15px 30px;
}

.k-editable-area.ProseMirror {
    position: relative;
    max-height: 210px;
}

div#doc-footer {
    position: absolute;
    width: 100%;
    bottom: 0;
}

.section {
    position: relative;
    display: inline-block !important;
    width: 33% !important;
    height: auto !important;
    padding: 15px;
}

    .section span {
        position: absolute;
        right: 0;
        top: 0;
        cursor: pointer;
    }

    .section .section-img {
        max-width: 100px;
        max-height: 100px;
    }

.closeIcon {
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 32px;
    opacity: 0.3;
}

    .closeIcon:hover {
        opacity: 1;
    }

    .closeIcon:before, .closeIcon:after {
        position: absolute;
        left: 15px;
        content: ' ';
        height: 20px;
        width: 2px;
        background-color: #333;
        top: 5px;
    }

    .closeIcon:before {
        transform: rotate(45deg);
    }

    .closeIcon:after {
        transform: rotate(-45deg);
    }

/**dashboard */
.notification-wrapper {
    /*margin-top: 40px;*/
}

.notification-items > li {
    cursor: pointer;
}

    .notification-items > li > a.active {
        text-decoration: underline !important;
    }

.bold {
    font-weight: bold
}

.templateBody {
    padding: 10px;
    border-bottom: 1px solid #E2E4E7;
}

/* Common */
.clickable {
    cursor: pointer;
}

/*Language Translations CSS*/
.translations-section .form-group .col-form-label {
    word-break: break-all;
}

.page-next-prev {
    padding: 5px 5px;
    text-align: center;
    position: relative;
    display: block;
}

    .page-next-prev:hover {
        text-decoration: none;
        background-color: #e9ecef;
        border-color: #dee2e6;
        text-align: center;
        border-radius: 50%;
    }

.page-item.active .page-link {
    z-index: 1;
    color: #5990FF;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item .page-link {
    color: #383D48;
}

/* steppers */
.current-step span {
    color: var(--primary-color) !important;
    font-weight: bold !important;
}

    .current-step span svg {
        color: var(--primary-color) !important;
    }

.control-disabled .form-wrapper .form-group .col-form-control input, .control-disabled .form-wrapper .form-group .col-form-control textarea, .control-disabled .form-group .col-form-control select, .control-disabled .col-checkbox-control .checkbox-control, .control-disabled .col-radiobox-control .radiobox-control *, .control-disabled .form-group .multiselect-wrapper * {
    cursor: not-allowed;
}

.control-disabled .link-text, .control-disabled .linkText, .control-disabled .page-link, .control-disabled .page-link a, .control-disabled .form-group .page-link a {
    cursor: not-allowed;
}

.control-disabled .multiselect-wrapper .mul-fieldset .col-form-control {
    cursor: not-allowed;
}

    .control-disabled .multiselect-wrapper .mul-fieldset .col-form-control .pills .pill {
        cursor: pointer !important;
    }

.search-group-content .form-wrapper::after {
    content: "";
    background-color: #000;
    position: absolute;
    width: 5px;
    height: 100px;
    top: 10px;
    left: 50%;
    display: block;
}
/* Search Textbox CSS*/
.button-action-section .button-group .search-control {
    position: relative;
    border-radius: 2px;
    background-color: var(--bg-white-color);
    margin-left: 0;
    width: 100%;
    border: 1px solid #b5b2b2;
    padding: 8px 25px;
}

    .button-action-section .button-group .search-control:hover {
        background-color: var(--bg-white-color);
    }

.button-action-section .button-group .search {
    height: 100%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 9;
    color: var(--primary-color);
}

.button-action-section .button-group .searchIcon {
    left: 0px;
    top: 0px;
    min-width: 32px;
    margin-left: 0px;
}

.button-action-section .button-group .clearIcon {
    right: 5px;
    top: 0px;
}

.button-action-section .button-group .advanceSearchIcon {
    right: 0px;
    top: 0px;
    min-width: 32px;
}

.button-action-section .button-group .inputRoot {
    color: inherit;
}

.button-action-section .button-group .searchInput {
    padding: 10px 10px;
    width: 180px;
}

    .button-action-section .button-group .searchInput:focus {
        width: 180px;
    }

.button-action-section .button-group .form-group {
    margin-left: 10px;
}

.col-date-control input {
    min-width: 80px;
}

.col-date-control div:nth-child(2) {
    margin-left: 0px;
}

    .col-date-control div:nth-child(2) > button {
        color: var(--font-color);
    }

.preview-wrapper .preview-list .icon-section button {
    color: var(--font-color);
}

.control-disabled .col-date-control .Mui-disabled {
    color: var(--disable-control-color);
}

.control-disabled .preview-wrapper .preview-list .icon-section button {
    color: var(--disable-control-color);
}

.control-disabled .preview-list .file-preview-name {
    color: var(--disable-control-color);
}

.workflowTab .activity-heading {
    color: var(--font-color);
}

.kendo-error-wrapper th:first-child .k-filtercell .k-button {
    font-size: 32px !important;
}

.date-range-error {
    text-align: right;
}

.MuiPickersToolbar-toolbar, .MuiPickersDay-daySelected {
    background-color: var(--primary-color) !important;
}

.collapse-section {
    margin: 17px 0px 30px;
    border: 1px solid #CAD7DE;
    padding: 10px 10px;
    background-color: var(--bg-color);
    position: relative;
}

.button-action-section .button-group .advanceSearchIcon:focus {
    outline: none !important;
}

.button-action-section .button-group .advanceSearchIcon.active:before, .button-action-section .button-group .advanceSearchIcon.active:after {
    content: '';
    position: absolute;
    bottom: -85%;
    left: 0%;
    width: 0;
    height: 0;
    border-style: solid;
    z-index: 9;
}

.button-action-section .button-group .advanceSearchIcon.active:before {
    border-color: transparent transparent #CAD7DE transparent;
    border-width: 21px;
    left: -2%;
}

.button-action-section .button-group .advanceSearchIcon.active:after {
    border-color: transparent transparent var(--bg-color) transparent;
    border-width: 20px;
}

.collapse-section .search-group-head {
    padding: 0px 20px 0px 8%;
}

    .collapse-section .search-group-head .page-link {
        padding: 10px 10px;
    }
/* Search Textbox CSS*/

/* search-group-content */
.search-group-content .form-wrapper {
    background-color: var(--bg-white-color);
    border: 1px solid #B7BBC2;
    padding: 15px 30px;
    min-height: auto !important;
    width: 92%;
    margin: 10px 20px 10px 6%
}

    .search-group-content .form-wrapper .form-group-search .form-group .lookup-control-renderer {
        padding: 0px 5px;
    }

        .search-group-content .form-wrapper .form-group-search .form-group .lookup-control-renderer .col-form-control {
            padding: 10px 1px;
        }

.search-group-content .form-group-search .form-group {
    margin-bottom: 0px !important;
}

.form-wrapper .form-group-search .form-group .multiselect-wrapper .col-form-control {
    padding: 15px 0px;
}

.form-wrapper .form-group-search .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
    padding: 10px;
}

.page-content-section .form-wrapper .form-group-search .form-group-heading {
    margin-bottom: 15px;
    padding: 2px 15px;
    border-bottom: 1px solid #CAD7DE;
    font-size: 16px;
    font-family: robotomedium, Helvetica, Arial, sans-serif;
}

.page-content-section .alert {
    border-radius: 2px;
    padding: 8px 10px;
}

.form-group-search .form-group-heading .group-title {
    padding: 7px 20px 7px 0px;
}

.form-group-search .form-group-heading .col-switch-control {
    margin: 0px 10%;
}

.form-group-search .form-group .col-date-control {
    margin-top: 0px;
    margin-bottom: 0px;
}

    .form-group-search .form-group .col-date-control > div:after {
        border-bottom: 2px solid #5990ff;
    }

.form-group-search .form-group .col-checkbox-control.Mui-checked {
    color: var(--primary-color);
    margin-top: 2px;
}

.form-group-search .form-group .multiselect-wrapper {
    margin-top: 10px;
    padding: 0px 10px;
}

    .form-group-search .form-group .multiselect-wrapper .mul-dropdown {
        width: 200%;
    }

        .form-group-search .form-group .multiselect-wrapper .mul-dropdown ul li label {
            width: 100%;
            padding: 0px 15px;
        }

.collapse-section .page-link {
    padding: 10px 15px;
    display: inline-block;
    font-size: 14px;
    color: var(--primary-color) !important;
    margin-right: 0px;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

    .collapse-section .page-link:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
        border-radius: 4em;
    }

.search-group-action {
    padding: 0px 20px;
}

    .search-group-action .button-group button {
        margin-right: 10px;
    }

/*Column Switch Control*/
.form-group .col-switch-control {
    padding: 7px 0px;
}

.col-switch-control .switch-control .Mui-checked + .MuiSwitch-track {
    background-color: #5990FF;
}

.col-switch-control .switch-control .Mui-checked {
    color: #5990FF;
}
/*Column Switch Control*/

/*Advanced Search Connector CSS*/
.search-group-content .form-wrapper {
    position: relative;
    padding: 15px 15px !important;
}
    /* vertical line */
    .search-group-content .form-wrapper::after {
        content: '';
        position: absolute;
        background-color: var(--primary-color);
        width: 2px;
        height: 110%;
        top: -10px;
        left: -40px;
    }

    /* horizontal line */
    .search-group-content .form-wrapper::before {
        content: '';
        position: absolute;
        background-color: var(--primary-color);
        width: 3.5%;
        height: 2px;
        top: 50%;
        left: -38px;
    }

    .search-group-content .form-wrapper:only-child::after, .search-group-content .form-wrapper:only-child::before {
        content: none;
    }

    .search-group-content .form-wrapper:first-child::after {
        height: 55%;
        top: 50%;
    }

    .search-group-content .form-wrapper:last-child::after {
        height: 55%;
        top: -6px;
    }

    .search-group-content .form-wrapper:last-child:nth-of-type(2n)::before {
        height: 57%;
        top: -8px;
    }

    /* vertical line */
    .search-group-content .form-wrapper:nth-of-type(2n)::after {
        content: '';
        position: absolute;
        background-color: var(--primary-color);
        width: 3.5%;
        height: 2px;
        left: -38px;
        top: 50%;
    }
    /* horizontal line */
    .search-group-content .form-wrapper:nth-of-type(2n)::before {
        content: '';
        position: absolute;
        background-color: var(--primary-color);
        width: 2px;
        height: 110%;
        top: -10px;
        left: -40px;
    }
/*Advanced Search Connector CSS*/

.text-editor-wrapper .rdw-link-wrapper .rdw-link-modal {
    height: auto;
}

.col-checkbox-control .checkbox-control.Mui-checked {
    color: var(--primary-color);
}

.button-action-section .multiselect-wrapper {
    position: relative;
}

.button-action-section .mul-dropdown {
    width: 100%;
}

    .button-action-section .mul-dropdown .col-form-control input {
        padding: 7px 14px;
    }

    .button-action-section .mul-dropdown .col-form-control {
        max-width: none !important;
        min-width: auto !important;
    }

.button-action-section .button-group .multiselect-control {
    display: inline-block;
    margin-left: 10px;
    margin-bottom: 0px;
}

.pos-header {
    position: absolute;
    top: -1%;
}

.pos-header-fr {
    position: absolute;
    top: 7%;
}

.multiselect-wrapper fieldset.mul-fieldset, .fieldset-wrapper fieldset.mul-fieldset {
    border: 1px solid #B7BBC2;
    top: 0px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    padding: 0;
    position: relative;
    border-width: 1px;
}

.mul-fieldset.mul-error {
    border: 1px solid #f44336 !important;
}

.multiselect-wrapper .mul-fieldset:focus, .fieldset-wrapper .mul-fieldset:focus {
    border: 1px solid #346BCB !important;
}

.multiselect-wrapper legend.mul-legend, .fieldset-wrapper legend.mul-legend {
    margin: 0px 0px 0px 10px;
    max-width: 1000px;
    width: auto;
    min-height: -7px;
    height: 0px;
    display: block;
    padding: 0px 5px;
    font-size: 0.75em;
    position: relative;
    z-index: 15;
    top: -7px;
    /*color: rgba(0, 0, 0, 0.54);*/
    color: var(--page-control-label-color);
}

.mul-fieldset.mul-error .mul-legend {
    color: #f44336 !important;
}

.mul-dropdown {
    width: 100%;
}

.kendo-grid-wrapper .rais-grid-scroll .scrollbar-inner {
    max-height: 65vh !important;
}

    .kendo-grid-wrapper .rais-grid-scroll.scrollbar-inner .scroll-element .scroll-bar {
        height: 100px !important;
    }

.page-left-section .sidebar-section .list-group-section .scrollbar-inner {
    max-height: 60vh !important;
}

.page-left-section .sidebar-workflow .list-group-section .scrollbar-inner {
    max-height: 90vh !important;
}

.page-left-section .sidebar-section .list-group-section .scrollbar-inner .scroll-element .scroll-bar {
    height: 100px !important;
}

.wf-align-right {
    margin-left: 26%;
}

.wf-align-left {
    margin-top: 0%;
    position: absolute;
}

.wf-floating-arrow {
    position: absolute;
    cursor: pointer;
    float: right;
    z-index: 1;
    background-color: #F7F9FA;
    border: 1px solid #D4DBE9;
    color: #346bcb;
}

    .wf-floating-arrow a {
        padding: 7px 7px;
        width: 27px;
        display: block;
    }

    .wf-floating-arrow.expanded {
        left: 24%;
    }

    .wf-floating-arrow.collapsed {
        left: -12px;
    }

.wf-zoom {
    position: absolute;
    right: 0;
    z-index: 10;
}

/* user screen */
.box {
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
    background-color: var(--bg-white-color);
    cursor: pointer;
}

.box-content {
    min-height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.box.active {
    background: var(--primary-color);
    color: #fff
}

    .box.active .box-content .icon svg {
        color: #fff;
    }

.box-content .icon svg {
    font-size: 56px;
    color: #D2DEE4;
}

.box.active .box-content span {
    color: #fff;
}

.box .box-content span {
    font-size: .9em;
    color: var(--primary-color);
}

.box.disabled {
    cursor: not-allowed;
}
/*Wizard Stepper CSS*/
.stepper-wrapper > .MuiStepper-vertical {
    background-color: var(--body-background-color);
}

.stepper-wrapper .stepper-list-active span {
    font-weight: bold !important;
    cursor: pointer;
    color: var(--font-color);
}

    .stepper-wrapper .stepper-list-active span svg {
        color: var(--primary-color) !important;
    }

.stepper-wrapper .stepper-list-active.MuiStep-completed span {
    color: var(--primary-color) !important;
    font-weight: bold !important;
}

.stepper-wrapper .stepper-list.MuiStep-completed svg {
    color: var(--primary-color) !important;
    fill: var(--primary-color) !important;
    border: none !important;
    opacity: 0.8;
}

.stepper-wrapper .stepper-list span {
    color: var(--font-color);
    cursor: pointer;
}

    .stepper-wrapper .stepper-list span svg {
        color: #000;
        fill: #fff;
        border: 1px solid var(--primary-color);
        border-radius: 50%;
    }

        .stepper-wrapper .stepper-list span svg text {
            fill: #4F5664;
        }

.auto-complete-wrapper .auto-complete-list {
    margin-bottom: 0px;
}

.auto-complete-list > li {
    padding: 7px 7px;
    cursor: pointer;
}

    .auto-complete-list > li:hover {
        background-color: #D8E3F8
    }

h4.usr-menu-title {
    font-size: 1.1em !important;
    font-weight: bold !important;
}

.sub-account {
    width: 45%;
    position: absolute !important;
    z-index: 1;
}

    .sub-account .col-form-control {
        min-width: 100%;
        max-width: 100%;
    }

        .sub-account .col-form-control input {
            padding: 10px 14px;
            font-size: 14px;
            line-height: 16px;
        }

        .sub-account .col-form-control fieldset {
            border-radius: 0px;
            border-color: #B7BBC2;
        }

.form-group .note {
    color: var(--primary-color);
    margin-top: 5px;
}

.fd-floating-arrow {
    top: 40%;
    position: fixed;
    left: 26.3%;
}

/*Dashboard Notification CSS*/
#module {
    font-size: 1rem;
    line-height: 1.5;
}

    #module .collapse:not(.show) {
        display: block;
        height: 3rem;
        overflow: hidden;
        margin-bottom: 10px;
    }

    #module .collapsing {
        height: 3rem;
    }

    #module a.collapsed::after {
        content: '+ Show More';
    }

    #module a:not(.collapsed)::after {
        content: '- Show Less';
    }

.dashboard-container .templateBody {
    position: relative;
}

    .dashboard-container .templateBody .col-form-control {
        min-width: 100%;
        max-width: 100%;
    }

    .dashboard-container .templateBody .button-action-section button {
        margin-left: 10px;
        padding: 5px 10px;
    }

    .dashboard-container .templateBody .button-action-section {
        display: none;
        margin-top: -5px;
    }

    .dashboard-container .templateBody:hover .button-action-section {
        display: block;
    }

.dashboard-container .templateRead:before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    -moz-border-radius: 7.5px;
    -webkit-border-radius: 7.5px;
    border-radius: 50%;
    background-color: var(--primary-color);
    position: absolute;
    left: 5px;
    top: 20px;
}

.dashboard-container .templateUnread .template-title {
    min-height: 45px;
}

.dashboard-container .templateUnread .template-title, .dashboard-container .templateUnread #module {
    opacity: 0.8;
}

.template-title {
    font-size: 1rem;
    color: var(--primary-color);
}

#dashboard .page-left-section {
    height: 100%;
}

#dashboard .sticky-sidebar {
    position: -webkit-sticky;
    position: sticky;
    top: 6.5rem;
    z-index: 10;
    height: calc(100vh - 7.5rem);
}

#dashboard .sticky-sidebar-right {
    position: -webkit-sticky;
    position: sticky;
    top: 7rem;
    z-index: 10;
}

#dashboard .dashboard-container .templateHeader {
    position: -webkit-sticky;
    position: sticky;
    top: 5.4rem;
    z-index: 10;
    background-color: var(--body-background-color);
}

.dashboard-container .templateList .templateBody:nth-child(even) {
    /*background-color: #F5F6F8;*/
    background-color: var(--bg-color);
}

.dashboard-container .templateBody {
    padding: 15px 15px 10px 20px;
    border-bottom: 1px solid #E2E4E7;
}

.template-body span[style] {
    font-size: 0.9rem !important;
}

.templateBody .template-title {
    min-height: 45px;
}

.fd-floating-arrow {
    top: 40%;
    position: fixed;
    left: 26.3%;
}

.searchable-dropdown .mul-dropdown {
    padding: 0px;
}

.searchable-dropdown .material-textfield {
    z-index: 12;
    line-height: 1;
}

.searchable-dropdown .mul-input.col-form-control {
    padding: 10px;
}

.searchable-dropdown .col-form-control {
    position: -webkit-sticky;
    position: sticky;
    top: 0px;
    z-index: 1000;
    background-color: var(--bg-white-color);
    padding: 10px;
    width: 100%;
}

.searchable-dropdown ul.ddl {
    padding: 0px 10px;
    overflow-y: scroll;
    max-height: 350px !important;
    margin-bottom: 0px;
}

.searchable-dropdown .pagination {
    position: sticky;
    bottom: 0px;
    z-index: 1000;
    background-color: var(--bg-white-color);
    padding: 10px 10px;
    justify-content: center;
}

    .searchable-dropdown .pagination ul li .Mui-selected {
        background-color: var(--primary-color);
        color: rgb(255, 255, 255) !important;
        text-decoration: none;
        border-color: rgb(0, 123, 255);
    }

    .searchable-dropdown .pagination ul li button {
        color: var(--primary-color);
    }

        .searchable-dropdown .pagination ul li button:hover {
            background-color: var(--primary-color);
            color: rgb(255, 255, 255) !important;
            border-radius: 50%;
        }

.searchable-dropdown .mul-dropdown {
    max-height: inherit;
    overflow-y: auto;
}

.current-executing-step span {
    color: #34cb69 !important;
}

    .current-executing-step span svg {
        color: #34cb69 !important;
    }

.clickable span {
    font-size: 1.026vw;
    color: var(--stepper-step-color);
}

    .clickable span svg {
        font-size: 1.759vw;
        color: var(--stepper-step-color);
    }

        .clickable span svg text {
            fill: var(--bg-white-color);
        }

.wf-stepper-wrapper .MuiStepper-horizontal {
    background-color: var(--body-background-color);
}

.rais-modal .rais-modal-body .MuiStepper-horizontal {
    background-color: var(--bg-color);
}

.wf-stepper-wrapper .MuiStepper-horizontal.clickable span, .wf-stepper-wrapper .MuiStepper-horizontal.clickable span + span.MuiStepLabel-label {
    cursor: pointer;
    color: var(--font-color) !important;
}

.wf-stepper-wrapper .MuiStepper-horizontal .MuiStepLabel-horizontal svg, .wf-stepper-wrapper .MuiStepper-horizontal .MuiStepLabel-horizontal span {
    color: var(--disable-control-color);
}

.wf-stepper-wrapper .MuiStepper-horizontal.clickable span, .wf-stepper-wrapper .MuiStepper-horizontal.clickable span + span.MuiStepLabel-label {
    cursor: pointer;
    color: var(--font-color) !important;
}

.wf-stepper-wrapper .clickable span svg {
    color: var(--primary-color) !important;
}

.wf-stepper-wrapper .clickable span svg {
    color: var(--primary-color) !important;
    opacity: 0.8;
}

.wf-stepper-wrapper .current-step span svg {
    color: var(--primary-color) !important;
    opacity: 1;
}

/*Media query for responsive screens*/
/* Hide/rearrange for smaller screens */
@media screen and (max-width: 767px) {
    /* Hide captions */
    .carousel-caption {
        display: none;
    }

    .wf-info .text-right {
        text-align: left !important;
    }
}

@media screen and (max-width: 1200px) {
    .column-section-wrap .design-section-1 .col-control-label, .column-section-wrap .design-section-2 .col-control-label, .column-section-wrap .design-section-3 .col-control-label {
        max-width: 100% !important;
        flex-basis: 100% !important;
    }

    .column-section-wrap .design-section-1 .col-control-renderer, .column-section-wrap .design-section-2 .col-control-renderer, .column-section-wrap .design-section-3 .col-control-renderer {
        max-width: 100% !important;
        flex-basis: 100% !important;
    }

    .column-section-wrap .column-section .lookup-text {
        left: 0px;
    }

    .column-section-wrap .renderer-section-1 .col-control-label, .column-section-wrap .renderer-section-2 .col-control-label, .column-section-wrap .renderer-section-3 .col-control-label {
        max-width: 100% !important;
        flex-basis: 100% !important;
    }

    .column-section-wrap .renderer-section-1 .col-control-renderer, .column-section-wrap .renderer-section-2 .col-control-renderer, .column-section-wrap .renderer-section-3 .col-control-renderer {
        max-width: 100% !important;
        flex-basis: 100% !important;
    }

    .column-section-wrap .renderer-section-1 .form-group .col-form-label, .column-section-wrap .renderer-section-2 .form-group .col-form-label, .column-section-wrap .renderer-section-3 .form-group .col-form-label {
        padding: 5px 5px !important;
    }

    .form-section-wrapper .design-section-1 .col-control-label {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-1 .col-control-renderer {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-1 .col-control-actions {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-2 .col-control-label {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-2 .col-control-renderer {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-2 .col-control-actions {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-2 .col-lookup-control .lookup-text {
        right: 20%;
    }

    .form-section-wrapper .design-section-3 .col-control-label {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-3 .col-control-renderer {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-section-wrapper .design-section-3 .col-control-actions {
        max-width: 100%;
        flex-basis: 100%;
    }

    .form-render-wrapper .col-flex-1 .col-control-renderer .lookup-control-renderer .col-form-control {
        padding-bottom: 10px;
    }
}

@media (min-width: 1600px) {
    .container {
        width: 90% !important;
        max-width: 90% !important;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .fa {
        font-size: 1.026vw;
    }

    .form-wrapper .form-group .col-form-control svg {
        font-size: 1.759vw;
        top: calc(50% - 0.879vw);
    }

    .form-wrapper .form-group .col-form-control .pills .close-pill-box {
        font-size: 1.466vw !important;
        position: absolute;
        top: 0.339vw;
        right: 0.439vw;
    }
    /*.kendo-grid-wrapper .rais-grid{
        height: 75vh !important;
    }*/
    .scrollbar-inner > .scroll-element.scroll-y {
        width: 0.585vw !important;
    }

    .kendo-grid-wrapper .rais-grid-scroll .scrollbar-inner {
        max-height: 75vh !important;
        padding-right: 1.466vw !important;
    }

    .rais-grid-scroll.scrollbar-inner > .scroll-element.scroll-y {
        width: 1vw !important;
    }

    .rais-grid-scroll.scrollbar-inner > .scroll-element .scroll-bar {
        height: 3.657vw !important;
    }

    .kendo-grid-wrapper .rais-grid-scroll.scrollbar-inner .scroll-element .scroll-bar {
        height: 7.315vw !important;
    }

    .page-left-section .sidebar-section .list-group-section .scrollbar-inner {
        max-height: 70vh !important;
        padding-right: 0.6vw !important;
    }

        .page-left-section .sidebar-section .list-group-section .scrollbar-inner .scroll-element .scroll-bar {
            height: 7.315vw !important;
        }
}

@media screen and (max-width: 767px) {
    .button-action-section .button-group button {
        margin-left: 5px;
        min-width: inherit !important;
    }

    .column-section-wrap .column-section .lookup-text {
        position: relative !important;
        right: 0% !important;
        margin-right: 0px;
    }

    .page-left-section .sidebar-section {
        min-height: inherit;
    }

    .pos-header {
        position: relative;
        top: 0px;
    }

    .document-title-section {
        width: 100%;
    }

    .button-action-section {
        width: 100%;
    }

    .page-content-section .page-full-section .form-wrapper {
        padding: 5% 5%;
    }

    #user .user-form-container .form-render-wrapper .form-section .col-flex-1 {
        padding: 2% 5%;
    }
}

@media (min-width: 1200px) and (max-width: 1366px) {
    .form-signin-heading {
        left: 35% !important;
        top: 22% !important;
    }
}

@media (min-width: 1200px) and (max-width: 1599px) {
    .kendo-grid-wrapper .rais-grid-scroll .scrollbar-inner {
        max-height: 65vh !important;
    }
}

@media (min-width: 1200px) and (max-width: 1599.98px) {
    .container {
        width: 1230px !important;
        max-width: 1230px !important;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .column-section-wrap .column-section {
        padding: 15px 5px !important;
    }

    .form-section-wrapper .design-section-2 .col-control-renderer {
        max-width: 42% !important;
        flex-basis: 42% !important;
    }

    .form-section-wrapper .design-section-2 .col-control-actions {
        max-width: 33% !important;
        flex-basis: 33% !important;
    }
}

@media (min-width: 768px) and (max-width: 992px) {
    .bg-login-image {
        display: none !important;
    }

    .bg-login {
        flex: 0 0 100%;
        max-width: 100%;
    }

    #user .page-right-section .button-action-section {
        margin-bottom: 5%;
    }

    .wf-info .text-right {
        text-align: left !important;
    }
}

@media screen and (max-width: 1024px) {
    .login-heading {
        width: 270px !important;
    }

    .page-content-section .page-full-section .form-wrapper {
        padding: 5% 5% !important;
    }

    #user .box-content {
        min-height: 125px;
    }
}
/*Media query for responsive screens*/

/*Target 4k monitors and bigger screens*/
/*Media query for larger screens*/
@media only screen and (min-width: 1281px) {
    body {
        font-size: 1.026vw;
    }

    .btn-mui {
        font-size: 1.026vw;
        padding: 1.10vw 1.10vw !important;
    }

    .btn-grey, .btn-blue {
        padding: 0.512vw 1.10vw;
        font-size: 1.026vw;
    }

    .button-action-section .button-group button {
        margin-left: 1.10vw;
        min-width: 7.315vw;
    }

    .bg-login-message .bg-heading {
        font-size: 3.372vw;
        margin-bottom: 1.466vw;
        line-height: 3.665vw;
        left: 5px;
    }

    .bg-login-message .bg-message {
        font-size: 2.3460vw;
    }

    .login-heading {
        width: 21vw;
    }

        .login-heading img {
            width: 4.5vw;
        }

        .login-heading .app-logo {
            font-size: 2.346vw;
        }

        .login-heading .app-logo-text {
            font-size: 1.0vw;
            line-height: 1.026vw;
        }

    .login-wrapper .form-control {
        padding: 0.36vw 0.53vw;
        min-height: 3.52vw;
        font-size: 1.173vw;
    }

    .login-wrapper {
        width: 22vw;
        max-width: 22vw;
        min-height: 22vh;
    }

        .login-wrapper .loginTitleSection {
            margin-bottom: 1.10vw;
            padding: 0.513vw 0px;
        }

            .login-wrapper .loginTitleSection .registerTitle {
                font-size: 1.759vw;
            }

        .login-wrapper .nav-tabs {
            padding: 0.733vw 0.733vw;
        }

        .login-wrapper input[type=checkbox], input[type=radio] {
            transform: scale(1.5);
            margin: 5px 5px;
        }

        .login-wrapper .nav-tabs > li > a {
            font-size: 1.319vw;
            padding: 0.733vw 0.733vw;
        }

        .login-wrapper .tab-content {
            min-height: 22vw;
        }

    .loginContentSection .QR-code {
        font-size: 1.026vw;
    }

    .md-form .input-prefix {
        font-size: 1.466vw;
        top: -0.733vw;
    }

    .md-form .fa:before {
        top: 1.759vw;
        right: 0px;
    }

    .md-form.md-outline label.active {
        font-size: 1.173vw;
    }

    .md-form.md-outline input[type="text"] {
        font-size: 1.173vw !important;
    }

        .md-form.md-outline input[type="text"].valid, .md-form.md-outline input[type="text"]:focus.valid, .md-form.md-outline input[type="password"].valid, .md-form.md-outline input[type="password"]:focus.valid {
            font-size: 1.173vw !important;
            line-height: 2.20vw !important;
        }

    .md-form.md-outline > input[type]:-webkit-autofill:not(.browser-default):not([type="search"]) + label, .md-form.md-outline > input[type="time"]:not(.browser-default) + label {
        font-size: 1.173vw;
    }

    .md-form.md-outline.form-lg .form-control.form-control-lg {
        padding: 0.36vw 0.53vw;
        min-height: 3.52vw;
    }

    .md-form.md-outline.form-lg label {
        font-size: 1.173vw !important;
        -webkit-transform: translateY(0.879vw);
        transform: translateY(0.879vw);
        color: #858B97 !important;
    }

    .page-breadcrumb .breadcrumb .breadcrumb-item {
        font-size: 1.026vw;
        min-height: 1.466vw;
    }

    .document-title-section .heading-title {
        font-size: 1.610vw;
    }

    .tab-wrapper .rais-tabs {
        min-height: 3.52vw;
        border-bottom: 0.073vw solid #CAD7DE;
        margin-bottom: 1.466vw;
    }

        .tab-wrapper .rais-tabs .rais-tab {
            font-size: 1.173vw;
            padding: 0.438px 0.879vw;
            min-height: 3.52vw;
        }

    .page-left-section .sidebar-section .list-group-title {
        padding: 0.733vw 0px;
    }

        .page-left-section .sidebar-section .list-group-title h4 {
            font-size: 1.319vw;
        }

    .page-left-section .sidebar-section .list-group .list-group-item a {
        font-size: 1.173vw;
        padding: 0.512vw 1.10vw;
    }

    .page-left-section .sidebar-section .list-group .list-group-item .list-item-text {
        font-size: 1.173vw;
        padding: 0.512vw 1.10vw;
        margin: 0px;
    }

    .page-left-section .sidebar-section.fl-sidebar-section .list-group .list-group-item .list-item-text {
        display: flex;
        width: 100%;
    }

    .page-left-section .sidebar-section.dashboard-sidebar-section .list-group .list-group-item .list-item-text {
        display: flex;
        width: 100%;
        padding-left: 2.5vw;
    }

    .page-left-section .sidebar-section.fl-sidebar-section .list-group .list-item-text > div {
        margin: 0px;
    }

    .page-left-section .sidebar-section .list-group .list-group-item button {
        padding: 0.733vw 0.733vw;
    }

        .page-left-section .sidebar-section .list-group .list-group-item button svg {
            font-size: 1.466vw;
        }

    button svg {
        font-size: 1.466vw !important;
    }

    .rais-error .Mui-error {
        font-size: 0.879vw;
    }

    .current-step span {
        font-size: 1.026vw;
        color: var(--primary-color) !important;
    }

        .current-step span svg {
            font-size: 1.759vw;
        }

    .clickable span {
        font-size: 1.026vw;
        color: var(--font-color) !important;
    }

    .current-step.clickable span {
        color: var(--primary-color) !important;
    }

    .clickable span svg {
        font-size: 1.759vw;
        color: var(--stepper-step-color);
    }

        .clickable span svg text {
            fill: var(--bg-white-color);
        }

    .list-group .list-group-item svg {
        font-size: 1.466vw;
    }

    .sidebar-section .list-group-title svg {
        font-size: 1.466vw;
    }

    #form-designer .section-wrapper {
        padding: 1.10vw 2.20vw;
        border-top: 1.10vw solid #fff;
        position: relative;
    }

    #form-designer .form-wrapper .form-section {
        padding: 1.10vw 0px;
    }

    #form-designer .form-wrapper .column-section-wrap {
        padding: 1.10vw 1.10vw;
    }

    #form-designer .column-section-wrap .column-section {
        min-height: 14.630vw;
    }

    #form-designer .column-section-wrap .design-section-1 .col-control-renderer, #form-designer .column-section-wrap .design-section-2 .col-control-renderer, #form-designer .column-section-wrap .design-section-3 .col-control-renderer {
        max-width: 83.333333% !important;
        flex-basis: 83.333333% !important;
    }

    .kendo-grid-wrapper .k-grid-header .k-header .k-link .grid-icon .fa-filter {
        font-size: 1.466vw;
        margin-top: 3px;
    }

    .kendo-grid-wrapper .k-icon {
        font-size: 1.173vw;
    }

    .export-icon svg {
        font-size: 1.466vw;
    }

    .form-title-section .heading-title {
        font-size: 1.319vw;
        margin-bottom: 1.828vw;
    }

    .form-wrapper .form-group .page-link {
        font-size: 1.026vw;
    }

        .form-wrapper .form-group .page-link > a {
            font-size: 1.026vw;
        }

    #permission-restriction .form-wrapper .form-group .col-form-control label, #add-edit-functionalRolePermission .form-wrapper .form-group .col-form-control label, #securityprofile .form-wrapper .form-group .col-form-control label, #webservice_account .form-wrapper .form-group .col-form-control label, #add-entity .form-wrapper .form-group .col-form-control label, #application-settings .form-wrapper .form-group .col-form-control label, .form-wrapper .form-group .col-form-control label {
        font-size: 1.173vw;
    }

    #application-settings .form-wrapper .form-group-section .form-group-heading, #manageWorkflow .form-wrapper .form-group-section .form-group-heading, #add-entity .form-wrapper.ran-form-wrapper .form-group-section .form-group-heading {
        padding: 0.733vw 0px;
        margin-bottom: 1.10vw;
        font-size: 1.173vw;
    }

    .form-wrapper .form-group .col-form-control input, .form-wrapper .form-group .col-form-control textarea, .form-group .col-form-control select {
        padding: 0.768vw 1.026vw;
        font-size: 1.026vw;
        line-height: 1.173vw;
    }

    .form-group .col-form-control select {
        padding: 0.768vw 2.346vw 0.768vw 1.026vw;
    }

    .form-wrapper .form-group {
        margin-bottom: 0.879vw !important;
    }

        .form-wrapper .form-group .col-radiobox-control .radiobox-control span {
            font-size: 1.173vw;
        }

            .form-wrapper .form-group .col-radiobox-control .radiobox-control span svg {
                font-size: 1.173vw;
            }

        .form-render-wrapper .column-section .form-group .col-form-control label, .form-wrapper .form-group .col-form-control label, .col-form-control label {
            font-size: 1.173vw;
        }

    .form-group .col-form-control fieldset legend {
        font-size: 0.90vw;
    }

    .kendo-editor-wrapper .k-toolbar .k-button {
        padding: 1.026vw 1.173vw;
    }

    .kendo-editor-wrapper .k-toolbar .k-dropdown {
        width: 10.971vw;
    }

    .kendo-editor-wrapper .k-dropdown-wrap .k-input {
        font-size: 1.173vw;
        padding: 1.026vw 1.173vw;
    }

    .kendo-editor-wrapper .k-toolbar .k-button .k-icon {
        font-size: 1.319vw;
    }

    .multiselect-wrapper .mul-input {
        min-height: 2.487vw;
        padding: 1.319vw 0.7rem;
    }

    .multiselect-wrapper legend.mul-legend, .fieldset-wrapper legend.mul-legend {
        padding: 0px 0.365vw;
        font-size: 0.879vw;
        top: -0.512vw;
        height: 2px;
    }

    .page-content-section .form-wrapper {
        min-height: 32.918vw;
    }

    .form-wrapper-center .no-record-text {
        font-size: 1.173vw;
    }

    .form-wrapper .form-group .col-form-label {
        font-size: 1.026vw;
    }

    .form-wrapper .form-group .col-checkbox-control .checkbox-control + span {
        font-size: 1.026vw;
    }

    .form-wrapper .form-group .col-radiobox-control .radiobox-control + span {
        font-size: 1.026vw;
    }

    #permission-restriction .rais-modal-dialog .rais-modal-body .button-action-section {
        margin-top: 1.466vw;
        min-height: 2.633vw;
    }

    .down-arrow:after {
        position: absolute;
        top: 1.10vw;
        right: 0.733vw;
        border-left: 0.365vw solid transparent;
        border-right: 0.365vw solid transparent;
        /*border-top: 0.365vw solid rgba(0, 0, 0, 0.54);*/
        border-top: 0.365vw solid var(--select-dropdown-icon);
    }

    .pills .pill {
        font-size: 0.879vw;
        max-width: 18.28vw;
    }

    .close-pill-box {
        font-size: 1.466vw !important;
        position: absolute;
        top: 0.739vw;
        right: 0.739vw;
    }

    .multi-pill .close-pill-box {
        top: 0.439vw;
        right: 0.439vw;
    }

    .rais-modal-header .rais-modal-header-text {
        font-size: 1.466vw;
        color: var(--page-heading-color);
    }

    .rais-modal-header .modal-hd-btn {
        padding: 0.581vw 0.733vw;
        font-size: 1.319vw !important;
    }

    .kendo-grid-wrapper .k-grid-header .k-header .k-link {
        font-size: 1.173vw;
    }

    .kendo-grid-wrapper .k-filtercell .k-filtercell-wrapper > .k-textbox {
        font-size: 1.026vw;
        font-weight: 500;
    }

    .kendo-grid-wrapper .k-grid .k-grid-content .k-grid-norecords td {
        font-size: 1.026vw;
    }

    .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td {
        padding: 0.584vw 1.759vw;
        font-size: 1.026vw;
    }

    .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control .col-date-control, .kendo-error-wrapper .k-filter-row .k-filtercell label,
    .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control > div:nth-child(2), .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control fieldset, .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control svg {
        color: #fff !important;
        border-color: #fff;
        opacity: 0.7;
    }

    .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control:first-child {
        margin-right: 1px;
    }

    .kendo-error-wrapper .k-filter-row .k-filtercell .col-date-control input {
        padding: 10px 0px 10px 10px;
        font-size: 14px;
    }

    .document-title-section {
        margin: 1.10vw 0px;
    }

    .document-template-wrapper .document-section .btn-blue {
        min-width: 7.315vw;
    }

    #manageWorkflow .page-right-section .button-action-section {
        top: -5.486vw;
    }

    .wf-align-right {
        margin-left: 26%;
    }

    .wf-align-left {
        margin-top: 0%;
        position: absolute;
    }

    .wf-floating-arrow {
        position: absolute;
        cursor: pointer;
        float: right;
        z-index: 1;
        background-color: #F7F9FA;
        border: 1px solid #D4DBE9;
        color: var(--primary-color);
    }

        .wf-floating-arrow a {
            padding: 0.512vw 0.512vw;
            width: 1.975vw;
            display: block;
        }

        .wf-floating-arrow.expanded {
            left: 24%;
        }

        .wf-floating-arrow.collapsed {
            left: -12px;
        }

    .fd-floating-arrow {
        top: 50%;
        position: fixed;
        left: 26.3%;
    }

        .fd-floating-arrow.expanded {
            left: 26.3%;
        }

        .fd-floating-arrow.collapsed {
            left: 3.4%;
        }

    .wf-zoom {
        position: absolute;
        right: 0;
        z-index: 10;
    }

    .non-draggable {
        cursor: not-allowed !important;
    }

    .draggable {
        cursor: move !important;
    }
    /* user screen */
    .sub-account .col-form-control input {
        padding: 0.568vw 1.026vw;
        font-size: 1.026vw;
        line-height: 1.173vw;
    }

    .box-content {
        min-height: 10.97vw;
    }

        .box-content .icon svg {
            font-size: 4.096vw;
            color: #D2DEE4;
        }

    .box .box-content span {
        font-size: 1.026vw;
        color: var(--primary-color);
    }

    .box.disabled {
        cursor: not-allowed;
    }

    #user .search-wrapper {
        min-height: 14.630vw;
    }
    /*Wizard Stepper CSS*/
    .stepper-wrapper .stepper-list-active span {
        font-size: 1.026vw;
    }

        .stepper-wrapper .stepper-list-active span svg {
            color: var(--primary-color) !important;
            font-size: 1.759vw;
        }

    .stepper-wrapper .stepper-list.MuiStep-completed svg {
        font-size: 1.759vw;
    }

    .stepper-wrapper .stepper-list span {
        font-size: 1.026vw;
    }

        .stepper-wrapper .stepper-list span svg {
            font-size: 1.759vw;
        }

    .stepper-wrapper .MuiStepConnector-vertical {
        padding: 0 0 0vw;
        margin-left: 0.879vw;
    }

    .stepper-wrapper .stepper-list span svg text {
        fill: #4F5664;
    }

    .auto-complete-wrapper .auto-complete-list {
        margin-bottom: 0px;
    }

    .auto-complete-list > li {
        padding: 7px 7px;
        cursor: pointer;
    }

        .auto-complete-list > li:hover {
            background-color: #D8E3F8
        }

    h4.usr-menu-title {
        font-size: 1.1em !important;
        font-weight: bold !important;
    }

    .sub-account {
        width: 45%;
        position: absolute !important;
        z-index: 1;
    }

        .sub-account .col-form-control {
            min-width: 100%;
            max-width: 100%;
        }

            .sub-account .col-form-control input {
                padding: 0.568vw 1.026vw;
                font-size: 1.026vw;
                line-height: 1.173vw;
            }

            .sub-account .col-form-control fieldset {
                border-radius: 0px;
                border-color: #B7BBC2;
            }

    .form-group .note {
        color: var(--primary-color);
        margin-top: 5px;
    }

    /*WF stepper CSS*/

    .wf-stepper-wrapper .MuiStepper-horizontal .MuiStepLabel-horizontal svg, .wf-stepper-wrapper .MuiStepper-horizontal .MuiStepLabel-horizontal span {
        color: var(--disable-control-color);
    }

    .wf-stepper-wrapper .MuiStepper-horizontal.clickable span, .wf-stepper-wrapper .MuiStepper-horizontal.clickable span + span.MuiStepLabel-label {
        cursor: pointer;
        color: var(--font-color) !important;
    }

    .wf-stepper-wrapper .clickable span svg {
        color: var(--primary-color) !important;
    }

    .wf-stepper-wrapper .clickable span svg {
        color: var(--primary-color) !important;
        opacity: 0.8;
    }

    .wf-stepper-wrapper .current-step span svg {
        color: var(--primary-color) !important;
        opacity: 1;
    }

    .wf-stepper-wrapper .current-executing-step span svg {
        color: #34cb69 !important;
        opacity: 1;
    }

    .current-executing-step span {
        color: #34cb69 !important;
    }

    .wf-stepper-wrapper .clickable .stepper-label span + span {
        opacity: 1;
    }

    .note {
        font-size: 1.026vw;
    }

    .text-small.sub-acc-note {
        font-size: 1.026vw;
        margin-top: 15%;
    }

    .inventory-note {
        position: relative;
        width: 100%;
        text-align: center;
    }

    .active-query-builder-wrapper .col-form-control, .active-query-builder-wrapper .col-form-control {
        padding: 0.500vw 1.026vw;
    }

    .query-tab .tab-content .tab-pane:first-child .md-form.md-outline label {
        -webkit-transform: translateY(-1.759vw) scale(0.8) !important;
        transform: translateY(-1.759vw) scale(0.8) !important;
        z-index: 9;
    }

    #querybuilderform .page-right-section .button-action-section {
        position: absolute;
        top: -5.486vw;
    }

    .kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td svg {
        font-size: 1.466vw;
    }

    .kendo-grid-wrapper .k-pager-wrap .k-pager-info {
        font-size: 1.026vw;
    }

    .collapse-section .page-link {
        font-size: 1.026vw;
        padding: 0.733vw 0.733vw;
    }

    .page-link svg {
        font-size: 1.466vw !important;
    }

    .page-content-section .form-wrapper .form-group-search .form-group-heading {
        margin-bottom: 1.10vw;
        padding: 2px 1.10vw;
        font-size: 1.173vw;
    }

    .button-action-section .button-group .search-control {
        padding: 0.585vw 1.828vw;
    }

    .button-action-section .button-group .searchInput, .button-action-section .button-group .searchInput:focus {
        padding: 0.733vw 0.733vw;
        width: 13.16vw;
        font-size: 1.026vw;
    }

    .button-action-section .button-group .searchIcon {
        min-width: 2.346vw;
    }

    .button-action-section .button-group .advanceSearchIcon {
        min-width: 2.346vw;
    }

    .collapse-section {
        margin: 1.24vw 0px 2.20vw;
        padding: 0.733vw 0.733vw;
    }

    .button-action-section {
        margin: 0.733vw 0px 0.733vw;
    }

    .search-group-content .form-wrapper::before {
        height: 0.146vw;
        top: 50%;
        left: -2.77vw;
    }

    .search-group-content .form-wrapper::after {
        width: 0.146vw;
        left: -2.926vw;
    }

    .search-group-content .form-wrapper:nth-of-type(2n)::before {
        width: 0.146vw;
        top: -8px;
        left: -2.926vw;
    }

    .search-group-content .form-wrapper:nth-of-type(2n)::after {
        height: 0.146vw;
        left: -2.77vw;
    }

    #dashboard .sticky-sidebar {
        top: 9.807vw;
        z-index: 10;
        height: calc(100vh - 10.778vw);
    }

    #dashboard .dashboard-container .templateHeader {
        /*top: 6.050vw;*/
        top: 8.5vw;
    }
}

/*Media Queries End*/

/*Query Parameter CSS*/
.query-tab .tab-wrapper .rais-tabs .rais-tab {
    min-height: 2.52vw;
    padding: 10px 20px;
}

.query-tab .tab-wrapper .rais-tabs {
    min-height: 2.52vw;
}

    .tab-wrapper .nav-tabs > li > a.active, .tab-wrapper .nav-tabs > li > a.active:hover, .query-tab .tab-wrapper .rais-tabs li.active a {
        border: 0px;
        color: var(--primary-color);
        border-bottom: 3px solid var(--primary-color);
    }

/*md CSS for controls*/
.md-form.md-outline {
    position: relative;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.query-tab .tab-content .tab-pane:first-child .md-form.md-outline {
    margin-top: 0px;
    margin-bottom: 0px;
}

    .query-tab .tab-content .tab-pane:first-child .md-form.md-outline label {
        -webkit-transform: translateY(-23px) scale(0.8);
        transform: translateY(-23px) scale(0.8);
        z-index: 9;
    }

.md-form.md-outline .form-control {
    padding: 0.36vw 0.53vw;
    min-height: 2.75vw;
    border-radius: 0px;
}

    .md-form.md-outline .form-control:focus {
        box-shadow: none;
        border: 2px solid var(--primary-color);
    }

.active-query-builder-wrapper .col-form-control:focus {
    border: 2px solid var(--primary-color);
    outline: none;
}

.active-query-builder-wrapper .md-form:focus label {
    color: var(--primary-color) !important;
}

.active-query-builder-wrapper .md-form.form-error .col-form-control {
    border: 1px solid #f44336;
}

.md-form.md-outline label {
    font-size: 1.173vw !important;
    -webkit-transform: translateY(0.879vw);
    transform: translateY(0.879vw);
    color: #858B97 !important;
}

    .md-form.md-outline label.active, .md-form.md-outline.md-select label {
        left: 8px;
        padding-right: 5px;
        padding-left: 5px;
        font-weight: 500;
        background: #fff;
        -webkit-transform: translateY(-13px) scale(0.8);
        transform: translateY(-13px) scale(0.8);
    }

.query-tab .md-form.md-outline label {
    left: 8px;
    padding-right: 5px;
    padding-left: 5px;
    font-weight: 500;
    background: var(--body-background-color);
    color: var(--primary-color) !important;
    -webkit-transform: translateY(-13px) scale(0.8) !important;
    transform: translateY(-13px) scale(0.8) !important;
}

.md-form.md-outline label {
    position: absolute;
    top: 0;
    left: 0;
    padding-left: 10px;
    font-size: 1rem;
    color: #757575;
    cursor: text;
    -webkit-transition: color .2s ease-out, -webkit-transform .2s ease-out;
    transition: color .2s ease-out, -webkit-transform .2s ease-out;
    transition: transform .2s ease-out, color .2s ease-out;
    transition: transform .2s ease-out, color .2s ease-out, -webkit-transform .2s ease-out;
    -webkit-transform: translateY(9px);
    transform: translateY(9px);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
}

.md-form > label {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.md-form.md-outline label.active, .md-form.md-outline.md-select label {
    font-size: 1.1rem;
    -webkit-transform: translateY(-14px) scale(0.8);
    transform: translateY(-14px) scale(0.8);
}

.query-tab .fieldset-wrapper {
    margin: 15px 0px;
}

.fieldset-wrapper .mul-fieldset:focus {
    border: 1px solid #346BCB !important;
}

fieldset:focus > legend {
    color: #346BCB !important;
}

.query-tab .mul-input {
    width: 100%;
    padding: 0px;
    min-height: 36px;
}

    .query-tab .mul-input select.form-control {
        padding: 0.568vw 1.026vw;
    }

    .query-tab .mul-input .form-control {
        padding: 0.768vw 1.026vw;
        font-size: 1.026vw;
        line-height: 1.173vw;
        border-radius: 0px;
        background-color: var(--control-background-color);
        color: var(--font-color);
        border: 0px;
        height: auto;
    }

        .query-tab .mul-input .form-control:focus {
            box-shadow: none;
        }
/*Query Parameter CSS*/

fieldset.pending > div {
    margin-top: 15px;
}

fieldset.pending div.control-renderer {
    justify-items: center;
    display: flex;
    flex-direction: column;
}

fieldset.pending > legend {
    margin-left: 10px;
    font-size: large;
    color: var(--primary-color);
}

fieldset.pending div.document-format > span {
    color: #346BCB;
    font-size: small;
    font-weight: 800
}

.pending-form div.comment .col-form-control {
    width: 84vw
}

/* Document template */
.documentTemplate {
    /*min-height: 800px;*/
    margin-top: 20px;
    margin-bottom: 30px;
    width: 90vw;
}

.documentTemplate__header {
    position: relative;
    top: 0px;
    max-height: 100px;
    border-bottom: 1px dashed #CAD7DE;
}

#document-template table {
    border-collapse: collapse;
    border: 1px solid rgb(198,198,198);
}

#document-template td, #document-template th {
    border-collapse: collapse;
    border: 1px solid rgb(198,198,198);
    width: 120px;
}

#document-template .kendo-editor-wrapper {
    border-top: 0px;
}

#document-template .form-group .col-form-control fieldset {
    border-radius: 0px;
    border-color: #B7BBC2;
}

#document-template .form-group .col-form-control {
    min-width: 100%;
    max-width: 100%;
    display: inline-flex;
    flex-direction: column;
}

    #document-template .form-group .col-form-control .Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #346BCB;
    }

.document-format .col-file-control {
    background-color: var(--bg-white-color);
    font-size: 14px;
    line-height: 16px;
    width: 100%;
    cursor: pointer;
    min-width: 100%;
    max-width: 100%;
    margin-right: 5px;
}

    .document-format .col-file-control .page-link {
        padding: 5px 7px;
    }

        .document-format .col-file-control .page-link:hover {
            background-color: var(--bg-white-color);
        }

.documentTemplate .kendo-editor-wrapper .k-toolbar {
    width: 100%;
    max-height: 100px;
    box-shadow: none;
    border-bottom: 1px solid #DCDCDC;
}

.kendo-editor-wrapper .k-editor-content {
    color: var(--font-color);
    background-color: var(--bg-white-color);
}

.kendo-editor-wrapper .k-toolbar {
    background-color: var(--bg-white-color);
}

    .kendo-editor-wrapper .k-toolbar .k-button {
        color: var(--font-color);
    }

        .kendo-editor-wrapper .k-toolbar .k-button.k-state-disabled {
            color: var(--disable-control-color);
        }

    .kendo-editor-wrapper .k-toolbar .k-button-group .k-widget, .kendo-editor-wrapper .k-toolbar .k-button-group .k-widget span {
        color: var(--font-color);
    }

    .kendo-editor-wrapper .k-toolbar .k-picker-wrap, .kendo-editor-wrapper .k-toolbar .k-dropdown-wrap,
    .kendo-editor-wrapper .k-toolbar .k-picker-wrap, .kendo-editor-wrapper .k-toolbar .k-dropdown-wrap .k-input {
        color: var(--font-color);
    }

.documentTemplate__footer {
    /*position: absolute;*/
    position: relative;
    bottom: 0px;
    max-height: 100px;
    border-top: 0px !important;
}

.documentTemplate__headerEmptyColumn,
.documentTemplate__footerEmptyColumn {
    /*background-color: #F7F9FA;*/
}

.documentTemplate__header,
.documentTemplate__footer {
    display: flex;
    justify-content: center;
    min-height: 102px;
    width: 100%;
    z-index: 1;
    border-left: 1px solid #CAD7DE;
    border-bottom: 1px solid #CAD7DE;
    border-right: 1px solid #CAD7DE;
    border-top: 1px solid #CAD7DE;
}

.documentTemplate__editor {
    position: relative;
}

.kendo-editor-wrapper.k-editor .k-editable-area {
    height: 450px !important;
}

.pending-form div.comment .col-form-control {
    width: 84vw
}

.documentTemplate__headerLeft,
.documentTemplate__headerCenter,
.documentTemplate__headerRight,
.documentTemplate__footerLeft,
.documentTemplate__footerCenter,
.documentTemplate__footerRight {
    flex: 0.333;
    position: relative;
    border-right: 1px solid #CAD7DE;
    cursor: pointer;
    background-color: #F7F9FA;
}

    .documentTemplate__headerLeft.active,
    .documentTemplate__headerCenter.active,
    .documentTemplate__headerRight.active,
    .documentTemplate__footerLeft.active,
    .documentTemplate__footerCenter.active,
    .documentTemplate__footerRight.active {
        background-color: #ffffff;
        text-align: center;
    }

.documentTemplate__headerRight,
.documentTemplate__footerRight {
    border-right: none;
}

.documentTempalte__moreOption {
    position: absolute;
    right: 0;
    cursor: pointer
}

.textBox {
    width: 50vw;
}

.plus__icon {
    font-size: 3em !important;
    color: grey;
    left: 200px;
    top: 20px;
    position: absolute !important;
}

.k-editor .k-content {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    border: 0;
    background: none;
    display: block;
    margin-top: 100px;
}

.k-editor-content > div.ProseMirror {
    height: 483px !important;
    border: none;
    overflow-y: auto;
    padding: 10px 10px;
}

    .k-editor-content > div.ProseMirror::after focus {
        border: none;
    }

.details__option.form-group {
    display: flex;
    align-items: center;
    justify-content: center;
}

.details__optionAlignment div.details__boxes {
    display: flex;
    align-items: center;
}

.details__optionAlignment h4 {
    font-size: 1rem;
    margin-top: 10px;
}

.details__optionAlignment div.box {
    border: 1px solid grey;
    padding: 20px;
    margin-right: 5px;
    margin-top: 10px;
    align-items: center;
    position: relative;
    cursor: pointer
}

.details__optionAlignment .box.active > div {
    background-color: #fff;
}

.details__optionAlignment div.left {
    background-color: #346BCB;
    position: absolute;
    height: 10px;
    width: 20px;
    left: 0;
    top: 0;
}

.details__optionAlignment div.center {
    background-color: #346BCB;
    position: absolute;
    height: 10px;
    width: 20px;
    left: 10px;
    top: 0;
}

.details__optionAlignment div.right {
    background-color: #346BCB;
    position: absolute;
    height: 10px;
    width: 20px;
    right: 0;
    top: 0;
}

.documentTempalte__moreOption div.menu {
    padding: 20px;
    position: absolute;
    top: 30px;
    right: 3px;
    width: auto;
    min-width: 120px;
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid grey;
    text-align: left;
}

.pull-top-5 {
    position: absolute;
    margin-top: -5%;
    right: 0;
}

div.menu .menu__option {
    font-size: 16px;
    color: #346BCB;
    font-weight: 500;
}

img.img {
    height: 100px;
    width: 100%;
    object-fit: contain;
}

.doc_preview div img {
    height: 120px;
    width: 120px;
    object-fit: contain;
    border: none;
}

.doc_preview {
    display: flex;
    align-items: center;
    justify-content: center
}

    .doc_preview div {
        width: 140px;
        flex: 3.3
    }

        .doc_preview div.header_left, .doc_preview div.footer_left {
            width: 33%;
            text-align: left;
        }

        .doc_preview div.header_center, .doc_preview div.footer_center {
            width: 33%;
            text-align: center;
        }

        .doc_preview div.header_right, .doc_preview div.footer_right {
            width: 33%;
            text-align: right;
        }

.header_footer_wrapper {
    border: 1px solid #DCDCDC;
    cursor: pointer;
    background-color: var(--bg-white-color);
    position: relative;
}

    .header_footer_wrapper .btn-groups {
        position: absolute;
        right: 10px;
        top: 10px;
        width: 30px;
        z-index: 10;
        display: flex;
    }

        .header_footer_wrapper .btn-groups button {
            margin-right: 15px;
            position: relative;
            right: 0px;
            z-index: 11;
        }

    .header_footer_wrapper a {
        color: #346BCB;
    }

    .header_footer_wrapper .options {
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: space-evenly;
        width: 50%;
        margin: 0 auto;
    }

.small-box {
    height: auto;
    color: var(--primary-color);
    opacity: .7;
    padding: 30px;
}

.header_footer_wrapper:hover .options .small-box {
    opacity: 1;
    font-weight: 600;
}

.header_footer_wrapper .k-editor-content {
    max-height: 150px !important;
    overflow-y: auto;
}

    .header_footer_wrapper .k-editor-content > div.ProseMirror {
        min-height: 150px !important;
        overflow-y: auto;
        max-height: inherit !important;
    }

.searchable-dropdown ul.ddl > li {
    font-size: 0.9em;
    padding: 5px;
    cursor: pointer;
}

    .searchable-dropdown ul.ddl > li:hover {
        background-color: var(--primary-color);
        color: #fff
    }

.preview_wrapper {
    background: white;
    color: var(--black-font-color);
    display: block;
    margin: 0 auto;
    margin-bottom: 0.5cm;
    box-shadow: 0 0 0.1cm rgba(0,0,0,0.5);
    padding: 5px 7px;
    position: relative;
}

    .preview_wrapper div:last-child {
        position: sticky;
        top: 100%;
    }

    .preview_wrapper[data-size="A4"] {
        height: 100%;
        min-height: 29.7cm;
    }

        .preview_wrapper[data-size="A4"][data-layout="portrait"] {
            height: 100%;
            min-height: 21cm;
        }

@page {
    size: A4;
    margin: 0;
    size: 7in 9.25in;
}

@media print {
    html, body {
        width: 210mm;
        height: 297mm;
    }

    .page {
        margin: 0;
        border: initial;
        border-radius: initial;
        width: initial;
        min-height: initial;
        box-shadow: initial;
        background: initial;
        page-break-after: always;
    }

    .preview_wrapper {
        margin: 0;
        box-shadow: 0;
    }
}

a.link {
    cursor: pointer;
    color: #346BCB;
}

/*Kendo Sticky Grid CSS*/
.k-sticky,
.k-pos-sticky {
    position: sticky;
}

.k-grid-header .k-header.k-grid-header-sticky,
.k-grid-header .k-filter-row .k-grid-header-sticky,
.k-grid-content-sticky,
.k-grid-row-sticky,
.k-grid-footer-sticky {
    position: sticky;
    z-index: 2;
}

    .k-grid-header .k-header.k-grid-header-sticky.k-edit-cell,
    .k-grid-header .k-filter-row .k-grid-header-sticky.k-edit-cell,
    .k-grid-content-sticky.k-edit-cell,
    .k-grid-row-sticky.k-edit-cell,
    .k-grid-footer-sticky.k-edit-cell {
        overflow: visible;
        z-index: 3;
    }

    .k-grid-content-sticky.k-grid-row-sticky {
        z-index: 3;
    }

.k-grid .k-grid-header-sticky,
.k-grid .k-grid-content-sticky,
.k-grid .k-grid-footer-sticky {
    border-right-width: 1px;
}

    .k-grid .k-grid-header-sticky:not([style*="display: none"]) + td,
    .k-grid .k-grid-header-sticky:not([style*="display: none"]) + th,
    .k-grid .k-grid-content-sticky:not([style*="display: none"]) + td,
    .k-grid .k-grid-content-sticky:not([style*="display: none"]) + th,
    .k-grid .k-grid-footer-sticky:not([style*="display: none"]) + td,
    .k-grid .k-grid-footer-sticky:not([style*="display: none"]) + th {
        border-left-width: 0;
    }

.k-grid-header-sticky.k-header.k-grid-no-left-border.k-first {
    border-left-width: 0;
}

.k-master-row .k-grid-content-sticky {
    background-color: var(--bg-white-color);
}

.k-master-row .k-grid-row-sticky {
    background-color: var(--bg-white-color);
}

.k-master-row.k-alt .k-grid-content-sticky,
.k-master-row.k-alt .k-grid-row-sticky {
    background-color: var(--bg-white-color);
}

.k-master-row.k-state-selected .k-grid-content-sticky,
.k-master-row.k-state-selected .k-grid-row-sticky {
    background-color: whitesmoke;
}

.k-master-row.k-state-selected.k-alt .k-grid-content-sticky,
.k-master-row.k-state-selected.k-alt .k-grid-row-sticky {
    background-color: whitesmoke;
}

.k-master-row:hover .k-grid-content-sticky,
.k-master-row:hover .k-grid-row-sticky,
.k-master-row.k-state-hover .k-grid-content-sticky,
.k-master-row.k-state-hover .k-grid-row-sticky {
    background-color: var(--grid-hover-color);
}

.k-master-row.k-state-selected:hover .k-grid-content-sticky,
.k-master-row.k-state-selected:hover .k-grid-row-sticky,
.k-master-row.k-state-selected.k-state-hover .k-grid-content-sticky,
.k-master-row.k-state-selected.k-state-hover .k-grid-row-sticky {
    /*background-color: #F7F9FA;*/
    background-color: var(--grid-hover-color);
}

.k-grouping-row .k-grid-content-sticky {
    background-color: var(--bg-white-color);
}
/*Kendo Sticky Grid CSS*/
a.link {
    cursor: pointer;
    color: var(--primary-color);
}

    a.link:hover {
        cursor: pointer;
        color: var(--primary-color);
    }

/* Tooltip Info icon CSS */
span.control {
    display: flex;
    align-items: center;
}

svg.helpText {
    color: var(--theme-color);
    font-size: 1.2rem;
    position: absolute;
    top: 7px;
    right: -25px;
}

.column-section-wrap .renderer-section-2 .col-control-renderer svg.helpText, .column-section-wrap .renderer-section-3 .col-control-renderer svg.helpText {
    top: 7px;
    right: -21px;
}
/* Tooltip Info icon CSS */

.theme-dark .app-logo {
    filter: brightness(10);
}

.wf-info {
    background-color: var(--list-hover-color);
    color: #545b6a;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

    .wf-info .col-form-control label {
        color: var(--primary-color);
    }

.import-grid-wrapper .import-grid-header {
    background-color: var(--grid-heading-color);
    color: #fff;
    padding: 0px 20px;
    border-bottom: 0px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}

.import-grid-wrapper .import-grid-content {
    border: 1px solid #CAD7DE;
    box-sizing: inherit;
    background-color: var(--bg-color);
    padding: 15px 20px;
}

.hide_aqb_lookup {
    display: none;
}

#file-library .button-action-section .form-group .col-form-control {
    max-width: 250px;
    min-width: 250px;
}

#file-library .button-action-section .button-group .multiselect-control {
    max-height: 25px;
    margin-bottom: 10px;
}
.k-column-resizer {
    width: 0.2em !important;
    height: 1000%;
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    cursor: col-resize;
}

td.selectedCell {
    background-color: var(--primary-color);
    opacity: 0.6;
    color:#fff
}


#entityRecordAuditLog .k-grid-header .k-header {
    width: 220px !important;
}

#entityRecordAuditLog.kendo-grid-wrapper .k-grid .k-grid-content .k-master-row td {
    width: 220px !important;
}

#entityRecordAuditLog.kendo-grid-wrapper .k-grid .k-grid-content {
    overflow-x: scroll;
}

#entityRecordAuditLog.kendo-grid-wrapper .k-grid .k-grid-table {
    width: 100% !important
}
