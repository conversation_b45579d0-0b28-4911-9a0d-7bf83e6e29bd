﻿.hide {
    display: none;
}

.show {
    display: block;
}
/* Absolute Center Spinner */
.loading {
    position: fixed;
    z-index: 10000;
    height: 2em;
    width: 2em;
    overflow: visible;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

    /* Transparent Overlay */
    .loading::before {
        content: '';
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.3);
    }

    /* :not(:required) hides these rules from IE9 and below */
    .loading:not(:required) {
        /* hide "loading..." text */
        font: 0/0 a;
        color: transparent;
        text-shadow: none;
        background-color: transparent;
        border: 0;
    }

        .loading:not(:required)::after {
            content: '';
            display: block;
            font-size: 10px;
            width: 1em;
            height: 1em;
            margin-top: -0.5em;
            -webkit-animation: spinner 1500ms infinite linear;
            -moz-animation: spinner 1500ms infinite linear;
            -ms-animation: spinner 1500ms infinite linear;
            -o-animation: spinner 1500ms infinite linear;
            animation: spinner 1500ms infinite linear;
            border-radius: 0.5em;
            -webkit-box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
            box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) -1.5em 0 0 0, rgba(0, 0, 0, 0.75) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
        }

/* Animation */

@-webkit-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-moz-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@-o-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/**Modal dialog */

/* The Modal (background) */
.app-modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    /*z-index: 1;*/ /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: var(--modal-overlay); /* Black w/ opacity */
}

@keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/* The Modal (background) */
.app-modal {
    display: block; /* Hidden by default */
    position: fixed; /* Stay in place */
    /*z-index: 100;*/ /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    opacity: 0;
}
.app-modal-dialog {
    min-height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

@media(max-width: 768px) {
    .app-modal-dialog {
        min-height: calc(100vh - 20px);
    }
}

/* Modal Content/Box */
.app-modal-body {
    /*/*background-color: #fefefe;*/
    /*background-color: #fff;*/
    background-color: var(--bg-white-color);
    padding: 20px;
    position: relative;
}

/* The Close Button */
.app-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

    .app-modal-close:hover,
    .app-modal-close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }


.app-modal .app-modal-dialog {
    /*transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;*/
    /*-webkit-transform: translate(0,-25%);
    transform: translate(0,-25%);*/
}
@media (min-width: 576px) {
    .app-modal-md .app-modal-dialog {
        width: 50%;
        margin: 0% auto !important;
    }
    .app-modal-right .app-modal-dialog {
        max-width: 72%;
        margin: 0% auto !important;
    }
}

@media (min-width: 992px) {
    .app-modal-sm .app-modal-dialog {
        width: 40%;
        margin: 0% auto !important;
    }
    .app-modal-lg .app-modal-dialog {
        width: 75%;
        margin: 0% auto !important;
    }
}

.app-modal-dialog {
    position: relative;
    margin: .5rem;
}

.app-modal-content {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: var(--bg-white-color);
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    outline: 0;
}
.app-modal-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
}
.app-modal-header .app-modal-header-text{
    margin: 5px;
}

.app-modal-open {
    overflow: hidden;
}

.app-modal.app-modal-show {
    overflow-x: hidden;
    overflow-y: auto;
    opacity: 1;
    z-index: 1072;
}
.full-screen .app-modal-dialog {
    max-width: 100%;
    width: 100% !important;
    margin: 0% auto !important;
    height: 100vh;
}
.full-screen .app-modal-content {
    height: 100vh;
}

.app-modal-body .form-wrapper {
    min-height: auto;
}

.app-modal-open .header{
    position: static;
}
.app-modal-header .modal-hd-btn {
    /*color: #000;*/
    color: var(--font-color);
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
    padding: 8px 10px;
    font-size: 18px !important;
    background-color: transparent;
    border: 0;
    line-height: 18px;
}
.app-modal-header .modal-hd-btn:hover{
    border-radius: 50px;
    background-color: rgba(0, 0, 0, 0.04);
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5 !important;
}


/** MODAL AS LEFT/RIGHT SIDEBAR*/
.app-modal-right .app-modal-dialog {
    position: fixed;
    margin: auto;
    width: 72%;
    height: 100%;
    -webkit-transform: translate3d(0%, 0, 0);
    -ms-transform: translate3d(0%, 0, 0);
    -o-transform: translate3d(0%, 0, 0);
    transform: translate3d(0%, 0, 0);
}
.app-modal-right .app-modal-dialog .app-modal-content {
    height: 100%;
    overflow-y: auto;
}
.app-modal-right .app-modal-dialog {
    right: -72%;
    -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
    -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
    -o-transition: opacity 0.3s linear, right 0.3s ease-out;
    transition: opacity 0.3s linear, right 0.3s ease-out;
}
.app-modal-right .app-modal-dialog {
    right: 0;
}
/** MODAL AS LEFT/RIGHT SIDEBAR*/

.app-modal-dialog .app-modal-body .button-action-section {
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}
.app-modal-dialog .app-modal-body .button-action-section .button-group{
    margin-top: 17px;
}

