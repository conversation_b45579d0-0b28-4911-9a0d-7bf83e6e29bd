﻿using System;
using System.IO;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {
        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services, IWebHostEnvironment env)
        {
            var rootPath = Constants.Startup.SpaBuildFolderName;

            Console.WriteLine($"[SPA] Registering SpaStaticFiles with RootPath = {rootPath}");

            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = rootPath;
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

#if DEBUG
                // Development - Use proxy
                Console.WriteLine("[SPA] Development mode: using proxy to Vite dev server");
                spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);
                spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
#else
                // Production - Serve static files
                var buildPath = Path.Combine(env.ContentRootPath, Constants.Startup.SpaBuildFolderName);
                var indexPath = Path.Combine(buildPath, "index.html");

                Console.WriteLine("[SPA] Production mode: serving static files");
                Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
                Console.WriteLine($"[SPA] Build Path: {buildPath}");
                Console.WriteLine($"[SPA] index.html path: {indexPath}");
                Console.WriteLine($"[SPA] Build directory exists: {Directory.Exists(buildPath)}");
                Console.WriteLine($"[SPA] index.html exists: {File.Exists(indexPath)}");

                spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions
                {
                    OnPrepareResponse = ctx =>
                    {
                        ctx.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
                        ctx.Context.Response.Headers["Expires"] = "-1";
                    }
                };

                spa.Options.DefaultPage = "/index.html";
#endif
            });

            return app;
        }
    }
}
