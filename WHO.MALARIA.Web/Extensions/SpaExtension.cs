using System;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {
        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services, IWebHostEnvironment env)
        {
            Console.WriteLine($"[SPA] Registering SpaStaticFiles for production deployment");
            Console.WriteLine($"[SPA] Environment: {env.EnvironmentName}");
            Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
            Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

            // For Azure App Service, static files are deployed to the content root, not wwwroot subdirectory
            // Check if we're in Azure App Service (ContentRootPath contains /home/<USER>/wwwroot)
            var isAzureAppService = env.ContentRootPath.Contains("/home/<USER>/wwwroot");
            var rootPath = isAzureAppService ? "." : "wwwroot";

            Console.WriteLine($"[SPA] Detected Azure App Service: {isAzureAppService}");
            Console.WriteLine($"[SPA] Setting SPA RootPath to: {rootPath}");

            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = rootPath;
                Console.WriteLine($"[SPA] SpaStaticFiles configured with RootPath: {configuration.RootPath}");
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

#if DEBUG
                // Development - Use proxy
                Console.WriteLine("[SPA] Development mode: using proxy to Vite dev server");
                spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);
                spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
#else
                // Production - Serve static files
                Console.WriteLine("[SPA] Production mode: serving static files from wwwroot");
                Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
                Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

                // Check if index.html exists - handle Azure App Service deployment structure
                var isAzureAppService = env.ContentRootPath.Contains("/home/<USER>/wwwroot");
                var staticFilesPath = isAzureAppService ? env.ContentRootPath : (env.WebRootPath ?? Path.Combine(env.ContentRootPath, "wwwroot"));
                var indexPath = Path.Combine(staticFilesPath, "index.html");

                Console.WriteLine($"[SPA] Detected Azure App Service: {isAzureAppService}");
                Console.WriteLine($"[SPA] Static files path: {staticFilesPath}");
                Console.WriteLine($"[SPA] index.html path: {indexPath}");
                Console.WriteLine($"[SPA] Static files directory exists: {Directory.Exists(staticFilesPath)}");
                Console.WriteLine($"[SPA] index.html exists: {File.Exists(indexPath)}");

                // Additional debugging - comprehensive file system exploration
                try
                {
                    Console.WriteLine($"[SPA] Current working directory: {Directory.GetCurrentDirectory()}");
                    Console.WriteLine($"[SPA] Files in ContentRootPath ({env.ContentRootPath}):");
                    if (Directory.Exists(env.ContentRootPath))
                    {
                        var allFiles = Directory.GetFiles(env.ContentRootPath, "*", SearchOption.AllDirectories);
                        var htmlFiles = allFiles.Where(f => f.EndsWith(".html")).ToList();
                        var jsFiles = allFiles.Where(f => f.EndsWith(".js")).Take(3).ToList();
                        var cssFiles = allFiles.Where(f => f.EndsWith(".css")).Take(3).ToList();

                        Console.WriteLine($"[SPA] Total files found: {allFiles.Length}");
                        Console.WriteLine($"[SPA] HTML files found: {htmlFiles.Count}");
                        foreach (var file in htmlFiles)
                        {
                            Console.WriteLine($"[SPA]   HTML: {file}");
                        }

                        Console.WriteLine($"[SPA] JS files found: {jsFiles.Count}");
                        foreach (var file in jsFiles)
                        {
                            Console.WriteLine($"[SPA]   JS: {file}");
                        }

                        Console.WriteLine($"[SPA] CSS files found: {cssFiles.Count}");
                        foreach (var file in cssFiles)
                        {
                            Console.WriteLine($"[SPA]   CSS: {file}");
                        }

                        // Check for possible alternative locations
                        var possibleLocations = new[]
                        {
                            Path.Combine(env.ContentRootPath, "index.html"),
                            Path.Combine(env.ContentRootPath, "wwwroot", "index.html"),
                            Path.Combine(env.ContentRootPath, "build", "index.html"),
                            Path.Combine(env.ContentRootPath, "dist", "index.html")
                        };

                        Console.WriteLine($"[SPA] Checking possible index.html locations:");
                        foreach (var location in possibleLocations)
                        {
                            var exists = File.Exists(location);
                            Console.WriteLine($"[SPA]   {location}: {exists}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[SPA] Error listing files: {ex.Message}");
                }

                spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions
                {
                    OnPrepareResponse = ctx =>
                    {
                        ctx.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
                        ctx.Context.Response.Headers["Expires"] = "-1";
                    }
                };

                spa.Options.DefaultPage = "/index.html";
#endif
            });

            return app;
        }
    }
}
