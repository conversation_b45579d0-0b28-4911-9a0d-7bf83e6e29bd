﻿using System;
using System.IO;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {
        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services, IWebHostEnvironment env)
        {
            Console.WriteLine($"[SPA] Registering SpaStaticFiles for production deployment");
            Console.WriteLine($"[SPA] Environment: {env.EnvironmentName}");
            Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
            Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

            // In production, the SPA files are deployed to wwwroot, so we don't need a custom RootPath
            services.AddSpaStaticFiles(configuration =>
            {
                // Don't set RootPath - let it use the default wwwroot
                Console.WriteLine("[SPA] Using default wwwroot path for SPA static files");
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

#if DEBUG
                // Development - Use proxy
                Console.WriteLine("[SPA] Development mode: using proxy to Vite dev server");
                spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);
                spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
#else
                // Production - Serve static files
                Console.WriteLine("[SPA] Production mode: serving static files from wwwroot");
                Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
                Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

                // Check if index.html exists in wwwroot
                var wwwrootPath = env.WebRootPath ?? Path.Combine(env.ContentRootPath, "wwwroot");
                var indexPath = Path.Combine(wwwrootPath, "index.html");
                Console.WriteLine($"[SPA] wwwroot path: {wwwrootPath}");
                Console.WriteLine($"[SPA] index.html path: {indexPath}");
                Console.WriteLine($"[SPA] wwwroot directory exists: {Directory.Exists(wwwrootPath)}");
                Console.WriteLine($"[SPA] index.html exists: {File.Exists(indexPath)}");

                spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions
                {
                    OnPrepareResponse = ctx =>
                    {
                        ctx.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
                        ctx.Context.Response.Headers["Expires"] = "-1";
                    }
                };

                spa.Options.DefaultPage = "/index.html";
#endif
            });

            return app;
        }
    }
}
