using System;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {
        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services, IWebHostEnvironment env)
        {
            Console.WriteLine($"[SPA] Registering SpaStaticFiles for production deployment");
            Console.WriteLine($"[SPA] Environment: {env.EnvironmentName}");
            Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
            Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

            // Set RootPath to wwwroot for production deployment
            var rootPath = "wwwroot";
            Console.WriteLine($"[SPA] Setting SPA RootPath to: {rootPath}");

            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = rootPath;
                Console.WriteLine($"[SPA] SpaStaticFiles configured with RootPath: {configuration.RootPath}");
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

#if DEBUG
                // Development - Use proxy
                Console.WriteLine("[SPA] Development mode: using proxy to Vite dev server");
                spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);
                spa.UseProxyToSpaDevelopmentServer("http://localhost:3000");
#else
                // Production - Serve static files
                Console.WriteLine("[SPA] Production mode: serving static files from wwwroot");
                Console.WriteLine($"[SPA] ContentRootPath: {env.ContentRootPath}");
                Console.WriteLine($"[SPA] WebRootPath: {env.WebRootPath}");

                // Check if index.html exists in wwwroot
                var wwwrootPath = env.WebRootPath ?? Path.Combine(env.ContentRootPath, "wwwroot");
                var indexPath = Path.Combine(wwwrootPath, "index.html");
                Console.WriteLine($"[SPA] wwwroot path: {wwwrootPath}");
                Console.WriteLine($"[SPA] index.html path: {indexPath}");
                Console.WriteLine($"[SPA] wwwroot directory exists: {Directory.Exists(wwwrootPath)}");
                Console.WriteLine($"[SPA] index.html exists: {File.Exists(indexPath)}");

                // Additional debugging - list all files in the deployment directory
                try
                {
                    Console.WriteLine($"[SPA] Current working directory: {Directory.GetCurrentDirectory()}");
                    Console.WriteLine($"[SPA] Files in ContentRootPath ({env.ContentRootPath}):");
                    if (Directory.Exists(env.ContentRootPath))
                    {
                        var files = Directory.GetFiles(env.ContentRootPath, "*", SearchOption.AllDirectories)
                            .Where(f => f.Contains("index.html") || f.Contains("wwwroot") || f.EndsWith(".dll"))
                            .Take(10);
                        foreach (var file in files)
                        {
                            Console.WriteLine($"[SPA]   - {file}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[SPA] Error listing files: {ex.Message}");
                }

                spa.Options.DefaultPageStaticFileOptions = new StaticFileOptions
                {
                    OnPrepareResponse = ctx =>
                    {
                        ctx.Context.Response.Headers["Cache-Control"] = "no-cache, no-store";
                        ctx.Context.Response.Headers["Expires"] = "-1";
                    }
                };

                spa.Options.DefaultPage = "/index.html";
#endif
            });

            return app;
        }
    }
}
