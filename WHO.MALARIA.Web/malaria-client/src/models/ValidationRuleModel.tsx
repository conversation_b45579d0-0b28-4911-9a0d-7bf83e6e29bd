export interface IValidationRuleProvider {
  [index: string]: ValidationRuleModel;
}

export default class ValidationRuleModel {
  public constructor(
    public dataType: string,
    public required: boolean,
    public condition: string = "",
    public errorMessage: string = ""
  ) {
    this.dataType = dataType;
    this.required = required;
    this.condition = condition;
    this.errorMessage = errorMessage;
  }
}
