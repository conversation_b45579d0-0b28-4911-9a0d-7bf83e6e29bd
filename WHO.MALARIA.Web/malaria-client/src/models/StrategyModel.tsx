import { AssessmentStrategies } from "./Enums";

export class StrategyModel {
  constructor(
    public id: string,
    public name: string,
    public shortName: string,
    public type: number,
    public order: number
  ) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.order = order;
  }

  static init = () =>
    new StrategyModel("", "", "", AssessmentStrategies.Case, 0);
}
