import { Constants } from "./Constants";

export class GridBaseRequestModel {
  constructor(
    public filterCriterias: Array<FilterCriteria> | null,
    public sortCriterias: SortCriteria | null,
    public skip: number = Constants.Common.skip,
    public take: number = Constants.Common.take,
    public countryId: string = ""
  ) {
    this.filterCriterias = filterCriterias;
    this.sortCriterias = sortCriterias;
    this.skip = skip;
    this.take = take;
    this.countryId = countryId;
  }

  static init = () =>
    new GridBaseRequestModel([], null, 0, Constants.Common.DefaultPageSize);

  public buildParam = () =>
    new GridBaseRequestModel(
      this.filterCriterias,
      this.sortCriterias,
      this.skip,
      this.take,
      this.countryId
    );
}

export class FilterCriteria {
  constructor(
    public field: string,
    public operator: string,
    public value: any
  ) {
    this.field = field;
    this.operator = operator;
    this.value = value;
  }
}

export class SortCriteria {
  constructor(public field: string, public direction: number) {
    this.field = field;
    this.direction = direction;
  }
}
