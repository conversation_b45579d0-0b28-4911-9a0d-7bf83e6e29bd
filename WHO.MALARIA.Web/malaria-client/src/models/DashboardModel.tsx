export class AssessmentStatistics {
  constructor(
    public totalCreatedAssesments: number | null,
    public totalInProgressAssessments: number | null,
    public totalNumberOfCountriesWithOneAssesment: number | null,
    public totalNumberOfCountriesWithMoreThanOneAssesment: number | null,
    public inProgressAssessmentsCountries: string,
    public oneAssessmentCountries: string,
    public moreThanOneAssessmentCountries: string,
    public createdAssessmentsCountries: string,
    public totalCompletedRapidAssessments: number,
    public totalCompletedTailoredAssesments: number,
    public totalCompletedComprehensiveAssesments: number,
    public completedRapidAssessmentsCountries: string,
    public completedTailoredAssessmentCountries: string,
    public completedComprehensiveAssessmentCountries: string
  ) {
    this.totalCreatedAssesments = totalCreatedAssesments;
    this.totalNumberOfCountriesWithOneAssesment =
      totalNumberOfCountriesWithOneAssesment;
    this.totalInProgressAssessments = totalInProgressAssessments;
    this.totalNumberOfCountriesWithMoreThanOneAssesment =
      totalNumberOfCountriesWithMoreThanOneAssesment;
    this.inProgressAssessmentsCountries = inProgressAssessmentsCountries;
    this.oneAssessmentCountries = oneAssessmentCountries;
    this.moreThanOneAssessmentCountries = moreThanOneAssessmentCountries;
    this.createdAssessmentsCountries = createdAssessmentsCountries;

    this.totalCompletedRapidAssessments = totalCompletedRapidAssessments;
    this.totalCompletedTailoredAssesments = totalCompletedTailoredAssesments;
    this.totalCompletedComprehensiveAssesments =
      totalCompletedComprehensiveAssesments;
    this.completedRapidAssessmentsCountries =
      completedRapidAssessmentsCountries;
    this.completedTailoredAssessmentCountries =
      completedTailoredAssessmentCountries;
    this.completedComprehensiveAssessmentCountries =
      completedComprehensiveAssessmentCountries;
  }
}

export class DashboardModel extends AssessmentStatistics {
  constructor(
    public totalCreatedAssesments: number | null,
    public totalInProgressAssessments: number | null,
    public totalNumberOfCountriesWithOneAssesment: number | null,
    public totalNumberOfCountriesWithMoreThanOneAssesment: number | null,
    public inProgressAssessmentsCountries: string,
    public oneAssessmentCountries: string,
    public moreThanOneAssessmentCountries: string,
    public createdAssessmentsCountries: string,
    public totalCompletedRapidAssessments: number,
    public totalCompletedTailoredAssesments: number,
    public totalCompletedComprehensiveAssesments: number,
    public completedRapidAssessmentsCountries: string,
    public completedTailoredAssessmentCountries: string,
    public completedComprehensiveAssessmentCountries: string
  ) {
    super(
      totalCreatedAssesments,
      totalInProgressAssessments,
      totalNumberOfCountriesWithOneAssesment,
      totalNumberOfCountriesWithMoreThanOneAssesment,
      inProgressAssessmentsCountries,
      oneAssessmentCountries,
      moreThanOneAssessmentCountries,
      createdAssessmentsCountries,
      totalCompletedRapidAssessments,
      totalCompletedTailoredAssesments,
      totalCompletedComprehensiveAssesments,
      completedRapidAssessmentsCountries,
      completedTailoredAssessmentCountries,
      completedComprehensiveAssessmentCountries
    );
  }
  static init = () =>
    new DashboardModel(0, 0, 0, 0, "", "", "", "", 0, 0, 0, "", "", "");
}

export class CountryDataModel {
  constructor(public iso: string, public status: number) {
    this.iso = iso;
    this.status = status;
  }
}

export class ObjectivePercentageModel {
  constructor(
    public year: Array<number>,
    public objectives: Array<SubObjectivePercentageModel>,
  ) {
    this.year = year;
    this.objectives = objectives;
  }
}

export class GetCountrySpecificSubObjectiveModel{
  constructor(
    public id: string,
    public name: string,
    public percentage: number,
    public objectiveId: string,
    public isNotAssessed:boolean
  ) {
    this.id = id;
    this.name = name;
    this.percentage = percentage;
    this.objectiveId = objectiveId;
    this.isNotAssessed = isNotAssessed;
  }
}

export class SubObjectivePercentageModel{
  constructor(
    public id: string,
    public name: string,
    public percentage: number,
    public subObjective: Array<GetCountrySpecificSubObjectiveModel>,
    public year: string
  ) {
    this.id = id;
    this.name = name;
    this.percentage = percentage;
    this.subObjective = subObjective;
    this.year = year
  }

  static init = () => new SubObjectivePercentageModel("", "", 0, [], "");
}
