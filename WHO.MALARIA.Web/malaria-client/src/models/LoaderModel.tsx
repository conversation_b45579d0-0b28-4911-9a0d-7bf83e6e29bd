﻿import { UtilityHelper } from '../utils/UtilityHelper'
import { StatusCode } from '../models/Enums'

export class LoaderModel {
    constructor(public statusCode: number, public message: string, public isLoading: boolean) {
        this.statusCode = statusCode;
        this.message = message;
        this.isLoading = isLoading;
    }

    static init = () => new LoaderModel(StatusCode.Ok, UtilityHelper.IsEmpty(), false)
}