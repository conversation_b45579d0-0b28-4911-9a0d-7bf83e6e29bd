export class DeskReviewRequestModel {
    constructor(
        public assessmentId: string,
        public indicatorId: string,
        public strategyId: string,
        public assessmentIndicatorId: string,
        public assessmentStrategyId: string,
        public status: number,
        public response: string,
        public sequence: string,
    ) {
        this.assessmentId = assessmentId;
        this.indicatorId = indicatorId;
        this.strategyId = strategyId;
        this.assessmentIndicatorId = assessmentIndicatorId;
        this.assessmentStrategyId = assessmentStrategyId;
        this.status = status;
        this.response = response;
        this.sequence = sequence;
    }
    static init = () => new DeskReviewRequestModel("","", "", "", "", 0, "", "");
}
