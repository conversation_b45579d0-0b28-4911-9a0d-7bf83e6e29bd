﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public strategyId: string | null,
        public malariaIndicators: Array<MalariaIndicator>,
        // property is used for checklist indicators count and compare it with malariaIndicators arrayOfObject count in validation rules
        public checkListIndicatorsCount: number,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.strategyId = strategyId;
        this.malariaIndicators = malariaIndicators;
        this.checkListIndicatorsCount = checkListIndicatorsCount;
    }
    static init = (strategyId: string) =>
        new Response_2(
            false,
            null,
            strategyId,
            [],
            0
        );
}

export class MalariaIndicator {
    constructor(
        public checklistIndicatorId: string,
        public indicatorMonitored: boolean | null = null,
        public underFive: boolean | null = null,
        public overFive: boolean | null = null,
        public gender: boolean | null = null,
        public pregnantWoman: boolean | null = null,
        public healthSector: boolean | null = null,
        public geography: boolean | null = null,
        public confirmationMethod: boolean | null = null,
        public other: boolean | null = null
    ) {
        this.checklistIndicatorId = checklistIndicatorId;
        this.indicatorMonitored = indicatorMonitored;
        this.underFive = underFive;
        this.overFive = overFive;
        this.gender = gender;
        this.pregnantWoman = pregnantWoman;
        this.healthSector = healthSector;
        this.geography = geography;
        this.confirmationMethod = confirmationMethod;
        this.other = other;
    }
    [index: string]: string | any;
}