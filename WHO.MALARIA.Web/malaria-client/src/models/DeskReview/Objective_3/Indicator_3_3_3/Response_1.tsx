import { Response_1 as Parent } from '../Indicator_3_3_1/Response_1';

export class Response_1 {
    constructor(
        public reportingTools: Array<ReportingTool>,
        public parentData: Parent | null
    ) {
        this.reportingTools = reportingTools;
        this.parentData = parentData;
    }

    static init = () => new Response_1([new ReportingTool("", null, "")], null);
}

export class ReportingTool {
    constructor(public name: string, public isStandardized: boolean | null, public details: string) {
        this.name = name;
        this.isStandardized = isStandardized;
        this.details = details;
    }
}

export class StandardizedReportingToolsRadioResponse {
    constructor(
        public recordingTool1: boolean | null = null,
        public recordingTool2: boolean | null = null,
        public recordingTool3: boolean | null = null,
        public recordingTool4: boolean | null = null,
    ) {
        this.recordingTool1 = recordingTool1;
        this.recordingTool2 = recordingTool2;
        this.recordingTool3 = recordingTool3;
        this.recordingTool4 = recordingTool4;
    }
}

export class StandardizedReportingToolsTextboxResponse {
    constructor(
        public recordingTool1: string | null = null,
        public recordingTool2: string | null = null,
        public recordingTool3: string | null = null,
        public recordingTool4: string | null = null,
    ) {
        this.recordingTool1 = recordingTool1;
        this.recordingTool2 = recordingTool2;
        this.recordingTool3 = recordingTool3;
        this.recordingTool4 = recordingTool4;
    }
}