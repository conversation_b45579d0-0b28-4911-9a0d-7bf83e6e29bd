import { DeskReviewAssessmentResponseStatus } from "../../Enums";

export default class DiagramUploadModel {
    constructor(
        public dataAccess: string | null,
        public dataQualityAssurance: string | null,
        public dataRecording: string | null,
        public dataReporting: string | null,
        public dataAnalysis: string | null,
        public status: number,
        public diagramDetails: Array<DiagramDetail>
    ) {
        this.dataAccess = dataAccess;
        this.dataQualityAssurance = dataQualityAssurance;
        this.dataRecording = dataRecording;
        this.dataReporting = dataReporting;
        this.dataAnalysis = dataAnalysis;
        this.status = status;
        this.diagramDetails = diagramDetails;
    }

    static init = (diagramDetails: Array<DiagramDetail>) => new DiagramUploadModel("", "", "", "", "", DeskReviewAssessmentResponseStatus.InProgress, diagramDetails);
}

export class DiagramDetail {
    constructor(public id: string,
        public file: File | null,
        public extension: string,
        public dataFlow: string | null,
        public plannedChanges: string | null,
        public modifyingProcess: string | null,
        public order: number) {
        this.id = id;
        this.file = file;
        this.extension = extension;
        this.dataFlow = dataFlow;
        this.plannedChanges = plannedChanges;
        this.modifyingProcess = modifyingProcess;
        this.order = order;
    }

    [index: string]: any;
}