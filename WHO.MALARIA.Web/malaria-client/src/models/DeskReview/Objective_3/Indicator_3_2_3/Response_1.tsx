import { Response_1 as Parent } from "../Indicator_3_2_1/Response_1";

export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public standardizedRecordingTools: Array<StandardizedRecordingTool>,
        public standardizedFormsDetails: Array<StandardizedFormsDetail>,
        public parentRecordingToolCount: number,
        //these properties are applied when 3.2.3 data is assessed without assessing the parent 3.2.1 screen
        public recordingTool1: RecordingTool,
        public recordingTool2: RecordingTool,
        public recordingTool3: RecordingTool,
        public recordingTool4: RecordingTool,
        //this indicator is depend on the selection of indicator 3.2.1 's data so based on the data of indicator 3.2.1 we render the view
        public parentData?: Parent,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.standardizedRecordingTools = standardizedRecordingTools;
        this.standardizedFormsDetails = standardizedFormsDetails;
        this.recordingTool1 = recordingTool1;
        this.recordingTool2 = recordingTool2;
        this.recordingTool3 = recordingTool3;
        this.recordingTool4 = recordingTool4;
        this.parentData = parentData;
        this.parentRecordingToolCount = parentRecordingToolCount;
    }
    [index: string]: string | any;

    static init = (strategyId: string) => new Response_1(false, null, null, [], [], 0,
        new RecordingTool(null, null, null),
        new RecordingTool(null, null, null),
        new RecordingTool(null, null, null),
        new RecordingTool(null, null, null),
        Parent.init(strategyId));
}

export class StandardizedRecordingTool {
    constructor(
        public recordingToolId: string,
        public areStandardizedAcrossAllService: boolean
    ) {
        this.recordingToolId = recordingToolId;
        this.areStandardizedAcrossAllService = areStandardizedAcrossAllService;
    }

    static init = () => new StandardizedRecordingTool("", false);
}

export class StandardizedFormsDetail {
    constructor(public recordingToolId: string, public detail: string) {
        this.recordingToolId = recordingToolId;
        this.detail = detail;
    }
    static init = () => new StandardizedFormsDetail("", "");
}

export class RecordingTool {
    constructor(
        public sourceDocumentName: string | null,
        public areStandardizedAcrossAllService: boolean | null,
        public detail: string | null
    ) {
        this.sourceDocumentName = sourceDocumentName;
        this.areStandardizedAcrossAllService = areStandardizedAcrossAllService;
        this.detail = detail;
    }

    static init = () => new RecordingTool(null, null, null);
}
