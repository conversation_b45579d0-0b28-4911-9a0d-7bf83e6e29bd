export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public strategyId: string | null,
        public metNotMetStatus: string | null,
        public hasPVivaxCases: boolean | null,
        public hasMalariaInpatients: boolean | null,
        public transmitMalariaVariables: Array<TransmitMalariaVariable>,
        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public checkListVariablesCount: number,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.strategyId = strategyId;
        this.metNotMetStatus = metNotMetStatus;
        this.hasPVivaxCases = hasPVivaxCases;
        this.hasMalariaInpatients = hasMalariaInpatients;
        this.transmitMalariaVariables = transmitMalariaVariables;
    }
    static init = (strategyId: string) => new Response_1(false, null, strategyId, null, null, null, [], 0);
}

export class TransmitMalariaVariable {
    constructor(
        public variableId: string,
        public recordedInSource: boolean | null = null,
        public disagregation: boolean | null = null,
        public over5: boolean | null = null,
        public gender: boolean | null = null,
        public pregnantWomen: boolean | null = null,
        public healthSector: boolean | null = null,
        public geography: boolean | null = null,
        public other: boolean | null = null
    ) {
        this.variableId = variableId;
        this.recordedInSource = recordedInSource;
        this.disagregation = disagregation;
        this.over5 = over5;
        this.gender = gender;
        this.pregnantWomen = pregnantWomen;
        this.healthSector = healthSector;
        this.geography = geography;
        this.other = other;
    }
    [index: string]: string | any;
}
