﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public stragegyId: string| null,
        public malariaVariables: Array<MalariaVariable>,
        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public checkListVariablesCount: number,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.stragegyId = stragegyId;
        this.malariaVariables = malariaVariables;
    }
    static init = (strategyId: string) =>
        new Response_2(
            false,
            null,
            strategyId,
            [],
            0
        );
}

export class MalariaVariable {
    constructor(
        public variableId: string,
        public recordedInSource: boolean | null,
        public under5: boolean | null,
        public over5: boolean | null,
        public gender: boolean | null,
        public pregnantWoman: boolean | null,
        public healthSector: boolean | null,
        public geography: boolean | null,
        public other: boolean | null,

    ) {
        this.variableId = variableId;
        this.recordedInSource = recordedInSource;
        this.under5 = under5;
        this.over5 = over5;
        this.gender = gender;
        this.pregnantWoman = pregnantWoman;
        this.healthSector = healthSector;
        this.geography = geography;
        this.other = other;
    }
    [index: string]: string | any;
}