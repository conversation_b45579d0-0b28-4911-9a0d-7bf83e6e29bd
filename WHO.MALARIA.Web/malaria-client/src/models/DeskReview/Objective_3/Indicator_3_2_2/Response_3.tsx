﻿export class Response_3 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public step_A: Step_A_Response,
        public step_B: Step_B_Response,
        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public checkListVariablesCount: number
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_A = step_A;
        this.step_B = step_B;
        this.checkListVariablesCount = checkListVariablesCount;
    }

    static init = () =>
        new Response_3(
            false,
            null,
            null,
            new Step_A_Response(null, []),
            new Step_B_Response(
                new ToolsMalariaVariables(),
                new ToolsMalariaVariables(),
                new ToolsMalariaVariables()
            ),
            0
        );
}

export class Step_A_Response {
    constructor(
        public hasPVivaxCases: boolean | null,
        public transmitMalariaVariables: Array<TransmitMalariaVariable>,
    ) {
        this.hasPVivaxCases = hasPVivaxCases;
        this.transmitMalariaVariables = transmitMalariaVariables;
    }
    [index: string]: string | any;
}

export class TransmitMalariaVariable {
    constructor(
        public variableId: string,
        public recordedInSource: boolean | null = null,
        public underFive: boolean | null = null,
        public overFive: boolean | null = null,
        public gender: boolean | null = null,
        public pregnantWoman: boolean | null = null,
        public healthSector: boolean | null = null,
        public geography: boolean | null = null,
        public other: boolean | null = null,
        public caseCategory: number | null = null
    ) {
        this.variableId = variableId;
        this.recordedInSource = recordedInSource;
        this.underFive = underFive;
        this.overFive = overFive;
        this.gender = gender;
        this.pregnantWoman = pregnantWoman;
        this.healthSector = healthSector;
        this.geography = geography;
        this.other = other;
        this.caseCategory = caseCategory;
    }
    [index: string]: string | any;
}

export class Step_B_Response {
    constructor(
        public correctlyDistinguisedPresumedCase: ToolsMalariaVariables,
        public rdtConfirmCase: ToolsMalariaVariables,
        public falciparumInfection: ToolsMalariaVariables,
    ) {
        this.correctlyDistinguisedPresumedCase = correctlyDistinguisedPresumedCase;
        this.rdtConfirmCase = rdtConfirmCase;
        this.falciparumInfection = falciparumInfection;
    }
    [index: string]: string | any;
}

export class ToolsMalariaVariables {
    constructor(
        public checklist: boolean | null = null,
        public details: string | null = null
    ) {
        this.checklist = checklist;
        this.details = details;
    }
}