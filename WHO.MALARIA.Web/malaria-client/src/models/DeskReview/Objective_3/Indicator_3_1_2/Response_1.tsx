export default class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null = null,
        public metNotMetStatus: string | null,
        public step_A: AdequateCommoditiesForTreatment,
        public step_B: AdequateCommoditiesForTreatment
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_A = step_A;
        this.step_B = step_B;
    }

    static init = () =>
        new Response_1(
            false,
            null,
            null,
            new AdequateCommoditiesForTreatment(null, null, null),
            new AdequateCommoditiesForTreatment(null, null, null)
        );
}

export class AdequateCommoditiesForTreatment {
    constructor(
        public healthFacilitiesWithStockOut: number | null,
        public healthFacilitiesReporting: number | null,
        //proportionRate is used calculate the percentage of proportion and based on this field validation is added in validation rules 
        //not saved in DB
        public proportionRate: number | null
    ) {
        this.healthFacilitiesWithStockOut = healthFacilitiesWithStockOut;
        this.healthFacilitiesReporting = healthFacilitiesReporting;
        this.proportionRate = proportionRate;
    }
}
