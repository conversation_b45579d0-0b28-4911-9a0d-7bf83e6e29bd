export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null = null,
    public metNotMetStatus: string | null,
    public healthFacilitiesWithStockOut: number | null = null,
    public healthFacilitiesReporting: number | null = null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.healthFacilitiesWithStockOut = healthFacilitiesWithStockOut;
    this.healthFacilitiesReporting = healthFacilitiesReporting;
  }

  static init = () => new Response_1(false, null, null, null, null);
}
