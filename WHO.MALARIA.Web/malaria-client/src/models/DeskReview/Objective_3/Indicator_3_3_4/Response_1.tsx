export class Response_1 {
    constructor(             
        public reportingFrequencyCriteriaMet: boolean | null = null,
        public aggregationOfReportedDataCriteriaMet: boolean | null = null,
        public reportingOfZeroCasesCriteriaMet: boolean | null = null,
    ) {
        this.reportingFrequencyCriteriaMet = reportingFrequencyCriteriaMet;
        this.aggregationOfReportedDataCriteriaMet = aggregationOfReportedDataCriteriaMet;
        this.reportingOfZeroCasesCriteriaMet = reportingOfZeroCasesCriteriaMet;
    }

    static init = () => new Response_1(null, null, null);
}
