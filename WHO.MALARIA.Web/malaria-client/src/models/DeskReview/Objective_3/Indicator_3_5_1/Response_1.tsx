export class Response_1 {
  constructor(
    public metNotMetStatus: string | null,
    public dataQualityAssuranceProcedureInPlace: DataQualityResponse,
    public dataValidated: DataResponse,
    public toolsAndMethods: DataResponse,
    public validationLevel: DataResponse,
    public personResponsible: DataResponse,
    public followUpAction: DataResponse,
    public outputMaterialCopyLink: DataResponse
  ) {
    this.metNotMetStatus = metNotMetStatus;
    this.dataQualityAssuranceProcedureInPlace =
      dataQualityAssuranceProcedureInPlace;   
    this.dataValidated = dataValidated;
    this.toolsAndMethods = toolsAndMethods;
    this.validationLevel = validationLevel;
    this.personResponsible = personResponsible;
    this.followUpAction = followUpAction;
    this.outputMaterialCopyLink = outputMaterialCopyLink;
  }

  static init = () =>
    new Response_1(
      null,
      new DataQualityResponse(),     
      new DataResponse(),
      new DataResponse(),
      new DataResponse(),
      new DataResponse(),
      new DataResponse(),
      new DataResponse()
    );
}

export class DataQualityResponse {
  constructor(
    public dataCleaning: boolean | null = null,
    public dataReviewMeetings: boolean | null = null,
    public dataQualityAssessments: boolean | null = null,
    public dataQualityIndicators: boolean | null = null
  ) {
    this.dataCleaning = dataCleaning;
    this.dataReviewMeetings = dataReviewMeetings;
    this.dataQualityAssessments = dataQualityAssessments;
    this.dataQualityIndicators = dataQualityIndicators;
  }
}

export class DataResponse {
  constructor(
    public dataCleaning: string | null = null,
    public dataReviewMeetings: string | null = null,
    public dataQualityAssessments: string | null = null,
    public dataQualityIndicators: string | null = null
  ) {
    this.dataCleaning = dataCleaning;
    this.dataReviewMeetings = dataReviewMeetings;
    this.dataQualityAssessments = dataQualityAssessments;
    this.dataQualityIndicators = dataQualityIndicators;
  }
}
