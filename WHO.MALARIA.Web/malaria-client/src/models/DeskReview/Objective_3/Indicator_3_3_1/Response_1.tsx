import { Response_1 as Parent } from "../Indicator_3_2_1/Response_1";

export class Response_1 {
    constructor(
        public reportingTools: Array<ReportingTool>,
        public areReportingToolsSameAsRecordingTools: boolean | null,
        public parentData: Parent | null,
        public isParentCannotBeAssessed: boolean | null
    ) {
        this.reportingTools = reportingTools;
        this.areReportingToolsSameAsRecordingTools = areReportingToolsSameAsRecordingTools;
        this.parentData = parentData;
        this.isParentCannotBeAssessed = isParentCannotBeAssessed;
    }

    static init = () => new Response_1([new ReportingTool()], false, null, null);
}

export class ReportingTool {
    constructor(
        public documentId: string = "",
        public reportingToolName: string | null = null,
        public toolType: string | null = null,
        public yearTool: string | null = null,
        public reportingHealthSystemLevel: string | null = null,
        public methodOfReporting: string | null = null,
        public aggregation: string | null = null,
        public frequencyData: string | null = null,
        public personResponsible: string | null = null,
        public reportsRecipient: string | null = null,
        public variablesReportedList: string | null = null,
        public linkOrScreenshot: string | null = null,
        public file: File | null = null,
    ) {
        this.documentId = documentId;
        this.reportingToolName = reportingToolName;
        this.toolType = toolType;
        this.yearTool = yearTool;
        this.reportingHealthSystemLevel = reportingHealthSystemLevel;
        this.methodOfReporting = methodOfReporting;
        this.aggregation = aggregation;
        this.frequencyData = frequencyData;
        this.personResponsible = personResponsible;
        this.reportsRecipient = reportsRecipient;
        this.variablesReportedList = variablesReportedList;
        this.linkOrScreenshot = linkOrScreenshot;
        this.file = file;
    }
}
