import { KeyValuePair } from "../../KeyValueType";

export class Response_1 {
    constructor(      
        public builtInChecksAtDataEntry: DataQualityControlCheckResponse,
        public producesDataVerificationReport: DataQualityControlCheckResponse,
        public avoidDuplicateEntryOfRecord: DataQualityControlCheckResponse,
    ) {     
        this.builtInChecksAtDataEntry = builtInChecksAtDataEntry;
        this.producesDataVerificationReport = producesDataVerificationReport;
        this.avoidDuplicateEntryOfRecord = avoidDuplicateEntryOfRecord;
    }

    static init = () => new Response_1(new DataQualityControlCheckResponse(), new DataQualityControlCheckResponse(), new DataQualityControlCheckResponse());
}

export class DataQualityControlCheckResponse {
    constructor(
        public dataQualityControlCheck: string | null = null,
        public inPlace: boolean | null = null,     
    ) {
        this.dataQualityControlCheck = dataQualityControlCheck;
        this.inPlace = inPlace;      
    }
}