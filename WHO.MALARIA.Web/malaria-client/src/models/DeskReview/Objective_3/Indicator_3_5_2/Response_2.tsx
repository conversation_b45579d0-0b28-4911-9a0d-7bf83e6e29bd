﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public dataQualityActivity: DataQualityActivity,
        public nationalValidationActivity: ValidationActivity,
        public regionalValidationActivity: ValidationActivity,
        public districtValidationActivity: ValidationActivity,
        public frequencyDenominator: number | null,
        public nationalLevelDataAssessed: number | null,
        public malariaSurveillanceQuality: string | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.dataQualityActivity = dataQualityActivity;
        this.nationalValidationActivity = nationalValidationActivity;
        this.regionalValidationActivity = regionalValidationActivity;
        this.districtValidationActivity = districtValidationActivity;
        this.frequencyDenominator = frequencyDenominator;
        this.nationalLevelDataAssessed = nationalLevelDataAssessed;
        this.malariaSurveillanceQuality = malariaSurveillanceQuality;
    }

    static init = () => new Response_2(
        false,
        null,
        new DataQualityActivity(null, null, null, null),
        new ValidationActivity(null, null, null, null),
        new ValidationActivity(null, null, null, null),
        new ValidationActivity(null, null, null, null),
        null,
        null,
        null);
}

export class DataQualityActivity {
    constructor(
        public dataCleaning: boolean | null,
        public dataReviewMeeting: boolean | null,
        public dataQualityAssessment: boolean | null,
        public dataQualityIndicator: boolean | null
    ) {
        this.dataCleaning = dataCleaning;
        this.dataReviewMeeting = dataReviewMeeting;
        this.dataQualityAssessment = dataQualityAssessment;
        this.dataQualityIndicator = dataQualityIndicator;
    }
}

export class ValidationActivity {
    constructor(
        public dataCleaning: string | null,
        public dataReviewMeeting: string | null,
        public dataQualityAssessment: string | null,
        public dataQualityIndicator: string | null
    ) {
        this.dataCleaning = dataCleaning;
        this.dataReviewMeeting = dataReviewMeeting;
        this.dataQualityAssessment = dataQualityAssessment;
        this.dataQualityIndicator = dataQualityIndicator;
    }
}