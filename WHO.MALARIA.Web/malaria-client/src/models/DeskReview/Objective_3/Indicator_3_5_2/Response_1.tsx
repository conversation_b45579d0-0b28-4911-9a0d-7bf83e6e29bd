export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public nationalValidationActivity: ValidationActivity,
        public regionalValidationActivity: ValidationActivity,
        public districtValidationActivity: ValidationActivity,
        public frequencyDenominator: number | null,
        public nationalLevelDataAssessed: number | null,
        public malariaSurveillanceQuality: string | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.nationalValidationActivity = nationalValidationActivity;
        this.regionalValidationActivity = regionalValidationActivity;
        this.districtValidationActivity = districtValidationActivity;
        this.frequencyDenominator = frequencyDenominator;
        this.nationalLevelDataAssessed = nationalLevelDataAssessed;
        this.malariaSurveillanceQuality = malariaSurveillanceQuality;
    }

    static init = () => new Response_1(
        false,
        null,
        null,
        new ValidationActivity(null, null, null, null),
        new ValidationActivity(null, null, null, null),
        new ValidationActivity(null, null, null, null),
        null,
        null,
        null);
}

export class ValidationActivity {
    constructor(
        public dataCleaning: string | null,
        public dataReviewMeeting: string | null,
        public dataQualityAssessment: string | null,
        public dataQualityIndicator: string | null
    ) {
        this.dataCleaning = dataCleaning;
        this.dataReviewMeeting = dataReviewMeeting;
        this.dataQualityAssessment = dataQualityAssessment;
        this.dataQualityIndicator = dataQualityIndicator;
    }
}