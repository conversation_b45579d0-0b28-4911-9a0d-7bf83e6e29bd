export class Response_1 {
    constructor(
        public nameTypeExpectedOutput: RoutineAnalysisOutput,
        public dataSourceUsed: RoutineAnalysisOutput,
        public indicatorsAndVisualizations: RoutineAnalysisOutput,
        public toolsUsed: RoutineAnalysisOutput,
        public method: RoutineAnalysisOutput,
        public frequencyOfAnalysis: RoutineAnalysisOutput,
        public levelOfAnalysisDone: RoutineAnalysisOutput,
        public personResponsible: RoutineAnalysisOutput,
        public recipients: RoutineAnalysisOutput,
        public methodOfDissemination: RoutineAnalysisOutput,
        public screenshot: Screenshot,
    ) {
        this.nameTypeExpectedOutput = nameTypeExpectedOutput;
        this.dataSourceUsed = dataSourceUsed;
        this.indicatorsAndVisualizations = indicatorsAndVisualizations;
        this.toolsUsed = toolsUsed;
        this.method = method;
        this.frequencyOfAnalysis = frequencyOfAnalysis;
        this.levelOfAnalysisDone = levelOfAnalysisDone;
        this.personResponsible = personResponsible;
        this.recipients = recipients;
        this.methodOfDissemination = methodOfDissemination;
        this.screenshot = screenshot;
    }

    static init = () =>
        new Response_1(
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new RoutineAnalysisOutput(),
            new Screenshot("", "", "", "", "", "", null, null, null, null, null, null),
        );
}

export class RoutineAnalysisOutput {
    constructor(
        public output1: string | null = null,
        public output2: string | null = null,
        public output3: string | null = null,
        public output4: string | null = null,
        public output5: string | null = null,
        public output6: string | null = null
    ) {
        this.output1 = output1;
        this.output2 = output2;
        this.output3 = output3;
        this.output4 = output4;
        this.output5 = output5;
        this.output6 = output6;
    }
}

export class Screenshot {
    constructor(
        public documentId1: string,
        public documentId2: string,
        public documentId3: string,
        public documentId4: string,
        public documentId5: string,
        public documentId6: string,
        public file1: File | null,
        public file2: File | null,
        public file3: File | null,
        public file4: File | null,
        public file5: File | null,
        public file6: File | null,
    ) {
        this.documentId1 = documentId1;
        this.documentId2 = documentId2;
        this.documentId3 = documentId3;
        this.documentId4 = documentId4;
        this.documentId5 = documentId5;
        this.documentId6 = documentId6;
        this.file1 = file1;
        this.file2 = file2;
        this.file3 = file3;
        this.file4 = file4;
        this.file5 = file5;
        this.file6 = file6;
    }
}
