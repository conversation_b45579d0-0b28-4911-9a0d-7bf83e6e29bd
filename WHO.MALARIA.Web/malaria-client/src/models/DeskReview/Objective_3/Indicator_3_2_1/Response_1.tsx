export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public strategyId: string | null,
        public metNotMetStatus: string | null,
        public isShowWarning: boolean,
        public recordingTools: Array<NumberOfRecordingForm>
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.strategyId = strategyId;
        this.metNotMetStatus = metNotMetStatus;
        this.isShowWarning = isShowWarning;
        this.recordingTools = recordingTools;
    }
    static init = (strategyId: string) =>
        new Response_1(false, "", strategyId, null, false, [
            new NumberOfRecordingForm(
                Math.round(new Date().getTime() / 1000).toString()
            ),
        ]);
}

export class NumberOfRecordingForm {
    constructor(
        public recordingToolId: string,
        public documentId: string = "",
        public nameOfReportingToolSourceDocument: string | null = null,
        public toolType: string | null = null,
        public yearToolWasIntroduced: string | null = null,
        public frequencyDataReported: string | null = null,
        public personResponsibleForReporting: string | null = null,
        public recipientListVariablesReported: string | null = null,
        public lastUpdatedDate: Date | null = null,
        public file: File | null = null,
        public fileContent: string = "",
    ) {
        this.recordingToolId = recordingToolId;
        this.documentId = documentId;
        this.nameOfReportingToolSourceDocument = nameOfReportingToolSourceDocument;
        this.toolType = toolType;
        this.yearToolWasIntroduced = yearToolWasIntroduced;
        this.frequencyDataReported = frequencyDataReported;
        this.personResponsibleForReporting = personResponsibleForReporting;
        this.recipientListVariablesReported = recipientListVariablesReported;
        this.lastUpdatedDate = lastUpdatedDate;
        this.file = file;
        this.fileContent = fileContent;
    }
}
