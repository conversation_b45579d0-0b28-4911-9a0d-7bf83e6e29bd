export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public masterHealthFacilityListExists: boolean | null,
    public masterHealthFacilityIdentifierDetails: string | null,
    public masterHealthFacilityExists: boolean | null,
    public masterHealthFacilityDetails: string | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.masterHealthFacilityListExists = masterHealthFacilityListExists;
    this.masterHealthFacilityIdentifierDetails =
      masterHealthFacilityIdentifierDetails;
    this.masterHealthFacilityExists = masterHealthFacilityExists;
    this.masterHealthFacilityDetails = masterHealthFacilityDetails;
  }

  static init = () => new Response_1(false, null, null, null, null, null, null);
}
