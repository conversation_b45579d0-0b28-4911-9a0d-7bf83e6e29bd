﻿export default class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public step_A: Step_A_Response,
        public step_B: Step_B_Response
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.step_A = step_A;
        this.step_B = step_B;
    }

    static init = () =>
        new Response_2(
            false,
            null,
            new Step_A_Response(
                new Flexibility(),
                new Stability(),
                new InteroperabilityIntegration(),
                new VisualizationCapacity()
            ),
            new Step_B_Response()
        );
}

export class Step_A_Response {
    constructor(
        public flexibility: Flexibility,
        public stability: Stability,
        public interoperabilityIntegration: InteroperabilityIntegration,
        public visualizationCapacity: VisualizationCapacity
    ) {
        this.flexibility = flexibility;
        this.stability = stability;
        this.interoperabilityIntegration = interoperabilityIntegration;
        this.visualizationCapacity = visualizationCapacity;
    }
    [index: string]: string | any;
}

export class Step_B_Response {
    constructor(
        public informationSystem: string | null = null,
        public additionalAttributes: string | null = null,
    ) {
        this.informationSystem = informationSystem;
        this.additionalAttributes = additionalAttributes;
    }
}

export class Flexibility {
    constructor(
        public hasFlexibility: boolean | null = null,
        public explaination: string | null = null,
        public process: string | null = null
    ) {
        this.hasFlexibility = hasFlexibility;
        this.explaination = explaination;
        this.process = process;
    }
}
export class Stability {
    constructor(
        public hasStability: boolean | null = null,
        public explaination: string | null = null,
        public answers: string | null = null
    ) {
        this.hasStability = hasStability;
        this.explaination = explaination;
        this.answers = answers;
    }
}
export class InteroperabilityIntegration {
    constructor(
        public hasInteroperabilityOrIntegration: boolean | null = null,
        public explaination: string | null = null,
        public answers: string | null = null
    ) {
        this.hasInteroperabilityOrIntegration = hasInteroperabilityOrIntegration;
        this.explaination = explaination;
        this.answers = answers;
    }
}
export class VisualizationCapacity {
    constructor(
        public areCapabilitiesPresent: boolean | null = null,
        public explaination: string | null = null,
        public answers: string | null = null
    ) {
        this.areCapabilitiesPresent = areCapabilitiesPresent;
        this.explaination = explaination;
        this.answers = answers;
    }
}
