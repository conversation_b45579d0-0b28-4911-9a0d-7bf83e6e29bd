export default class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public step_A: Step_A_Response,
    public step_B: Step_B_Response
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.step_A = step_A;
    this.step_B = step_B;
  }

  static init = () =>
    new Response_1(
      false,
      null,
      null,
      new Step_A_Response(
        new FlexibilityResponse(),
        new StabilityResponse(),
        new InteroperabilityOrIntegrationResponse(),
        new VisualizationCapacitiesResponse()
      ),
      new Step_B_Response()
    );
}

export class Step_A_Response {
  constructor(
    public flexibilityResponse: FlexibilityResponse,
    public stabilityResponse: StabilityResponse,
    public interoperabilityOrIntegrationResponse: InteroperabilityOrIntegrationResponse,
    public visualizationCapacitiesResponse: VisualizationCapacitiesResponse
  ) {
    this.flexibilityResponse = flexibilityResponse;
    this.stabilityResponse = stabilityResponse;
    this.interoperabilityOrIntegrationResponse =
      interoperabilityOrIntegrationResponse;
    this.visualizationCapacitiesResponse = visualizationCapacitiesResponse;
  }
  [index: string]: string | any;
}

export class Step_B_Response {
  constructor(
    public informationSystemEasy: string | null = null,
    public additionalAttributes: string | null = null,
    public survellianceInformation: string | null = null
  ) {
    this.informationSystemEasy = informationSystemEasy;
    this.additionalAttributes = additionalAttributes;
    this.survellianceInformation = survellianceInformation;
  }
}

export class FlexibilityResponse {
  constructor(
    public isFlexibility: boolean | null = null,
    public explain: string | null = null,
    public flexibilityProcess: string | null = null
  ) {
    this.isFlexibility = isFlexibility;
    this.explain = explain;
    this.flexibilityProcess = flexibilityProcess;
  }
}
export class StabilityResponse {
  constructor(
    public isStability: boolean | null = null,
    public explain: string | null = null,
    public systemOperating: string | null = null
  ) {
    this.isStability = isStability;
    this.explain = explain;
    this.systemOperating = systemOperating;
  }
}
export class InteroperabilityOrIntegrationResponse {
  constructor(
    public isInteroperability: boolean | null = null,
    public explain: string | null = null,
    public standardDataFormats: string | null = null
  ) {
    this.isInteroperability = isInteroperability;
    this.explain = explain;
    this.standardDataFormats = standardDataFormats;
  }
}
export class VisualizationCapacitiesResponse {
  constructor(
    public areCapabilitiesPresent: boolean | null = null,
    public explain: string | null = null,
    public dashboardInterface: string | null = null
  ) {
    this.areCapabilitiesPresent = areCapabilitiesPresent;
    this.explain = explain;
    this.dashboardInterface = dashboardInterface;
  }
}
