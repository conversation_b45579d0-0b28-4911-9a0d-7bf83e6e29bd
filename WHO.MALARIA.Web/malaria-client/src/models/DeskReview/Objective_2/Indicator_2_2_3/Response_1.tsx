export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public uniqueIdentifierExists: boolean | null,
    public uniqueIdentifierDetails: string | null,
    public commonUniqueIdentifierExists: boolean | null,
    public commonUniqueIdentifierDetails: string | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.uniqueIdentifierExists = uniqueIdentifierExists;
    this.uniqueIdentifierDetails = uniqueIdentifierDetails;
    this.commonUniqueIdentifierExists = commonUniqueIdentifierExists;
    this.commonUniqueIdentifierDetails = commonUniqueIdentifierDetails;
  }

  static init = () => new Response_1(false, null, null, null, null, null, null);
}
