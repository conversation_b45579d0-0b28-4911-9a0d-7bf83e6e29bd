export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public noInformationSystemExists: boolean | null,
    public informationSystems: Array<InformationSystem>
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.noInformationSystemExists = noInformationSystemExists;
    this.informationSystems = informationSystems;
  }
  static init = () => new Response_1(false, null, null, null, []);
}

export class InformationSystem {
  constructor(
    public informationSystem1: string | null,
    public informationSystem2: string | null,
    public informationSystem3: string | null,
    public informationSystem4: string | null
  ) {
    this.informationSystem1 = informationSystem1;
    this.informationSystem2 = informationSystem2;
    this.informationSystem3 = informationSystem3;
    this.informationSystem4 = informationSystem4;
  }
}
