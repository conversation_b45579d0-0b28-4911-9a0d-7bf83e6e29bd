export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public guideline1: Guideline,
        public guideline2: Guideline,
        public guideline3: Guideline, 
        public guideline4: Guideline,
        public malariaCase: MinimumExpectedContent,
        public proceduralGuideLine: MinimumExpectedContent,
        public dataSecurityStatement: MinimumExpectedContent,
        public nonCompliance: MinimumExpectedContent,
        public highRiskPopulation: MinimumExpectedContent,
        public caseInvestigation: MinimumExpectedContent,
        public caseAcquiredLocally: MinimumExpectedContent,
        public additionalInvestigation: MinimumExpectedContent,
        public malariaOutbreak: MinimumExpectedContent,
        public malariaCasesAnnuallyReported: MinimumExpectedContent

    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.guideline1 = guideline1;
        this.guideline2 = guideline2;
        this.guideline3 = guideline3;
        this.guideline4 = guideline4;
        this.malariaCase = malariaCase;
        this.proceduralGuideLine = proceduralGuideLine;
        this.dataSecurityStatement = dataSecurityStatement;
        this.nonCompliance = nonCompliance;
        this.highRiskPopulation = highRiskPopulation;
        this.caseInvestigation = caseInvestigation;
        this.caseAcquiredLocally = caseAcquiredLocally;
        this.additionalInvestigation = additionalInvestigation;
        this.malariaOutbreak = malariaOutbreak;
        this.malariaCasesAnnuallyReported = malariaCasesAnnuallyReported;
    }

    static init = () => new Response_1(false, null,
        new Guideline(null, null),
        new Guideline(null, null),
        new Guideline(null, null),
        new Guideline(null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
        new MinimumExpectedContent(null, null, null, null, null, null, null, null),
    );
}

export class Guideline {
    constructor(
        public name: string | null,
        public publicationDate: string | null,
    ) {
        this.name = name;
        this.publicationDate = publicationDate;
    }
}

export class MinimumExpectedContent {
    constructor(
        public isFirstChecklist: boolean | null,
        public firstCheckListDetails: string | null,
        public isSecondChecklist: boolean | null,
        public secondCheckListDetails: string | null,
        public isThirdChecklist: boolean | null,
        public thirdCheckListDetails: string | null,
        public isFourthChecklist: boolean | null,
        public fourthCheckListDetails: string | null,
    ) {
        this.isFirstChecklist = isFirstChecklist;
        this.firstCheckListDetails = firstCheckListDetails;
        this.isSecondChecklist = isSecondChecklist;
        this.secondCheckListDetails = secondCheckListDetails;
        this.isThirdChecklist = isThirdChecklist;
        this.thirdCheckListDetails = thirdCheckListDetails;
        this.isFourthChecklist = isFourthChecklist;
        this.fourthCheckListDetails = fourthCheckListDetails;
    }
}