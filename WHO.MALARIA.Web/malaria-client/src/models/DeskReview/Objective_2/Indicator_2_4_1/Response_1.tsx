export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public surveillanceStaffs: Array<SurveillanceStaff>
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.surveillanceStaffs = surveillanceStaffs;
  }

  static init = () => new Response_1(false, null, null, []);
}

export class SurveillanceStaff {
  constructor(
    public staffRole: string | null,
    public nationalRequired: number | null,
    public nationalCurrentlyAvailable: number | null,
    public subNationalRequired: number | null,
    public subNationalCurrentlyAvailable: number | null,
    public serviceDeliveryRequired: number | null,
    public serviceDeliveryCurrentlyAvailable: number | null
  ) {
    this.staffRole = staffRole;
    this.nationalRequired = nationalRequired;
    this.nationalCurrentlyAvailable = nationalCurrentlyAvailable;
    this.subNationalRequired = subNationalRequired;
    this.subNationalCurrentlyAvailable = subNationalCurrentlyAvailable;
    this.serviceDeliveryRequired = serviceDeliveryRequired;
    this.serviceDeliveryCurrentlyAvailable = serviceDeliveryCurrentlyAvailable;
  }

  static init = () =>
    new SurveillanceStaff(null, null, null, null, null, null, null);
}
