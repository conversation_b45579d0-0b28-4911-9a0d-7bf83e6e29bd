export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public drugEfficacyMonitoringIntegrated: boolean | null,
    public drugEfficacyMonitoringDetails: string | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.drugEfficacyMonitoringIntegrated = drugEfficacyMonitoringIntegrated;
    this.drugEfficacyMonitoringDetails = drugEfficacyMonitoringDetails;
  }

  static init = () => new Response_1(false, null, null, null, null);
}
