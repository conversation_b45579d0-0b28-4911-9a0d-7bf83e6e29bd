import { DeskReviewAssessmentResponseStatus } from "../../Enums";

export default class DiagramUploadModel {
    constructor(public assessmentId: string,
        public strategyId: string,
        public status: number,
        public diagramFiles: Array<DiagramFile>) {
        this.assessmentId = assessmentId;
        this.strategyId = strategyId;
        this.status = status;
        this.diagramFiles = diagramFiles;
    }

    static init = (diagramFiles: Array<DiagramFile>) => new DiagramUploadModel("", "", DeskReviewAssessmentResponseStatus.InProgress, diagramFiles);
}

export class DiagramFile {
    constructor(public id: string,
        public file: File | null,
        public order: number,
        public status: number,
        public extension: string = "",
    ) {
        this.id = id;
        this.file = file;
        this.order = order;
        this.status = status;
        this.extension = extension;
    }
}