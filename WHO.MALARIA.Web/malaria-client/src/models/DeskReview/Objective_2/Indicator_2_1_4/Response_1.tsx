export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public step_A: Step_A_Response,
    public step_B: Step_B_Response
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.step_A = step_A;
    this.step_B = step_B; 
  } 
  static init = () =>
    new Response_1(
      false,
      null,
      null,
      new Step_A_Response(
        new CaseDefinition(
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline()
        ),
        new CaseClassification(
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline()
        ),
        new FociClassification(
          new StrategiesGuideline(),
          new StrategiesGuideline(),
          new StrategiesGuideline()
        ),
        new CaseActiveDetection(null, null)
      ),
      new Step_B_Response(
        new TimeframeGuideline(),
        new TimeframeGuideline(),
        new TimeframeGuideline(),
        new TimeframeGuideline(),
        new TimeframeGuideline()
      )
    );
}

export class Step_A_Response { 
  constructor(
    public caseDefinition: CaseDefinition,
    public caseClassification: CaseClassification,
    public fociClassification: FociClassification,
    public activeCaseDetection: CaseActiveDetection
  ) {
    this.caseDefinition = caseDefinition;
    this.caseClassification = caseClassification;
    this.fociClassification = fociClassification;
    this.activeCaseDetection = activeCaseDetection;
  }
  [index: string]: string | any;
}

export class CaseDefinition {
  constructor(
    public suspectedCase: StrategiesGuideline,
    public presumedCase: StrategiesGuideline,
    public confirmedCase: StrategiesGuideline
  ) {
    this.suspectedCase = suspectedCase;
    this.presumedCase = presumedCase;
    this.confirmedCase = confirmedCase;
  }
  [index: string]: string | any;
}

export class CaseClassification {
  constructor(
    public local: StrategiesGuideline,
    public indigenous: StrategiesGuideline,
    public introduced: StrategiesGuideline,
    public imported: StrategiesGuideline,
    public induced: StrategiesGuideline,
    public recrudescent: StrategiesGuideline,
    public relapsing: StrategiesGuideline,
    public recurrent: StrategiesGuideline
  ) {
    this.local = local;
    this.indigenous = indigenous;
    this.introduced = introduced;
    this.imported = imported;
    this.induced = induced;
    this.recrudescent = recrudescent;
    this.relapsing = relapsing;
    this.recurrent = recurrent;
  }
  [index: string]: string | any;
}

export class FociClassification {
  constructor(
    public active: StrategiesGuideline,
    public residualNonActive: StrategiesGuideline,
    public cleared: StrategiesGuideline
  ) {
    this.active = active;
    this.residualNonActive = residualNonActive;
    this.cleared = cleared;
  }
  [index: string]: string | any;
}

export class CaseActiveDetection {
  constructor(
    public countryDefinition: string | null = null,
    public definitionOK: boolean | null = null
  ) {
    this.countryDefinition = countryDefinition;
    this.definitionOK = definitionOK;
  }
  [index: string]: string | any;
}

export class StrategiesGuideline {
  constructor(
    public countryDefinition: string | null = null,
    public definitionOK: boolean | null = null
  ) {
    this.countryDefinition = countryDefinition;
    this.definitionOK = definitionOK;
  }
}

export class Step_B_Response {
  constructor(
    public notification: TimeframeGuideline,
    public caseInvestigation: TimeframeGuideline,
    public caseClassification: TimeframeGuideline,
    public fociInvestigation: TimeframeGuideline,
    public response: TimeframeGuideline
  ) {
    this.notification = notification;
    this.caseInvestigation = caseInvestigation;
    this.caseClassification = caseClassification;
    this.fociInvestigation = fociInvestigation;
    this.response = response;
  }
  [index: string]: string | any;
}

export class TimeframeGuideline {
  constructor(
    public national: string | null = null,
    public subNational: string | null = null,
    public timeframeOk: boolean | null = null
  ) {
    this.national = national;
    this.subNational = subNational;
    this.timeframeOk = timeframeOk;
  }
}
