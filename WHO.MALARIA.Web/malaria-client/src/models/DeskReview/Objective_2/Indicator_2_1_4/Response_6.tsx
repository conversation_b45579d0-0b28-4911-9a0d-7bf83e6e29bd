﻿export class Response_6 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public definition1: Definition,
        public definition2: Definition,
        public definition3: Definition      
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.definition1 = definition1;
        this.definition2 = definition2;
        this.definition3 = definition3;
    }

    static init = () => new Response_6(
        false,
        null,
        new Definition(),
        new Definition(),
        new Definition(),
    );
}

export class Definition {
    constructor(
        public country: string | null = null,
        public isOk: boolean | null = null
    ) {
        this.country = country;
        this.isOk = isOk;
    }
}