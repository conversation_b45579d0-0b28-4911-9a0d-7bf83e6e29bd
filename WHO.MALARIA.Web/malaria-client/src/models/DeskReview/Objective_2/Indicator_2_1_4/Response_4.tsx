﻿export class Response_4 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public step_A: Step_A_Response,
        public step_B: Step_B_Response
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_A = step_A;
        this.step_B = step_B;
    }
    static init = () =>
        new Response_4(
            false,
            null,
            null,
            new Step_A_Response(
                new CaseDefinition(
                    new StrategiesGuideline(),
                    new StrategiesGuideline(),
                    new StrategiesGuideline()
                ),                                           
            ),
            new Step_B_Response(
                new TimeframeGuideline()               
            )
        );
}

export class Step_A_Response {
    constructor(
        public caseDefinition: CaseDefinition,       
    ) {
        this.caseDefinition = caseDefinition;     
    }
    [index: string]: string | any;
}

export class CaseDefinition {
    constructor(
        public suspectedCase: StrategiesGuideline,
        public presumedCase: StrategiesGuideline,
        public confirmedCase: StrategiesGuideline
    ) {
        this.suspectedCase = suspectedCase;
        this.presumedCase = presumedCase;
        this.confirmedCase = confirmedCase;
    }
    [index: string]: string | any;
}

export class StrategiesGuideline {
    constructor(
        public countryDefinition: string | null = null,
        public definitionOK: boolean | null = null
    ) {
        this.countryDefinition = countryDefinition;
        this.definitionOK = definitionOK;
    }
}

export class Step_B_Response {
    constructor(
        public notification: TimeframeGuideline,       
    ) {
        this.notification = notification;       
    }
    [index: string]: string | any;
}

export class TimeframeGuideline {
    constructor(
        public national: string | null = null,
        public subNational: string | null = null,
        public timeframeOk: boolean | null = null
    ) {
        this.national = national;
        this.subNational = subNational;
        this.timeframeOk = timeframeOk;
    }
}