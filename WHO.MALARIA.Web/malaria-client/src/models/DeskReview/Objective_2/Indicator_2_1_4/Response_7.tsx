export class Response_7 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public definition1: Definition,
        public definition2: Definition,
        public definition3: Definition,
        public definition4: Definition,
        public definition5: Definition
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.definition1 = definition1;
        this.definition2 = definition2;
        this.definition3 = definition3;
        this.definition4 = definition4;
        this.definition5 = definition5;
    }

    static init = () => new Response_7(
        false,
        null,
        Definition.init(),
        Definition.init(),
        Definition.init(),
        Definition.init(),
        Definition.init()
    );
}

export class Definition {
    constructor(
        public country: string | null,
        public isOk: boolean | null
    ) {
        this.country = country;
        this.isOk = isOk;
    }
    static init = () => new Definition(null, null);
}