﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public definition1: Definition,
        public definition2: Definition,
        public adverseEvent: Definition,
        public adverseDrugReaction: Definition,
        public seriousAdverseEvent: Definition
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.definition1 = definition1;
        this.definition2 = definition2;
        this.adverseEvent = adverseEvent;
        this.adverseDrugReaction = adverseDrugReaction;
        this.seriousAdverseEvent = seriousAdverseEvent;
    }

    static init = () => new Response_2(
        false,
        null,
        new Definition(),
        new Definition(),
        new Definition(),
        new Definition(),
        new Definition()
    );
}

export class Definition {
    constructor(
        public country: string | null = null,
        public isOk: boolean | null = null
    ) {
        this.country = country;
        this.isOk = isOk;
    }
}
