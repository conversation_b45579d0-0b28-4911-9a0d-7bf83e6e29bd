﻿export class Response_5 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public definition1: Definition,
        public definition2: Definition,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.definition1 = definition1;
        this.definition2 = definition2;
    }

    static init = () => new Response_5(
        false,
        null,
        Definition.init(),
        Definition.init()
    );
}

export class Definition {
    constructor(
        public country: string | null,
        public isOk: boolean | null
    ) {
        this.country = country;
        this.isOk = isOk;
    }
    static init = () => new Definition(null, null);
}