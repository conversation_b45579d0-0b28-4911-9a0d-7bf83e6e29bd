﻿export class Response_3 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public definition1: Definition,
        public definition2: Definition,
        public definition3: Definition,
        public definition4: Definition
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.definition1 = definition1;
        this.definition2 = definition2;
        this.definition3 = definition3;
        this.definition4 = definition4;
    }

    static init = () => new Response_3(
        false,
        null,
        new Definition(),
        new Definition(),
        new Definition(),
        new Definition(),
    );
}

export class Definition {
    constructor(
        public country: string | null = null,
        public isOk: boolean | null = null
    ) {
        this.country = country;
        this.isOk = isOk;
    }
}