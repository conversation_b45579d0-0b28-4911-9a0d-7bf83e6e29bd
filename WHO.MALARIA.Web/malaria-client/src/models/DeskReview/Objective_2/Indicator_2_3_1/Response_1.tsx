export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public guideline1: Guideline,
        public guideline2: Guideline,
        public guideline3: Guideline,
        public guideline4: Guideline,
        public healthSector: HealthSector,
        public metNotMetStatus: string
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.guideline1 = guideline1;
        this.guideline2 = guideline2;
        this.guideline3 = guideline3;
        this.guideline4 = guideline4;
        this.healthSector = healthSector;
        this.metNotMetStatus = metNotMetStatus;
    }

    static init = () => new Response_1(
        false,
        null,
        new Guideline(null, null, null),
        new Guideline(null, null, null),
        new Guideline(null, null, null),
        new Guideline(null, null, null),
        HealthSector.init(),
        ""
    );
}

export class Guideline {
    constructor(
        public guideLineName: string | null,
        public linkToCopy: string | null,
        public internetAvailability: boolean | null
    ) {
        this.guideLineName = guideLineName;
        this.linkToCopy = linkToCopy;
        this.internetAvailability = internetAvailability;
    }
}

export class HealthSector {
    constructor(public publicHealthSector: PublicHealthSector,
        public privateFormal: PrivateFormalHealthSector,
        public privateInformal: GuidelineDetails,
        public community: GuidelineDetails) {
        this.publicHealthSector = publicHealthSector;
        this.privateFormal = privateFormal;
        this.privateInformal = privateInformal;
        this.community = community;
    }

    static init = () => new HealthSector(
        PublicHealthSector.init(),
        PrivateFormalHealthSector.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init());

    [index: string]: PublicHealthSector | PrivateFormalHealthSector | GuidelineDetails;
}

class CommonHealthSectorCategories {
    constructor(public healthFacility: GuidelineDetails,
        public hospital: GuidelineDetails,
        public laboratory: GuidelineDetails) {
        this.healthFacility = healthFacility;
        this.hospital = hospital;
        this.laboratory = laboratory;
    }
}

class PublicHealthSector extends CommonHealthSectorCategories {
    constructor(public publicReportingDetails: GuidelineDetails,
        public healthFacility: GuidelineDetails,
        public hospital: GuidelineDetails,
        public laboratory: GuidelineDetails) {
        super(healthFacility, hospital, laboratory);
        this.publicReportingDetails = publicReportingDetails
    }

    static init = () => new PublicHealthSector(
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init());

    [index: string]: GuidelineDetails;
}

class PrivateFormalHealthSector extends CommonHealthSectorCategories {
    constructor(public privateFormal: GuidelineDetails,
        public healthFacility: GuidelineDetails,
        public hospital: GuidelineDetails,
        public laboratory: GuidelineDetails,
        public faithBasedClinic: GuidelineDetails,
        public ngoClinic: GuidelineDetails,
        public military: GuidelineDetails,
        public police: GuidelineDetails,
        public prison: GuidelineDetails) {
        super(healthFacility, hospital, laboratory);
        this.privateFormal = privateFormal;
        this.faithBasedClinic = faithBasedClinic;
        this.ngoClinic = ngoClinic;
        this.military = military;
        this.police = police;
        this.prison = prison;
    }

    static init = () => new PrivateFormalHealthSector(
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init(),
        GuidelineDetails.init());

    [index: string]: GuidelineDetails;
}

export class GuidelineDetails {
    constructor(
        public isFirstGuidelineReceived: boolean | null,
        public firstGuidelineDetails: string | null,
        public isSecondGuidelineReceived: boolean | null,
        public secondGuidelineDetails: string | null,
        public isThirdGuidelineReceived: boolean | null,
        public thirdGuidelineDetails: string | null,
        public isFourthGuidelineReceived: boolean | null,
        public fourthGuidelineDetails: string | null
    ) {
        this.isFirstGuidelineReceived = isFirstGuidelineReceived;
        this.firstGuidelineDetails = firstGuidelineDetails;
        this.isSecondGuidelineReceived = isSecondGuidelineReceived;
        this.secondGuidelineDetails = secondGuidelineDetails;
        this.isThirdGuidelineReceived = isThirdGuidelineReceived;
        this.thirdGuidelineDetails = thirdGuidelineDetails;
        this.isFourthGuidelineReceived = isFourthGuidelineReceived;
        this.fourthGuidelineDetails = fourthGuidelineDetails;
    }

    static init = () => new GuidelineDetails(null, null, null, null, null, null, null, null);

    [index: string]: boolean | string | null;
}