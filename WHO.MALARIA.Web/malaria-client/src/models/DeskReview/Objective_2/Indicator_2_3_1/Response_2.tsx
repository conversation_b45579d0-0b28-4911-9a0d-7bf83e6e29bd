﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public guideline1: Guideline,
        public publicSector: HealthSector,
        public privateFormal: HealthSector,
        public privateInformal: HealthSector,
        public community: HealthSector
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.guideline1 = guideline1;
        this.publicSector = publicSector;
        this.privateFormal = privateFormal;
        this.privateInformal = privateInformal;
        this.community = community;
    }

    static init = () => new Response_2(
        false,
        null,
        new Guideline(null, null, null),
        new HealthSector(null, null),
        new HealthSector(null, null),
        new HealthSector(null, null),
        new HealthSector(null, null),
    );
}

export class Guideline {
    constructor(
        public name: string | null,
        public linkToCopy: string | null,
        public internetAvailability: boolean | null
    ) {
        this.name = name;
        this.linkToCopy = linkToCopy;
        this.internetAvailability = internetAvailability;
    }
}

export class HealthSector {
    constructor(
        public hasGuidelineReceived : boolean | null,
        public guidelineDetails: string | null,
    ) {
        this.hasGuidelineReceived = hasGuidelineReceived;
        this.guidelineDetails = guidelineDetails;
    }
}