export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public equipmentAvailabilities: Array<EquipmentAvailability>
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.equipmentAvailabilities = equipmentAvailabilities;
  }

  static init = () => new Response_1(false, null, null, []);
}

export class EquipmentAvailability {
  constructor(
    public equipment: string | null,
    public nationalRequired: number | null,
    public nationalCurrentlyAvailable: number | null,
    public subNationalRequired: number | null,
    public subNationalCurrentlyAvailable: number | null,
    public serviceDeliveryRequired: number | null,
    public serviceDeliveryCurrentlyAvailable: number | null
  ) {
    this.equipment = equipment;
    this.nationalRequired = nationalRequired;
    this.nationalCurrentlyAvailable = nationalCurrentlyAvailable;
    this.subNationalRequired = subNationalRequired;
    this.subNationalCurrentlyAvailable = subNationalCurrentlyAvailable;
    this.serviceDeliveryRequired = serviceDeliveryRequired;
    this.serviceDeliveryCurrentlyAvailable = serviceDeliveryCurrentlyAvailable;
  }
  [index: string]: any;
  static init = () =>
    new EquipmentAvailability(null, null, null, null, null, null, null);
}