
export class Response_1 {
    constructor(
        public plannedAllocation: string | null,
        public procedureForTroubleShooting: string | null,
        public discrepanciesInThePlan: string | null
    ) {
        this.plannedAllocation = plannedAllocation;
        this.procedureForTroubleShooting = procedureForTroubleShooting;
        this.discrepanciesInThePlan = discrepanciesInThePlan;
    }
    static init = () => new Response_1(null, null, null);
}