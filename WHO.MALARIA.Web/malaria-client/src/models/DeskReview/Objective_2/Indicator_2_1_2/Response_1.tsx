export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public healthSector: HealthSector | null,
    public allSectorsMandatedToReport: boolean | null,
    public allSectorsReportedCaseBasedData: boolean | null,
    public isOnePublicHealthSystemDescendentNotSelected: boolean = false, //It is only used in the validation rules to hold validation error key
    public isParentPublicHealthSectorNotSelected: boolean = false, //It is only used in the validation rules to hold validation error key
    public isOnePrivateFormalDescendentHealthSystemNotSelected: boolean = false, //It is only used in the validation rules to hold validation error key
    public isParentPrivateFormalHealthSectorNotSelected: boolean = false, //It is only used in the validation rules to hold validation error key
    public isBurdenReductionStrategy: boolean | null = null //It is used in the validation rules in conditions
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.healthSector = healthSector;
    this.metNotMetStatus = metNotMetStatus;
    this.allSectorsMandatedToReport = allSectorsMandatedToReport;
    this.isOnePublicHealthSystemDescendentNotSelected = isOnePublicHealthSystemDescendentNotSelected;
    this.isParentPublicHealthSectorNotSelected = isParentPublicHealthSectorNotSelected;
    this.isOnePrivateFormalDescendentHealthSystemNotSelected = isOnePrivateFormalDescendentHealthSystemNotSelected;
    this.isParentPrivateFormalHealthSectorNotSelected = isParentPrivateFormalHealthSectorNotSelected;
    this.isBurdenReductionStrategy = isBurdenReductionStrategy;
  }
  static init = () => new Response_1(false, null, null, HealthSector.init(), null, null);
}

export class HealthSector {
  constructor(public publicHealthSector: PublicHealthSector,
    public privateFormal: PrivateFormalHealthSector,
    public privateInformal: ReportingDetails,
    public community: ReportingDetails) {
    this.publicHealthSector = publicHealthSector;
    this.privateFormal = privateFormal;
    this.privateInformal = privateInformal;
    this.community = community;
  }

  static init = () => new HealthSector(
    PublicHealthSector.init(),
    PrivateFormalHealthSector.init(),
    ReportingDetails.init(),
    ReportingDetails.init());

  [index: string]: PublicHealthSector | PrivateFormalHealthSector | ReportingDetails;
}

class CommonHealthSectorCategories {
  constructor(public healthFacility: ReportingDetails,
    public hospital: ReportingDetails,
    public laboratory: ReportingDetails) {
    this.healthFacility = healthFacility;
    this.hospital = hospital;
    this.laboratory = laboratory;
  }
}

export class PublicHealthSector extends CommonHealthSectorCategories {
  constructor(public publicReportingDetails: ReportingDetails,
    public healthFacility: ReportingDetails,
    public hospital: ReportingDetails,
    public laboratory: ReportingDetails) {
    super(healthFacility, hospital, laboratory);
    this.publicReportingDetails = publicReportingDetails
  }

  static init = () => new PublicHealthSector(
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init());

  [index: string]: ReportingDetails;
}

export class PrivateFormalHealthSector extends CommonHealthSectorCategories {
  constructor(public privateFormal: ReportingDetails,
    public healthFacility: ReportingDetails,
    public hospital: ReportingDetails,
    public laboratory: ReportingDetails,
    public faithBasedClinic: ReportingDetails,
    public ngoClinic: ReportingDetails,
    public military: ReportingDetails,
    public police: ReportingDetails,
    public prison: ReportingDetails) {
    super(healthFacility, hospital, laboratory);
    this.privateFormal = privateFormal;
    this.faithBasedClinic = faithBasedClinic;
    this.ngoClinic = ngoClinic;
    this.military = military;
    this.police = police;
    this.prison = prison;
  }

  static init = () => new PrivateFormalHealthSector(
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init(),
    ReportingDetails.init());

  [index: string]: ReportingDetails;
}

export class ReportingDetails {
  constructor(
    public reportingMalaria: boolean | null,
    public mandatedToReport: boolean | null,
    public caseBasedDataReported: boolean | null,
    public national: string | null,
    public subNational: string | null,
    public serviceDelivery: string | null,
    public notificationProcesses: string | null
  ) {
    this.reportingMalaria = reportingMalaria;
    this.mandatedToReport = mandatedToReport;
    this.caseBasedDataReported = caseBasedDataReported;
    this.national = national;
    this.subNational = subNational;
    this.serviceDelivery = serviceDelivery;
    this.notificationProcesses =
      notificationProcesses;
  }

  static init = () => new ReportingDetails(null, null, null, null, null, null, null);

  [index: string]: any;
}
