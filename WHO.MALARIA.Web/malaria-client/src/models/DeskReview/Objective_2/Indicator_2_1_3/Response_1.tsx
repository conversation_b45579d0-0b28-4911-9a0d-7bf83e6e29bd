export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public step_A: Step_A_Response,
        public step_B: Step_B_Response,
        //proportionRate is used calculate the percentage of proportion and based on this field validation is added in validation rules 
        //not saved in DB
        public proportionRate: number | null,
        public proportionRateForOtherStrategy: number | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_A = step_A;
        this.step_B = step_B;
        this.proportionRate = proportionRate;
        this.proportionRateForOtherStrategy = proportionRateForOtherStrategy;
    }

    static init = () =>
        new Response_1(
            false,
            null,
            null,
            new Step_A_Response(
                new EliminationActivity(),
                new EliminationActivity(),
                new EliminationActivity(),
                new EliminationActivity(),
                new EliminationActivity(),
                new EliminationActivity(),
                new EliminationActivity()
            ),
            new Step_B_Response(
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy(),
                new OtherMalariaControlStrategy()
            ),
            null,
            null
        );
}

export class Step_A_Response {
    constructor(
        public caseInvestigation: EliminationActivity,
        public caseClassification: EliminationActivity,
        public focusInvestigation: EliminationActivity,
        public focusClassificiation: EliminationActivity,
        public activeCaseDetection: EliminationActivity,
        public reactiveDetection: EliminationActivity,
        public proactiveDetection: EliminationActivity
    ) {
        this.caseInvestigation = caseInvestigation;
        this.caseClassification = caseClassification;
        this.focusInvestigation = focusInvestigation;
        this.focusClassificiation = focusClassificiation;
        this.activeCaseDetection = activeCaseDetection;
        this.reactiveDetection = reactiveDetection;
        this.proactiveDetection = proactiveDetection;
    }
}

export class EliminationActivity {
    constructor(
        public activityInPlace: boolean | null = null,
        public surveillanceImplemented: boolean | null = null,
        public dataIntegratedWithRoutineCaseNotificationData: boolean | null = null,
        public dataLinkage: boolean | null = null,
        public detailsOnDataLinkageToTheIndexCase: string | null = null,
        public nationalLevelOfInvolvement: string | null = null,
        public subNationalLevelOfInvolvement: string | null = null,
        public serviceDeliveryLevelOfInvolvement: string | null = null,
        public challengesWithReporting: string | null = null
    ) {
        this.activityInPlace = activityInPlace;
        this.surveillanceImplemented = surveillanceImplemented;
        this.dataIntegratedWithRoutineCaseNotificationData =
            dataIntegratedWithRoutineCaseNotificationData;
        this.dataLinkage = dataLinkage;
        this.detailsOnDataLinkageToTheIndexCase =
            detailsOnDataLinkageToTheIndexCase;
        this.nationalLevelOfInvolvement = nationalLevelOfInvolvement;
        this.subNationalLevelOfInvolvement = subNationalLevelOfInvolvement;
        this.serviceDeliveryLevelOfInvolvement = serviceDeliveryLevelOfInvolvement;
        this.challengesWithReporting = challengesWithReporting;
    }
}

export class Step_B_Response {
    constructor(
        public chemoPreventionInPregnantWomen: OtherMalariaControlStrategy,
        public chemoPreventionInInfancy: OtherMalariaControlStrategy,
        public chemoPreventionSMC: OtherMalariaControlStrategy,
        public chemoPreventionMDA: OtherMalariaControlStrategy,
        public vectorControlRoutineChannel: OtherMalariaControlStrategy,
        public vectorControlMassCampaign: OtherMalariaControlStrategy,
        public vectorControlIRS: OtherMalariaControlStrategy,
        public vectorControlLSM: OtherMalariaControlStrategy,
        public drugEfficacy: OtherMalariaControlStrategy,
        public genomicSurveillance: OtherMalariaControlStrategy,
        public entomologicalSurveillance: OtherMalariaControlStrategy,
        public commodityTracking: OtherMalariaControlStrategy,
        public vitalRegistrationSystem: OtherMalariaControlStrategy,
        public laboratoryData: OtherMalariaControlStrategy,
        public specifyOther: OtherMalariaControlStrategy
    ) {
        this.chemoPreventionInPregnantWomen = chemoPreventionInPregnantWomen;
        this.chemoPreventionInInfancy = chemoPreventionInInfancy;
        this.chemoPreventionSMC = chemoPreventionSMC;
        this.chemoPreventionMDA = chemoPreventionMDA;
        this.vectorControlRoutineChannel = vectorControlRoutineChannel;
        this.vectorControlMassCampaign = vectorControlMassCampaign;
        this.vectorControlIRS = vectorControlIRS;
        this.vectorControlLSM = vectorControlLSM;
        this.drugEfficacy = drugEfficacy;
        this.genomicSurveillance = genomicSurveillance;
        this.entomologicalSurveillance = entomologicalSurveillance;
        this.commodityTracking = commodityTracking;
        this.vitalRegistrationSystem = vitalRegistrationSystem;
        this.laboratoryData = laboratoryData;
        this.specifyOther = specifyOther;
    }
}

export class OtherMalariaControlStrategy {
    constructor(
        public otherStrategy: string | null = null,
        public strategyInPlace: boolean | null = null,
        public surveillanceImplemented: boolean | null = null,
        public dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem:
            | boolean
            | null = null,
        public methodOfIntegration: string | null = null,
        public dataLinkage: string | null = null,
        public detailsOnDataLinkageToTheIndexCase: string | null = null
    ) {
        this.otherStrategy = otherStrategy;
        this.strategyInPlace = strategyInPlace;
        this.surveillanceImplemented = surveillanceImplemented;
        this.dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem =
            dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem;
        this.methodOfIntegration = methodOfIntegration;
        this.dataLinkage = dataLinkage;
        this.detailsOnDataLinkageToTheIndexCase =
            detailsOnDataLinkageToTheIndexCase;
    }
}
