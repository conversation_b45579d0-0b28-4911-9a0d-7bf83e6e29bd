﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public step_B: Step_B_Response,
        //proportionRate is used calculate the percentage of proportion and based on this field validation is added in validation rules 
        //not saved in DB
        public proportionRateForOtherStrategy: number | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_B = step_B;
        this.proportionRateForOtherStrategy = proportionRateForOtherStrategy;
    }

    static init = () =>
        new Response_2(
            false,
            null,
            null,
            new Step_B_Response(
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null),
                new OtherMalariaControlStrategy(null, null, null, null, null, null, null)
            ),
            null
        );
}

export class Step_B_Response {
    constructor(
        public chemoPreventionInPregnantWomen: OtherMalariaControlStrategy,
        public chemoPreventionInInfancy: OtherMalariaControlStrategy,
        public chemoPreventionSMC: OtherMalariaControlStrategy,
        public chemoPreventionMDA: OtherMalariaControlStrategy,
        public vectorControlRoutineChannel: OtherMalariaControlStrategy,
        public vectorControlMassCampaign: OtherMalariaControlStrategy,
        public vectorControlIRS: OtherMalariaControlStrategy,
        public vectorControlLSM: OtherMalariaControlStrategy,
        public drugEfficacy: OtherMalariaControlStrategy,
        public genomicSurveillance: OtherMalariaControlStrategy,
        public entomologicalSurveillance: OtherMalariaControlStrategy,
        public commodityTracking: OtherMalariaControlStrategy,
        public vitalRegistrationSystem: OtherMalariaControlStrategy,
        public laboratoryData: OtherMalariaControlStrategy,
        public specifyOther: OtherMalariaControlStrategy
    ) {
        this.chemoPreventionInPregnantWomen = chemoPreventionInPregnantWomen;
        this.chemoPreventionInInfancy = chemoPreventionInInfancy;
        this.chemoPreventionSMC = chemoPreventionSMC;
        this.chemoPreventionMDA = chemoPreventionMDA;
        this.vectorControlRoutineChannel = vectorControlRoutineChannel;
        this.vectorControlMassCampaign = vectorControlMassCampaign;
        this.vectorControlIRS = vectorControlIRS;
        this.vectorControlLSM = vectorControlLSM;
        this.drugEfficacy = drugEfficacy;
        this.genomicSurveillance = genomicSurveillance;
        this.entomologicalSurveillance = entomologicalSurveillance;
        this.commodityTracking = commodityTracking;
        this.vitalRegistrationSystem = vitalRegistrationSystem;
        this.laboratoryData = laboratoryData;
        this.specifyOther = specifyOther;
    }
}

export class OtherMalariaControlStrategy {
    constructor(
        public otherStrategy: string | null,
        public strategyInPlace: boolean | null,
        public surveillanceImplemented: boolean | null,
        public dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem: boolean | null,
        public methodOfIntegration: string | null,
        public dataLinkage: string | null,
        public detailsOnDataLinkageToTheIndexCase: string | null
    ) {
        this.otherStrategy = otherStrategy;
        this.strategyInPlace = strategyInPlace;
        this.surveillanceImplemented = surveillanceImplemented;
        this.dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem = dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem;
        this.methodOfIntegration = methodOfIntegration;
        this.dataLinkage = dataLinkage;
        this.detailsOnDataLinkageToTheIndexCase =
            detailsOnDataLinkageToTheIndexCase;
    }
}