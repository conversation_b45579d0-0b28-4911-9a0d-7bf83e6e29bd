export class Response_1 {
    constructor(
        public totalEstimated: MalariaSurveillance,
        public fundsAvailable: MalariaSurveillance,
        public domesticFunds: MalariaSurveillance,
    ) {
        this.totalEstimated = totalEstimated;
        this.fundsAvailable = fundsAvailable;
        this.domesticFunds = domesticFunds;
    }

    static init = () => new Response_1(new MalariaSurveillance(null,null), new MalariaSurveillance(null, null), new MalariaSurveillance(null, null));
}

export class MalariaSurveillance {
    constructor(
        public currency: string | null,
        public value: number | null
    ) {
        this.currency = currency;
        this.value = value;
    }
    static init = () => new MalariaSurveillance(null, null);
}