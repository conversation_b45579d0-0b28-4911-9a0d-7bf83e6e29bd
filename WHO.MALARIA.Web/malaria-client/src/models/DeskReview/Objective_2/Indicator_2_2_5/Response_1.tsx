export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public integratedNationalElectronicMalariaDatabase: boolean | null,
    public integratedNationalElectronicMalariaDatabaseDetails: string | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.integratedNationalElectronicMalariaDatabase =
      integratedNationalElectronicMalariaDatabase;
    this.integratedNationalElectronicMalariaDatabaseDetails =
      integratedNationalElectronicMalariaDatabaseDetails;
  }
  static init = () => new Response_1(false, null, null, null, null);
}
