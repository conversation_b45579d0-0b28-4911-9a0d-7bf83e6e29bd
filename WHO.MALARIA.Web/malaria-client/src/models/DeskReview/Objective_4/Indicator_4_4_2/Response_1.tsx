export class Response_1 {
    constructor(
        public jobAidContent: JobAid,
        public proportionOfRelevantStaff: JobAidProportion,
        public estimationDescriptions: string | null
    ) {
        this.jobAidContent = jobAidContent;
        this.proportionOfRelevantStaff = proportionOfRelevantStaff;
        this.estimationDescriptions = estimationDescriptions;
    }

    static init = () => new Response_1(new JobAid(), new JobAidProportion(), null);
}

export class JobAid {
    constructor(
        public nationalLevel: string | null = null,
        public subNationalLevel: string | null = null,
        public serviceDeliveryLevel: string | null = null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}

export class JobAidProportion {
    constructor(
        public nationalLevel: number | null = null,
        public subNationalLevel: number | null = null,
        public serviceDeliveryLevel: number | null = null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}
