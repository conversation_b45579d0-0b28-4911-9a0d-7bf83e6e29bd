export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public goverenanceStructures: Array<GoverenanceStructure>
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.goverenanceStructures = goverenanceStructures;
  }

  static init = () => new Response_1(false, null, null, []);
}

export class GoverenanceStructure {
  constructor(
    public structure: string | null = null,
    public inPlace: boolean | null = null,
    public details: string | null = null
  ) {
    this.structure = structure;
    this.inPlace = inPlace;
    this.details = details;
  }
}
