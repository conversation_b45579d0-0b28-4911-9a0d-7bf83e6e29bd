﻿import { TrainingDateDetails } from "./Response_1";

export class Response_3 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public hasTraining: boolean | null,
        public dataCollection: SurveillanceDetailTask,
        public dataReporting: SurveillanceDetailTask,
        public dataQualityReview: SurveillanceDetailTask,
        public dataAnalysis: SurveillanceDetailTask,
        public disseminationReport: SurveillanceDetailTask,
        public supervision: SurveillanceDetailTask,
        public publicPrivateSectorTraining: SurveillanceDetailTask,
        public attendant: TrainingDetailTask,
        public trainingFrequency: TrainingDetailTask,
        public lastDateOfTraining: TrainingDateDetails
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.hasTraining = hasTraining;
        this.dataCollection = dataCollection;
        this.dataReporting = dataReporting;
        this.dataQualityReview = dataQualityReview;
        this.dataAnalysis = dataAnalysis;
        this.disseminationReport = disseminationReport;
        this.supervision = supervision;
        this.publicPrivateSectorTraining = publicPrivateSectorTraining;
        this.attendant = attendant;
        this.trainingFrequency = trainingFrequency;
        this.lastDateOfTraining = lastDateOfTraining;
    }

    static init = () =>
        new Response_3(
            false,
            null,
            true,
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new SurveillanceDetailTask(null, null, null),
            new TrainingDetailTask(null, null, null),
            new TrainingDetailTask(null, null, null),
            new TrainingDateDetails()
        );
}

export class SurveillanceDetailTask {
    constructor(
        public nationalLevel: boolean | null,
        public subNationalLevel: boolean | null,
        public serviceDeliveryLevel: boolean | null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}
export class TrainingDetailTask {
    constructor(
        public nationalLevel: string | null,
        public subNationalLevel: string | null,
        public serviceDeliveryLevel: string | null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}