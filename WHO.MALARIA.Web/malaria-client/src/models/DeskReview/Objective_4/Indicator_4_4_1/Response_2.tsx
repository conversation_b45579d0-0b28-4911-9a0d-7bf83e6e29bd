﻿import { TrainingDateDetails } from "./Response_1";

export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public hasTraining: boolean | null,
        public dataCollection: SurveillanceTask,
        public dataReporting: SurveillanceTask,
        public dataQualityReview: SurveillanceTask,
        public dataAnalysis: SurveillanceTask,
        public disseminationReport: SurveillanceTask,
        public supervision: SurveillanceTask,
        public adverseEvent: SurveillanceTask,
        public drugResistance: SurveillanceTask,
        public publicPrivateSectorTraining: SurveillanceTask,
        public attendant: TaskTrainingDetail,
        public trainingFrequency: TaskTrainingDetail,
        public lastDateOfTraining: TrainingDateDetails
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.hasTraining = hasTraining;
        this.dataCollection = dataCollection;
        this.dataReporting = dataReporting;
        this.dataQualityReview = dataQualityReview;
        this.dataAnalysis = dataAnalysis;
        this.disseminationReport = disseminationReport;
        this.supervision = supervision;
        this.adverseEvent = adverseEvent;
        this.drugResistance = drugResistance;
        this.publicPrivateSectorTraining = publicPrivateSectorTraining;
        this.attendant = attendant;
        this.trainingFrequency = trainingFrequency;
        this.lastDateOfTraining = lastDateOfTraining;
    }

    static init = () =>
        new Response_2(
            false,
            null,
            true,
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new SurveillanceTask(null, null, null),
            new TaskTrainingDetail(null, null, null),
            new TaskTrainingDetail(null, null, null),
            new TrainingDateDetails()
        );
}

export class SurveillanceTask {
    constructor(
        public nationalLevel: boolean | null,
        public subNationalLevel: boolean | null,
        public serviceDeliveryLevel: boolean | null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}
export class TaskTrainingDetail {
    constructor(
        public nationalLevel: string | null,
        public subNationalLevel: string | null,
        public serviceDeliveryLevel: string | null
    ) {
        this.nationalLevel = nationalLevel;
        this.subNationalLevel = subNationalLevel;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}