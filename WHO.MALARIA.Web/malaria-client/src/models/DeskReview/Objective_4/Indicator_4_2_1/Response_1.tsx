export class Response_1 {
    constructor(
        public informationalInterviews: Array<InformationalInterview>
    ) {
        this.informationalInterviews = informationalInterviews;
    }

    static init = () => new Response_1([]);
}

export class InformationalInterview {
    constructor(
        public trueOrFalse: boolean | null = null,
        public details: string | null = null
    ) {
        this.trueOrFalse = trueOrFalse;
        this.details = details;
    }
}