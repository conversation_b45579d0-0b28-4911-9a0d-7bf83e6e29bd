export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public descriptionOfSupervisionPlan: SupervisionPlanAndProcesses,
    public levelOfSupervision: SupervisionPlanAndProcesses,
    public supervisionGuidelines: SupervisionGuidlines,
    public supervisionVisitsChecklists: SupervisionGuidlines,
    public supervisionVisitSchedule: SupervisionGuidlines
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.descriptionOfSupervisionPlan = descriptionOfSupervisionPlan;
    this.levelOfSupervision = levelOfSupervision;
    this.supervisionGuidelines = supervisionGuidelines;
    this.supervisionVisitsChecklists = supervisionVisitsChecklists;
    this.supervisionVisitSchedule = supervisionVisitSchedule;
  }

  static init = () =>
    new Response_1(
      false,
      null,
      null,
      new SupervisionPlanAndProcesses(),
      new SupervisionPlanAndProcesses(),
      new SupervisionGuidlines(),
      new SupervisionGuidlines(),
      new SupervisionGuidlines()
    );
}

export class SupervisionPlanAndProcesses {
  constructor(
    public national: string | null = null,
    public subNational: string | null = null
  ) {
    this.national = national;
    this.subNational = subNational;
  }
}

export class SupervisionGuidlines {
  constructor(
    public national: boolean | null = null,
    public subNational: boolean | null = null
  ) {
    this.national = national;
    this.subNational = subNational;
  }
}
