export class Response_1 {
    constructor(
        public surveillanceStaffPerceivedAbilities: Array<SurveillanceStaffPerceivedAbility>,
        public jobRole: string | null
    ) {
        this.surveillanceStaffPerceivedAbilities = surveillanceStaffPerceivedAbilities;
        this.jobRole = jobRole;
    }

    static init = () => new Response_1([], null);
}

export class SurveillanceStaffPerceivedAbility {
    constructor(
        public tasks: string | null,
        public completionDifficulty: boolean | null,
        public description: string | null
    ) {
        this.tasks = tasks;
        this.completionDifficulty = completionDifficulty;
        this.description = description;
    }
}
