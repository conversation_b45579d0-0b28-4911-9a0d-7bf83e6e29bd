﻿export class ChecklistIndicatorModel {
    constructor(
        public id: string,
        public name: string,
        public confirmationMethod: boolean,
        public gender: boolean,
        public geography: boolean,
        public healthSector: boolean,
        public indicatorMonitored: boolean,
        public other: boolean,
        public overFive: boolean,
        public pregnantWoman: boolean,
        public underFive: boolean
    ) {
        this.id = id;
        this.name = name;
        this.confirmationMethod = confirmationMethod;
        this.gender = gender;
        this.geography = geography;
        this.healthSector = healthSector;
        this.indicatorMonitored = indicatorMonitored;
        this.other = other;
        this.overFive = overFive;
        this.pregnantWoman = pregnantWoman;
        this.underFive = underFive;
    }
}