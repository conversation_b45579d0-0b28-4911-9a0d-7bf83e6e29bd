export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public step_A: Step_A_Response,
        public step_B: Step_B_Response,
        public step_C: Step_C_Response,
        public step_D: Step_D_Response,
        public step_E: Step_E_Response
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.step_A = step_A;
        this.step_B = step_B;
        this.step_C = step_C;
        this.step_D = step_D;
        this.step_E = step_E;
    }

    static init = () =>
        new Response_1(
            false,
            null,
            null,
            new Step_A_Response(),
            new Step_B_Response(),
            new Step_C_Response(),
            new Step_D_Response(),
            new Step_E_Response(null, null, null, [new MalariaDeathsOverTimeData(), new MalariaDeathsOverTimeData(), new MalariaDeathsOverTimeData()])
        );
}

export class Step_A_Response {
    constructor(
        public isVitalRegistration: boolean | null = true,
        public vitalRegistrationDetail: string | null = null
    ) {
        this.isVitalRegistration = isVitalRegistration;
        this.vitalRegistrationDetail = vitalRegistrationDetail;
    }
}

export class Step_B_Response {
    constructor(
        public isDeathRecorded: string | null = null,
        public deathRecordedDetail: string | null = null
    ) {
        this.isDeathRecorded = isDeathRecorded;
        this.deathRecordedDetail = deathRecordedDetail;
    }
}

export class Step_C_Response {
    constructor(
        public deathCompletenessPercentage: number | null = null,
        public deathProportionPercentage: number | null = null
    ) {
        this.deathCompletenessPercentage = deathCompletenessPercentage;
        this.deathProportionPercentage = deathProportionPercentage;
    }
    [index: string]: string | any;
}

export class PercentageStatus {
    constructor(public percentage: number | null = null) {
        this.percentage = percentage;
    }
}

export class Step_D_Response {
    constructor(
        public isNMPRecorded: string | null = null,
        public nmpDeathRecordedDetail: string | null = null
    ) {
        this.isNMPRecorded = isNMPRecorded;
        this.nmpDeathRecordedDetail = nmpDeathRecordedDetail;
    }
}

export class Step_E_Response {
    constructor(
        public dataSource1: string | null,
        public dataSource2: string | null,
        public dataSource3: string | null,
        public nationalLevelDeathOverTimeData: Array<MalariaDeathsOverTimeData>,
    ) {
        this.dataSource1 = dataSource1;
        this.dataSource2 = dataSource2;
        this.dataSource3 = dataSource3;
        this.nationalLevelDeathOverTimeData = nationalLevelDeathOverTimeData;
    }
}

export class MalariaDeathsOverTimeData {
    constructor(
        public year: number | null = null,
        public dataSource1Deaths: number | null = null,
        public dataSource2Deaths: number | null = null,
        public dataSource3Deaths: number | null = null
    ) {
        this.year = year;
        this.dataSource1Deaths = dataSource1Deaths;
        this.dataSource2Deaths = dataSource2Deaths;
        this.dataSource3Deaths = dataSource3Deaths;
    }
    [index: string]: string | any;
    static init = () => new MalariaDeathsOverTimeData(null, null, null, null);
}

export class LineDatamodel {
    constructor(public name: string, public data: any) {
        this.name = name;
        this.data = data;
    }
}
