export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public isMolecularAnalysisCarriedOutForDrugResistance: boolean | null,
    public detailsOnMethodForDrugResistance: string | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.isMolecularAnalysisCarriedOutForDrugResistance =
      isMolecularAnalysisCarriedOutForDrugResistance;
    this.detailsOnMethodForDrugResistance = detailsOnMethodForDrugResistance;
  }
  static init = () => new Response_1(false, null, null, null, null);
}
