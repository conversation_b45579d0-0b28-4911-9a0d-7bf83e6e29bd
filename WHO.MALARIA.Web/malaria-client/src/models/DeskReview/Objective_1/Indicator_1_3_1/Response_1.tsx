export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public healthManagementStructure: Array<DataUseResponse>
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.healthManagementStructure = healthManagementStructure;
  }
  static init = (healthManagementStructure: Array<DataUseResponse>) =>
    new Response_1(false, null, null, healthManagementStructure);
}

export class DataUseResponse {
  constructor(
    public evidence: boolean | null = null,
    public details: string | null = null,
    public links: string | null = null
  ) {
    this.evidence = evidence;
    this.details = details;
    this.links = links;
  }
}
