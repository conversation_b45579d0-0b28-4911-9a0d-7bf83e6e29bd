export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public challengesOfUsingMalariaSurveillanceData: string | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.challengesOfUsingMalariaSurveillanceData = challengesOfUsingMalariaSurveillanceData;
    }
    static init = () => new Response_1(false, null, null);
}