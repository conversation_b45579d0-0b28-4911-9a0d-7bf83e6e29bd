import { KeyValuePair } from "../../KeyValueType";

export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public caseClassificationRate: CaseClassification,
        public yearOfData: number | null,
        public caseClassificationRates: Array<KeyValuePair<string, number | null>>
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.caseClassificationRate = caseClassificationRate;
        this.yearOfData = yearOfData;
        this.caseClassificationRates = caseClassificationRates;
    }
    static init = (months: Array<KeyValuePair<string, number | null>>) =>
        new Response_1(false, null, new CaseClassification(), null, months);
}

export class CaseClassification {
    constructor(
        public numerator: number | null = null,
        public denominator: number | null = null,
        public evidenceOfFollowupOrUse: string | null = null
    ) {
        this.numerator = numerator;
        this.denominator = denominator;
        this.evidenceOfFollowupOrUse = evidenceOfFollowupOrUse;
    }
}
