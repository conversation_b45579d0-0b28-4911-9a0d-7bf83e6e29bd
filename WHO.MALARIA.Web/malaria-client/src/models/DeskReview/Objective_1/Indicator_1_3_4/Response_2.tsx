﻿export class Response_2 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public dataAreNotUsed: boolean | false,
        public nationalLevel: Bulletin | null,
        public regionalLevel: Bulletin | null,
        public districtLevel: Bulletin | null,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.dataAreNotUsed = dataAreNotUsed;
        this.nationalLevel = nationalLevel;
        this.regionalLevel = regionalLevel;
        this.districtLevel = districtLevel;
    }

    static init = () =>
        new Response_2(
            false,
            null,
            false,
            new Bulletin(null, 12, null),
            new Bulletin(null, null, null),
            new Bulletin(null, null, null),
        );

    [index: string]: string | any;
}

export class Bulletin {
    constructor(
        public noOfMonthlyBulletinsProduced: number | null,
        public noOfWeeksOrMonths: number | null,
        public proportionRate: number | null
    ) {
        this.noOfMonthlyBulletinsProduced = noOfMonthlyBulletinsProduced;
        this.noOfWeeksOrMonths = noOfWeeksOrMonths;
        this.proportionRate = proportionRate;
    }
}