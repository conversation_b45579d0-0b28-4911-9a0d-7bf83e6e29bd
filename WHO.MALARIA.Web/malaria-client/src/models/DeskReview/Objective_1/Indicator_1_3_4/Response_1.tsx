export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public dataAreNotUsed: boolean | false,
    public routineOutputProducedTab: RoutineOutputProducedTab | null,
    public annualMalariaTab: AnnualMalariaTab | null,
    public otherAnalyticalTab: OtherAnalyticalTab | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.dataAreNotUsed = dataAreNotUsed;
    this.routineOutputProducedTab = routineOutputProducedTab;
    this.annualMalariaTab = annualMalariaTab;
    this.otherAnalyticalTab = otherAnalyticalTab;
  }

  static init = () =>
    new Response_1(
      false,
      null,
      null,
      false,
      RoutineOutputProducedTab.init(),
      AnnualMalariaTab.init(),
      OtherAnalyticalTab.init()
    );
}

export class RoutineOutputProducedTab {
  constructor(
    public frequencyOfMonitoring: string | null,
    public nationalLevel: Last12Month | null,
    public regionalLevel: Last12Month | null,
    public districtLevel: Last12Month | null,
    public epidemicMonitoring_NationalLevel: EpidemicMonitoring | null,
    public epidemicMonitoring_RegionalLevel: EpidemicMonitoring | null,
    public epidemicMonitoring_DistrictLevel: EpidemicMonitoring | null,
    public numeratorTitle: string,
    public denominatorTitle: string
  ) {
    this.frequencyOfMonitoring = frequencyOfMonitoring;
    this.nationalLevel = nationalLevel;
    this.regionalLevel = regionalLevel;
    this.districtLevel = districtLevel;
    this.epidemicMonitoring_NationalLevel = epidemicMonitoring_NationalLevel;
    this.epidemicMonitoring_RegionalLevel = epidemicMonitoring_RegionalLevel;
    this.epidemicMonitoring_DistrictLevel = epidemicMonitoring_DistrictLevel;
    this.numeratorTitle = numeratorTitle;
    this.denominatorTitle = denominatorTitle;
  }
  static init = () =>
    new RoutineOutputProducedTab(
      null,
      new Last12Month(null, 12, null),
      new Last12Month(null, null, null),
      new Last12Month(null, null, null),
      new EpidemicMonitoring(null, 12, null),
      new EpidemicMonitoring(null, null, null),
      new EpidemicMonitoring(null, null, null),
      "Numerator",
      "Denominator"
    );

  [index: string]: string | any;
}

export class AnnualMalariaTab {
  constructor(
    public isSurveillanceReportProducedInLast12Month: boolean | null,
    public links: string | null
  ) {
    this.isSurveillanceReportProducedInLast12Month =
      isSurveillanceReportProducedInLast12Month;
    this.links = links;
  }
  static init = () => new AnnualMalariaTab(null, null);
}

export class OtherAnalyticalTab {
  constructor(
    public isMapOfCountryWithStratification: boolean | null,
    public specifyLastYear: number | null,
    public isHighRiskPopulation: boolean | null,
    public highRiskPopulationDetail: string | null,
    public otherAnalysisDetail: string | null
  ) {
    this.isMapOfCountryWithStratification = isMapOfCountryWithStratification;
    this.specifyLastYear = specifyLastYear;
    this.isHighRiskPopulation = isHighRiskPopulation;
    this.highRiskPopulationDetail = highRiskPopulationDetail;
    this.otherAnalysisDetail = otherAnalysisDetail;
  }
  static init = () => new OtherAnalyticalTab(null, null, null, null, null);
}

export class Last12Month {
  constructor(
    public noOfMonthlyBulletinsProduced: number | null,
    public noOfWeeksOrMonths: number | null,
    public proportionRate: number | null
  ) {
    this.noOfMonthlyBulletinsProduced = noOfMonthlyBulletinsProduced;
    this.noOfWeeksOrMonths = noOfWeeksOrMonths;
    this.proportionRate = proportionRate;
  }
}

export class EpidemicMonitoring {
  constructor(
    public noOfMonthlyEpidemicMonitoringGraph: number | null,
    public noOfWeeksOrMonths: number | null,
    public proportionRate: number | null
  ) {
    this.noOfMonthlyEpidemicMonitoringGraph =
      noOfMonthlyEpidemicMonitoringGraph;
    this.noOfWeeksOrMonths = noOfWeeksOrMonths;
    this.proportionRate = proportionRate;
  }
}
