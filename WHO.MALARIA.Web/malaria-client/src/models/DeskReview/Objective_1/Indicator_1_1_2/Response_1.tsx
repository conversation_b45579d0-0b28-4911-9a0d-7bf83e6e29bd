import { KeyValuePair } from "../../KeyValueType";

export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public portionOfSuspectsTestedOverTimeTab: PortionOfSuspectsTestedOverTimeTab | null,
        public portionOfSuspectsTestedOverRegionTab: PortionOfSuspectsTestedOverRegionTab | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.portionOfSuspectsTestedOverTimeTab =
            portionOfSuspectsTestedOverTimeTab;
        this.portionOfSuspectsTestedOverRegionTab =
            portionOfSuspectsTestedOverRegionTab;
    }
    static init = () =>
        new Response_1(
            false,
            null,
            null,
            PortionOfSuspectsTestedOverTimeTab.init(),
            PortionOfSuspectsTestedOverRegionTab.init()
        );
}

export class PortionOfSuspectsTestedOverTimeTab {
    constructor(
        public values: Array<KeyValuePair<number | null, number | null>>,
        public reasonForChangeObservedOvertime: string | null
    ) {
        this.values = values;
        this.reasonForChangeObservedOvertime = reasonForChangeObservedOvertime;
    }
    static init = () => new PortionOfSuspectsTestedOverTimeTab([], null);
}

export class PortionOfSuspectsTestedOverRegionTab {
    constructor(
        public yearOfData: string | null,
        public values: Array<KeyValuePair<string | null, number | null>>,
        public reasonForChangeObservedOvertime: string | null
    ) {
        this.yearOfData = yearOfData;
        this.values = values;
        this.reasonForChangeObservedOvertime = reasonForChangeObservedOvertime;
    }
    static init = () => new PortionOfSuspectsTestedOverRegionTab(null, [], null);
}
