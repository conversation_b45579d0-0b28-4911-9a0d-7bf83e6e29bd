export class Response_3 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public improvementsInFeedbackAndSupervision: DataUse,
        public improvementsInDataQuality: DataUse | null,
        public initiateSurveillanceTrainingAndDataAnalysis: DataUse | null,
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.improvementsInFeedbackAndSupervision = improvementsInFeedbackAndSupervision;
        this.improvementsInDataQuality = improvementsInDataQuality;
        this.initiateSurveillanceTrainingAndDataAnalysis = initiateSurveillanceTrainingAndDataAnalysis;
    }
    static init = () => new Response_3(false, null, new DataUse(), new DataUse(), new DataUse());
}

export class DataUse {
    constructor(
        public evidence: boolean | null = null,
        public details: string | null = null,
        public links: string | null = null
    ) {
        this.evidence = evidence;
        this.details = details;
        this.links = links;
    }
}