export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public improvementsInFeedbackAndSupervision: DataUseResponse,
    public improvementsInDataQuality: DataUseResponse,
    public initiateSurveillanceTrainingAndDataAnalysis: DataUseResponse | null,
    public responseActivities: DataUseResponse | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.improvementsInFeedbackAndSupervision =
      improvementsInFeedbackAndSupervision;
    this.improvementsInDataQuality = improvementsInDataQuality;
    this.initiateSurveillanceTrainingAndDataAnalysis =
      initiateSurveillanceTrainingAndDataAnalysis;
    this.responseActivities = responseActivities;
  }
  static init = () =>
    new Response_1(
      false,
      null,
      null,
      new DataUseResponse(),
      new DataUseResponse(),
      new DataUseResponse(),
      new DataUseResponse()
    );
}

export class DataUseResponse {
  constructor(
    public evidence: boolean | null = null,
    public details: string | null = null,
    public links: string | null = null
  ) {
    this.evidence = evidence;
    this.details = details;
    this.links = links;
  }
}
