import { KeyValuePair } from "../../KeyValueType";

export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public nationalData: number | null,
        public publicData: number | null,
        public privateData: number | null,
        public yearOfData: number | null,
        public graph: RegionGraph | null,

    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.nationalData = nationalData;
        this.publicData = publicData;
        this.privateData = privateData;
        this.yearOfData = yearOfData
        this.graph = graph;
    }
    static init = () =>
        new Response_1(false, null, null, null, null, null, null, new RegionGraph([], null));
}

export class RegionGraph {
    constructor(
        public values: Array<KeyValuePair<string | null, number | null>>,
        public reasonForChangeObservedOvertime: string | null
    ) {
        this.values = values;
        this.reasonForChangeObservedOvertime = reasonForChangeObservedOvertime;
    }
}
