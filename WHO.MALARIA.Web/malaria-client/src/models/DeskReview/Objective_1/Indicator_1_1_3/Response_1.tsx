export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public nationalData: number | null,
    public publicData: number | null,
    public privateData: number | null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.nationalData = nationalData;
    this.publicData = publicData;
    this.privateData = privateData;
  }
  static init = () => new Response_1(false, null, null, null, null, null);
}
