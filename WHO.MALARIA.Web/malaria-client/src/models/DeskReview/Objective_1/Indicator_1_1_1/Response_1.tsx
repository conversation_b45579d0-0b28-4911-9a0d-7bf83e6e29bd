import { KeyValuePair } from "../../KeyValueType";

export default class Response_1_1_1 {
  constructor(
    public step_A: Step_A_Response,
    public step_B: Step_B_Response,
    public step_C: Step_C_Response,
    public step_D: Step_D_Response,
    public step_E: Step_E_Response
  ) {
    this.step_A = step_A;
    this.step_B = step_B;
    this.step_C = step_C;
    this.step_D = step_D;
    this.step_E = step_E;
  }

  static init = () =>
    new Response_1_1_1(
      new Step_A_Response(
        new Section_1_1_1_a([], ""),
        new Section_1_1_1_b("", [], "")
      ),
      new Step_B_Response([], null, ""),
      new Step_C_Response(null, ""),
      new Step_D_Response(null, ""),
      new Step_E_Response([], "")
    );
}

export class Step_A_Response {
  constructor(
    public section_1_1_1_a: Section_1_1_1_a,
    public section_1_1_1_b: Section_1_1_1_b
  ) {
    this.section_1_1_1_a = section_1_1_1_a;
    this.section_1_1_1_b = section_1_1_1_b;
  }
}

export class Section_1_1_1_a {
  constructor(
    public values: Array<KeyValuePair<number | null, number | null>>,
    public reasonForChangeObservedOvertime: string
  ) {
    this.values = values;
    this.reasonForChangeObservedOvertime = reasonForChangeObservedOvertime;
  }
}

class Section_1_1_1_b {
    constructor(
        public yearOfData: string,
        public values: Array<KeyValuePair<string | null, number | null>>,
        public reasonForChangeObservedByRegion: string
    ) {
        this.yearOfData = yearOfData;
        this.values = values;
        this.reasonForChangeObservedByRegion = reasonForChangeObservedByRegion;
    }
}

export class Step_B_Response {
  constructor(
    public services: Array<string>,
    public differByAge: boolean | null,
    public details: string
  ) {
    this.services = services;
    this.differByAge = differByAge;
    this.details = details;
  }
}

export class Step_C_Response {
  constructor(
    public groupsNotCapturedByNSS: boolean | null,
    public details: string
  ) {
    this.groupsNotCapturedByNSS = groupsNotCapturedByNSS;
    this.details = details;
  }
}

export class Step_D_Response {
  constructor(
    public newStrategiesUsed: boolean | null,
    public details: string
  ) {
    this.newStrategiesUsed = newStrategiesUsed;
    this.details = details;
  }
}

export class Step_E_Response {
  constructor(
    public treatmentOptions: Array<string>,
    public otherDetails: string
  ) {
    this.treatmentOptions = treatmentOptions;
    this.otherDetails = otherDetails;
  }
}
