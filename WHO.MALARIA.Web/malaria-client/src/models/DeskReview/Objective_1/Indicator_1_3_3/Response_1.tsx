export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public nationalLevel: DataReviewMonitoringResponse,
    public regionalLevel: DataReviewMonitoringResponse,
    public districtLevel: DataReviewMonitoringResponse,
    //This variable is only used to hold the error message of the form validation, this should never be used for data capturing and should not save in the DB.
    private healthSystemLevelValidationRuleKey: string | null = null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.healthSystemLevelValidationRuleKey =
      healthSystemLevelValidationRuleKey;
    this.nationalLevel = nationalLevel;
    this.regionalLevel = regionalLevel;
    this.districtLevel = districtLevel;
  }

  static init = () =>
    new Response_1(
      false,
      null,
      null,
      new DataReviewMonitoringResponse(null, null, null),
      new DataReviewMonitoringResponse(null, null, null),
      new DataReviewMonitoringResponse(null, null, null)
    );
}

export class DataReviewMonitoringResponse {
  constructor(
    public noOfMeetingsOccured: number | null,
    public noOfMeetingsExpected: number | null,
    public percentageRate: number | null
  ) {
    this.noOfMeetingsOccured = noOfMeetingsOccured;
    this.noOfMeetingsExpected = noOfMeetingsExpected;
    this.percentageRate = percentageRate;
  }
}
