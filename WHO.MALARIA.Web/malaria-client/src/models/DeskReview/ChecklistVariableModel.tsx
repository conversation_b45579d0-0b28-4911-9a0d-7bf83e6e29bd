﻿export class ChecklistVariableModel {
    constructor(
        public variableId: string,
        public name: string,
        public gender: boolean,
        public geography: boolean,
        public healthSector: boolean,
        public other: boolean,
        public overFive: boolean,
        public pregnantWoman: boolean,
        public recorded: boolean,
        public reported: boolean,
        public underFive: boolean,
        public caseCategory: number | null,
        public originallyBelongsTo: number
    ) {
        this.variableId = variableId;
        this.name = name;
        this.gender = gender;
        this.geography = geography;
        this.healthSector = healthSector;
        this.other = other;
        this.overFive = overFive;
        this.pregnantWoman = pregnantWoman;
        this.recorded = recorded;
        this.reported = reported;
        this.underFive = underFive;
        this.caseCategory = caseCategory;
        this.originallyBelongsTo = originallyBelongsTo;
    }
}