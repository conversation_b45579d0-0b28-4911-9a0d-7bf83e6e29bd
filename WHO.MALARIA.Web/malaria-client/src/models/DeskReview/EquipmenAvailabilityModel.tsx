export class EquipmenAvailabilityModel {
    constructor(
        public national: string,
        public nationalAvailable: number,
        public subNational: string,
        public subNationalAvailable: number,
        public serviceDelivery: string,
        public serviceDeliveryAvailable: number
    ) {
        this.national = national;
        this.nationalAvailable = nationalAvailable;
        this.subNational = subNational;
        this.subNationalAvailable = subNationalAvailable;
        this.serviceDelivery = serviceDelivery;
        this.serviceDeliveryAvailable = serviceDeliveryAvailable;
    }

    static init = () => new EquipmenAvailabilityModel("", 0, "", 0, "", 0);

}

