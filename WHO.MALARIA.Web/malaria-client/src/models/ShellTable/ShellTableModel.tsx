﻿import { KeyValuePair } from "../DeskReview/KeyValueType";

export class RespondentAndHealthFacilityType {
    constructor(
        public respondentTypes: Array<KeyValuePair<number, string>>,
        public healthFacilityTypes: Array<HealthFacilityRepondentType>
    ) {
        this.respondentTypes = respondentTypes;
        this.healthFacilityTypes = healthFacilityTypes;
    }

    static init = () => new RespondentAndHealthFacilityType([], []);
}

export class HealthFacilityRepondentType {
    constructor(
        public healthFacilityName: string,
        public healthFacilityType: number,
        public respondentType: number
    ) {
        this.healthFacilityName = healthFacilityName;
        this.healthFacilityType = healthFacilityType;
        this.respondentType = respondentType;
    }
}

export class ObjectivesSubObjectivesIndicatorsModel {
    constructor(
        public objectives: Array<ObjectiveModel>,
        public subObjectives: Array<SubObjectiveModel>,
        public indicators: Array<IndicatorModel>
    ) {
        this.objectives = objectives;
        this.subObjectives = subObjectives;
        this.indicators = indicators;
    }

    static init = () => new ObjectivesSubObjectivesIndicatorsModel([], [], []);
}

export class IdNameSequenceModel {
    constructor(
        public id: string,
        public name: string,
        public sequence: string
    ) {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
    }
}

export class ObjectiveModel extends IdNameSequenceModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string
    ) {
        super(id, name, sequence);
        this.respondentType = respondentType;
    }
}

export class SubObjectiveModel extends IdNameSequenceModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string,
        public objectiveId: string
    ) {
        super(id, name, sequence);
        this.respondentType = respondentType;
        this.objectiveId = objectiveId;
    }
}

export class IndicatorModel extends IdNameSequenceModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string,
        public subObjectiveId: string
    ) {
        super(id, name, sequence);
        this.respondentType = respondentType;
        this.subObjectiveId = subObjectiveId;
    }
}

export class ShellTableHeader {
    constructor(
        public headerName: string,
        public regionName: string,
    ) {
        this.headerName = headerName;
        this.regionName = regionName;
    }
}

export class QuestionModel {
    constructor(
        public indicatorId: string,
        public shellTableQuestionCalculationResultType: number,
        public headers: Array<ShellTableHeader>,
        public options: Array<Array<string>>,
        public totals: Array<number>
    ) {
        this.indicatorId = indicatorId;
        this.shellTableQuestionCalculationResultType = shellTableQuestionCalculationResultType;
        this.headers = headers;
        this.options = options;
        this.totals = totals;
    }
}

