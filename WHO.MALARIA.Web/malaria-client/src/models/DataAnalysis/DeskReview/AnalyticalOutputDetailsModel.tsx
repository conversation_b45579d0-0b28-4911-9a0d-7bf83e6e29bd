﻿export class AnalyticalOutputDetailsModel {
    constructor(
        public strategies: Array<StrategyModel>,
        public objectives: Array<ObjectiveModel>,
        public subObjectives: Array<SubObjectiveModel>,
        public indicators: Array<IndicatorModel>
    ) {
        this.strategies = strategies;
        this.objectives = objectives;
        this.subObjectives = subObjectives;
        this.indicators = indicators;
    }

    static init = () => new AnalyticalOutputDetailsModel([], [], [], []);
}

export class BaseAnalyticalOutputModel {
    constructor(
        public id: string,
        public name: string,
        public sequence: string
    ) {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
    }
}

export class StrategyModel {
    constructor(
        public id: string,
        public name: string,
    ) {
        this.id = id;
        this.name = name;
    }
}

export class ObjectiveModel extends BaseAnalyticalOutputModel {
    constructor(
        public strategyId: string,
        public id: string,
        public name: string,
        public sequence: string
    ) {
        super(id, name, sequence);
        this.strategyId = strategyId;
    }
}

export class SubObjectiveModel extends BaseAnalyticalOutputModel {
    constructor(
        public strategyId: string,
        public id: string,
        public name: string,
        public sequence: string,
        public objectiveId: string
    ) {
        super(id, name, sequence);
        this.strategyId = strategyId;
        this.objectiveId = objectiveId;
    }
}

export class IndicatorModel extends BaseAnalyticalOutputModel {
    constructor(
        public strategyId: string,
        public id: string,
        public name: string,
        public sequence: string,
        public subObjectiveId: string,
        public assessmentIndicatorId: string,
        public assessmentStrategyId: string,
        public isSelected: boolean
    ) {
        super(id, name, sequence);
        this.strategyId = strategyId;
        this.subObjectiveId = subObjectiveId;
        this.assessmentIndicatorId = assessmentIndicatorId;
        this.assessmentStrategyId = assessmentStrategyId;
        this.isSelected = isSelected;
    }
}