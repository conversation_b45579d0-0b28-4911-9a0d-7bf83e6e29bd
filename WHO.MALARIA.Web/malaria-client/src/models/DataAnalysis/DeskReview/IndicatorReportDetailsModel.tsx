﻿import { KeyValuePair } from "./../../DeskReview/KeyValueType";

export class IndicatorReportModel {
    constructor(
        public indicatorSequence: string,
        public type: number,
        public response: any,
    ) {
        this.indicatorSequence = indicatorSequence;
        this.type = type;
        this.response = response;
    }

    static init = () => new IndicatorReportModel("", 1, "");
}

export class TabularReportModel {
    constructor(
        public rows: Array<any>,
        public columns: Array<Column>,
        public hasCalculation: boolean,
        public tabName: string,
        public tabId: number,
    ) {
        this.rows = rows;
        this.columns = columns;
        this.hasCalculation = hasCalculation;
        this.tabName = tabName;
        this.tabId = tabId;
    }
}

export class Column {
    constructor(
        public key: string,
        public label: string,
        public width: string
    ) {
        this.key = key;
        this.label = label;
        this.width = width;
    }
}

export class GraphReportModel {
    constructor(
        public values: Array<KeyValuePair<string | null, number | null>>,
        public xAxis: string,
        public yAxis: string,
        public graphTitle: string,
        public tabName: string,
        public tabId: number,
        public graphName: string,
        public graphType: number
    ) {
        this.values = values;
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        this.graphTitle = graphTitle;
        this.tabName = tabName;
        this.tabId = tabId;
        this.graphName = graphName;
        this.graphType = graphType;
    }
}

export class LineGraphReportModel {
    constructor(
        public values: Array<KeyValuePair<string | undefined, Array<number>>>,
        public xAxis: string,
        public yAxis: string,
        public graphTitle: string,
        public tabName: string,
        public tabId: number,
        public graphName: string,
        public graphType: number,
        public categories: Array<string | number>
    ) {
        this.values = values;
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        this.graphTitle = graphTitle;
        this.tabName = tabName;
        this.tabId = tabId;
        this.graphName = graphName;
        this.graphType = graphType;
        this.categories = categories;
    }
}

export class DiagramModel {
    constructor(
        public order: number,
        public file: string,
        public extension: string
    ) {
        this.order = order;
        this.file = file;
        this.extension = extension;
    }
}