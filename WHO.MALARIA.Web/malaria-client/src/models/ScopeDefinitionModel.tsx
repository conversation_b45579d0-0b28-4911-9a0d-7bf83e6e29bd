import { DiagramsStatusModel } from "./DiagramsStatusModel";
import { SaveIndicatorSelectionRequestModel } from "./RequestModels/ScopeDefinitionRequestModel";

/** Parent level class model which hold all information related to scope definition */
export class ScopeDefinitionModel {
  constructor(
    public assessmentId: string,
    public strategySelection: StrategySelectionModel,
    public indicatorSelection: SaveIndicatorSelectionRequestModel,
    public diagramsStatus: DiagramsStatusModel
  ) {
    this.assessmentId = assessmentId;
    this.strategySelection = strategySelection;
    this.indicatorSelection = indicatorSelection;
    this.diagramsStatus = diagramsStatus;
  }

  static init = () =>
    new ScopeDefinitionModel(
      "",
      new StrategySelectionModel([], false, []),
      new SaveIndicatorSelectionRequestModel("", []),
      DiagramsStatusModel.init()
    );
}

/** Holds all properties required to submit the strategy selection */
export class StrategySelectionModel {
  constructor(
    public caseStrategyIds: Array<string>,
    public isMalariaControlStrategy: boolean,
    public malariaControlStrategyIds: Array<string>,
    public canUpdate: boolean = false
  ) {
    this.caseStrategyIds = caseStrategyIds;
    this.isMalariaControlStrategy = isMalariaControlStrategy;
    this.malariaControlStrategyIds = malariaControlStrategyIds;
    this.canUpdate = canUpdate; // true for update else false
  }

  static init = () => new StrategySelectionModel([], false, [], false);
}

export class GetObjectiveModel {
  constructor(
    public id: string,
    public name: string,
    public sequence: string,
    public order: number
  ) {
    this.id = id;
    this.name = name;
    this.sequence = sequence;
    this.order = order;
  }

  static init = () => new GetObjectiveModel("", "", "", 0);
}

export class GetSubObjectiveModel extends GetObjectiveModel {
  constructor(
    public id: string,
    public name: string,
    public sequence: string,
    public order: number,
    public objectiveId: string
  ) {
    super(id, name, sequence, order);
    this.objectiveId = objectiveId;
  }
}
