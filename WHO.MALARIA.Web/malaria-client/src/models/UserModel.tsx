import { UserRoleEnum } from "./Enums";

export class UserListModel {
    constructor(
        public id: string,
        public identityId: string = "",
        public name: string,
        public email: string,
        public organizationName: string,
        public country: string,
        public status: number,
        public userType: number = UserRoleEnum.Viewer,
        public countryAccessStatus: string = "",
        public countryId: string = "",
        // Used in model to assign the role as per the userType which is a number to filter data in datagrid on FE
        public role: string = "",
        public displayStatus: string = "",
        public comment: string = "",
        public userCountryAccessId: string = ""
    ) {
        this.id = id;
        this.identityId = identityId;
        this.name = name;
        this.email = email;
        this.organizationName = organizationName;
        this.country = country;
        this.status = status;
        this.userType = userType;
        this.countryAccessStatus = countryAccessStatus;
        this.countryId = countryId;
        this.role = role;
        this.displayStatus = displayStatus;
        this.comment = comment;
        this.userCountryAccessId = userCountryAccessId;
    }

    static init = () =>
        new UserListModel(
            "",
            "",
            "",
            "",
            "",
            "",
            0,
            UserRoleEnum.Viewer,
            "",
            "",
            "",
            "",
            "",
            ""
        );
}

export class PendingUserModel {
    constructor(
        public id: string,
        public name: string,
        public email: string,
        public organizationName: string
    ) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.organizationName = organizationName;
    }
}

export class AddSuperManagerModel {
    constructor(
        public name: string,
        public email: string,
        public organizationName: string,
        public assignCountry: string,
        public userType: number = UserRoleEnum.SuperManager
    ) {
        this.name = name;
        this.email = email;
        this.organizationName = organizationName;
        this.assignCountry = assignCountry;
        this.userType = userType;
    }

    static init = () => new AddSuperManagerModel("", "", "", "");
}

export class InviteUserModel {
    constructor(
        public name: string,
        public email: string,
        public organizationName: string,
        public assignCountry: string,
        public userType: number = UserRoleEnum.SuperManager
    ) {
        this.name = name;
        this.email = email;
        this.organizationName = organizationName;
        this.assignCountry = assignCountry;
        this.userType = userType;
    }

    static init = () => new InviteUserModel("", "", "", "");
}

export class AssignSuperManagerModel {
    constructor(
        public name: string,
        public email: string,
        public countryId: string,
        public organizationName: string,
        public role: UserRoleEnum | null
    ) {
        this.name = name;
        this.email = email;
        this.countryId = countryId;
        this.organizationName = organizationName;
        this.role = role;
    }

    static init = () => new AssignSuperManagerModel("", "", "", "WHO", null);
}

export class EditUserModel {
    constructor(
        public userId: string,
        public userType: number,
        public status: boolean
    ) {
        this.userId = userId;
        this.userType = userType;
        this.status = status;
    }

    static init = () => new EditUserModel("", UserRoleEnum.Viewer, false);
}
