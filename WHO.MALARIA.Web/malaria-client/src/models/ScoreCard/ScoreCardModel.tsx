import { KeyValuePair } from "../DeskReview/KeyValueType";

export class SubObjectiveIndicatorModel {
    constructor(
        public indicators: Array<IndicatorModel>,
        public objectives: Array<ObjectiveModel>,
        public subObjectives: Array<SubObjectiveModel>,
        public isFinalized: boolean
    ) {
        this.indicators = indicators;
        this.objectives = objectives;
        this.subObjectives = subObjectives;
        this.isFinalized = isFinalized;
    }

    static init = () => new SubObjectiveIndicatorModel([], [], [], false);
}

export class IndicatorModel {
    constructor(
        public id: string,
        public name: string,
        public sequence: string,
        public subObjectiveId: string,
        public indicatorMetNotMetStatus: number,
        public surveyMetNotMetStatus: number,
        public isSurveyIndicator: boolean,
        public isServiceDeliveryIndicator: boolean,
        public serviceDeliveryMetNotMetStatus: number,
        public reasonForResult: string,
        public recommendation: string,
    ) {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
        this.subObjectiveId = subObjectiveId;
        this.indicatorMetNotMetStatus = indicatorMetNotMetStatus;
        this.surveyMetNotMetStatus = surveyMetNotMetStatus;
        this.isSurveyIndicator = isSurveyIndicator;
        this.isServiceDeliveryIndicator = isServiceDeliveryIndicator;
        this.serviceDeliveryMetNotMetStatus = serviceDeliveryMetNotMetStatus;
        this.reasonForResult = reasonForResult;
        this.recommendation = recommendation;
    }

}

export class ObjectiveModel {
    constructor(
        public id: string,
        public name: string,
        public sequence: string,
        public nationalScorePercentage: number,
        public surveyScorePercentage: number,
        public totalMetIndicators: number,
        public totalIndicators: number,
        public indicatorsMetPercentage: number,
    ) {
        this.id = id;
        this.name = name;
        this.sequence = sequence;
        this.nationalScorePercentage = nationalScorePercentage;
        this.surveyScorePercentage = surveyScorePercentage;
        this.totalMetIndicators = totalMetIndicators;
        this.totalIndicators = totalIndicators;
        this.indicatorsMetPercentage = indicatorsMetPercentage;
    }

}

export class SubObjectiveModel {
    constructor(
        public id: string,
        public name: string,
        public objectiveId: string,
        public sequence: number,
        public surveyScorePercentage: number,
        public nationalScorePercentage: number
    ) {
        this.id = id;
        this.name = name;
        this.objectiveId = objectiveId;
        this.sequence = sequence;
        this.surveyScorePercentage = surveyScorePercentage;
        this.nationalScorePercentage = nationalScorePercentage;
    }

}

export class ScoreCardRequestModel {
    constructor(
        public assessmentId: string,
        public subObjectiveId: string,
        public indicatorDetails: Array<ScoreCardDetailModel>
    ) {
        this.assessmentId = assessmentId;
        this.indicatorDetails = indicatorDetails
    }
    static init = () => new ScoreCardRequestModel("", "", []);
}

export class ScoreCardDetailModel {
    constructor(
        public indicatorId: string,
        public subObjectiveId: string,
        public indicatorMetNotMetStatus: number | null,
        public indicatorReasonForResult: string,
        public indicatorRecommendation: string
    ) {
        this.indicatorId = indicatorId;
        this.indicatorMetNotMetStatus = indicatorMetNotMetStatus;
        this.indicatorReasonForResult = indicatorReasonForResult;
        this.indicatorRecommendation = indicatorRecommendation;
    }
    static init = () => new ScoreCardRequestModel("", "", []);
}

export class FinalizeScoreCardModel {
    constructor(
        public assessmentId: string,
    ) {
        this.assessmentId = assessmentId;
    }
    static init = () => new FinalizeScoreCardModel("");
}

export class CanScoreCardBeGenerated {
    constructor(
        public assessmentId: string,
    ) {
        this.assessmentId = assessmentId;
    }
    static init = () => new CanScoreCardBeGenerated("");
}
