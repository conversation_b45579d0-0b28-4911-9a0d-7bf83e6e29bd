﻿export class FilterCritria  {
    constructor(public field: string, public operator: string, public value: string) {
        this.field = field;
        this.operator = operator;
        this.value = value;
    }
}

export class RetrieveMultipleRecords {
    constructor(public entity: string, public filterCriterias: Array<FilterCritria> | null = null ) {
        this.entity = entity;
        this.filterCriterias = filterCriterias;
    }
}