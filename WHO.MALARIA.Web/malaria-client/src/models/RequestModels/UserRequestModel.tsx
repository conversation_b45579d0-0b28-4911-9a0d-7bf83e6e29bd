export class CreateUserRequestModel {
  constructor(
    public email: string,
    public name: string,
    public organizationName: string,
    public countryRequestedForIds: Array<string> = []
  ) {
    this.email = email;
    this.name = name;
    this.organizationName = organizationName;
    this.countryRequestedForIds = countryRequestedForIds;
  }
}

export class CreateSuperManagerRequestModel extends CreateUserRequestModel {
  constructor(
    public currentUserId: string = "",
    public email: string,
    public countryId: string,
    public name: string,
    public organizationName: string,
    public role: number | null = null
  ) {
    super(email, name, organizationName, []);
    this.currentUserId = currentUserId;
    this.countryId = countryId;
    this.role = role;
  }
}
export class UserCountryRequestModel {
  constructor(
    public countryRequestedForIds: Array<string> = []
  ) {
    this.countryRequestedForIds = countryRequestedForIds;
  }
}

export class UpdateUserRequestModel extends CreateUserRequestModel {
  constructor(
    public userId: string,
    public emailId: string,
    public name: string,
    public organizationName: string,
    public countryRequestedForIds: Array<string> = []
  ) {
    super(emailId, name, organizationName, countryRequestedForIds);
    this.userId = userId;
  }
}

export class UpdateUserRoleAndStatusRequestModel {
  constructor(
    public userId: string,
    public userType: number,
    public status: number,
    public countryId: string
  ) {
    this.userId = userId;
    this.userType = userType;
    this.status = status;
    this.countryId = countryId;
  }
}

export class ChangeUserStatusRequestModel {
  constructor(public userId: string, public countryId: string, public isActive: boolean, public userType: number) {
    this.userId = userId;
    this.countryId = countryId;
    this.isActive = isActive;
    this.userType = userType;
  }
}

export class ChangeUserTypeRequestModel {
  constructor(public userId: string, public countryId: string) {
    this.userId = userId;
    this.countryId = countryId;
  }
  static init = () => new ChangeUserTypeRequestModel("", "");
}

export class PendingUserDetailModel {
  constructor(
    public id: string,
    public identityId: string,
    public userType: string,
    public name: string,
    public status: number,
    public countryAccessStatus: number,
    public email: string,
    public organizationName: string,
    public country: string,
    public countryId: string,
    public userCountryAccessId: string
  ) {
    this.id = id;
    this.identityId = identityId;
    this.userType = userType;
    this.name = name;
    this.status = status;
    this.countryAccessStatus = countryAccessStatus;
    this.email = email;
    this.organizationName = organizationName;
    this.country = country;
    this.countryId = countryId;
    this.userCountryAccessId = userCountryAccessId;
  }
}

export class PendingRequestModel {
  constructor(
    public userActivationRequests: Array<PendingUserDetailModel>,
    public countryAccessRequests: Array<PendingUserDetailModel>
  ) {
    this.userActivationRequests = userActivationRequests;
    this.countryAccessRequests = countryAccessRequests;
  }

  static init = () => new PendingRequestModel([], []);
}

export class CountryAccessRequestModel {
  constructor(
    public userCountryAccessId: string,
    public countryId: string,
    public comment: string
  ) {
    this.userCountryAccessId = userCountryAccessId;
    this.countryId = countryId;
    this.comment = comment;
  }

  static init = () => new CountryAccessRequestModel("", "", "");
}

export class ResendInvitationRequestModel {
  constructor(public userId: string, public countryId: string) {
    this.userId = userId;
    this.countryId = countryId;
  }
}

export class InvitationRequestModel {
  constructor(public userId: string, public userCountryAccessId: string, public email: string) {
    this.userId = userId;
    this.userCountryAccessId = userCountryAccessId;
    this.email = email;
  }
}
