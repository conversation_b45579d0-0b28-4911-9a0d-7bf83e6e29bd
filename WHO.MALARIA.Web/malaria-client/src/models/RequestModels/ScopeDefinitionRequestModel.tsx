import { AssessmentApproach } from "../Enums";

export class SaveStrategyRequestModel {
  constructor(public assessmentId: string, public strategyIds: Array<string>) {
    this.assessmentId = assessmentId;
    this.strategyIds = strategyIds;
  }
}

export class GetIndicatorsRequestModel {
  constructor(
    public caseStrategyIds: Array<string>,
    public malariaControlStrategyIds: Array<string>
  ) {
    this.caseStrategyIds = caseStrategyIds;
    this.malariaControlStrategyIds = malariaControlStrategyIds;
  }
}

export class SaveIndicatorSelectionRequestModel {
  constructor(
    public assessmentId: string,
    public indicatorIds: Array<string>,
    public assessmentApproach: number = AssessmentApproach.Rapid,
    public canUpdate: boolean = false // true for update else false
  ) {
    this.assessmentId = assessmentId;
    this.indicatorIds = indicatorIds;
    this.assessmentApproach = assessmentApproach;
    this.canUpdate = canUpdate;
  }

  static init = () =>
    new SaveIndicatorSelectionRequestModel("", [], AssessmentApproach.Rapid);
}
