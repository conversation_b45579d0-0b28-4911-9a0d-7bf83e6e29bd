﻿
export class IndicatorsRequestModel {
    constructor(
        public assessmentId: string,
        public strategyId: string,
        public analyticalOutputIndicators: Array<IndicatorSelectionRequestModel>
    ) {
        this.assessmentId = assessmentId;
        this.strategyId = strategyId;
        this.analyticalOutputIndicators = analyticalOutputIndicators;
    }

    static init = () =>
        new IndicatorsRequestModel("", "", []);
}

export class IndicatorSelectionRequestModel {
    constructor(
        public indicatorId: string,
        public assessmentIndicatorId: string,
        public assessmentStrategyId: string,
        public isSelected?: boolean
    ) {
        this.indicatorId = indicatorId;
        this.assessmentIndicatorId = assessmentIndicatorId;
        this.assessmentStrategyId = assessmentStrategyId;
        this.isSelected = isSelected;
    }
}