﻿import { RespondentType } from "../Enums";

export class RespondentTypeRequestModel {
    constructor(
        public assessmentId: string,
        public respondentTypes: Array<number>,
        public hasSelfAssessmentQuestions: boolean | null
    ) {
        this.assessmentId = assessmentId;
        this.respondentTypes = respondentTypes;
        this.hasSelfAssessmentQuestions = hasSelfAssessmentQuestions;
    }

    static init = (assessmentId: string) => {
        return new RespondentTypeRequestModel(assessmentId, [RespondentType.ServiceDeliveryLevel], null)
    }
}