﻿import { RespondentType } from "../Enums";

export class SurveyQuestionRequestModel {
    constructor(
        public assessmentId: string,
        public respondentType: number,
        public questions: Array<Question>
    ) {
        this.assessmentId = assessmentId;
        this.respondentType = respondentType;
        this.questions = questions;
    }

    static init = (assessmentId: string) => {
        return new SurveyQuestionRequestModel(assessmentId, RespondentType.ServiceDeliveryLevel, [])
    }
}

export class Question {
    constructor(
        public id: string,
        public modifiedQuestion: string,
        public responseOption: string,
        public questionCode: string,
    ) {
        this.id = id;
        this.modifiedQuestion = modifiedQuestion;
        this.responseOption = responseOption;
        this.questionCode = questionCode;
    }
}