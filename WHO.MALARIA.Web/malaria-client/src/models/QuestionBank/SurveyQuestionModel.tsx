﻿export class SurveyQuestionModel {
    constructor(
        public id: string,
        public question: string,
        public questionCode: string,
        public notes: string,
        public options: string,
        public order: number,
        public isMandatory: boolean,
        public forSelfAssessment: boolean,
        public canEditOptions: boolean,
        public modifiedQuestion: string,
        public responseOptions: string,
        public staticResponseOptions: string,
        public respondentType: number,
        public indicatorId: string,
        public subObjectiveId: string,
        public isSelected: boolean,
        public areAllSelected: boolean,
        public showError :boolean,
    ) {
        this.id = id;
        this.question = question;
        this.questionCode = questionCode;
        this.notes = notes;
        this.options = options;
        this.order = order;
        this.isMandatory = isMandatory;
        this.forSelfAssessment = forSelfAssessment;
        this.canEditOptions = canEditOptions;
        this.modifiedQuestion = modifiedQuestion;
        this.responseOptions = responseOptions;
        this.staticResponseOptions = staticResponseOptions;
        this.respondentType = respondentType;
        this.indicatorId = indicatorId;
        this.subObjectiveId = subObjectiveId;
        this.isSelected = isSelected;
        this.areAllSelected = areAllSelected;
        this.showError =showError;
    }
}

export class SubObjectiveIndicatorModel {
    constructor(
        public objectives: Array<ObjectiveModel>,
        public subObjectives: Array<SubObjectiveModel>,
        public indicators: Array<IndicatorModel>
    ) {
        this.objectives = objectives;
        this.subObjectives = subObjectives;
        this.indicators = indicators;
    }

    static init = () => new SubObjectiveIndicatorModel([], [], []);
}

export class SubObjectiveModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string,
        public objectiveId: string
    ) {
        this.respondentType = respondentType;
        this.id = id;
        this.name = name;
        this.sequence = sequence;
        this.objectiveId = objectiveId;
    }
}

export class IndicatorModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string,
        public subObjectiveId: string,
    ) {
        this.respondentType = respondentType;
        this.id = id;
        this.name = name;
        this.subObjectiveId = subObjectiveId;
        this.sequence = sequence;
    }

}

export class ObjectiveModel {
    constructor(
        public respondentType: number,
        public id: string,
        public name: string,
        public sequence: string
    ) {
        this.respondentType = respondentType;
        this.id = id;
        this.name = name;
        this.sequence = sequence;
    }
}
