﻿import { RespondentType } from "../Enums";

export class RespondentTypeModel {
    constructor(
        public respondentType: number,
        public isSaved: boolean,
        public isFinalized: boolean,
        public hasSelfAssessmentQuestions: boolean | null
    ) {
        this.respondentType = respondentType;
        this.isSaved = isSaved;
        this.isFinalized = isFinalized;
        this.hasSelfAssessmentQuestions = hasSelfAssessmentQuestions;
    }

    static init = () => {
        return new RespondentTypeModel(RespondentType.ServiceDeliveryLevel, false, false, null);
    }
}