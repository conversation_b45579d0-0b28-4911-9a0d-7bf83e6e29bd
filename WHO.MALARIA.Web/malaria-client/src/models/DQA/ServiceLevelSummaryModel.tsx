export class ServiceLevelSummaryModel {
    constructor(
        public serviceLevelId: string,
        public year: number,
        public variableCompletenesses: VariableCompletenessModel,
        public variableData: Array<VariableDataModel>
    ) {
        this.serviceLevelId = serviceLevelId;
        this.year = year;
        this.variableCompletenesses = variableCompletenesses;
        this.variableData = variableData;
    }
    static init = () => new ServiceLevelSummaryModel("", 0, VariableCompletenessModel.init(), [])
}

export class VariableCompletenessModel {
    constructor(
        public total: number | null,
        public sex: number | null,
        public diagnosis: number | null,
        public age: number | null,
        public observedDataQualityResultReason: string
    ) {
        this.total = total;
        this.sex = sex;
        this.diagnosis = diagnosis;
        this.age = age;
        this.observedDataQualityResultReason = observedDataQualityResultReason
    }
    static init = () => new VariableCompletenessModel(null, null, null, null, "");
}

export class VariableDataModel {
    constructor(
        public serviceLevelVariableName: string,
        public monthConcordance: number | null,
        public errorInDataSources: number | null

    ) {
        this.serviceLevelVariableName = serviceLevelVariableName;
        this.monthConcordance = monthConcordance;
        this.errorInDataSources = errorInDataSources;
    }
}

export class ServiceLevelObservedDataQualityReasonModel {
    constructor(
        public assessmentId: string,
        public serviceLevelId: string,
        public observedDataQualityResultReason: string
    ) {
        this.assessmentId = assessmentId;
        this.serviceLevelId = serviceLevelId;
        this.observedDataQualityResultReason = observedDataQualityResultReason;
    }
}

export class FinalizeObservedDataQualityReasonModel {
    constructor(
        public assessmentId: string,
        public serviceLevelId: string,
    ) {
        this.assessmentId = assessmentId;
        this.serviceLevelId = serviceLevelId;
    }
}