import { KeyValuePair } from "../DeskReview/KeyValueType";

export class DeskLevelRequestModel {
    constructor(
        public deskLevelId: string,
        public deskLevelVariables: Array<string>,
        public deskLevelDataSystems: Array<DeskLevelDataSystemModel>,
        public dataSystemsOneId: string,
        public dataSystemsTwoId: string | null,
        public isOtherDataSystem1Selected: boolean,
        public otherDataSystem1SourceName: string,
        public isOtherDataSystem2Selected: boolean,
        public otherDataSystem2SourceName: string,
        public assessmentId: string,
        public isFinalized: boolean,
        public isReFinalized: boolean,
        public priorityVariableIds: Array<string>,
        public optionalVariableIds: Array<string>,
        public concordanceVariables: Array<string>,
        public fileName:string,
    ) {
        this.deskLevelVariables = deskLevelVariables;
        this.deskLevelDataSystems = deskLevelDataSystems
        this.dataSystemsOneId = dataSystemsOneId;
        this.dataSystemsTwoId = dataSystemsTwoId;
        this.isOtherDataSystem1Selected = isOtherDataSystem1Selected;
        this.otherDataSystem1SourceName = otherDataSystem1SourceName;
        this.isOtherDataSystem2Selected = isOtherDataSystem2Selected;
        this.otherDataSystem2SourceName = otherDataSystem2SourceName;
        this.assessmentId = assessmentId;
        this.isFinalized = isFinalized;
        this.isReFinalized = isReFinalized;
        this.priorityVariableIds = priorityVariableIds;
        this.optionalVariableIds = optionalVariableIds;
        this.concordanceVariables = concordanceVariables;
        this.fileName = fileName;
    }

    static init = (assessmentId: string) => new DeskLevelRequestModel("", [], [], "", "", false, "", false, "", assessmentId, false, false, [], [], [],"");
}

export class DeskLevelDataSystemModel {
    constructor(
        public dqaDataSourceId: string,
        public otherSystemName: string
    ) {
        this.dqaDataSourceId = dqaDataSourceId;
        this.otherSystemName = otherSystemName;
    }
    static init = (dqaDataSourceId: string, otherSystemName: string) => new DeskLevelDataSystemModel(dqaDataSourceId, otherSystemName);
}

export class DLDQARequestModel {
    constructor(
        public variableIds: Array<KeyValuePair<string, boolean | null>>,
        public dataSystem_1_DataSourceId: string,
        public dataSystem_2_DataSourceId: string | null,
        public dataSystem_1_OtherDataSourceName: string,
        public dataSystem_2_OtherDataSourceName: string,
        public assessmentId: string,
    ) {
        this.variableIds = variableIds;
        this.dataSystem_1_DataSourceId = dataSystem_1_DataSourceId;
        this.dataSystem_2_DataSourceId = dataSystem_2_DataSourceId;
        this.dataSystem_1_OtherDataSourceName = dataSystem_1_OtherDataSourceName;
        this.dataSystem_2_OtherDataSourceName = dataSystem_2_OtherDataSourceName;
        this.assessmentId = assessmentId;
    }
}

export class DeskLevelFinalizeModel {
    constructor(
        public assessmentId: string
    ) {
        this.assessmentId = assessmentId;
    }
}