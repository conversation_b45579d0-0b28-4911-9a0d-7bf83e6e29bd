﻿export class SummaryModel {
    constructor(
        public summary: EliminationSummaryModel,
    ) {
        this.summary = summary;
    }
}

export class EliminationSummaryModel {
    constructor(
        public assessmentId: string,
        public reportCompleteness: boolean | null,
        public reportTimeliness: number | null,
        public caseInvestigationReportsCompleteness: number | null,
        public caseNotificationReportsTimeliness: number | null,
        public caseInvestigationReportsTimeliness: number | null,
        public fociInvestigationReportsTimeliness: number | null,
        public coreVariableCompletenessWithinReport: number | null,
        public consistencyBetweenCoreVariables: number | null,
        public consistencyOverTimeCoreIndicators: boolean | null,
        public confirmMalariaCasesNotified: boolean | null,
        public confirmMalariaCasesInvestigated: boolean | null,
        public confirmMalariaCasesClassified: boolean | null,
        public confirmMalariaCasesClassifiedAsLocal: boolean | null,
        public confirmMalariaCasesClassifiedAsIndigenous: boolean | null,
        public confirmMalariaCasesClassifiedAsIntroduced: boolean | null,
        public confirmMalariaCasesClassifiedAsImported: boolean | null,
        public malariaCasesDueToPF: boolean | null,
        public malariaCasesDueToPK: boolean | null,
        public malariaCasesDueToPM: boolean | null,
        public malariaCasesDueToPO: boolean | null,
        public malariaCasesDueToPV: boolean | null,
        public keyVariableConcordanceBtwTwoReportingSystem: boolean | null,
        public coreVariableCompletenessWithinRegister: number | null,
        public coreVariableConcordanceBtwRegister: number | null,
        public dataQualityResultReason: string,
        public year: number,
        public isFinalized: boolean,
    ) {
        this.assessmentId = assessmentId;
        this.reportCompleteness = reportCompleteness;
        this.reportTimeliness = reportTimeliness;
        this.caseInvestigationReportsCompleteness = caseInvestigationReportsCompleteness
        this.caseNotificationReportsTimeliness = caseNotificationReportsTimeliness;
        this.caseInvestigationReportsTimeliness = caseInvestigationReportsTimeliness;
        this.fociInvestigationReportsTimeliness = fociInvestigationReportsTimeliness;
        this.coreVariableCompletenessWithinReport = coreVariableCompletenessWithinReport;
        this.consistencyBetweenCoreVariables = consistencyBetweenCoreVariables;
        this.consistencyOverTimeCoreIndicators = consistencyOverTimeCoreIndicators;
        this.confirmMalariaCasesNotified = confirmMalariaCasesNotified
        this.confirmMalariaCasesInvestigated = confirmMalariaCasesInvestigated;
        this.confirmMalariaCasesClassified = confirmMalariaCasesClassified;
        this.confirmMalariaCasesClassifiedAsLocal = confirmMalariaCasesClassifiedAsLocal;
        this.confirmMalariaCasesClassifiedAsIndigenous = confirmMalariaCasesClassifiedAsIndigenous;
        this.confirmMalariaCasesClassifiedAsIntroduced = confirmMalariaCasesClassifiedAsIntroduced;
        this.confirmMalariaCasesClassifiedAsImported = confirmMalariaCasesClassifiedAsImported;
        this.malariaCasesDueToPF = malariaCasesDueToPF;
        this.malariaCasesDueToPK = malariaCasesDueToPK;
        this.malariaCasesDueToPM = malariaCasesDueToPM;
        this.malariaCasesDueToPO = malariaCasesDueToPO;
        this.malariaCasesDueToPV = malariaCasesDueToPV;
        this.keyVariableConcordanceBtwTwoReportingSystem = keyVariableConcordanceBtwTwoReportingSystem;
        this.coreVariableCompletenessWithinRegister = coreVariableCompletenessWithinRegister;
        this.coreVariableConcordanceBtwRegister = coreVariableConcordanceBtwRegister;
        this.dataQualityResultReason = dataQualityResultReason;
        this.year = year;
        this.isFinalized = isFinalized;
    }

    static init = (assessmentId: string) => new EliminationSummaryModel(assessmentId, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "", 0, false);
}