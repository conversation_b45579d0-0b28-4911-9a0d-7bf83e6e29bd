import format from "date-fns/format";
import { UtilityHelper } from "../../utils/UtilityHelper";
import { getMonth, getYear } from "date-fns";

export class ServiceLevelRequestModel {
    constructor(
        public serviceLevelId: string,
        public from: string,
        public to: string,
        public validationPeriodStartDate: string,
        public year: number | null,
        public registerTypes: Array<number | string>,
        public variableIds: Array<string>,
        public assessmentId: string,
        public isFinalized: boolean,
        public isReFinalized: boolean,
        public fromValidationStartDate: string, // Set minimum validation period start date
        public toValidationEndDate: string, // Set maximum validation period end date
        public fromStartDate: string,// Set minimum To start date
        public toEndDate: string,// Set maximum To end date
        public hasSummaryData: boolean,
        public fileName: string
    ) {
        this.serviceLevelId = serviceLevelId;
        this.from = from;
        this.to = to;
        this.validationPeriodStartDate = validationPeriodStartDate;
        this.year = year;
        this.registerTypes = registerTypes;
        this.variableIds = variableIds;
        this.assessmentId = assessmentId;
        this.isFinalized = isFinalized;
        this.fromValidationStartDate = fromValidationStartDate;
        this.toValidationEndDate = toValidationEndDate;
        this.fromStartDate = fromStartDate;
        this.toEndDate = toEndDate;
        this.hasSummaryData = hasSummaryData;
        this.fileName = fileName;
    }

    static init = (assessmentId: string) => {
        const toStartDate = UtilityHelper.getFromDate(getYear(new Date()), getMonth(new Date()));
        const toEndDate = UtilityHelper.getEndDate(format(new Date(), "MM/dd/yyyy"));
        return new ServiceLevelRequestModel("", toStartDate, toEndDate, format(new Date(), "MM/dd/yyyy"), 0, [], [], assessmentId, false, false, toStartDate, toEndDate, toStartDate, toEndDate, false,"");
    }
}

export class ServiceLevelFinalizeModel {
    constructor(
        public serviceLevelId: string,
        public assessmentId: string
    ) {
        this.serviceLevelId = serviceLevelId;
        this.assessmentId = assessmentId;
    }
}

export class ServiceLevelDeleteModel {
    constructor(
        public serviceLevelId: string,
        public assessmentId: string
    ) {
        this.serviceLevelId = serviceLevelId;
        this.assessmentId = assessmentId;
    }
}
