﻿export class Constants {
    static Common = class {
        static TechnicalAndProcessObjectiveId = "431b62e2-77d9-47a1-85a3-0f8769a1fd7c";
        static InformationSystemSubObjectiveId = "243cc827-5569-415b-90a3-36c7b765555c";
        static RootObjectNameSubstitute = "{root}";
        static IndexSubstitute = "{index}";
        static KeySubstitute = "{key}";
        static ValueSubstitute = "{value}";
        static newUserCookieName: string = "WHO.MALARIA.IsNewUser";
        static RedirectTimeInterval: number = 5000; // 5 sec
        static ReloadTimeInterval: number = 3000; // 3 sec
        static EncryptionKey: string = "01234567890123456789012345678901";
        static UserInfoCookieName: string = "WHO.MALARIA.UserInfo";
        static DefaultDateTimeFormat: string = "yyyy/MM/dd HH:mm a";
        static DefaultDateFormat: string = "yyyy/MM/dd";
        static WHOUser: string = "WHO";
        static skip: number = 0;
        static take: number = 10;
        static DefaultPageSize: number = 10;
        static DownloadFileName: string = "MALARIA SURVEILLANCE TOOLKIT Tools_{language}.zip";
        static OverviewFileName: string = "Introduction to the Malaria Surveillance Assessment Toolkit_{language}.pdf";
        static DQAEliminationFileName = "DQA_Elimination.xlsx";
        static EmailRegx: RegExp =
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        static NUMBER_MAX_LENGTH: number = 4;
        static Months = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ];
        static MonthsLocale = [
            {
                name: "Jan",
                name_fr: "janv."
            },
            {
                name: "Feb",
                name_fr: "févr."
            },
            {
                name: "Mar",
                name_fr: "mars"
            },
            {
                name: "Apr",
                name_fr: "avr."
            },
            {
                name: "May",
                name_fr: "mai"
            },
            {
                name: "Jun",
                name_fr: "juin"
            },
            {
                name: "Jul",
                name_fr: "juil."
            },
            {
                name: "Aug",
                name_fr: "août"
            },
            {
                name: "Sep",
                name_fr: "sept."
            },
            {
                name: "Oct",
                name_fr: "oct."
            },
            {
                name: "Nov",
                name_fr: "nov."
            },
            {
                name: "Dec",
                name_fr: "déc."
            }
        ];


        static MonthsGraph = [

            {
                name: "January",
                name_fr: "Janvier"
            },
            {
                name: "February",
                name_fr: "Février"
            },
            {
                name: "March",
                name_fr: "Mars"
            },
            {
                name: "April",
                name_fr: "Avril"
            },
            {
                name: "May",
                name_fr: "Mai"
            },
            {
                name: "June",
                name_fr: "Juin"
            },
            {
                name: "July",
                name_fr: "Juillet"
            },
            {
                name: "August",
                name_fr: "Août"
            },
            {
                name: "September",
                name_fr: "Septembre"
            },
            {
                name: "October",
                name_fr: "Octobre"
            },
            {
                name: "November",
                name_fr: "Novembre"
            },
            {
                name: "December",
                name_fr: "Décembre"
            }
        ];

        static DQADataSystem1OtherId: string = "103c0c11-b432-409c-8cdb-96152fd9dac0";
        static DQADataSystem2OtherId: string = "104c274c-ddb0-4e73-9aca-bb217f39b7cc";
        static dataSystem1HMIS = "101f5f67-8dd5-4459-8182-d88b4f044a0a";
        static dataSystem2HMIS = "10263c8e-8c68-493c-8702-758e6a86f12a";
        static MaxFileSize: number = 26214400;
        static MaxDiagramFileSize: number = 10485760;
        static ValidExcelExtensions: Array<string> = ['xlsx', 'xls'];
        static MaxToolkitZipFileSize: number = 52428800;
        static iptp = "iptp 1-4";
        static iptpColumnfield = "ipTp 1-4";
        static tpig = "tpig1-4";
        static tpigColumnfield = "tpIg1-4";
        static healthFacilityType = "healthFacilityType";
        static healthFacilityName = "healthFacilityName";
        static Percentage = "percentage";
        static RequiredResponseCodeSubnational = ['is_1', 'reporting_1', 'analysis_1', 'QA_3'];
        static RequiredResponseCodeServiceDelivery = ['is_1', 'recording_1', 'reporting_1', 'analysis_1'];
        static RequiredResponseCodeCommunity = ['recording_1', 'reporting_1'];
    };

    static SessionStorageKey = class {
        static STRATEGY_SELECTION_NAV_IDS: string = "STRATEGY_SELECTION_NAV_IDS";
        static ACTIVE_USER_COUNTRIES: string = "ACTIVE_USER_COUNTRIES";
        static SELECTED_COUNTRY: string = "SELECTED_COUNTRY";
        static SELECTED_COUNTRY_NAME: string = "SELECTED_COUNTRY_NAME";
        static SELECTED_USER_TYPE: string = "SELECTED_USER_TYPE";
        static SELECTED_REPORT_YEAR: string = "SELECTED_REPORT_YEAR";
    };

    static RetrieveMultipleRecordsEntity = class {
        static COUNTRY: string = "Country";
        static USER: string = "User";
    };

    static Diagrams = class {
        static Objective: string = "Objective";
        static SubObjective: string = "SubObjective";
    }

    /** All route URLs */
    static Route = class {

        /** Holds all the URLs for routing */
        static Url = class {
            static ANALYTICAL_OUTPUT_UPLOADED_DIAGRAM: string = '/assessment/data-analysis/desk-review/uploaded-diagram';
        }
    }

    /** All information related to API */
    static Api = class {
        static baseUrl: string = `${window.location.origin}/api`;

        /** Holds all the URLs for xhr calls */
        static Url = class {
            // PUBLIC PAGE URLS
            static DOWNLOAD_ALL_TOOL: string = `external/tools/download`;
            static OVERVIEW_DOWNLOAD: string = `external/download/overview-document`;
            static DASHBOARD_ASSESSMENT_STATISTICS: string = `external/dashboard/assessment/statistics`;

            // USER RELATED URLS
            static REGISTER_USER: string = `user/register`;
            static CREATE_SUPER_MANAGER_EXTERNAL: string = `user/create/supermanager/external`;
            static CREATE_SUPER_MANAGER_INTERNAL: string = `user/create/supermanager/internal`;
            static RETRIEVE_MULTIPLE_RECORDS: string = `internal/{0}/records`;
            static RETRIEVE_COUNTRY_RECORDS: string = `external/country/records`;
            static GET_USER_PROFILE: string = `user/profile`;
            static GET_PENDING_REQUESTS: string = `user/requests/pending/{0}`;
            static GET_ANALYTICS_DATA: string = `analyticsTracking/statistics`;
            static UPDATE_USER_STATUS_BY_WHO_USER: string = `user/update/status/requester/who`;
            static UPDATE_USER_TYPE_BY_WHO_USER: string = `user/makeViewerAsSupermanager`;
            static UPDATE_USER_TYPE_AND_STATUS_BY_SUPER_MANAGER: string = `user/update/statusAndType/requester/supermanager`;
            static GET_SUPER_MANAGERS_WHO_ADMIN_USER_REQUESTED_BY_WHO_ADMIN: string = `user/users/requester/who`;
            static GET_REGISTERED_USERS_FOR_WHO_ADMIN: string = `user/new-inactive-viewers`;
            static GET_VIEWERS_AND_MANAGERS_REQUESTED_BY_SM: string = `user/users/requester/supermanager/{0}`;
            static GET_USERS_BY_COUNTRY: string = `user/users/country/{0}`;
            static GET_MANAGERS_BY_COUNTRY: string = `user/managers/{0}`;
            static GET_VIEWERS_BY_COUNTRY: string = `user/viewers/{0}`;
            static SEND_USER_ACTIVATION: string = `user/sendActivation`;
            static REJECT_USER_ACTIVATION_REQUEST: string = `user/request/useractivation/reject`;
            static UPDATE_USER: string = `user/update`;
            static RETRIEVE_COUNTRY_RECORDS_LOGIN_USER: string = `user/countries`;
            static APPROVE_USER_COUNTRY_ACCESS_REQUEST: string = `user/request/countryaccess/approve`;
            static REJECT_USER_COUNTRY_ACCESS_REQUEST: string = `user/request/countryaccess/reject`;
            static FORWARD_USER_TO_WHO_ADMIN_REQUEST: string = 'user/request/countryaccess/updatecomment'
            static GET_COUNTRIES_WITHOUT_SUPER_MANAGER: string = `country/countries/without/supermanager`;
            static ACCEPT_USER_INVITE: string = "user/invitation/accept";
            static RESEND_INVITATION: string = `user/resend-invitation`;
            static USER_UPDATE_DEFAULT_COUNTRY_ON_LANDING_PAGE_REQUEST: string = `user/update/defaultCountryOnLandingPage`;
            static USER_ACTIVE_COUNTRY: string = `user/activecountries`;
            static USER_DEACTIVATED_PROFILE: string = `user/profile/countrydetail`;
            static USER_ADD_COUNTRY_ACCESS_REQUEST: string = `user/add/countryAccess`;

            // ASSESSMENTS URLSs
            static CREATE_ASSESSMENT: string = `assessment/create`;
            static UPDATE_ASSESSMENT: string = `assessment/update`;
            static GET_ASSESSMENTS: string = `assessment/assessments`;
            static GET_ASSESSMENT: string = `assessment/{assessmentId}`;
            static GET_ASSESSMENT_STRATEGIES: string = `assessment/strategies`;
            static SAVE_ASSESSMENT_STRATEGIES: string = `assessment/saveStrategies`;
            static SAVE_ASSESSMENT_OBJECTIVES: string = `assessment/objectives`;
            static GET_ASSESSMENT_SUBOBJECTIVES: string = `assessment/subObjectives/{0}`;
            static GET_ASSESSMENT_STRATEGY_INDICATORS: string = `assessment/strategy/indicators`;
            static SAVE_ASSESSMENT_STRATEGY: string = `assessment/strategy/saveStrategies`;
            static SAVE_ASSESSMENT_INDICATORS: string = `assessment/saveIndicators`;
            static GET_STRATEGIES_BY_ASSESSMENTID: string = `assessment/strategies/{0}`;
            static GET_INDICATORS_BY_ASSESSMENTID: string = `assessment/scopedefinition/indicators/{0}`;
            static FINALIZE_ASSESSMENT: string = `assessment/finalize`;
            static GET_ASSESSMENT_PERMISSIONS: string = `assessment/permission/{0}`;
            static GET_INDICATORS_FOR_DESK_REVIEW: string = `assessment/datacollect/deskreview/indicators`;
            static DATA_COLLECTION_DESK_REVIEW_SAVE_RESPONSE: string = `assessment/dataCollect/deskReview/response/save`;
            static DATA_COLLECTION_DESK_REVIEW_SAVE_RESPONSE_UPLOAD_DOCUMENT: string = `assessment/dataCollect/deskReview/response/save/upload-document`;
            static GET_DATA_COLLECTION_DESK_REVIEW_RESPONSE_DOCUMENTS: string = `assessment/dataCollect/deskReview/response/documents/{assessmentIndicatorId}/{assessmentStrategyId}`;
            static DATA_COLLECTION_DESK_REVIEW_GET_RESPONSE: string = `assessment/dataCollect/deskReview/response/{0}/{1}`;
            static DATA_COLLECTION_DESK_REVIEW_GET_PARENT_RESPONSE: string = `assessment/dataCollect/deskReview/parent/response/{0}/{1}/{2}`;
            static DATA_COLLECTION_DESK_REVIEW_RECORDED_CHECKLIST_VARIABLES: string = `assessment/dataCollect/deskReview/{0}/recordedChecklistVariables`;
            static DATA_COLLECTION_DESK_REVIEW_REPORTED_CHECKLIST_VARIABLES: string = `assessment/dataCollect/deskReview/{0}/reportedChecklistVariables`
            static DATA_COLLECTION_DESK_REVIEW_CHECKLIST_INDICATORS: string = `assessment/dataCollect/deskReview/{0}/checklistIndicators`;
            static DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_UPLOAD_DIAGRAM: string = 'assessment/dataCollect/deskReview/objective-diagram/upload';
            static DATA_COLLECTION_DESK_REVIEW_SUBOBJECTIVE_UPLOAD_DIAGRAM: string = 'assessment/dataCollect/deskReview/sub-objective-diagram/upload';
            static GET_DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_UPLOAD_DIAGRAM: string = 'assessment/dataCollect/deskReview/{assessmentId}/{strategyId}/objective-diagram';
            static GET_DATA_COLLECTION_DESK_REVIEW_SUBOBJECTIVE_UPLOAD_DIAGRAM: string = 'assessment/dataCollect/deskReview/{assessmentId}/{strategyId}/sub-objective-diagram';
            static GET_DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_DIAGRAM_STATUS: string = 'assessment/dataCollect/deskReview/{assessmentId}/{strategyId}/diagram-status';

            // DQA  URLS
            static EXPORT_SL_DQA_EXCEL_FILE: string = `dqa/sl/export/{0}`;
            static UPLOAD_SL_DQA_EXCEL_FILE: string = `dqa/sl/upload`;
            static DATA_COLLECTION_DQA_VARIABLES_GET_RESPONSE: string = `dqa/variables/{0}/{1}`;
            static DQA_DATASOURCES_GET_RESPONSE: string = `dqa/datasources`;
            static DQA_SERVICE_LEVEL_SAVE_RESPONSE: string = `dqa/servicelevel`;
            static DQA_SERVICE_LEVEL_UPDATE_RESPONSE: string = `dqa/servicelevel`;
            static DQA_SERVICE_LEVEL_FINALIZE_RESPONSE: string = `dqa/servicelevel/finalize`;
            static DQA_SERVICE_LEVEL_DELETE_RESPONSE: string = `dqa/servicelevel/delete`;
            static DQA_SERVICE_LEVEL_GET_RESPONSE: string = `dqa/{0}/servicelevel`;
            static DQA_SERVICE_LEVEL_SUMMARY_GET_RESPONSE: string = `dqa/{0}/servicelevelsummary`;
            static DQA_SERVICE_LEVEL_UPDATE_OBSERVED_DATA_QUALITY_REASON_RESPONSE: string = `dqa/servicelevel/updateObservedDataQualityReason`;
            static DQA_SERVICE_LEVEL_FINALIZE_OBSERVED_DATA_QUALITY_REASON_RESPONSE: string = `dqa/servicelevel/finalizeObservedDataQualityReason`;
            static DQA_DESK_LEVEL_REPORT: string = `dqa/desklevel/report/{0}/{1}`;
            static DQA_DESK_LEVEL_SAVE_RESPONSE: string = `dqa/desklevel/save`;
            static GENERATE_DL_DQA_EXCEL_FILE: string = `dqa/desklevel/generateTemplate/{0}`;
            static UPLOAD_DL_DQA_EXCEL_FILE: string = `dqa/desklevel/processFile`;
            static DQA_DESK_LEVEL_INDICATORS: string = `dqa/deskLevel/indicators`;
            static DQA_DESK_LEVEL_FINALIZE_RESPONSE: string = `dqa/desklevel/finalize`;
            static DQA_DESK_LEVEL_GET_RESPONSE: string = `dqa/deskLevel/selectedParameters/{0}`;
            static DQA_DESK_LEVEL_REPORT_EXPORT: string = `dqa/desklevel/report/export/{0}`;
            static ELIMINATION_DQA_EXPORT: string = `dqa/elimination/download`;
            static SAVE_ELIMINATION_DQA_SUMMARY: string = `dqa/elimination/SaveNationalLevelResult`;
            static ELIMINATION_DQA_GET_SUMMARY_RESPONSE: string = `dqa/elimination/nationalLevelSummary/{0}`;
            static GENERATE_ELIMINATION_DQA_EXCEL_FILE: string = `dqa/elimination/report/export/{0}`;
            static ELIMINATION_DQA_FINALIZE_RESPONSE: string = `dqa/elimination/FinalizeNationalLevelResult`;
            static DQA_DESK_LEVEL_SUMMARY_YEARS: string = `dqa/deskLevel/summary/years/{0}`;
            static DQA_DESK_LEVEL_NATIONAL_LEVEL_SUMMARY: string = `dqa/deskLevel/nationalLevelSummary/{0}/{1}`;
            static DQA_DESK_LEVEL_SAVE_NATIONAL_LEVEL_SUMMARY: string = `dqa/deskLevel/SaveNationalLevelTarget`;
            static DQA_DESK_LEVEL_FINALIZE_NATIONAL_LEVEL_SUMMARY: string = `dqa/deskLevel/FinalizeNationalLevelResult`;
            static GENERATE_DESK_LEVEL_DQA_EXCEL_FILE: string = `dqa/desklevel/report/export/{0}`;

            // Question Bank Survey URLS
            static QUESTION_BANK_GET_RESPONDENT_TYPES: string = `assessmentQuestionBank/{0}/respondentTypes`;
            static QUESTION_BANK_SAVE_RESPONDENT_TYPES: string = `assessmentQuestionBank/saveRespondentTypes`;
            static QUESTION_BANK_GET_SURVEY_QUESTIONS: string = `assessmentQuestionBank/{0}/questions`;
            static QUESTION_BANK_GET_OBJECTIVE_SUBOBJECTIVE_INDICATORS: string = `assessmentQuestionBank/{0}/objectives/sub-objectives/indicators`;
            static QUESTION_BANK_SAVE_SURVEY_QUESTIONS: string = `assessmentQuestionBank/saveQuestions`;
            static QUESTION_BANK_FINALIZE_SURVEY_QUESTIONS: string = `assessmentQuestionBank/finalize-questionnaire`;
            static QUESTION_BANK_GENERATE_QUESTIONNAIRE: string = `assessmentQuestionBank/questionaire/generate/{0}`;
            static QUESTION_BANK_DOWNLOAD_HEALTH_FACILITIES: string = `assessmentQuestionBank/healthFacilityTemplate/download`;
            static QUESTION_BANK_UPLOAD_HEALTH_FACILITIES: string = `assessmentQuestionBank/healthfacility/upload`;
            static QUESTION_BANK_EXPORT_HEALTH_FACILITIES: string = `assessmentQuestionBank/healthfacilities/export/{0}`;
            static QUESTION_BANK_HAS_HEALTH_FACILITIES_DATA: string = `assessmentQuestionBank/hasHealthFacilityData/{0}`;
            static QUESTION_BANK_EXPORT_HEALTH_FACILITIES_FOR_ASSESSMENT: string = `assessmentQuestionBank/healthfacilities/export/{0}/{1}`;
            static QUESTION_BANK_GET_UPLOADES_FILENAME: string = `assessmentQuestionBank/healthfacility/filename/{0}`;
            static QUESTION_BANK_GET_UPLOADED_SHELL_REPORT_FILES: string = `assessmentQuestionBank/shelltable/getUploadedQuestionBankShellTableFileInformation/{0}`;
            // Shell table URLS
            static SHELL_TABLE_UPLOAD_FILE: string = `assessmentQuestionBank/shelltable/upload`;
            static SHELL_TABLE_RESPONDENT_TYPES_HEALTH_FACILITY_TYPES: string = `shellTable/respondent-types/health-facility-types/{0}`;
            static SHELL_TABLE_OBJECTIVES_SUB_OBJECTIVES_INDICATORS: string = `shellTable/objectives/sub-objectives/indicators/{0}`;
            static SHELL_TABLE_QUESTIONS: string = `shellTable/questions/{0}/{1}/{2}/{3}`;
            static SHELL_TABLE_EXPORT: string = `shellTable/export/{0}/{1}/{2}`;


            // Analytical output URLS
            static ANALYTICAL_OUTPUT_GET_STRATEGY_OBJECTIVE_SUBOBJECTIVE_INDICATORS: string = `analyticalOutput/details/{0}`;
            static ANALYTICAL_OUTPUT_INDICATOR_RESPONSE: string = `analyticalOutput/indicator/response`;
            static ANALYTICAL_OUTPUT_REPORT_DOWNLOAD: string = `analyticalOutput/report/download`;
            static ANALYTICAL_OUTPUT_REPORT_DOWNLOAD_ALL: string = `analyticalOutput/report/download/all`;
            static GET_ANALYTICAL_OUTPUT_OBJECTIVE_UPLOAD_DIAGRAM: string = 'analyticalOutput/objective-diagram/{0}/{1}';
            static ANALYTICAL_OUTPUT_DOWNLOAD_OBJECTIVE_UPLOADED_DIAGRAM: string = 'analyticalOutput/objective-diagrams/{0}/{1}/download';
            static ANALYTICAL_OUTPUT_DOWNLOAD_SUBOBJECTIVE_UPLOADED_DIAGRAM: string = 'analyticalOutput/subObjective-diagrams/{0}/{1}/download';

            // Data analysis  report
            static DATA_ANALYSIS_REPORT_PUBLISH_RESPONSE: string = `dataAnalysisReport/publishAssessment`;
            static DATA_ANALYSIS_SHOW_ASSESSMENT_ON_GLOBAL_DASHBOARD_RESPONSE: string = `dataAnalysisReport/showAssessmentOnGlobalDashboard`;
            static DATA_ANALYSIS_UPLOAD_FILE: string = `dataAnalysisReport/upload`;
            static DATA_ANALYSIS_REPORTS: string = `dataAnalysisReport/reports/{0}`;
            static DATA_ANALYSIS_DOWNLOAD_FILE: string = `dataAnalysisReport/download/{0}`;
            static DATA_ANALYSIS_DELETE_FILE: string = `dataAnalysisReport/delete`;

            //Score card URLS
            static GET_SCORE_CARD_SURVEY: string = `scoreCard/details/{0}`;
            static GENERATE_SCORE_CARD_EXCEL_FILE: string = `scoreCard/export/{0}`;
            static SAVE_SCORE_CARD_SURVEY: string = `scoreCard/survey/setMetNotMetForIndicators`;
            static FINALIZE_SCORE_CARD_SURVEY: string = `scoreCard/survey/finalize`;
            static CAN_SCORE_CARD_BE_GENERATED: string = `scoreCard/canScoreCardBeGenerated/{0}`;
            static HAS_SCORE_CARD_DATA: string = `scoreCard/hasScordCardData/{0}`;

            //Dashboard URLS
            static GET_DASHBOARD_YEARS: string = `dashboard/years/{0}`;
            static GET_DASHBOARD_RECORDS: string = `dashboard/records/{0}/{1}`;
            static GET_REGIONAL_SUMMARY: string = `dashboard/regionalSummary`;
            static GET_OBJECTIVE_SUMMARY: string = `dashboard/mapsummary`;
            static GET_INDICATOR_SUMMARY: string = `dashboard/indicatorSummary`;
            static GET_DASHBOARD_ASSESSMENTS_STATISTICS: string = `dashboard/assessments/status-and-approach`;
            static GET_DASHBOARD_ASSESSMENTS_YEAR_WISE_STATISTICS: string = `dashboard/assessments/{0}/status`;

            //Upload Document URLS
            static SAVE_UPLOADED_TOOLKIT_DOCUMENT: string = `internal/toolkit-document/upload`;
        };
    };
}

