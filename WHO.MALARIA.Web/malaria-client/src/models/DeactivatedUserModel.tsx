﻿import { UserListModel } from "../models/UserModel";
import MultiSelectModel from "./MultiSelectModel";

export class DeactivatedUserModel {
    constructor(
        public userProfileDetail: UserListModel,
        public countries: Array<IdAndName> = [],
    ) {
        this.userProfileDetail = userProfileDetail;
        this.countries = countries;
    }

    static init = () =>
        new DeactivatedUserModel(
            UserListModel.init(),
            []
        );
}

export class IdAndName {
    constructor(
        public id: string,
        public name: string,
    ) {
        this.id = id;
        this.name = name;
    }
}

export class UserCountryAssessRequestModel {
    constructor(
        public countryRequestedForIds: Array<string> = []
    ) {
        this.countryRequestedForIds = countryRequestedForIds;
    }
}