﻿/* eslint-disable */

export class DashboardStatisticsModel {
    constructor(
        public inProgressSurveillanceAssessments: Array<CountryDataModel>,
        public completedSurveillanceAssessments: Array<CountryDataModel>,
        public multipleCompletedSurveillanceAssessments: Array<CountryDataModel>,
        public rapidSurveillanceAssessments: Array<CountryDataModel>,
        public tailoredSurveillanceAssessments: Array<CountryDataModel>,
        public comprehensiveSurveillanceAssessments: Array<CountryDataModel>
    ) {
        this.inProgressSurveillanceAssessments = inProgressSurveillanceAssessments,
            this.completedSurveillanceAssessments = completedSurveillanceAssessments,
            this.multipleCompletedSurveillanceAssessments = multipleCompletedSurveillanceAssessments,
            this.rapidSurveillanceAssessments = rapidSurveillanceAssessments,
            this.tailoredSurveillanceAssessments = tailoredSurveillanceAssessments,
            this.comprehensiveSurveillanceAssessments = comprehensiveSurveillanceAssessments
    }
    static init = () =>
        new DashboardStatisticsModel([], [], [], [], [], []);
}

export class DashboardYearWiseStatisticsModel {
    constructor(
        public inProgressAssessments: Array<CountryDataModel>,
        public completedAssessments: Array<CountryDataModel>
    ) {
        this.inProgressAssessments = inProgressAssessments,
            this.completedAssessments = completedAssessments
    }
    static init = () =>
        new DashboardYearWiseStatisticsModel([], []);
}

export class CountryDataModel {
    constructor(public iso: string, public status: number) {
        this.iso = iso;
        this.status = status;
    }
}

