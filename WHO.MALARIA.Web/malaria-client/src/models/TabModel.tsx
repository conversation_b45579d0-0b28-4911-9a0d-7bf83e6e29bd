﻿export class TabModel {
    constructor(public id: string | number,
        public label: string,
        public children: React.ReactNode,
        public canDelete: boolean = false,
        public icon: string | React.ReactElement<any, string | React.JSXElementConstructor<any>> | undefined = undefined) {
        this.id = id;
        this.label = label;
        this.children = children;
        this.canDelete = canDelete;
        this.icon = icon;
    }
}

export class TabPanelModel {
    constructor(public index: number, public value: string | number, public children: React.ReactNode) {
        this.index = index;
        this.value = value;
        this.children = children;
    }
}