﻿export class PublishAssessment {
    constructor(
        public assessmentId: string

    ) {
        this.assessmentId = assessmentId;
    }
}

export class ShowAssessmentResultOnGlobalDashboard {
    constructor(
        public assessmentId: string,
        public showResultOnGlobalDashboard: boolean

    ) {
        this.assessmentId = assessmentId;
        this.showResultOnGlobalDashboard = showResultOnGlobalDashboard;
    }
}

export class DataAnalysisReport {
    constructor(
        public reportFiles: Array<DataAnalysisReportFile>,
        public showResultsOnGlobalDashboard: boolean,
        public isAssessmentPublished: boolean

    ) {
        this.reportFiles = reportFiles;
        this.showResultsOnGlobalDashboard = showResultsOnGlobalDashboard;
        this.isAssessmentPublished = isAssessmentPublished;
    }

    static init = () => new DataAnalysisReport([], false, false);
}


export class DataAnalysisReportFile {
    constructor(
        public id: string,
        public assessmentId: string,
        public name: string,
        public size: string

    ) {
        this.id = id;
        this.assessmentId = assessmentId;
        this.name = name;
        this.size = size;
    }
}

export class DeleteReportFile {
    constructor(
        public assessmentId: string,
        public reportId: string

    ) {
        this.assessmentId = assessmentId;
        this.reportId = reportId;
    }
}
