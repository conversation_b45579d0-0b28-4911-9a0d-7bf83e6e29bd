﻿import { KeyValuePair } from"../models/DeskReview/KeyValueType";

export class TabWithChartsModel {
    constructor(
        public tabName: string,
        public tabId: number,
        public charts?: Array<ChartModel>,
    ) {
        this.tabName = tabName;
        this.tabId = tabId;
        this.charts = charts;
    }
}

export class ChartModel {
    constructor(
        public values: Array<KeyValuePair<string | undefined, Array<number>>>,
        public xAxis: string,
        public yAxis: string,
        public title: string,
        public categories: Array<string | number>
    ) {
        this.values = values;
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        this.title = title;
        this.categories = categories;
    }
}