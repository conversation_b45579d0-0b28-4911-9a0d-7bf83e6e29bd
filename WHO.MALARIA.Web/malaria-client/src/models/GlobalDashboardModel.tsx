﻿import { KeyValuePair } from "./../models/DeskReview/KeyValueType";

export class DashboardReportModel {
    constructor(
        public type: number,
        public response: any,
    ) {
        this.type = type;
        this.response = response;
    }

    static init = () => new DashboardReportModel(1, "");
}

export class IndicatorDashboardModel {
    constructor(
        public objectives: Array<KeyValuePair<string | null, number | null>>,
        public subObjectives: Array<KeyValuePair<string | null, number | null>>,
        public indicators: TabularModel,
    ) {
        this.objectives = objectives;
        this.subObjectives = subObjectives;
        this.indicators = indicators;
    }
}

export class TabularModel {
    constructor(
        public rows: Array<any>,
        public columns: Array<Column>,
    ) {
        this.rows = rows;
        this.columns = columns;
    }
}

export class Column {
    constructor(
        public key: string,
        public label: string,
        public width: string
    ) {
        this.key = key;
        this.label = label;
        this.width = width;
    }
}

export class ObjectiveDashboardModel {
    constructor(
        public strategies: Array<StrategyModel>,
        public objective: ObjectiveModel,
        public countries: Array<CountryDataModel>,
    ) {
        this.strategies = strategies;
        this.objective = objective;
        this.countries = countries
    }

}

export class CountryDataModel {
    constructor(
        public iso: string, public status: number
    ) {
        this.iso = iso;
        this.status = status;
    }
}

export class ObjectiveModel {
    constructor(
        public key: string,
        public name: string,
        public value: string
    ) {
        this.key = key;
        this.name = name;
        this.value = value;
    }

    static init = () => new ObjectiveModel("", "", "");
}

export class StrategyModel {
    constructor(
        public id: string,
        public name: string,
    ) {
        this.id = id;
        this.name = name;
    }
}