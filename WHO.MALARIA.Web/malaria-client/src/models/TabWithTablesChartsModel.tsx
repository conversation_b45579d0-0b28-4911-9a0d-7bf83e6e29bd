﻿import { TableModel } from "../models/TableModel";
import { ChartModel } from "../models/ChartModel";

export class TabWithTablesChartsModel {
    constructor(
        public tabName: string,
        public tabId: number,
        public tables?: Array<TableModel>,
        public charts?: Array<ChartModel>,
    ) {
        this.tabName = tabName;
        this.tabId = tabId;
        this.tables = tables;
        this.charts = charts;
    }
}

export class MultipleChildTabsModel {
    constructor(
        public tabName: string,
        public tabId: number,
        public tabs: Array<TabWithTablesChartsModel>
    ) {
        this.tabName = tabName;
        this.tabId = tabId;
        this.tabs = tabs;
    }
}