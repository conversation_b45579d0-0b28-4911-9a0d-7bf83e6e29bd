export class AnalyticsResponseDataModel {
    constructor(
        public eventAction: string,
        public eventCategory: string,
        public userType: string,
        public pageTitle: string,
        public city: string,
        public date: Date,
        public activeUsersCount: number
    ) {
        this.eventAction = eventAction;
        this.eventCategory = eventCategory;
        this.userType = userType;
        this.pageTitle = pageTitle;
        this.city = city;
        this.date = date;
        this.activeUsersCount = activeUsersCount;
    }
    static init = () =>
        new AnalyticsResponseDataModel(
            "",
            "",
            "",
            "",
            "",
            new Date(),
            0
        );
}