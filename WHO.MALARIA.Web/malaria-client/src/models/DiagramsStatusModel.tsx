﻿import { DeskReviewAssessmentResponseStatus } from "./Enums";

export class DiagramsStatusModel {
    constructor(
        public objectiveDiagramStatus: number,
        public subObjectiveDiagramStatus: number,
    ) {
        this.objectiveDiagramStatus = objectiveDiagramStatus;
        this.subObjectiveDiagramStatus = subObjectiveDiagramStatus;
    }
    static init = () => new DiagramsStatusModel(DeskReviewAssessmentResponseStatus.InProgress, DeskReviewAssessmentResponseStatus.InProgress);
}