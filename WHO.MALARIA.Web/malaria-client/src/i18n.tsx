import i18next from "i18next";
import HttpApi from "i18next-http-backend";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

i18next
  .use(HttpApi)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    ns: ["translation", "indicator-guide", "indicators-responses"],
    defaultNS: "translation",
    supportedLngs: ["en", "fr"],
    fallbackLng: "en",
    debug: false,
    // Options for language detector
    detection: {
      order: ["cookie", "path", "htmlTag"],
      caches: ["cookie"],
    },
    // react: { useSuspense: false },
    backend: {
        loadPath: "/assets/locales/{{lng}}/{{ns}}.{{lng}}.json",
    },
  });

export default i18next;
