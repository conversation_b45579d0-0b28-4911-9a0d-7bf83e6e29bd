﻿
import React, { ReactElement } from "react";

const EllipsisIcon = ({ width = "20px", height = "20px" }): ReactElement => {
    return (

        <svg width="124" height="170" viewBox="0 0 124 170" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="122.5" cy="123.5" r="70" stroke="#3D8CCA" stroke-width="3" />
            <circle cx="137.5" cy="138.5" r="55.5" stroke="#3D8CCA" stroke-width="4" />
            <circle cx="109" cy="110" r="83" stroke="#3D8CCA" stroke-width="2" />
            <circle cx="100" cy="100" r="99.5" stroke="#3D8CCA" />
        </svg>

    );
}

export default EllipsisIcon;


