﻿@use "../../mixins.scss" as *;

.headerWrapper {
  min-height: 65px;
}
.brandLogo path {
  fill: #fff !important;
}
a {
  text-decoration: none !important;
}

a:hover {
  text-decoration: none !important;
  cursor: pointer;
}
.navbarBrand {
  display: flex;
  align-items: center;
  @include font-size-line-height(16px, 20px, 0);

  a {
    color: $white_color;
    margin-right: 15px;
    font-family: $font-stack;
    /*@include font-size($font_size_11);*/

    &:first-child {
      border-right: 1px solid #5d9acb;
    }

    &:hover {
      color: #ffffff;
    }
  }

  .app-logo-text {
    font-size: 18px;
    /*@include font-size($font_size_11);*/
    line-height: 22px;
    color: #ffffff !important;
  }
}
