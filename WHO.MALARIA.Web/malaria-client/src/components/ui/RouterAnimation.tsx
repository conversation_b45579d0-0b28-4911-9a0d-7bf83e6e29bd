﻿
import { motion } from "framer-motion";
import classes from './RouterAnimation.module.css'

type AnimationWrapper = {
    children: React.ReactNode
}

const AnimationWrapper = (props: AnimationWrapper) => {
    const { children } = props;
    const pageVariants = {
        initial: {
            opacity: 0,
            x: "-100vw",
            scale: 0.8
        },
        in: {
            opacity: 1,
            x: 0,
            scale: 1
        },
        out: {
            opacity: 0,
            x: "100vw",
            scale: 1.2
        }
    };

    const pageTransition = {
        type: "tween",
        ease: "anticipate",
        duration: 0.5
    };


    return (
        <motion.div
            className={classes.absolute}
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
        >
            {children}
        </motion.div>
    )
}

export default AnimationWrapper;

