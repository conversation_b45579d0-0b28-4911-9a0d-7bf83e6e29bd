﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Modal from "../controls/Modal";
import { Table, TableBody, TableCell, TableRow } from "@mui/material";
import TableHeader from "../assessments/data-collection/desk-review/responses/TableHeader";
import parse from "html-react-parser";

/** Renders the tools card component on Landing page with link modal */
const ToolsCardModal = ({
    src = "",
    title = "",
    shortDescription = "",
    alt = "",
    linkDetails = ""
}) => {
    const { t } = useTranslation();
    const [assessmentApproachDialog, setAssessmentApproachDialog] = useState<boolean>(false);

    const headers = [
        {
            field: "Approach",
            label: t("translation:Landing:LandingTable2:Approach")
        },
        {
            field: "rapid",
            label: t("translation:Landing:LandingTable2:Rapid"),
        },
        {
            field: "tailored",
            label: t("translation:Landing:LandingTable2:Tailored"),
        },
        {
            field: "comprehensive",
            label: t("translation:Landing:LandingTable2:Comprehensive"),
        },
    ];

    const columns = [
        {
            field: "scope",
            labelOne: t("translation:Landing:LandingTable2:Scope"),
            labelTwo: t("translation:Landing:LandingTable2:ScopeRapidLabel"),
            labelThree: t("translation:Landing:LandingTable2:ScopeTailoredLabel"),
            labelFour: t("translation:Landing:LandingTable2:ScopeComprehensiveLabel"),
        },
        {
            field: "methods",
            labelOne: t("translation:Landing:LandingTable2:Methods"),
            labelTwo: t("translation:Landing:LandingTable2:MethodsRapidLabel"),
            labelThree: t("translation:Landing:LandingTable2:MethodsTailoredLabel"),
            labelFour: t("translation:Landing:LandingTable2:MethodsComprehensiveLabel"),
        },
        {
            field: "resourceRequirement",
            labelOne: t("translation:Landing:LandingTable2:EstimatedResourceRequirement"),
            labelTwo: t("translation:Landing:LandingTable2:ResourceRequirementRapidLabel"),
            labelThree: t("translation:Landing:LandingTable2:ResourceRequirementTailoredLabel"),
            labelFour: t("translation:Landing:LandingTable2:ResourceRequirementComprehensiveLabel"),
        },
        {
            field: "suggestedFrequency",
            labelOne: t("translation:Landing:LandingTable2:SuggestedFrequency"),
            labelTwo: t("translation:Landing:LandingTable2:SuggestedFrequencyRapidLabel"),
            labelThree: t("translation:Landing:LandingTable2:SuggestedFrequencyTailoredLabel"),
            labelFour: t("translation:Landing:LandingTable2:SuggestedFrequencyComprehensiveLabel"),
        },
    ]

    return (
        <>
            <div className="col-sm-12 col-md-3 mb-3">
                <div className="card h-100 mb-3">
                    <img className="card-img-top" src={src} alt={alt} />
                    <div className="card-body">
                        <h5 className="card-title">{title}</h5>
                        <p className="card-text">{parse(shortDescription)}</p>
                        <a className="link-text" onClick={() => setAssessmentApproachDialog(true)}>{linkDetails}</a>
                    </div>
                </div>
            </div>

            {/* Modal to show assessment approaches */}
            {
                <Modal
                    open={assessmentApproachDialog}
                    title={t("translation:Landing:LandingTable2Heading")}
                    onEscPress={false}
                    modalClassName="app-modal-md"
                    onDialogClose={() => setAssessmentApproachDialog(false)}
                >
                    <div className="w-100">                                               
                        <div className="custom-table">
                            <Table className="table-grey">
                                <TableHeader headers={headers.map((header: any) => header.label)} />
                                <TableBody>
                                    <>
                                        {columns.map((column: any, index: number) => (
                                            <TableRow key={`column_${column.field}_${index}`}>
                                                <>
                                                    <TableCell className="table-highlight">
                                                        <label>{column.labelOne}</label>
                                                    </TableCell>
                                                    <TableCell>
                                                        <label>{column.labelTwo}</label>
                                                    </TableCell>
                                                    <TableCell>
                                                        <label>{column.labelThree}</label>
                                                    </TableCell>
                                                    <TableCell>
                                                        <label>{column.labelFour}</label>
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                    </>
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </Modal>
            }
        </>
    );
};

export default ToolsCardModal;
