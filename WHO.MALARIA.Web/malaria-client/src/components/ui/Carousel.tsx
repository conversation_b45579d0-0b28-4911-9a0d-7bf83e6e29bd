﻿import React, { useEffect, useState } from "react";
import classes from "./Carousel.module.scss";
import CarouselItem from "./CarouselItem";
import WorldMap from "../controls/WorldMap";
import { Carousel as ReactCarousel } from "react-responsive-carousel";
import { CountryDataModel } from "../../models/DashboardModel";
import { useTranslation } from "react-i18next";
import Loading from "../common/Loading";
import useYears from "../assessments/data-collection/useYears";
import MultiSelectModel from "../../models/MultiSelectModel";
import AutoComplete from "../controls/AutoComplete";
import {
  DashboardStatisticsModel,
  DashboardYearWiseStatisticsModel,
} from "../../models/DashboardStatisticsModel";
import { dashboardService } from "../../services/dashboardService";

type CarouselProps = {
  canRenderAssessmentStatistics: boolean;
};

/** Renders Carousel component */
function Carousel(props: CarouselProps) {
  const { t } = useTranslation();
  const { canRenderAssessmentStatistics } = props;
  const years = useYears(2050);

  const [assessmentStatisticsDetails, setAssessmentStatisticsDetails] =
    useState<DashboardStatisticsModel>(DashboardStatisticsModel.init());

  const [
    assessmentYearWiseStatisticsDetails,
    setAssessmentYearWiseStatisticsDetails,
  ] = useState<DashboardYearWiseStatisticsModel>(
    DashboardYearWiseStatisticsModel.init()
  );

  const [loading, setLoading] = useState<boolean>(false);

  const [selectedYear, setSelectedYear] = useState<MultiSelectModel>(
    MultiSelectModel.initModel()
  );

  useEffect(() => {
    getAssessmentStatisticsDetails();
  }, []);

  // Get assessment statistics
  const getAssessmentStatisticsDetails = () => {
    setLoading(true);
    dashboardService
      .getAssessementsDetailsForDashboard()
      .then((assessmentStatistics: DashboardStatisticsModel) => {
        setAssessmentStatisticsDetails(assessmentStatistics);
        setLoading(false);
      });
  };

  // Get year wise assessment statistics
  const getYearWiseAssessmentStatisticsDetails = (year: string) => {
    dashboardService
      .getYearWiseAssessementsDetailsForDashboard(year)
      .then((assessmentStatistics: DashboardYearWiseStatisticsModel) => {
        setAssessmentYearWiseStatisticsDetails(assessmentStatistics);
        setLoading(false);
      });
  };

  if (loading) {
    return <Loading />;
  }

  const {
    inProgressSurveillanceAssessments,
    completedSurveillanceAssessments,
    multipleCompletedSurveillanceAssessments,
    rapidSurveillanceAssessments,
    tailoredSurveillanceAssessments,
    comprehensiveSurveillanceAssessments,
  } = assessmentStatisticsDetails;

  const { inProgressAssessments, completedAssessments } =
    assessmentYearWiseStatisticsDetails;

  // generate country data for dashboard
  const generateCountryData = (): Array<Array<CountryDataModel>> => {
    return [
      [
        ...(inProgressSurveillanceAssessments || []),
        ...(completedSurveillanceAssessments || []),
        ...(multipleCompletedSurveillanceAssessments || []),
      ],
      [
        ...(rapidSurveillanceAssessments || []),
        ...(tailoredSurveillanceAssessments || []),
        ...(comprehensiveSurveillanceAssessments || []),
      ],
      [...(inProgressAssessments || []), ...(completedAssessments || [])],
    ];
  };

  const countryData: Array<Array<CountryDataModel>> = generateCountryData();

  // triggers whenever user add/removes items in autocomplete dropdown
  const onYearChange = (selectedItems: MultiSelectModel | null) => {
    if (selectedItems != null) {
      getYearWiseAssessmentStatisticsDetails(selectedItems.id.toString());
      setSelectedYear(selectedItems);
    } else {
      setAssessmentYearWiseStatisticsDetails(
        DashboardYearWiseStatisticsModel.init()
      );
      setSelectedYear(MultiSelectModel.initModel());
    }
  };

  return (
    <>
      <div className={classes.carouselWrapper}>
        <div
          id="app-carousel"
          className="carousel slide mb-4"
          data-ride="carousel"
        >
          <div className="carousel-inner">
            <ReactCarousel
              showArrows={true}
              showThumbs={false}
              showIndicators={true}
              showStatus={false}
            >
              <div className="row justify-content-end">
                <div className="col-xs-12 col-sm-12 col-md-8">
                  <WorldMap countries={countryData[0]} />
                </div>

                {canRenderAssessmentStatistics && (
                  <div className="col-xs-12 col-sm-12 col-md-4">
                    <div className="carousel-caption text-left">
                      <ul className={classes.carousellist}>
                        <CarouselItem
                          className={`assessment-icon-2`}
                          title={t("Landing.Carousel.TotalInitiatedAssesments")}
                          count={`${
                            (inProgressSurveillanceAssessments || []).length ||
                            "0"
                          }
                                                    ${t("Common.Countries")}`}
                        />
                        <CarouselItem
                          className={`assessment-icon-3`}
                          title={t(
                            "Landing.Carousel.TotalNumberOfCountriesWithOneAssesment"
                          )}
                          count={`${
                            (completedSurveillanceAssessments || []).length ||
                            "0"
                          }
                                                         ${t(
                                                           "Common.Countries"
                                                         )}`}
                        />
                        <CarouselItem
                          className={`assessment-icon-4`}
                          title={t(
                            "Landing.Carousel.TotalNumberOfCountriesWithMoreThanOneAssesment"
                          )}
                          count={`${
                            (multipleCompletedSurveillanceAssessments || [])
                              .length || "0"
                          } ${t("Common.Countries")}`}
                        />
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              <div className="row justify-content-end">
                <div className="col-xs-12 col-sm-12 col-md-8">
                  <WorldMap countries={countryData[1]} />
                </div>
                {canRenderAssessmentStatistics && (
                  <div className="col-xs-12 col-sm-12 col-md-4">
                    <div className="carousel-caption text-left">
                      <ul className={classes.carousellist}>
                        <CarouselItem
                          className={`assessment-icon-2`}
                          title={t(
                            "Landing.Carousel.TotalCompletedRapidAssessments"
                          )}
                          count={`${
                            (rapidSurveillanceAssessments || []).length || "0"
                          }`}
                        />
                        <CarouselItem
                          className={`assessment-icon-3`}
                          title={t(
                            "Landing.Carousel.TotalCompletedTailoredAssesments"
                          )}
                          count={`${
                            (tailoredSurveillanceAssessments || []).length ||
                            "0"
                          }`}
                        />
                        <CarouselItem
                          className={`assessment-icon-4`}
                          title={t(
                            "Landing.Carousel.TotalCompletedComprehensiveAssesments"
                          )}
                          count={`${
                            (comprehensiveSurveillanceAssessments || [])
                              .length || "0"
                          }`}
                        />
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              <div className="row justify-content-end">
                <div className="col-xs-12 col-sm-12 col-md-8">
                  <WorldMap countries={countryData[2]} />
                </div>
                {canRenderAssessmentStatistics && (
                  <div className="col-xs-12 col-sm-12 col-md-4">
                    <div className="carousel-caption text-left">
                      <p className="my-3">
                        {t("Landing.Carousel.TotalYearWiseAssessments")}
                      </p>

                      <AutoComplete
                        id="years"
                        label={t("Landing.Carousel.SelectAssessmentsYear")}
                        values={selectedYear}
                        options={years.map((year) => {
                          return new MultiSelectModel(
                            year,
                            year.toString(),
                            false,
                            false
                          );
                        })}
                        placeholder={t("Common.SelectYear")}
                        onUpdate={(selectedItems: MultiSelectModel | null) =>
                          onYearChange(selectedItems)
                        }
                      />

                      <ul className={classes.carousellist}>
                        <CarouselItem
                          className={`assessment-icon-2`}
                          title={t("Landing.Carousel.TotalInitiatedAssesments")}
                          count={`${
                            (inProgressAssessments || []).length || "0"
                          }`}
                        />
                        <CarouselItem
                          className={`assessment-icon-4`}
                          title={t(
                            "Landing.Carousel.CompletedAssesmentCountries"
                          )}
                          count={`${
                            (completedAssessments || []).length || "0"
                          }`}
                        />
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </ReactCarousel>
          </div>
          <small className="d-flex mt-2 fst-italic">
            * {t("Common.GeographicRegionsNote")}
          </small>
        </div>
      </div>
    </>
  );
}

export default Carousel;
