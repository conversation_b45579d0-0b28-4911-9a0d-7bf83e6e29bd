﻿.carousellist {
    list-style-type: none;
    margin: 0;
    padding: 0;

    li {
        position: relative;
        padding: 20px 0px;
        display: flex;
        justify-content: normal;
        align-items: center;
        border-top: 1px solid #CCDBE8;

        &:first-child {
            border-top: none;
        }

        span {
            display: flex;
            color: #686868;
        }

        .carousel-list-content {
            padding-left: 20px;
        }
    }

    .carousel-list-icon {
        width: 60px;
        height: 60px;
    }

    .carousel-list-icon::before {
        content: '';
        position: absolute;
        background-repeat: no-repeat;
        background-position: 0px 0px;
        width: 60px;
        height: 60px;
        top: 30px;
        left: 0px;
        cursor: pointer;
    }

    .assessment-icon-1::before {
        background-image: url(../../images/AssessmentInitiated.svg);
    }

    .assessment-icon-2::before {
        background-image: url(../../images/AssessmentsInProgress.svg);
    }

    .assessment-icon-3::before {
        background-image: url(../../images/CompletedOneAssessment.svg);
    }

    .assessment-icon-4::before {
        background-image: url(../../images/CompletedMoreAssessments.svg);
    }
}

.carouselWrapper {
    .carousel-inner {
        position: relative;
        width: 100%;
        overflow: hidden;
    }
}

.assessment-icon-1::before {
    content: '';
    background-image: url(../../images/WorldMap.png);
    position: absolute;
    background-repeat: no-repeat;
    background-position: 0px 0px;
    width: 20px;
    height: 20px;
    top: 10px;
    left: 7px;
    cursor: pointer;
}
