import { BrowserRouter, Route, Routes } from "react-router-dom";
import { AnimatePresence } from "framer-motion";

import Header from "../../components/ui/Header";
import Landing from "../landing/Landing";
import { useTranslation } from "react-i18next";
import InvitationAccepted from "../../components/user/InvitationAccepted";
import UnRegisteredUser from "../common/UnRegisteredUser";

function PublicRoutes() {
  const { t } = useTranslation();
  document.title = t("app.LandingPageTitle");
  return (
    <BrowserRouter>
      <Header />
      <AnimatePresence>
        <main className='app-main'>
          <Routes>
            <Route
              path='/user/invitation/accept/:ucaId'
              element={<InvitationAccepted />}
            />
            <Route path='/unregistereduser' element={<UnRegisteredUser />} />
            <Route path='/' element={<Landing />} />
          </Routes>
        </main>
      </AnimatePresence>
    </BrowserRouter>
  );
}

export default PublicRoutes;
