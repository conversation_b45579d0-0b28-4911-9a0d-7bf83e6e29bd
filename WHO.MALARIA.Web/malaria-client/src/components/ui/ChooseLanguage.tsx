﻿import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
import cookies from "js-cookie";
import classNames from "classnames";
import { UtilityHelper } from "../../utils/UtilityHelper";
const languages = [
  {
    code: "fr",
    name: "Français",
    country_code: "fr",
  },
  {
    code: "en",
    name: "English",
    country_code: "gb",
  },
];

function ChooseLanguage() {
  const currentLanguageCode = cookies.get("i18next") || "en";
  const currentLanguage: any = languages.find(
    (l) => l.code === currentLanguageCode
  );
  const { t } = useTranslation(["translation"]);

  useEffect(() => {
    document.body.dir = currentLanguage.dir || "ltr";
  }, [currentLanguage, t]);

  return (
    <>
      <ul
        className="dropdown-menu dropdown-menu-right dropdown-menu-end"
        aria-labelledby="navbarDropdownMenuLink"
      >
        {languages.map(({ code, name, country_code }) => (
          <li key={country_code}>
            <a
              className={`text-dark ${classNames("dropdown-item", {
                disabled: currentLanguageCode === code,
              })}`}
              onClick={() => {
                i18next.changeLanguage(code);

                //Tracking change language event in analytics
                UtilityHelper.onEventAnalytics("Translation", "Choose language", "Language Change");
              }}
            >
              <span
                className={`flag-icon flag-icon-${country_code} mx-2`}
                style={{
                  opacity: currentLanguageCode === code ? 0.5 : 1,
                }}
              ></span>
              {name}
            </a>
          </li>
        ))}
      </ul>
    </>
  );
}

export default ChooseLanguage;
