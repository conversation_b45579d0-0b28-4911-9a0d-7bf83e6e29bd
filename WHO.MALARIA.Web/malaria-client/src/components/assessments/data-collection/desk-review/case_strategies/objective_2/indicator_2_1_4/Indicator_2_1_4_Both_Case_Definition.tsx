﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import React from "react";
import {
    CaseDefinition,
    Step_A_Response,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_1";
import { useLocation } from "react-router";
import { useSelector } from "react-redux";
import parse from "html-react-parser";

type Indicator_2_1_4_CaseDefinitionProps = {
    step_A: Step_A_Response;
    updateStep_A: (step_A: any) => void;
    caseDefinition: CaseDefinition;
    getMetNotMetStatus: () => void;
};

/** Renders the response for indicator 2.1.4 Case definition*/
function Indicator_2_1_4_Both_Case_Definition(
    props: Indicator_2_1_4_CaseDefinitionProps
) {
    const { t } = useTranslation(["indicators-responses"]);
    const { caseDefinition, updateStep_A, step_A, getMetNotMetStatus } = props;
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const errors = useSelector((state: any) => state.error);

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
    ];

    // Triggered whenever the textbox control values are changed and update response
    const onValueChange = (
        fieldName: string,
        value: string | boolean,
        modelKeyName: string
    ) => {
        const _response = {
            ...step_A,
            caseDefinition: {
                ...step_A.caseDefinition,
                [modelKeyName]: {
                    ...step_A.caseDefinition[modelKeyName],
                    [fieldName]: value,
                },
            },
        };

        updateStep_A(_response);
    };

    const headers = [
        { field: "", label: "" },
        {
            field: "WHODefinition",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHODefinition"
            ),
        },
        {
            field: "countryDefinition",
            label: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CountryDefinition"
            ),
        },
        {
            field: "definitionOK",
            label: parse(t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DefinitionOK"
            )),
        },
    ];

    const columns: any = {
        suspectedCase: {
            field: "suspectedCase",
            labelOne: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:SuspectedCase"
            ),
            labelTwo: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:SuspectedCaseLabelTwo"
            ),
        },
        presumedCase: {
            field: "presumedCase",
            labelOne: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:PresumedCase"
            ),
            labelTwo: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:PresumedCaseLabelTwo"
            ),
        },
        confirmedCase: {
            field: "confirmedCase ",
            labelOne: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ConfirmedCase"
            ),
            labelTwo: t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ConfirmedCaseLabelTwo"
            ),
        },
    }

    return (
        <>
            <div className="response-wrapper">
                <div>
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(caseDefinition)
                                        .filter((key) => !excludedProperties.includes(key))
                                        .map((modelKeyName: string, index: number) => (
                                            <TableRow key={`row_${columns.field}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <span>{columns[modelKeyName]?.labelOne}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <span>{columns[modelKeyName]?.labelTwo}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <TextBox
                                                            id={`${columns.field}_${index}}`}
                                                            name="countryDefinition"
                                                            rows={2}
                                                            multiline
                                                            fullWidth
                                                            value={
                                                                caseDefinition[modelKeyName]?.countryDefinition
                                                            }
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onValueChange(
                                                                    e.target.name,
                                                                    e.currentTarget.value,
                                                                    modelKeyName
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ] &&
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ]
                                                            }
                                                            helperText={
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ] &&
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="definitionOK"
                                                            name="definitionOK"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={caseDefinition[modelKeyName]?.definitionOK}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onValueChange(
                                                                    e.target.name,
                                                                    e.currentTarget.value === "false"
                                                                        ? false
                                                                        : true,
                                                                    modelKeyName
                                                                );

                                                                getMetNotMetStatus();
                                                            }}
                                                            error={
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ] &&
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ]
                                                            }
                                                            helperText={
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ] &&
                                                                errors[
                                                                `step_A.caseDefinition.${modelKeyName}.definitionOK`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                        </>
                    </Table>
                </div>
            </div>
        </>
    );
}

export default Indicator_2_1_4_Both_Case_Definition;