import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  integratedNationalElectronicMalariaDatabase: new ValidationRuleModel(DataType.Boolean, true),
  integratedNationalElectronicMalariaDatabaseDetails:new ValidationRuleModel(DataType.String, true)
 };

export default ValidationRules;
