import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { MonitoringFrequency } from "../../../../../../../models/Enums";
import { RoutineOutputProducedTab } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_4/Response_1";
import useCalculation from "../../../responses/useCalculation";
import { useSelector } from "react-redux";

type Indicator_1_3_4_Props = {
    routineOutputProducedTab: RoutineOutputProducedTab;
    updateTab: (tabKey: any, response: any) => void;
    getMetNotMetStatus: () => void;
};

/** Renders the 1st Tab of indicator 1.3.4 */
function RoutineOutputProduced(props: Indicator_1_3_4_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const errors = useSelector((state: any) => state.error);
    const { routineOutputProducedTab, updateTab, getMetNotMetStatus } = props;
    const { calculatePercentage } = useCalculation();
    const errorMessage = t(
        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ResponseError"
    );
    const errorMessagesForAnnualMaleria = t(
        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ResponseErrors"
    );

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "epidemicMonitoring_NationalLevel",
        "epidemicMonitoring_RegionalLevel",
        "epidemicMonitoring_DistrictLevel",
        "frequencyOfMonitoring",
        "denominatorTitle",
        "numeratorTitle",
    ];

    // Excluded property that are not used in Array Map
    const excludedPropertiesAllWeeks: Array<string> = [
        "nationalLevel",
        "regionalLevel",
        "districtLevel",
        "frequencyOfMonitoring",
        "denominatorTitle",
        "numeratorTitle",
    ];

    const rows: any = {
        nationalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:National"
        ),
        regionalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:Regional"
        ),
        districtLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:District"
        ),
        epidemicMonitoring_NationalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:National"
        ),
        epidemicMonitoring_RegionalLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:Regional"
        ),
        epidemicMonitoring_DistrictLevel: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:District"
        ),
    };

    // trigger whenever Value Change of control
    const onValueChange = (
        fieldName: string,
        value: number | string,
        modelKeyName: string
    ) => {
        const _response = {
            ...routineOutputProducedTab,
            [modelKeyName]: {
                ...routineOutputProducedTab[modelKeyName],
                [fieldName]: value ? Math.round(+value) : value === 0 ? 0 : null,
            },
        };
        updateTab("routineOutputProducedTab", _response);
    };

    //Triggered whenever the radio control values are changed and update the denominator value
    const onRadioClick = (value: string | boolean) => {
        let noOfWeeksOrMonths: number | null = null;
        let numeratorTitle: string = "Numerator";
        let denominatorTitle: string = "Denominator";
        if (value === MonitoringFrequency.Monthly) {
            noOfWeeksOrMonths = 12;
            numeratorTitle = t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyNumerator"
            );
            denominatorTitle = t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyDenominator"
            );
        } else if (value === MonitoringFrequency.Weekly) {
            noOfWeeksOrMonths = 52;
            numeratorTitle = t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:WeeklyNumerator"
            );
            denominatorTitle = t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:WeeklyDenominator"
            );
        }

        const _response = {
            ...routineOutputProducedTab,
            frequencyOfMonitoring: value,

            epidemicMonitoring_NationalLevel: {
                ...routineOutputProducedTab.epidemicMonitoring_NationalLevel,
                noOfWeeksOrMonths,
            },
            numeratorTitle,
            denominatorTitle,
        };
        updateTab("routineOutputProducedTab", _response);
    };

    return (
        <>
            <div className="row">
                <div className="col-xs-12 col-md-8">
                    <p className="fw-lighter">
                        {parse(
                            t(
                                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ResponseDesc"
                            )
                        )}
                    </p>
                    <p className="mt-3 fst-italic">
                        {t("indicators-responses:Common:NoteToCompleteTheAssessment")}
                    </p>
                </div>
            </div>
            {
                (errors["annualMalariaTab.isSurveillanceReportProducedInLast12Month"]) && (
                    <span className="Mui-error d-flex mb-2">*{errorMessagesForAnnualMaleria}</span>
                )
            }
            {
                // Show error message if data from at least one health system is not entered
                (errors["routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced"] ||
                    errors["routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced"] ||
                    errors["routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced"] ||
                    errors["routineOutputProducedTab.nationalLevel.noOfWeeksOrMonths"] ||
                    errors["routineOutputProducedTab.regionalLevel.noOfWeeksOrMonths"] ||
                    errors["routineOutputProducedTab.districtLevel.noOfWeeksOrMonths"]) && (
                    <span className="Mui-error d-flex mb-2">*{errorMessage} </span>
                )
            }
            <div className="mt-2">
                <Table width="100%" className="app-table">
                    <>
                        <TableHeader
                            headers={[
                                t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:HealthSystemLevel"
                                ),
                                t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyBulletins"
                                ),
                                t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:NoOfMonths"
                                ),
                                t("indicators-responses:Common:Rate"),
                            ]}
                        />
                        <TableBody>
                            <>
                                {Object.keys(routineOutputProducedTab)
                                    .filter((key) => !excludedProperties.includes(key))
                                    .map((modelKeyName: string, index: number) => (
                                        <TableRow key={`row_${modelKeyName}_${index}`}>
                                            <>
                                                <TableCell>{rows[modelKeyName]}</TableCell>
                                                <TableCell style={{ textAlign: "center" }}>
                                                    <TextBox
                                                        id="noOfMonthlyBulletinsProduced"
                                                        name="noOfMonthlyBulletinsProduced"
                                                        type="number"
                                                        inputProps={{
                                                            max: 12,
                                                            min: 0,
                                                            maxLength: 2,
                                                        }}
                                                        fullWidth
                                                        value={
                                                            routineOutputProducedTab[modelKeyName]
                                                                ?.noOfMonthlyBulletinsProduced
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onValueChange(
                                                                "noOfMonthlyBulletinsProduced",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            );
                                                            getMetNotMetStatus();
                                                        }}
                                                        error={
                                                            errors["routineOutputProducedTab[modelKeyName]?.noOfMonthlyBulletinsProduced"] &&
                                                            errors["routineOutputProducedTab[modelKeyName]?.noOfMonthlyBulletinsProduced"]
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="noOfWeeksOrMonths"
                                                        name="noOfWeeksOrMonths"
                                                        type="number"
                                                        inputProps={{
                                                            max: 12,
                                                            min: 0,
                                                            maxLength: 2,
                                                        }}
                                                        fullWidth
                                                        value={
                                                            routineOutputProducedTab[modelKeyName]
                                                                ?.noOfWeeksOrMonths
                                                        }
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onValueChange(
                                                                "noOfWeeksOrMonths",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            );
                                                            getMetNotMetStatus();
                                                        }}
                                                        error={
                                                            errors["routineOutputProducedTab[modelKeyName]?.noOfWeeksOrMonths"] &&
                                                            errors["routineOutputProducedTab[modelKeyName]?.noOfWeeksOrMonths"]
                                                        }

                                                        disabled={modelKeyName === 'nationalLevel' ? true : false}
                                                    />
                                                </TableCell>
                                                <TableCell className="ps-3">
                                                    <label>
                                                        {errors[
                                                            `routineOutputProducedTab.${modelKeyName}.proportionRate`
                                                        ] ? <span className="Mui-error d-flex">{errors[`routineOutputProducedTab.${modelKeyName}.proportionRate`]}</span>
                                                            : `${calculatePercentage(
                                                                routineOutputProducedTab[modelKeyName]
                                                                    ?.noOfMonthlyBulletinsProduced,
                                                                routineOutputProducedTab[modelKeyName]
                                                                    ?.noOfWeeksOrMonths
                                                            )}%`}
                                                    </label>
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>
            <div className="row mt-3">
                <div className="col-md-6 ">
                    <label>
                        {t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:FrequencyOfMonitoring"
                        )}
                    </label>
                </div>
                <div className="col-md-6 ">
                    <RadioButtonGroup
                        id="frequencyOfMonitoring"
                        name="frequencyOfMonitoring"
                        row
                        color="primary"
                        options={[
                            new MultiSelectModel(
                                "Monthly",
                                t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:Monthly"
                                )
                            ),
                            new MultiSelectModel(
                                "Weekly",
                                t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_3_4:Weekly"
                                )
                            ),
                        ]} 
                        value={routineOutputProducedTab?.frequencyOfMonitoring != null ?routineOutputProducedTab?.frequencyOfMonitoring : MonitoringFrequency.Monthly}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onRadioClick(
                                e.currentTarget.value === MonitoringFrequency.Weekly
                                    ? MonitoringFrequency.Weekly
                                    : MonitoringFrequency.Monthly
                            )
                        }
                        error={
                            errors["routineOutputProducedTab.frequencyOfMonitoring"] &&
                            errors["routineOutputProducedTab.frequencyOfMonitoring"]
                        }
                        helperText={
                            errors["routineOutputProducedTab.frequencyOfMonitoring"] &&
                            errors["routineOutputProducedTab.frequencyOfMonitoring"]
                        }

                    />
                </div>
            </div>
            {
                // Show error message if data from at least one health system is not entered
                (errors["routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph"] ||
                    errors["routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph"] ||
                    errors["routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph"] ||
                    errors["routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfWeeksOrMonths"] ||
                    errors["routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfWeeksOrMonths"] ||
                    errors["routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfWeeksOrMonths"]) && (
                    <span className="Mui-error d-flex mb-2">*{errorMessage}</span>
                )
            }
            <div className="mt-3">
                <Table width="100%" className="app-table">
                    <>             
                        <TableHeader

                            headers={[
                                t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:HealthSystemLevel"
                                ),
                              
                                (routineOutputProducedTab?.frequencyOfMonitoring != null ?routineOutputProducedTab?.frequencyOfMonitoring : MonitoringFrequency.Monthly) === MonitoringFrequency.Monthly ? t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyNumerator") :  t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:WeeklyNumerator"
                                    ),

                                    (routineOutputProducedTab?.frequencyOfMonitoring != null ?routineOutputProducedTab?.frequencyOfMonitoring : MonitoringFrequency.Monthly) === MonitoringFrequency.Monthly ? t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MonthlyDenominator") :  t(
                                            "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:WeeklyDenominator"
                                        ),
                               
                                t("indicators-responses:Common:Rate"),
                            ]}
                        />
                        <TableBody> 
                            <>
                                {Object.keys(routineOutputProducedTab)
                                    .filter((key) => !excludedPropertiesAllWeeks.includes(key))
                                    .map((modelKeyName: string, index: number) => (
                                        <TableRow key={`row_${modelKeyName}_${index}`}>
                                            <>
                                                <TableCell>{rows[modelKeyName]}</TableCell>
                                                <TableCell style={{ textAlign: "center" }}>
                                                    <TextBox
                                                        id="noOfMonthlyEpidemicMonitoringGraph"
                                                        name="noOfMonthlyEpidemicMonitoringGraph"
                                                        type="number"
                                                        value={
                                                            routineOutputProducedTab[modelKeyName]
                                                                ?.noOfMonthlyEpidemicMonitoringGraph
                                                        }
                                                        inputProps={{
                                                            max: routineOutputProducedTab[modelKeyName]
                                                                .noOfWeeksOrMonths,
                                                            min: 0,
                                                            maxLength: 2,
                                                        }}
                                                        fullWidth
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onValueChange(
                                                                "noOfMonthlyEpidemicMonitoringGraph",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            );
                                                            getMetNotMetStatus();
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id="noOfWeeksOrMonths"
                                                        name="noOfWeeksOrMonths"
                                                        type="number"
                                                        value={
                                                            routineOutputProducedTab[modelKeyName]
                                                                ?.noOfWeeksOrMonths
                                                        }
                                                        inputProps={{
                                                            max: routineOutputProducedTab[modelKeyName]
                                                                .noOfWeeksOrMonths,
                                                            min: 0,
                                                            maxLength: 2,
                                                        }}
                                                        fullWidth
                                                        onChange={(
                                                            e: React.ChangeEvent<HTMLInputElement>
                                                        ) => {
                                                            onValueChange(
                                                                "noOfWeeksOrMonths",
                                                                e.currentTarget.value,
                                                                modelKeyName
                                                            );
                                                            getMetNotMetStatus();
                                                        }}
                                                        disabled={modelKeyName === 'epidemicMonitoring_NationalLevel' ? true : false}
                                                    />
                                                </TableCell>
                                                <TableCell className="ps-3">
                                                    <label>
                                                        {errors[
                                                            `routineOutputProducedTab.${modelKeyName}.proportionRate`
                                                        ] ? <span className="Mui-error d-flex"> {errors[`routineOutputProducedTab.${modelKeyName}.proportionRate`]} </span>
                                                            : `${calculatePercentage(
                                                                routineOutputProducedTab[modelKeyName]
                                                                    ?.noOfMonthlyEpidemicMonitoringGraph,
                                                                routineOutputProducedTab[modelKeyName]
                                                                    ?.noOfWeeksOrMonths
                                                            )}%`}
                                                    </label>
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    ))}
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>
        </>
    );
}

export default RoutineOutputProduced;
