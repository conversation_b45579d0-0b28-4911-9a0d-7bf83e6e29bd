import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { KeyValuePair } from "../../../../../../../models/DeskReview/KeyValueType";
import { Step_A_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Dropdown from "../../../../../../controls/Dropdown";
import useYears from "../../../../useYears";
import IndicatorBarGraph from "../IndicatorBarGraph";
import IndicatorLineGraph from "../IndicatorLineGraph";
import parse from "html-react-parser";

interface IIndicator_1_1_1_Step_a {
    step_A: Step_A_Response;
    updateStep_A: (step_A: any) => void;
    section: number;
    graphTitle?: string;
}

/** Renders the response for indicator 1.1.1 Step A */
const Indicator_1_1_1_Step_a = (props: IIndicator_1_1_1_Step_a) => {
    const { t } = useTranslation(["indicators-responses"]);
    const years = useYears();
    const { step_A, updateStep_A, section, graphTitle } = props;
    const { section_1_1_1_a, section_1_1_1_b } = step_A;
    const errors = useSelector((state: any) => state.error);

    //Updates national level estiamtes
    const nationalEstimateChangeHandler = (
        nationalEstimates: Array<KeyValuePair<any, any>>
    ) => {
        let values;
        switch (section) {
            case 1:
                //Convert key and values in number format as to align with model
                values = nationalEstimates.map((value) => ({
                    key: value.key ? +value.key : null,
                    value: value.value ? Math.round(+value.value) : value.value === 0 ? 0 : null
                }));

                updateStep_A({
                    ...step_A,
                    section_1_1_1_a: {
                        values,
                        reasonForChangeObservedOvertime:
                            nationalEstimates.length > 1
                                ? section_1_1_1_a.reasonForChangeObservedOvertime
                                : "",
                    },
                });
                break;
            case 2:
                //Convert values in number format as to align with model
                values = nationalEstimates.map((value) => ({
                    key: value.key,
                    value: value.value ? Math.round(+value.value) : value.value === 0 ? 0 : null
                }));

                updateStep_A({
                    ...step_A,
                    section_1_1_1_b: {
                        ...section_1_1_1_b,
                        values,
                        reasonForChangeObservedOvertime:
                            nationalEstimates.length > 1
                                ? section_1_1_1_b.reasonForChangeObservedByRegion
                                : "",
                    },
                });
                break;
        }
    };

    //Update reason for changes observed overtime
    const reasonForChangeObservedOvertimeHandler = (
        reasonForChangeObserved: string
    ) => {
        switch (section) {
            case 1:
                updateStep_A({
                    ...step_A,
                    section_1_1_1_a: {
                        ...section_1_1_1_a,
                        reasonForChangeObservedOvertime: reasonForChangeObserved,
                    },
                });
                break;
            case 2:
                updateStep_A({
                    ...step_A,
                    section_1_1_1_b: {
                        ...section_1_1_1_b,
                        reasonForChangeObservedByRegion: reasonForChangeObserved,
                    },
                });
                break;
        }
    };

    //Update selected year for the national level estimate
    const yearOfDataChangeHandler = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        updateStep_A({
            ...step_A,
            section_1_1_1_b: {
                ...section_1_1_1_b,
                yearOfData: e.target.value,
            },
        });
    };

    switch (section) {
        case 1:
            return (
                <IndicatorLineGraph
                    lineGraphXAxisText={t("indicators-responses:Common:Year")}
                    lineGraphYAxisText={t(
                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:NationalLevelEstimate"
                    )}
                    description={
                        <>
                            <p>
                                {t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CompleteAssessmentDesc"
                                )}
                            </p>
                            <p>
                                {parse(t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ResponseDesc"
                                ))}
                            </p>
                            <p className="fw-italic">
                                {t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:MinimumOnePointRequired"
                                )}
                            </p>
                            {/*Show error message if not all radio buttons are selected for Data quality control check in place*/}
                            {!!Object.keys(errors).length && (
                                <span className="Mui-error d-flex mb-2">
                                    *
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_2:ResponseError"
                                    )}
                                </span>
                            )}
                        </>
                    }
                    renderAddRemoveButton={true}
                    renderTextbox={true}
                    keyValuePairs={section_1_1_1_a.values}
                    onUpdatekeyValuePairs={nationalEstimateChangeHandler}
                    details={section_1_1_1_a.reasonForChangeObservedOvertime}
                    onDetailChange={reasonForChangeObservedOvertimeHandler}
                    showGraphOnLoad={section_1_1_1_a.values.length > 0}
                    graphTitle={graphTitle}
                    keyValuePairsErrors={
                        errors["step_A.section_1_1_1_a.values"] ||
                        errors["step_A.section_1_1_1_a.values.key"] ||
                        errors["step_A.section_1_1_1_a.values.value"]
                    }
                    errorInDetails={
                        errors["step_A.section_1_1_1_a.reasonForChangeObservedOvertime"]
                    }
                />
            );
        case 2:
            return (
                <>
                    <p>{t("indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:YearOfDataHeading")}</p>
                    <div className="col-xs-12 col-md-4">
                        <div className="row mb-2">
                            <div className="col-xs-12 col-md-6">
                                <Dropdown
                                    id="yearOfData"
                                    name="yearOfData"
                                    variant="outlined"
                                    size="small"
                                    label={t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:YearOfData"
                                    )}
                                    value={section_1_1_1_b.yearOfData}
                                    options={years.map((year) => {
                                        return new MultiSelectModel(
                                            year,
                                            year.toString(),
                                            false,
                                            false
                                        );
                                    })}
                                    onChange={(
                                        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
                                    ) => yearOfDataChangeHandler(e)}
                                />
                            </div>
                        </div>
                    </div>
                    <IndicatorBarGraph
                        barGraphXAxisText={t(
                            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Region"
                        )}
                        barGraphYAxisText={"%"}
                        description={
                            <>
                                <p>
                                    {t(
                                        "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ResponseDescB"
                                    )}
                                </p>
                                <p className="fw-italic"></p>
                            </>
                        }
                        renderTextbox={true}
                        keyValuePairs={section_1_1_1_b.values}
                        onUpdatekeyValuePairs={nationalEstimateChangeHandler}
                        details={section_1_1_1_b.reasonForChangeObservedByRegion}
                        onDetailChange={reasonForChangeObservedOvertimeHandler}
                        showGraphOnLoad={section_1_1_1_b.values.length > 0}
                        graphTitle={graphTitle}
                        keyValuePairsErrors={errors["step_A.section_1_1_1_b.values"]}
                        errorInDetails={
                            errors["step_A.section_1_1_1_b.reasonForChangeObservedByRegion"]
                        }
                    />
                </>
            );
        default:
            return <></>;
    }
};

export default Indicator_1_1_1_Step_a;
