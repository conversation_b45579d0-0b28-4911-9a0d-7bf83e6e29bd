import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  uniqueIdentifierExists: new ValidationRuleModel(DataType.Boolean, true),
  uniqueIdentifierDetails:new ValidationRuleModel(DataType.String, true),
  commonUniqueIdentifierExists: new ValidationRuleModel(DataType.Boolean, true),
  commonUniqueIdentifierDetails:new ValidationRuleModel(DataType.String, true)
 };

export default ValidationRules;
