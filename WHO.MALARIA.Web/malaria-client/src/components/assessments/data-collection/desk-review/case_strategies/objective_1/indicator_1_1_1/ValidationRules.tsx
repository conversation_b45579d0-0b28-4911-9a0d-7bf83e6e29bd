import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";
const currentYear = new Date().getFullYear();

const ValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, false),
  "step_A.section_1_1_1_a": new ValidationRuleModel(DataType.Object, false),
  "step_A.section_1_1_1_a.values": new ValidationRuleModel(
    DataType.ArrayOfKeyValuePair,
    false
  ),
  "step_A.section_1_1_1_a.values.key": new ValidationRuleModel(
    DataType.Number,
    false,
    `(${Constants.Common.KeySubstitute} && !(${Constants.Common.KeySubstitute} >=2010 && ${Constants.Common.KeySubstitute} <=${currentYear}))`,
    "Errors.YearBetween2010ToCurrentYear"
  ),
  "step_A.section_1_1_1_a.values.value": new ValidationRuleModel(
    DataType.Number,
    true,
    `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100))`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  "step_A.section_1_1_1_a.reasonForChangeObservedOvertime":
    new ValidationRuleModel(
      DataType.String,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.step_A.section_1_1_1_a.values.length > 1 && !${Constants.Common.RootObjectNameSubstitute}.step_A.section_1_1_1_a.reasonForChangeObservedOvertime`
    ),
  "step_A.section_1_1_1_b": new ValidationRuleModel(DataType.Object, false),
  "step_A.section_1_1_1_b.values": new ValidationRuleModel(
    DataType.ArrayOfKeyValuePair,
    false
  ),
  "step_A.section_1_1_1_b.values.key": new ValidationRuleModel(
    DataType.String,
    true,
  ),

  "step_A.section_1_1_1_b.values.value": new ValidationRuleModel(
    DataType.Number,
    false,
    `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100))`,
    "Errors.ValueBetweenZeroToHundred"
  ),

  "step_A.section_1_1_1_b.reasonForChangeObservedByRegion":
    new ValidationRuleModel(
      DataType.String,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.step_A.section_1_1_1_b.values.length > 1 && !${Constants.Common.RootObjectNameSubstitute}.step_A.section_1_1_1_b.reasonForChangeObservedByRegion`
    ),

  step_B: new ValidationRuleModel(DataType.ArrayOfObject, false),
  "step_B.services": new ValidationRuleModel(
    DataType.ArrayOfObject,
    true,
    `const servicesForData=Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["step_A","step_C","step_D","step_E"].includes(key));
    servicesForData.every(data =>  ${Constants.Common.RootObjectNameSubstitute}[data].services === null)`
  ),
  "step_B.differByAge": new ValidationRuleModel(DataType.Boolean, false),
  "step_B.details": new ValidationRuleModel(
    DataType.String,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.step_B.differByAge === true  && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.details)`
  ),

  step_C: new ValidationRuleModel(DataType.Object, false),
  "step_C.groupsNotCapturedByNSS": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_C.details": new ValidationRuleModel(
    DataType.String,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.step_C.groupsNotCapturedByNSS === true  && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_C.details)`
  ),

  step_D: new ValidationRuleModel(DataType.Object, false),
  "step_D.newStrategiesUsed": new ValidationRuleModel(DataType.Boolean, true),
  "step_D.details": new ValidationRuleModel(
    DataType.String,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.step_D.newStrategiesUsed === true  && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_D.details)`
  ),

  step_E: new ValidationRuleModel(DataType.Object, false),
  "step_E.otherDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.step_E.treatmentOptions.includes('other') && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_E.otherDetails)`
  ),
  "step_E.treatmentOptions": new ValidationRuleModel(
    DataType.ArrayOfObject,
    true,
    `const treatmentOptionsForData=Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["step_A","step_C","step_D","step_B"].includes(key));
    treatmentOptionsForData.every(data =>  ${Constants.Common.RootObjectNameSubstitute}[data].treatmentOptions === null)`
  ),
};

export default ValidationRules;
