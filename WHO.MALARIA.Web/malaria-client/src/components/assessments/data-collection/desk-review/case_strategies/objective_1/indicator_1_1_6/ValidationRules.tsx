import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    nationalData: new ValidationRuleModel(
        DataType.Number,
        true,
        `${Constants.Common.RootObjectNameSubstitute}.nationalData !==null && !(${Constants.Common.RootObjectNameSubstitute}.nationalData >=0 && ${Constants.Common.RootObjectNameSubstitute}.nationalData <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),

    publicData: new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.publicData !==null && !(${Constants.Common.RootObjectNameSubstitute}.publicData >=0 && ${Constants.Common.RootObjectNameSubstitute}.publicData <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),

    privateData: new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.privateData !==null && !(${Constants.Common.RootObjectNameSubstitute}.privateData >=0 && ${Constants.Common.RootObjectNameSubstitute}.privateData <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),
    yearOfData: new ValidationRuleModel(
        DataType.Number,
        true,
    ),
    graph: new ValidationRuleModel(
        DataType.Object,
        true
    ),
    "graph.values": new ValidationRuleModel(
        DataType.ArrayOfKeyValuePair,
        true
    ),
    "graph.values.key": new ValidationRuleModel(
        DataType.String,
        true
    ),
    "graph.values.value": new ValidationRuleModel(
        DataType.Number,
       false,
        `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100 ))`,
        "Errors.ValueBetweenZeroToHundred"
    ),
};

export default ValidationRules;
