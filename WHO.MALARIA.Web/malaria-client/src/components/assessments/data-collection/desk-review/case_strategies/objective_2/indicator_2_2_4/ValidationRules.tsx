import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
   masterHealthFacilityListExists: new ValidationRuleModel(DataType.Boolean, true),
  masterHealthFacilityIdentifierDetails:new ValidationRuleModel(DataType.String, true),
  masterHealthFacilityExists: new ValidationRuleModel(DataType.Boolean, true),
  masterHealthFacilityDetails:new ValidationRuleModel(DataType.String, true)
 };

export default ValidationRules;
