﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
    standardizedRecordingTools: new ValidationRuleModel(
        DataType.ArrayOfObject,
        true
    ),
    [`standardizedRecordingTools[${Constants.Common.IndexSubstitute}].areStandardizedAcrossAllService`]:
        new ValidationRuleModel(
            DataType.Boolean,
            true,
            `${Constants.Common.RootObjectNameSubstitute}.parentData.recordingTools.length > ${Constants.Common.RootObjectNameSubstitute}.standardizedRecordingTools.length`
        ),
    standardizedFormsDetails: new ValidationRuleModel(
        DataType.ArrayOfObject,
        true
    ),
    [`standardizedFormsDetails[${Constants.Common.IndexSubstitute}].details`]:
        new ValidationRuleModel(
            DataType.String,
            true,
            `${Constants.Common.RootObjectNameSubstitute}.parentData.recordingTools.length > ${Constants.Common.RootObjectNameSubstitute}.standardizedRecordingTools.length`
        ),
};

export const EliminationValidationRules: IValidationRuleProvider = {
    "recordingTool1": new ValidationRuleModel(DataType.Object, false),

    [`recordingTool1.sourceDocumentName`]:
        new ValidationRuleModel(DataType.String, false,
            `const evidencesForData= Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["cannotBeAssessed","cannotBeAssessedReason","standardizedRecordingTools","standardizedFormsDetails", "metNotMetStatus", "parentRecordingToolCount", "parentData"].includes(key));
    evidencesForData.every(data =>  ${Constants.Common.RootObjectNameSubstitute}[data].sourceDocumentName === null) && ${Constants.Common.RootObjectNameSubstitute}.parentRecordingToolCount === 0`),

    [`recordingTool1.areStandardizedAcrossAllService`]:
        new ValidationRuleModel(DataType.Boolean, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool1.sourceDocumentName !== null) && (${Constants.Common.RootObjectNameSubstitute}.recordingTool1.areStandardizedAcrossAllService === null)`),

    [`recordingTool1.detail`]: new ValidationRuleModel(DataType.String, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool1.sourceDocumentName !== null) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTool1.detail)`),

    "recordingTool2": new ValidationRuleModel(DataType.Object, false),

    [`recordingTool2.areStandardizedAcrossAllService`]:
        new ValidationRuleModel(DataType.Boolean, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool2.sourceDocumentName !== null) && (${Constants.Common.RootObjectNameSubstitute}.recordingTool2.areStandardizedAcrossAllService === null)`),

    [`recordingTool2.detail`]: new ValidationRuleModel(DataType.String, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool2.sourceDocumentName !== null) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTool2.detail)`),

    "recordingTool3": new ValidationRuleModel(DataType.Object, false),

    [`recordingTool3.areStandardizedAcrossAllService`]:
        new ValidationRuleModel(DataType.Boolean, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool3.sourceDocumentName !== null) && (${Constants.Common.RootObjectNameSubstitute}.recordingTool3.areStandardizedAcrossAllService === null)`),

    [`recordingTool3.detail`]: new ValidationRuleModel(DataType.String, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool3.sourceDocumentName !== null) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTool3.detail)`),

    "recordingTool4": new ValidationRuleModel(DataType.Object, false),

    [`recordingTool4.areStandardizedAcrossAllService`]:
        new ValidationRuleModel(DataType.Boolean, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool4.sourceDocumentName !== null) && (${Constants.Common.RootObjectNameSubstitute}.recordingTool4.areStandardizedAcrossAllService === null)`),

    [`recordingTool4.detail`]: new ValidationRuleModel(DataType.String, false, `(${Constants.Common.RootObjectNameSubstitute}.recordingTool4.sourceDocumentName !== null) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTool4.detail)`),
}