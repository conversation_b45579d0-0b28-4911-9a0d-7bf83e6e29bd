﻿import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    dataQualityAssuranceProcedureInPlace: new ValidationRuleModel(DataType.Object, true),
    "dataQualityAssuranceProcedureInPlace.dataCleaning": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityAssuranceProcedureInPlace.dataReviewMeetings": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityAssuranceProcedureInPlace.dataQualityAssessments": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityAssuranceProcedureInPlace.dataQualityIndicators": new ValidationRuleModel(DataType.Boolean, true),

    dataValidated: new ValidationRuleModel(DataType.Object, true),
    "dataValidated.dataCleaning": new ValidationRuleModel(DataType.String, true,),
    "dataValidated.dataReviewMeetings": new ValidationRuleModel(DataType.String, true,),
    "dataValidated.dataQualityAssessments": new ValidationRuleModel(DataType.String, true,),
    "dataValidated.dataQualityIndicators": new ValidationRuleModel(DataType.String, true,),

    toolsAndMethods: new ValidationRuleModel(DataType.Object, true),
    "toolsAndMethods.dataCleaning": new ValidationRuleModel(DataType.String, true,),
    "toolsAndMethods.dataReviewMeetings": new ValidationRuleModel(DataType.String, true,),
    "toolsAndMethods.dataQualityAssessments": new ValidationRuleModel(DataType.String, true,),
    "toolsAndMethods.dataQualityIndicators": new ValidationRuleModel(DataType.String, true,),

    validationLevel: new ValidationRuleModel(DataType.Object, true),
    "validationLevel.dataCleaning": new ValidationRuleModel(DataType.String, true,),
    "validationLevel.dataReviewMeetings": new ValidationRuleModel(DataType.String, true,),
    "validationLevel.dataQualityAssessments": new ValidationRuleModel(DataType.String, true,),
    "validationLevel.dataQualityIndicators": new ValidationRuleModel(DataType.String, true,),

    personResponsible: new ValidationRuleModel(DataType.Object, true),
    "personResponsible.dataCleaning": new ValidationRuleModel(DataType.String, true,),
    "personResponsible.dataReviewMeetings": new ValidationRuleModel(DataType.String, true,),
    "personResponsible.dataQualityAssessments": new ValidationRuleModel(DataType.String, true,),
    "personResponsible.dataQualityIndicators": new ValidationRuleModel(DataType.String, true,),

    followUpAction: new ValidationRuleModel(DataType.Object, true),
    "followUpAction.dataCleaning": new ValidationRuleModel(DataType.String, true,),
    "followUpAction.dataReviewMeetings": new ValidationRuleModel(DataType.String, true,),
    "followUpAction.dataQualityAssessments": new ValidationRuleModel(DataType.String, true,),
    "followUpAction.dataQualityIndicators": new ValidationRuleModel(DataType.String, true,),

}

export default ValidationRules;