import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  isMalariaMandatorily:new ValidationRuleModel(
    DataType.Boolean,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.isMalariaMandatorily === null`,
    "Select Yes or No to assess this indicator"
  )
};

export default ValidationRules;