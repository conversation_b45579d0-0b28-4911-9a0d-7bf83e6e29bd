import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import { TabModel } from "../../../../../../../models/TabModel";
import Checkbox from "../../../../../../controls/Checkbox";
import WHOTabs from "../../../../../../controls/WHOTabs";
import RoutineOutputProduced from "./RoutineOutputProduced";
import AnnualMalariaSuveillance from "./AnnualMalariaSuveillance";
import OtherAnalyticalOutput from "./OtherAnalyticalOutput";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import { useDispatch } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_3_4/Response_1";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import useYears from "../../../../../data-collection/useYears";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import useCalculation from "../../../responses/useCalculation";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Response for indicator 1.3.4 */
function Indicator_1_3_4_ResponseContainer() {
    const { t } = useTranslation(["indicators-responses"]);
    const { calculatePercentage } = useCalculation();
    document.title = t(
        "indicators-responses:app:DR_Objective_1_Indicator_1_3_4_Title"
    );

    const dispatch = useDispatch();
    const [currentTab, setCurrentTab] = useState<number>(0);
    const years = useYears();
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        getResponse,
        updateTab,
        onSave,
        onChange,
        onFinalize,
        onCannotBeAssessed,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCaptureForTabs<Response_1>(
        Response_1.init(),
        validate
    );

    const errors = useSelector((state: any) => state.error);

    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    //  This gets called only first time when component renders
    useEffect(() => {
        getResponse();
        dispatch(updateStepIndex(0));
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            (response?.cannotBeAssessed === true)
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const monthlyBulletinsProportions = calculatePercentage(
            response.routineOutputProducedTab.nationalLevel
                ?.noOfMonthlyBulletinsProduced,
            response.routineOutputProducedTab.nationalLevel?.noOfWeeksOrMonths
        );
        const epidemicGraphProportion = calculatePercentage(
            response?.routineOutputProducedTab.epidemicMonitoring_NationalLevel
                ?.noOfMonthlyEpidemicMonitoringGraph,
            response?.routineOutputProducedTab.epidemicMonitoring_NationalLevel
                ?.noOfWeeksOrMonths
        );

        const isSurveillanceReportProduced =
            response.annualMalariaTab.isSurveillanceReportProducedInLast12Month;

        onValueChange(
            "metNotMetStatus",
            response?.dataAreNotUsed === true ?
                MetNotMetEnum.NotMet :
                monthlyBulletinsProportions === 100 &&
                    epidemicGraphProportion === 100 &&
                    isSurveillanceReportProduced === true
                    ? MetNotMetEnum.Met
                    : monthlyBulletinsProportions < 50 &&
                        epidemicGraphProportion < 50 &&
                        isSurveillanceReportProduced === false || isSurveillanceReportProduced === null
                        ? MetNotMetEnum.NotMet
                        : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response.routineOutputProducedTab.nationalLevel?.noOfMonthlyBulletinsProduced,
        response.routineOutputProducedTab.nationalLevel?.noOfWeeksOrMonths,
        response.routineOutputProducedTab.nationalLevel?.percentageRate,
        response.routineOutputProducedTab.epidemicMonitoring_NationalLevel?.noOfMonthlyEpidemicMonitoringGraph,
        response.routineOutputProducedTab.epidemicMonitoring_NationalLevel?.noOfWeeksOrMonths,
        response.routineOutputProducedTab.epidemicMonitoring_NationalLevel?.percentageRate,
        response.annualMalariaTab.isSurveillanceReportProducedInLast12Month,
        response?.dataAreNotUsed
    ]);

    // Tab Creation Model
    const tabs: Array<TabModel> = [
        new TabModel(
            1,
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:RoutineOutputsProduced"
            ),
            (
                <RoutineOutputProduced
                    routineOutputProducedTab={response.routineOutputProducedTab}
                    updateTab={updateTab}
                    getMetNotMetStatus={getMetNotMetStatus}
                />
            )
        ),
        new TabModel(
            2,
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:AnnualSurveillanceReport"
            ),
            (
                <AnnualMalariaSuveillance
                    response={response}
                    updateTab={updateTab}
                    getMetNotMetStatus={getMetNotMetStatus}
                />
            )
        ),
        new TabModel(
            3,
            t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:OtherAnalyticalOutputs"
            ),
            (
                <OtherAnalyticalOutput
                    response={response}
                    updateTab={updateTab}
                    years={years}
                />
            )
        ),
    ];

    // triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
        dispatch(updateStepIndex(newValue));
    };
    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                {!response?.dataAreNotUsed &&
                    <Checkbox
                        id="cannotBeAssessed"
                        name="cannotBeAssessed"
                        label={t("indicators-responses:Common:IndicatorNoAssess")}
                        onChange={onCannotBeAssessedChange}
                        checked={response?.cannotBeAssessed}
                    />
                }
                {!response?.cannotBeAssessed && (
                    <Checkbox
                        id="dataAreNotUsed"
                        name="dataAreNotUsed"
                        label={t("indicators-responses:Common:DataAreNotUsed")}
                        onChange={onChange}
                        checked={response?.dataAreNotUsed}
                    />
                )}
            </div>

            {!(response?.cannotBeAssessed || response?.dataAreNotUsed) ? (
                <div className="response-wrapper">
                    <div className="mt-3">
                        <div className="app-tab-wrapper">
                            <WHOTabs
                                tabs={tabs}
                                value={currentTab}
                                onChange={onTabChange}
                                scrollable={false}
                            >
                                <div className="p-3">{tabs[currentTab].children}</div>
                            </WHOTabs>
                        </div>
                    </div>
                </div>
            ) : (
                <>
                    {response?.cannotBeAssessed ?
                        (
                            <div className="response-wrapper d-flex">
                                <TextBox
                                    id="cannotBeAssessedReason"
                                    name="cannotBeAssessedReason"
                                    label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                                    multiline
                                    rows={10}
                                    variant="outlined"
                                    fullWidth
                                    value={response?.cannotBeAssessedReason || ""}
                                    onChange={onChange}
                                    error={
                                        errors["cannotBeAssessedReason"] &&
                                        errors["cannotBeAssessedReason"]
                                    }
                                    helperText={
                                        errors["cannotBeAssessedReason"] &&
                                        errors["cannotBeAssessedReason"]
                                    }
                                />
                            </div>

                        ) : (
                            <>

                            </>

                        )}
                </>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_3_4_ResponseContainer;
