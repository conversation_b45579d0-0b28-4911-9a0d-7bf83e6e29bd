﻿import React from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import { useSelector } from "react-redux";

type Indicator_1_1_7_Props = {
  step_B: Step_B_Response;
  updateStep_B: (step_B: any) => void;
};

/** Renders the indicator 1.1.7 step B response for desk review */
const Indicator_1_1_7_Step_B = (props: Indicator_1_1_7_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { step_B, updateStep_B } = props;
  const errors = useSelector((state: any) => state.error);

  // Triggers when input control's value changes
  const onValueChange = (fieldName: string, value: any) => {
    const _response = {
      ...step_B,
      [fieldName]: value,
    };

    updateStep_B(_response);
  };

  return (
    <>
      <div className="response-wrapper">
        <div className="response-content">
          <div className="row mb-3">
            <div className="col-xs-12 col-md-6">
              <div className="row mb-3">
                <div className="col-xs-12 col-md-12">
                  <label>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DeathsSystematicallyRecorded"
                    )}
                  </label>
                  <RadioButtonGroup
                    id="isDeathRecorded"
                    name="isDeathRecorded"
                    row
                    color="primary"
                    options={[
                      new MultiSelectModel(
                        "H",
                        t("indicators-responses:Common:Hospital")
                      ),
                      new MultiSelectModel(
                        "C",
                        t("indicators-responses:Common:Community")
                      ),
                      new MultiSelectModel(
                        "B",
                        t("indicators-responses:Common:Both")
                      ),
                    ]}
                    value={step_B?.isDeathRecorded}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange("isDeathRecorded", e.currentTarget.value)
                    }
                    error={
                      errors["step_B.isDeathRecorded"] &&
                      errors["step_B.isDeathRecorded"]
                    }
                    helperText={
                      errors["step_B.isDeathRecorded"] &&
                      errors["step_B.isDeathRecorded"]
                    }
                  />
                </div>
              </div>
              <div className="row">
                <div className="col-xs-12 col-md-12">
                  <TextBox
                    id="deathRecordedDetail"
                    name="deathRecordedDetail"
                    label={t("indicators-responses:Common:ProvideDetails")}
                    multiline
                    rows={10}
                    variant="outlined"
                    fullWidth
                    value={step_B?.deathRecordedDetail || ""}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange(
                        "deathRecordedDetail",
                        e.currentTarget.value
                      )
                    }
                    error={
                      errors["step_B.deathRecordedDetail"] &&
                      errors["step_B.deathRecordedDetail"]
                    }
                    helperText={
                      errors["step_B.deathRecordedDetail"] &&
                      errors["step_B.deathRecordedDetail"]
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Indicator_1_1_7_Step_B;
