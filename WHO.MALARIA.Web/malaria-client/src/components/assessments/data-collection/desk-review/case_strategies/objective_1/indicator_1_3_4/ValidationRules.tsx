import { Constants } from "../../../../../../../models/Constants";
import { DataType, MonitoringFrequency } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    dataAreNotUsed: new ValidationRuleModel(DataType.Boolean, false),
    routineOutputProducedTab: new ValidationRuleModel(DataType.Object, true),
    "routineOutputProducedTab.nationalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.regionalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.districtLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true ? (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced !==null) && !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced >=0) ||
        ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced === null && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced === null && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced === null : null`,
            "Errors.ValueBetweenOneToTwelveDesc"
        ),

    "routineOutputProducedTab.nationalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true  && (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced !==null) && !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced >=0)`,
            "Errors.ValueBetweenOneToTwelveDesc"),

    "routineOutputProducedTab.regionalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced !==null) && !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced >=0)`,
            "Errors.ValueBetweenOneToTwelveDesc"),

    "routineOutputProducedTab.districtLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.nationalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.nationalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "routineOutputProducedTab.regionalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.regionalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "routineOutputProducedTab.districtLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.districtLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "routineOutputProducedTab.frequencyOfMonitoring": new ValidationRuleModel(DataType.Boolean, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.frequencyOfMonitoring === null`),

    "routineOutputProducedTab.epidemicMonitoring_NationalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.epidemicMonitoring_RegionalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.epidemicMonitoring_DistrictLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true ?  (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph !==null) && (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.frequencyOfMonitoring === "${MonitoringFrequency.Monthly}" ? !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph >=0) : !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph >=0)) ||${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph === null && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph === null && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph === null : null`,
            "Errors.ValueBetweenDenominatorRange"
        ),

    "routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph !==null) && ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.frequencyOfMonitoring === "${MonitoringFrequency.Monthly}" ? !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph >=0) : !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph >=0)`,
            "Errors.ValueBetweenDenominatorRange"
        ),

    "routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && (${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph !==null) &&${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.frequencyOfMonitoring === "${MonitoringFrequency.Monthly}" ? !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph >=0) : !(${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph >=0)`,
            "Errors.ValueBetweenDenominatorRange"
        ),

    "routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false,
        ),

    "routineOutputProducedTab.epidemicMonitoring_DistrictLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfWeeksOrMonths)*100) > 100 || ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_DistrictLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "routineOutputProducedTab.epidemicMonitoring_RegionalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfWeeksOrMonths)*100) > 100 || ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_RegionalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "routineOutputProducedTab.epidemicMonitoring_NationalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfWeeksOrMonths)*100) > 100 || ((${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfMonthlyEpidemicMonitoringGraph / ${Constants.Common.RootObjectNameSubstitute}.routineOutputProducedTab.epidemicMonitoring_NationalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    annualMalariaTab: new ValidationRuleModel(DataType.Object, false),
    "annualMalariaTab.isSurveillanceReportProducedInLast12Month":
        new ValidationRuleModel(DataType.Boolean, false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ${Constants.Common.RootObjectNameSubstitute}.annualMalariaTab.isSurveillanceReportProducedInLast12Month === null`
        ),

    "annualMalariaTab.links": new ValidationRuleModel(
        DataType.String,
        false
    ),

    otherAnalyticalTab: new ValidationRuleModel(DataType.Object, false),
    "otherAnalyticalTab.isMapOfCountryWithStratification": new ValidationRuleModel(
        DataType.Boolean,
        false
    ),
    "otherAnalyticalTab.specifyLastYear": new ValidationRuleModel(
        DataType.Number,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && ${Constants.Common.RootObjectNameSubstitute}.otherAnalyticalTab.specifyLastYear=== null && (${Constants.Common.RootObjectNameSubstitute}.otherAnalyticalTab.isMapOfCountryWithStratification !==null && ${Constants.Common.RootObjectNameSubstitute}.otherAnalyticalTab.isMapOfCountryWithStratification === true)`
    ),
    "otherAnalyticalTab.isHighRiskPopulation": new ValidationRuleModel(
        DataType.Boolean,
        false
    ),

    "otherAnalyticalTab.highRiskPopulationDetail": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.otherAnalyticalTab.highRiskPopulationDetail) && ${Constants.Common.RootObjectNameSubstitute}.otherAnalyticalTab.isHighRiskPopulation`,
        "Errors.MandatoryField"
    ),
};

export default ValidationRules;
