﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {   
    step_B: new ValidationRuleModel(DataType.Object, false),
    "step_B.chemoPreventionInPregnantWomen": new ValidationRuleModel(DataType.Object, false),
    "step_B.chemoPreventionInPregnantWomen.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.chemoPreventionInPregnantWomen.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.chemoPreventionInInfancy": new ValidationRuleModel(DataType.Object, false),
    "step_B.chemoPreventionInInfancy.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.chemoPreventionInInfancy.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.chemoPreventionSMC": new ValidationRuleModel(DataType.Object, false),
    "step_B.chemoPreventionSMC.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.chemoPreventionSMC.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.chemoPreventionMDA": new ValidationRuleModel(DataType.Object, false),
    "step_B.chemoPreventionMDA.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.chemoPreventionMDA.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.vectorControlRoutineChannel": new ValidationRuleModel(DataType.Object, false),
    "step_B.vectorControlRoutineChannel.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.vectorControlRoutineChannel.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.vectorControlMassCampaign": new ValidationRuleModel(DataType.Object, false),
    "step_B.vectorControlMassCampaign.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.vectorControlMassCampaign.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.vectorControlIRS": new ValidationRuleModel(DataType.Object, false),
    "step_B.vectorControlIRS.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.vectorControlIRS.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.vectorControlLSM": new ValidationRuleModel(DataType.Object, false),
    "step_B.vectorControlLSM.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.vectorControlLSM.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.drugEfficacy": new ValidationRuleModel(DataType.Object, false),
    "step_B.drugEfficacy.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.drugEfficacy.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.genomicSurveillance": new ValidationRuleModel(DataType.Object, false),
    "step_B.genomicSurveillance.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.genomicSurveillance.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.entomologicalSurveillance": new ValidationRuleModel(DataType.Object, false),
    "step_B.entomologicalSurveillance.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.entomologicalSurveillance.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_B.commodityTracking": new ValidationRuleModel(DataType.Object, false),
    "step_B.commodityTracking.strategyInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.commodityTracking.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "proportionRateForOtherStrategy": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.proportionRateForOtherStrategy !==null && !(${Constants.Common.RootObjectNameSubstitute}.proportionRateForOtherStrategy >=0 && ${Constants.Common.RootObjectNameSubstitute}.proportionRateForOtherStrategy <=100)`,
        "Errors.ProportionValueMessage"
    ),    
};

export const EliminationValidationRules: IValidationRuleProvider = {
    step_A: new ValidationRuleModel(DataType.Object, true),
    "step_A.caseInvestigation": new ValidationRuleModel(DataType.Object, false),
    "step_A.caseInvestigation.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.caseInvestigation.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.caseClassification": new ValidationRuleModel(DataType.Object, false),
    "step_A.caseClassification.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.caseClassification.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.focusInvestigation": new ValidationRuleModel(DataType.Object, false),
    "step_A.focusInvestigation.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.focusInvestigation.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.focusClassificiation": new ValidationRuleModel(DataType.Object, false),
    "step_A.focusClassificiation.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.focusClassificiation.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.activeCaseDetection": new ValidationRuleModel(DataType.Object, false),
    "step_A.activeCaseDetection.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.activeCaseDetection.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.reactiveDetection": new ValidationRuleModel(DataType.Object, false),
    "step_A.reactiveDetection.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.reactiveDetection.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "step_A.proactiveDetection": new ValidationRuleModel(DataType.Object, false),
    "step_A.proactiveDetection.activityInPlace": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.proactiveDetection.surveillanceImplemented": new ValidationRuleModel(DataType.Boolean, true),

    "proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.proportionRate !==null && !(${Constants.Common.RootObjectNameSubstitute}.proportionRate >=0 && ${Constants.Common.RootObjectNameSubstitute}.proportionRate <=100)`,
        "Errors.ProportionValueMessage"
    ),
};
