﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    proportionOfRelevantStaff: new ValidationRuleModel(DataType.Object, false),
    "proportionOfRelevantStaff.nationalLevel": new ValidationRuleModel(
        DataType.Number,
        false,
        `!(${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.nationalLevel >=0 && ${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.nationalLevel <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),
    "proportionOfRelevantStaff.subNationalLevel": new ValidationRuleModel(
        DataType.Number,
        false,
        `!(${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.subNationalLevel >=0 && ${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.subNationalLevel <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),
    "proportionOfRelevantStaff.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Number,
        false,
        `!(${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.serviceDeliveryLevel >=0 && ${Constants.Common.RootObjectNameSubstitute}.proportionOfRelevantStaff.serviceDeliveryLevel <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),
}

export default ValidationRules;