import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import useCalculation from "../../../responses/useCalculation";
import { useSelector } from "react-redux";
import { AdequateCommoditiesForTreatment } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_1_2/Response_1";

type RDTResponse_Props = {
    response: any;
    updateStep_A: (step_A: AdequateCommoditiesForTreatment) => void;
};

/** Renders response for Indication 3.1.2 RDT response*/
const RDTResponse = (props: RDTResponse_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { response, updateStep_A } = props;
    const { step_A } = response;
    const { calculatePercentage } = useCalculation();
    const {
        healthFacilitiesWithStockOut,
        healthFacilitiesReporting,
        provideReason,
    } = step_A;
    const errors = useSelector((state: any) => state.error);

    //Triggers on value change of input controls and updates the step a
    const onValueChange = (fieldName: string, value: number | string) => {
        updateStep_A({
            ...step_A, [fieldName]: value ? Math.round(+value) : value === 0 ? 0 : null
        });
    };

    const rows = [
        {
            field: "healthFacilityWithStocksOut",
            label: t("indicators-responses:Common:HealthFacilitiesWithStockOuts"),
        },
        {
            field: "healthFacilitiesReporting",
            label: t("indicators-responses:Common:HealthFacilitiesReporting"),
        },
        {
            field: "healthFacilitiesWithStockOuts",
            label: t(
                "indicators-responses:Common:ProportionHealthFacilitiesWithStockOuts"
            ),
        }
    ];

    return (
        <>
            <div className="row mt-3 mb-3">
                <div className="col-md-12">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_1_2:RDTResponseDesc"
                    )}
                </div>
            </div>
            <Table width="100%">
                <>
                    <TableHeader headers={rows.map((row: any) => row.label)} />
                    <TableBody>
                        <>
                            <TableRow>
                                <>
                                    <TableCell>
                                        <TextBox
                                            id="healthFacilitiesWithStockOut"
                                            name="healthFacilitiesWithStockOut"
                                            type="number"
                                            fullWidth
                                            value={healthFacilitiesWithStockOut}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onValueChange(
                                                    "healthFacilitiesWithStockOut",
                                                    e.currentTarget.value
                                                )
                                            }
                                            error={errors["step_A.healthFacilitiesWithStockOut"] && errors["step_A.healthFacilitiesWithStockOut"]}
                                            helperText={errors["step_A.healthFacilitiesWithStockOut"] && errors["step_A.healthFacilitiesWithStockOut"]}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <TextBox
                                            id="healthFacilitiesReporting"
                                            name="healthFacilitiesReporting"
                                            type="number"
                                            fullWidth
                                            value={healthFacilitiesReporting}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onValueChange(
                                                    "healthFacilitiesReporting",
                                                    e.currentTarget.value
                                                )
                                            }
                                            error={errors["step_A.healthFacilitiesReporting"] && errors["step_A.healthFacilitiesReporting"]}
                                            helperText={errors["step_A.healthFacilitiesReporting"] && errors["step_A.healthFacilitiesReporting"]}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <label>
                                            {errors["step_A.proportionRate"] ? <span className="Mui-error d-flex">{errors["step_A.proportionRate"]}</span>
                                                : `${calculatePercentage(
                                                    healthFacilitiesWithStockOut,
                                                    healthFacilitiesReporting
                                                )}%`}
                                        </label>
                                    </TableCell>
                                </>
                            </TableRow>
                        </>
                    </TableBody>
                </>
            </Table>
        </>
    );
};

export default RDTResponse;