﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
    step_A: new ValidationRuleModel(DataType.Object, true),
    "step_A.healthFacilitiesWithStockOut": new ValidationRuleModel(
        DataType.Number,
        true,      
    ),
    "step_A.healthFacilitiesReporting": new ValidationRuleModel(
        DataType.Number,
        true,      
    ),
    "step_A.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `((${Constants.Common.RootObjectNameSubstitute}.step_A.healthFacilitiesWithStockOut / ${Constants.Common.RootObjectNameSubstitute}.step_A.healthFacilitiesReporting) * 100) > 100 || ((${Constants.Common.RootObjectNameSubstitute}.step_A.healthFacilitiesWithStockOut / ${Constants.Common.RootObjectNameSubstitute}.step_A.healthFacilitiesReporting)) * 100 < 0`,
        "Errors.ProportionValueMessage"
    ),
    "step_B.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `((${Constants.Common.RootObjectNameSubstitute}.step_B.healthFacilitiesWithStockOut / ${Constants.Common.RootObjectNameSubstitute}.step_B.healthFacilitiesReporting) * 100) > 100 || ((${Constants.Common.RootObjectNameSubstitute}.step_B.healthFacilitiesWithStockOut / ${Constants.Common.RootObjectNameSubstitute}.step_B.healthFacilitiesReporting)) * 100 < 0`,
        "Errors.ProportionValueMessage"
    ),
};

export const EliminationValidationRules: IValidationRuleProvider = {
    step_B: new ValidationRuleModel(DataType.Object, true),
    "step_B.healthFacilitiesWithStockOut": new ValidationRuleModel(
        DataType.Number,
        true,       
    ),
    "step_B.healthFacilitiesReporting": new ValidationRuleModel(
        DataType.Number,
        true,      
    ),
};
