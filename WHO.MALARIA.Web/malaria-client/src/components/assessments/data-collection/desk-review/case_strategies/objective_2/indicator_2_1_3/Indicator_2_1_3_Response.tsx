﻿import React, { ChangeEvent, useRef, useState } from "react";
import Indicator_2_1_3_Elimination_Activities from "./Indicator_2_1_3_Elimination_Activities";
import Indicator_2_1_3_Other_Malaria_Control from "./Indicator_2_1_3_Other_Malaria_Control";
import { TabModel } from "../../../../../../../models/TabModel";
import WHOTabs from "../../../../../../controls/WHOTabs";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_3/Response_1";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import useFormValidation from "../../../../../../common/useFormValidation";
import { useSelector } from "react-redux";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import {
    CommonValidationRules,
    EliminationValidationRules,
} from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import useCalculation from "../../../responses/useCalculation";
import { MetNotMetEnum } from "../../../../../../../models/Enums";

/** Renders the response for indicator 2.1.3 */
const Indicator_2_1_3_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_2_Indicator_2_1_3_Title");
    const { calculateProportionRate } = useCalculation();

    // Excluded property that are not used in Array Map 
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
    ];

    let ValidationRules: IValidationRuleProvider;
    ValidationRules = {
        ...CommonValidationRules,
        ...EliminationValidationRules
    }

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const [currentTab, setCurrentTab] = useState<number>(0);
    const dispatch = useDispatch();

    const {
        response,
        onChange,
        updateStep_A,
        updateStep_B,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCaptureForTabs<Response_1>(Response_1.init(), validate);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    useEffect(() => {
        getResponse();
        dispatch(updateStepIndex(0));
    }, []);

    const tabs: Array<TabModel> = [
        new TabModel(
            0,
            t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:EliminationActivities"
            ),
            (
                <Indicator_2_1_3_Elimination_Activities
                    response={response}
                    updateStep_A={updateStep_A}
                    onValueChange={onValueChange}
                />
            )
        ),
        new TabModel(
            1,
            t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:OtherMalariaControlStrategies"
            ),
            (
                <Indicator_2_1_3_Other_Malaria_Control
                    response={response}
                    updateStep_B={updateStep_B}
                    onValueChange={onValueChange}
                />
            )
        ),
    ];

    // triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
        dispatch(updateStepIndex(newValue));
    };

    //Method creates the arrays for two different property to calculate the proportion rate
    const calculateProportionRateForProperty = (
        modelKeyNameOne: string,
        modelKeyNameTwo: string
    ) => {
        let columnOneSum: number = 0;
        let columnTwoSum: number = 0;

        Object.keys(response.step_A)
            .filter((key: string) => !excludedProperties.includes(key))
            .map((item: string) => {
                columnOneSum += response.step_A[item]?.[modelKeyNameOne];
                columnTwoSum += response.step_A[item]?.[modelKeyNameTwo];
            });

        return calculateProportionRate(columnOneSum, columnTwoSum);
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const relevantMalariaControlStrategiesProportion: number = calculateProportionRateForProperty("activityInPlace", "surveillanceImplemented");

        onValueChange(
            "metNotMetStatus",
            relevantMalariaControlStrategiesProportion === 100
                ? MetNotMetEnum.Met
                : relevantMalariaControlStrategiesProportion === 0
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet,
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response.step_A?.caseInvestigation?.activityInPlace,
        response.step_A?.caseClassification?.activityInPlace,
        response.step_A?.focusInvestigation?.activityInPlace,
        response.step_A?.focusClassificiation?.activityInPlace,
        response.step_A?.activeCaseDetection?.activityInPlace,
        response.step_A?.reactiveDetection?.activityInPlace,
        response.step_A?.proactiveDetection?.activityInPlace,
        response.step_A?.caseInvestigation?.surveillanceImplemented,
        response.step_A?.caseClassification?.surveillanceImplemented,
        response.step_A?.focusInvestigation?.surveillanceImplemented,
        response.step_A?.focusClassificiation?.surveillanceImplemented,
        response.step_A?.activeCaseDetection?.surveillanceImplemented,
        response.step_A?.reactiveDetection?.surveillanceImplemented,
        response.step_A?.proactiveDetection?.surveillanceImplemented,
    ]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    {!!Object.keys(errors).length && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_3:ResponseCommonError"
                            )}
                        </span>
                    )}
                    <div className="app-tab-wrapper">
                        <WHOTabs
                            tabs={tabs}
                            value={currentTab}
                            onChange={onTabChange}
                            scrollable={false}
                        >
                            <div className="p-2">{tabs[currentTab].children}</div>
                        </WHOTabs>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason || ""}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
};

export default Indicator_2_1_3_Response;
