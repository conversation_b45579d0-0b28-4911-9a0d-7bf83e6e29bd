import React from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Dropdown from "../../../../../../controls/Dropdown";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

type Indicator_1_3_4_Props = {
  response: any;
  updateTab: (tabKey: any, response: any) => void;
  years: number[];
};

/** Renders the 3rd Tab of indicator 1.3.4 */
function OtherAnalyticalOutput(props: Indicator_1_3_4_Props) {
  const { t } = useTranslation(["indicators-responses"]);
  const { otherAnalyticalTab } = props.response;

  // trigger whenever Value Change of control
  const onValueChange = (fieldName: string, value: any) => {
    const _response = {
      ...otherAnalyticalTab,
      [fieldName]: value,
    };
    props.updateTab("otherAnalyticalTab", _response);
  };

  const errors = useSelector((state: any) => state.error);

  return (
    <>
      <p className="mt-3">
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:Tab3ResponseNote"
        )}
      </p>

      <div>
        <RadioButtonGroup
          id="isMapOfCountryWithStratification"
          name="isMapOfCountryWithStratification"
          options={[
            new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
            new MultiSelectModel(false, t("indicators-responses:Common:No")),
          ]}
          row
          value={otherAnalyticalTab?.isMapOfCountryWithStratification}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onValueChange(
              "isMapOfCountryWithStratification",
              e.currentTarget.value === "false" ? false : true
            )
          }
          error={
            errors["otherAnalyticalTab.isMapOfCountryWithStratification"] &&
            errors["otherAnalyticalTab.isMapOfCountryWithStratification"]
          }
        
        />
      </div>

      {otherAnalyticalTab?.isMapOfCountryWithStratification && (
        <div className="row mt-3">
          <div className="col-md-5">
            <Dropdown
              id="specifyLastYear"
              name="specifyLastYear"
              label={t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:SpecifyLastYearUpdate"
              )}
              options={props.years.map((year) => {
                return new MultiSelectModel(
                  year,
                  year.toString(),
                  false,
                  false
                );
              })}
              fullWidth={false}
              value={otherAnalyticalTab?.specifyLastYear}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                onValueChange("specifyLastYear", +e.currentTarget.value)
              }
              error={
                errors["otherAnalyticalTab.specifyLastYear"] &&
                errors["otherAnalyticalTab.specifyLastYear"]
              }
              helperText={
                errors["otherAnalyticalTab.specifyLastYear"] &&
                errors["otherAnalyticalTab.specifyLastYear"]
              }
            />
          </div>
        </div>
      )}

      <p className="mt-3 col-md-5">
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:HighRiskPopulations"
        )}
      </p>
      <div>
        <RadioButtonGroup
          id="isHighRiskPopulation"
          name="isHighRiskPopulation"
          options={[
            new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
            new MultiSelectModel(false, t("indicators-responses:Common:No")),
          ]}
          row
          value={otherAnalyticalTab?.isHighRiskPopulation}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onValueChange(
              "isHighRiskPopulation",
              e.currentTarget.value === "false" ? false : true
            )
          }
          error={
            errors["otherAnalyticalTab.isHighRiskPopulation"] &&
            errors["otherAnalyticalTab.isHighRiskPopulation"]
          }
        />
      </div>
      <div className="row mt-3">
        <div className="col-md-5">
          <TextBox
            id="highRiskPopulationDetail"
            name="highRiskPopulationDetail"
            label={t("indicators-responses:Common:ProvideDetails")}
            fullWidth
            value={otherAnalyticalTab?.highRiskPopulationDetail}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              onValueChange("highRiskPopulationDetail", e.currentTarget.value)
            }
            error={
              errors["otherAnalyticalTab.highRiskPopulationDetail"] &&
              errors["otherAnalyticalTab.highRiskPopulationDetail"]
            }
            helperText={
              errors["otherAnalyticalTab.highRiskPopulationDetail"] &&
              errors["otherAnalyticalTab.highRiskPopulationDetail"]
            }
          />
        </div>
      </div>
      <div className="row mt-5">
        <div className="col-md-5">
          <TextBox
            id="otherAnalysisDetail"
            name="otherAnalysisDetail"
            label={t(
              "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4:ProvideDetailsOtherAnalysis"
            )}
            fullWidth
            multiline
            rows={3}
            value={otherAnalyticalTab?.otherAnalysisDetail || ""}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              onValueChange("otherAnalysisDetail", e.currentTarget.value)
            }
          />
        </div>
      </div>
    </>
  );
}

export default OtherAnalyticalOutput;
