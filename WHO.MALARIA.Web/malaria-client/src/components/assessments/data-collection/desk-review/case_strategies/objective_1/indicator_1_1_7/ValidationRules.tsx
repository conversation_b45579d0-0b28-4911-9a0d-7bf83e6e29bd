import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, false),
  "step_A.isVitalRegistration": new ValidationRuleModel(DataType.Boolean, false,
    `${Constants.Common.RootObjectNameSubstitute}.step_A.isVitalRegistration !==null && ${Constants.Common.RootObjectNameSubstitute}.step_A.isVitalRegistration === false`,
    
    ),
  "step_A.vitalRegistrationDetail": new ValidationRuleModel(
    DataType.String,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.step_A.isVitalRegistration === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_A.vitalRegistrationDetail)`
  ),

  step_B: new ValidationRuleModel(DataType.Object, false),
  "step_B.isDeathRecorded": new ValidationRuleModel(DataType.String, true),
  "step_B.deathRecordedDetail": new ValidationRuleModel(DataType.String, true),

  step_C: new ValidationRuleModel(DataType.Object, false),
  "step_C.deathCompletenessPercentage": new ValidationRuleModel(
    DataType.Number,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.step_C.deathCompletenessPercentage !==null && !(${Constants.Common.RootObjectNameSubstitute}.step_C.deathCompletenessPercentage >=0 && ${Constants.Common.RootObjectNameSubstitute}.step_C.deathCompletenessPercentage <=100)`,
      "Errors.ValueBetweenZeroToHundred"
  ),

  "step_C.deathProportionPercentage": new ValidationRuleModel(
    DataType.Number,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.step_C.deathProportionPercentage !==null && !(${Constants.Common.RootObjectNameSubstitute}.step_C.deathProportionPercentage >=0 && ${Constants.Common.RootObjectNameSubstitute}.step_C.deathProportionPercentage <=100)`,
      "Errors.ValueBetweenOneToHundred"
  ),

  step_D: new ValidationRuleModel(DataType.Object, false),
  "step_D.isNMPRecorded": new ValidationRuleModel(DataType.String, true),
  "step_D.nmpDeathRecordedDetail": new ValidationRuleModel(
    DataType.String,
    true
  ),
};

export default ValidationRules;
