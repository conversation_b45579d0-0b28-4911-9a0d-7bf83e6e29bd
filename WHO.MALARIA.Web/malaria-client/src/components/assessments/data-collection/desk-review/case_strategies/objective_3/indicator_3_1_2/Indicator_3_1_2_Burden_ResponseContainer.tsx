﻿import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import Checkbox from "../../../../../../controls/Checkbox";
import WHOTabs from "../../../../../../controls/WHOTabs";
import { TabModel } from "../../../../../../../models/TabModel";
import RDTResponse from "./RDTResponse";
import TextBox from "../../../../../../controls/TextBox";
import { useDispatch } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import Response_2 from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_1_2/Response_2";
import useIndicatorResponseCaptureForTabs from "../../../../desk-review/responses/useIndicatorResponseCaptureForTabs";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import useCalculation from "../../../responses/useCalculation";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders response for Indication 3.1.2 */
function Indicator_3_1_2_Burden_ResponseContainer() {
    const { t } = useTranslation(["indicators-responses"]);
    const [currentTab, setCurrentTab] = useState<number>(0);
    const dispatch = useDispatch();
    const { calculatePercentage } = useCalculation();  

    let ValidationRules: IValidationRuleProvider;
    ValidationRules = CommonValidationRules;

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);
    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        updateStep_A,
        onSave,
        onCannotBeAssessed,
        onFinalize,
        onChange,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCaptureForTabs<Response_2>(Response_2.init(), validate);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    
    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        dispatch(updateStepIndex(0));
    }, []);

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    const tabs: Array<TabModel> = [
        new TabModel(
            0,
            t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_1_2:RDTs"
            ),
            <RDTResponse
                response={response}
                updateStep_A={updateStep_A}
            />
        ),
    ];

    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
        dispatch(updateStepIndex(newValue));
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const rdtPercentage = calculatePercentage(
            response.step_A.healthFacilitiesWithStockOut,
            response.step_A.healthFacilitiesReporting
        );

        onValueChange(
            "metNotMetStatus",
            rdtPercentage <= 20
                ? MetNotMetEnum.Met
                : rdtPercentage > 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response.step_A.healthFacilitiesWithStockOut,
        response.step_A.healthFacilitiesReporting,
    ]);

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_1_2:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>
            {!response?.cannotBeAssessed ? (
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={tabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-3"> {tabs[currentTab].children} </div>
                    </WHOTabs>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

        </>
    );
}

export default Indicator_3_1_2_Burden_ResponseContainer;