﻿import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Checkbox from "../../../../../../controls/Checkbox";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import { useLocation } from "react-router";
import {
    Response_1,
    HealthSector,
    ReportingDetails,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_2/Response_1";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import {
    StrategiesEnum,
    MetNotMetEnum,
} from "../../../../../../../models/Enums";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { useSelector } from "react-redux";
import InformationDialog from "../../../../../../common/InformationDialog";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { event } from "react-ga";

/* Renders the indicator 2.1.2 response for desk review */
const Indicator_2_1_2_Response = () => {
    const location: any = useLocation();
    const { t } = useTranslation(["indicators-responses"]);
    const [showInformationDialog, setShowInformationDialog] =
        useState<boolean>(false);

    document.title = t(
        "indicators-responses:app:DR_Objective_2_Indicator_2_1_2_Title"
    );

    const headers = [
        {
            field: "healthSectors",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:HealthSectors")
        },
        {
            field: "reportingMalariaCases",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:ReportingMalariaCases")
        },
        {
            field: "mandatedToReport",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:MandatedToReport")
        },
        {
            field: "caseBasedDataReported",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:CaseBasedDataReported")
        },
        {
            field: "national",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:National")
        },
        {
            field: "subnational",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:Subnational")
        },
        {
            field: "serviceDelivery",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:ServiceDelivery")
        },
        {
            field: "detailsOnNotificationProcesses",
            label: t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:DetailsOnNotificationProcesses")
        },
    ];

    const strategyId: string = location?.state?.strategyId;
    const isBurdenReduction = strategyId.toLowerCase() === StrategiesEnum.BurdenReduction.toLowerCase();

    const validationRulesRef = useRef<IValidationRuleProvider | null>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current || ValidationRules);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onCannotBeAssessed,
        onChange,
        onValueChange,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    useEffect(() => {
        if (response?.isBurdenReductionStrategy === null) {
            onValueChange("isBurdenReductionStrategy", strategyId.toLowerCase() === StrategiesEnum.BurdenReduction.toLowerCase())
        }
    }, [response?.isBurdenReductionStrategy]);

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        getMetNotMetStatus();
    }, [response?.healthSector]);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        let metNotMetStatus: MetNotMetEnum = MetNotMetEnum.NotMet;

        let publicSector;
        let privateFormal;
        let privateInformal;
        let community;
        let allSectorsMandatedToReport = false;
        let allSectorsReportedCaseBasedData = false;

        if (isBurdenReduction) {
            if (response?.healthSector?.publicHealthSector?.healthFacility?.mandatedToReport &&
                response?.healthSector?.publicHealthSector?.hospital?.mandatedToReport &&
                response?.healthSector?.publicHealthSector?.laboratory?.mandatedToReport) {
                publicSector = true;
            }

            if (response?.healthSector?.privateFormal?.faithBasedClinic?.mandatedToReport &&
                response?.healthSector?.privateFormal?.healthFacility?.mandatedToReport &&
                response?.healthSector?.privateFormal?.hospital?.mandatedToReport &&
                response?.healthSector?.privateFormal?.laboratory?.mandatedToReport &&
                response?.healthSector?.privateFormal?.military?.mandatedToReport &&
                response?.healthSector?.privateFormal?.ngoClinic?.mandatedToReport &&
                response?.healthSector?.privateFormal?.police?.mandatedToReport &&
                response?.healthSector?.privateFormal?.prison?.mandatedToReport) {
                privateFormal = true;
            }

            privateInformal = response?.healthSector?.privateInformal?.mandatedToReport;
            community = response?.healthSector?.community?.mandatedToReport;
        }
        else {
            if (response?.healthSector?.publicHealthSector?.healthFacility?.caseBasedDataReported &&
                response?.healthSector?.publicHealthSector?.hospital?.caseBasedDataReported &&
                response?.healthSector?.publicHealthSector?.laboratory?.caseBasedDataReported) {
                publicSector = true;
            }

            if (response?.healthSector?.privateFormal?.faithBasedClinic?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.healthFacility?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.hospital?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.laboratory?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.military?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.ngoClinic?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.police?.caseBasedDataReported &&
                response?.healthSector?.privateFormal?.prison?.caseBasedDataReported) {
                privateFormal = true;
            }
            privateInformal = response?.healthSector?.privateInformal?.caseBasedDataReported;
            community = response?.healthSector?.community?.caseBasedDataReported;
        }

        if (publicSector === true
            && privateFormal === true
            && privateInformal === true
            && community === true) {
            metNotMetStatus = MetNotMetEnum.Met;
            allSectorsMandatedToReport = true;
            allSectorsReportedCaseBasedData = true;
        } else if (publicSector === true
            || privateFormal === true
            || privateInformal === true
            || community === true) {
            metNotMetStatus = MetNotMetEnum.PartiallyMet;
        }

        onValueChange("metNotMetStatus", metNotMetStatus);

        if (isBurdenReduction)
            onValueChange("allSectorsMandatedToReport", allSectorsMandatedToReport);
        else
            onValueChange("allSectorsReportedCaseBasedData", allSectorsReportedCaseBasedData);
    };

    //Finalize the indicator's response and show the information popup to let user know that he/she needs to re-assess the indicator 2.3.1
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            setShowInformationDialog(true);
            onFinalize();
        }
    };

    const handleNumericValueChange = (event: any) => {
        if (event.keyCode != 8 && event.keyCode != 0 && event.keyCode < 48 || event.keyCode > 57) {
            event.preventDefault();
        }
    }

    //Triggers for every input control of the screen that is associated with the health sector object, prepares the object to be updated for health sector
    const onHealthSectorDetailsChange = (healthSectorKeyName: string, healthSectorCategoryKeyName: string, reportingDetailKeyName: string, event: React.ChangeEvent<HTMLInputElement>) => {
        const healthSectors: HealthSector = response.healthSector;
        const healthSectorProperty = healthSectors[healthSectorKeyName];

        let value: string | boolean = event.target.value;


        if (event.target.type === 'radio') {
            switch (value) {
                case "true":
                    value = true;
                    break;
                case "false":
                    value = false;
                    break;
            }
        }

        let healthSector;

        if (healthSectorCategoryKeyName) {
            const healthSectorCategoryProperty: ReportingDetails = healthSectorProperty[healthSectorCategoryKeyName];
            if (reportingDetailKeyName == "reportingMalaria" && value == false) {

                healthSector = {
                    ...healthSectors,
                    [healthSectorKeyName]: {
                        ...healthSectorProperty,
                        [healthSectorCategoryKeyName]: {
                            ...healthSectorCategoryProperty,
                            [reportingDetailKeyName]: value,
                            ["national"]: "",
                            ["subNational"]: "",
                            ["serviceDelivery"]: "",
                            ["notificationProcesses"]: ""
                        }
                    }
                }

            } else {
                healthSector = {
                    ...healthSectors,
                    [healthSectorKeyName]: {
                        ...healthSectorProperty,
                        [healthSectorCategoryKeyName]: {
                            ...healthSectorCategoryProperty,
                            [reportingDetailKeyName]: value
                        }
                    }
                }
            }
        } else if (reportingDetailKeyName == "reportingMalaria" && value == false) {
            healthSector = {
                ...healthSectors,
                [healthSectorKeyName]: {
                    ...healthSectors[healthSectorKeyName],
                    [reportingDetailKeyName]: value,
                    ["national"]: "",
                    ["subNational"]: "",
                    ["serviceDelivery"]: "",
                    ["notificationProcesses"]: ""
                }
            }
        } else {
            healthSector = {
                ...healthSectors,
                [healthSectorKeyName]: {
                    ...healthSectors[healthSectorKeyName],
                    [reportingDetailKeyName]: value
                }
            }
        }

        onValueChange("healthSector", healthSector);
    }

    //Creates table rows for health sectors
    const renderHealthSectorRow = (healthSectorLabel: string,
        healthSectorLabelCssClass: string,
        healthSectorKeyName: string,
        healthSectorCategoryKeyName: string,
        rowNumber: number, isInputFieldShow: boolean = true) => {

        const getValue = (keyName: string) => {
            const healthSector = response?.healthSector;
            if (healthSectorCategoryKeyName)
                return healthSector[healthSectorKeyName] && healthSector[healthSectorKeyName][healthSectorCategoryKeyName] ? healthSector[healthSectorKeyName][healthSectorCategoryKeyName][keyName] : "";

            return healthSector[healthSectorKeyName] ? healthSector[healthSectorKeyName][keyName] : "";
        }

        const mandatedToReport = "mandatedToReport";
        const reportingMalaria = "reportingMalaria";
        const caseBasedDataReported = "caseBasedDataReported";
        const national = "national";
        const subNational = "subNational";
        const serviceDelivery = "serviceDelivery";
        const notificationProcesses = "notificationProcesses";
        const isHealthSectorKeyNameCommunity = healthSectorKeyName === "community";
        const commonErrorKey = healthSectorCategoryKeyName ? `healthSector.${healthSectorKeyName}.${healthSectorCategoryKeyName}` : `healthSector.${healthSectorKeyName}`;
        const disableTextbox = !getValue(reportingMalaria);



        return <TableRow key={rowNumber}>
            <>
                <TableCell className={healthSectorLabelCssClass} key={`${healthSectorLabel}cell`}>
                    {healthSectorLabel}
                </TableCell>
                {isInputFieldShow
                    ? (
                        <TableCell key={`${reportingMalaria}cell`}>
                            <RadioButtonGroup
                                key={`${reportingMalaria}`}
                                id={reportingMalaria}
                                name={reportingMalaria}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={getValue(reportingMalaria)}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, reportingMalaria, e)}
                                error={
                                    errors[`${commonErrorKey}.${reportingMalaria}`] &&
                                    errors[`${commonErrorKey}.${reportingMalaria}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${reportingMalaria}`] &&
                                    errors[`${commonErrorKey}.${reportingMalaria}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}

                {isInputFieldShow
                    ? (
                        <TableCell key={`${mandatedToReport}cell`}>
                            <RadioButtonGroup
                                key={`${mandatedToReport}`}
                                id={mandatedToReport}
                                name={mandatedToReport}
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes")
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No")
                                    ),
                                ]}
                                value={getValue(mandatedToReport)}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, mandatedToReport, e)}
                                error={
                                    errors[`${commonErrorKey}.${mandatedToReport}`] &&
                                    errors[`${commonErrorKey}.${mandatedToReport}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${mandatedToReport}`] &&
                                    errors[`${commonErrorKey}.${mandatedToReport}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}
                {StrategiesEnum.BurdenReduction.toLowerCase() !==
                    strategyId && isInputFieldShow ? (
                    <TableCell key={`${caseBasedDataReported}cell}`}>
                        <RadioButtonGroup
                            key={caseBasedDataReported}
                            id={caseBasedDataReported}
                            name={caseBasedDataReported}
                            color="primary"
                            options={[
                                new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes")
                                ),
                                new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No")
                                ),
                            ]}
                            value={getValue(caseBasedDataReported)}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, caseBasedDataReported, e)}
                            error={
                                errors[`${commonErrorKey}.${caseBasedDataReported}`] &&
                                errors[`${commonErrorKey}.${caseBasedDataReported}`]
                            }
                            helperText={
                                errors[`${commonErrorKey}.${caseBasedDataReported}`] &&
                                errors[`${commonErrorKey}.${caseBasedDataReported}`]
                            }
                        />
                    </TableCell>
                ) : (<></>)}

                {isInputFieldShow
                    ? (
                        <TableCell key={`nationalcell`}>
                            <TextBox style={{ width: 250 }}
                                id={`national${Math.random()}`}
                                key={`national`}
                                name={national}
                                value={getValue(national)}
                                type="number"
                                maxLength={20}
                                placeholder={
                                    t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:EnterNumberPlaceHolder")}
                                disabled={disableTextbox}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, national, e)}
                                onKeyDown={handleNumericValueChange}
                                error={
                                    errors[`${commonErrorKey}.${national}`] &&
                                    errors[`${commonErrorKey}.${national}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${national}`] &&
                                    errors[`${commonErrorKey}.${national}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}

                {isInputFieldShow
                    ? (
                        <TableCell key={`subNationalcell`}>
                            <TextBox style={{ width: 250 }}
                                id={`subNational${Math.random()}`}
                                key={subNational}
                                name={subNational}
                                type="number"
                                maxLength={20}
                                placeholder={t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:EnterNumberPlaceHolder")}
                                value={getValue(subNational)}
                                disabled={disableTextbox}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, subNational, e)}
                                onKeyDown={handleNumericValueChange}
                                error={
                                    errors[`${commonErrorKey}.${subNational}`] &&
                                    errors[`${commonErrorKey}.${subNational}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${subNational}`] &&
                                    errors[`${commonErrorKey}.${subNational}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}

                {isInputFieldShow
                    ? (
                        <TableCell key="serviceDeliverycell">
                            <TextBox style={{ width: 350 }}
                                id={`serviceDelivery${Math.random()}`}
                                key={serviceDelivery}
                                name={serviceDelivery}
                                type="number"
                                maxLength={20}
                                placeholder={t("indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:EnterNumberPlaceHolder")}
                                value={getValue(serviceDelivery)}
                                disabled={disableTextbox}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, serviceDelivery, e)}
                                onKeyDown={handleNumericValueChange}
                                error={
                                    errors[`${commonErrorKey}.${serviceDelivery}`] &&
                                    errors[`${commonErrorKey}.${serviceDelivery}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${serviceDelivery}`] &&
                                    errors[`${commonErrorKey}.${serviceDelivery}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}

                {isInputFieldShow
                    ? (
                        <TableCell key="notificationProcessescell">
                            <TextBox
                                id={`notificationProcesses${Math.random()}`}
                                key={notificationProcesses}
                                name={notificationProcesses}
                                value={getValue(notificationProcesses)}
                                disabled={disableTextbox}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => onHealthSectorDetailsChange(healthSectorKeyName, healthSectorCategoryKeyName, notificationProcesses, e)}
                                error={
                                    errors[`${commonErrorKey}.${notificationProcesses}`] &&
                                    errors[`${commonErrorKey}.${notificationProcesses}`]
                                }
                                helperText={
                                    errors[`${commonErrorKey}.${notificationProcesses}`] &&
                                    errors[`${commonErrorKey}.${notificationProcesses}`]
                                }
                            />
                        </TableCell>
                    ) : <TableCell><></></TableCell>}

            </>
        </TableRow>
    }

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response.cannotBeAssessed}
                />
            </div>
            {!response.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:ResponseDesc"
                        )}
                    </p>
                    <p>
                        {/* <i>
                            {t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:NoteToCompleteTheAssessment"
                            )}
                        </i> */}
                    </p>
                    {/* {errors["isOnePublicHealthSystemDescendentNotSelected"] && <span className="Mui-error d-flex mb-2">*{errors["isOnePublicHealthSystemDescendentNotSelected"]}</span>}
                    {errors["isParentPublicHealthSectorNotSelected"] && <span className="Mui-error d-flex mb-2">*{errors["isParentPublicHealthSectorNotSelected"]}</span>} 
                    {errors["isOnePrivateFormalDescendentHealthSystemNotSelected"] && <span className="Mui-error d-flex mb-2">*{errors["isOnePrivateFormalDescendentHealthSystemNotSelected"]}</span>}
                    {errors["isParentPrivateFormalHealthSectorNotSelected"] && <span className="Mui-error d-flex mb-2">*{errors["isParentPrivateFormalHealthSectorNotSelected"]}</span>}  */}
                    <Table className="app-table table">
                        <>
                            <TableHeader
                                headers={
                                    StrategiesEnum.BurdenReduction.toLowerCase() === strategyId
                                        ? headers
                                            .filter(
                                                (header: any, index: number) =>
                                                    header.field !== "caseBasedDataReported"
                                            )
                                            .map((header: any) => header.label)
                                        : headers.map((header: any) => header.label)
                                }
                            />
                            <TableBody key="tblbody">
                                <>
                                    {renderHealthSectorRow(t("indicators-responses:Common:Public"), "fw-bold", "publicHealthSector", "publicReportingDetails", 1, false)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:HealthFacilities"), "padding-left-25", "publicHealthSector", "healthFacility", 2)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Laboratory"), "padding-left-25", "publicHealthSector", "laboratory", 3)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Hospital"), "padding-left-25", "publicHealthSector", "hospital", 4)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:PrivateFormalText"), "fw-bold", "privateFormal", "privateFormal", 5, false)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:HealthFacilities"), "padding-left-25", "privateFormal", "healthFacility", 6)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:FaithBasedClinics"), "padding-left-25", "privateFormal", "faithBasedClinic", 7)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:NonGovernmentalOrganizationClinics"), "padding-left-25", "privateFormal", "ngoClinic", 8)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Laboratory"), "padding-left-25", "privateFormal", "laboratory", 9)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Hospital"), "padding-left-25", "privateFormal", "hospital", 10)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:MilitaryRowHeading"), "padding-left-25", "privateFormal", "military", 11)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:PoliceRowHeading"), "padding-left-25", "privateFormal", "police", 12)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Prison"), "padding-left-25", "privateFormal", "prison", 13)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:PrivateInformal"), "fw-bold", "privateInformal", "", 14)}
                                    {renderHealthSectorRow(t("indicators-responses:Common:Community"), "fw-bold", "community", "", 15)}
                                </>
                            </TableBody>
                        </>
                    </Table>

                    <div className="row mt-4">
                        <div className="col-xs-12 col-md-12">
                            <label>
                                {t(isBurdenReduction ? "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:SectorsMandatedToreport" : "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:AllSectorReportCaseBasedData")}
                            </label>
                            <RadioButtonGroup
                                id="allSectorsMandatedToReport"
                                name="allSectorsMandatedToReport"
                                row
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes"),
                                        true
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No"),
                                        true
                                    ),
                                ]}
                                value={isBurdenReduction ? response?.allSectorsMandatedToReport : response?.allSectorsReportedCaseBasedData}
                            />
                        </div>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        onChange={onChange}
                        value={response?.cannotBeAssessedReason || ""}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}

            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />

            <InformationDialog
                title={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:AssessChildInformationDialogTitle"
                )}
                content={t(
                    "indicators-responses:DRObjective_2_Responses:Indicator_2_1_2:AssessChildInformationDialogContent"
                )}
                open={showInformationDialog}
                onClick={() => {
                    setShowInformationDialog(false);
                }}
            />
        </>
    );
};

export default Indicator_2_1_2_Response;
