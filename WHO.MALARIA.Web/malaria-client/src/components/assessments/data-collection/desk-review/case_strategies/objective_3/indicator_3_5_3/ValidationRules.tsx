﻿import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

export const CommonValidationRules: IValidationRuleProvider = {
    "builtInChecksAtDataEntry": new ValidationRuleModel(DataType.Object, true),
    "producesDataVerificationReport": new ValidationRuleModel(DataType.Object, true),   
    "builtInChecksAtDataEntry.inPlace": new ValidationRuleModel(DataType.Boolean, true),
    "producesDataVerificationReport.inPlace": new ValidationRuleModel(DataType.Boolean, true),   
}

export const EliminationValidationRules: IValidationRuleProvider = {
    "avoidDuplicateEntryOfRecord": new ValidationRuleModel(DataType.Object, true),
    "avoidDuplicateEntryOfRecord.inPlace": new ValidationRuleModel(DataType.Boolean, true),
}