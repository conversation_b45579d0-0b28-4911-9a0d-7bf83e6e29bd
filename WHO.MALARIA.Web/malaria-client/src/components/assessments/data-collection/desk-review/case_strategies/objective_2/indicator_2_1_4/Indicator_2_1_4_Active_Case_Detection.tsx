﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import React from "react";
import {
  Step_A_Response,
  CaseActiveDetection,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_1";
import { useSelector } from "react-redux";
import parse from "html-react-parser";

type Indicator_2_1_4_CaseActiveDetectionProps = {
  step_A: Step_A_Response;
  updateStep_A: (step_A: any) => void;
  activeCaseDetection: CaseActiveDetection;
  getMetNotMetStatus: () => void;
};

/** Renders the response for indicator 2.1.4 Active_Case_Detection*/
function Indicator_2_1_4_Active_Case_Detection(
  props: Indicator_2_1_4_CaseActiveDetectionProps
) {
  const { t } = useTranslation(["indicators-responses"]);
  const { activeCaseDetection, updateStep_A, step_A, getMetNotMetStatus } = props;
  const errors = useSelector((state: any) => state.error);

  // Triggered whenever the control values are changed and update response
  const onValueChange = (fieldName: string, value: string | boolean) => {
    updateStep_A({
      ...step_A,
      activeCaseDetection: {
        ...step_A.activeCaseDetection,
        [fieldName]: value,
      },
    });
  };

  const headers = [
    { field: "", label: "" },
    {
      field: "WHODefinition",
      label: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHODefinition"
      ),
    },
    {
      field: "countryDefinition",
      label: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CountryDefinition"
      ),
    },
    {
      field: "definitionOK",
      label: parse(t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DefinitionOK"
      )),
    },
  ];

  return (
    <>
      <div className="response-wrapper">
        <div>
          <Table>
            <>
              <TableHeader
                headers={headers.map((header: any) => header.label)}
              />
              <TableBody>
                <>
                  <TableRow>
                    <>
                      <TableCell>
                        <>
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ActiveCaseDetection"
                          )}
                        </>
                      </TableCell>
                      <TableCell>
                        <>
                          {t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DetectionByHealth"
                          )}{" "}
                        </>
                      </TableCell>
                      <TableCell>
                        <TextBox
                          id="countryDefinition"
                          name="countryDefinition"
                          rows={2}
                          multiline
                          fullWidth
                          value={activeCaseDetection?.countryDefinition}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(e.target.name, e.currentTarget.value)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <RadioButtonGroup
                          id="definitionOK"
                          name="definitionOK"
                          row
                          color="primary"
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Yes")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:No")
                            ),
                          ]}
                          value={activeCaseDetection?.definitionOK}
                          onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            onValueChange(
                              e.target.name,
                              e.currentTarget.value === "false" ? false : true
                            );
                            getMetNotMetStatus();
                          }}
                        />
                      </TableCell>
                    </>
                  </TableRow>
                </>
              </TableBody>
            </>
          </Table>
        </div>
      </div>
    </>
  );
}

export default Indicator_2_1_4_Active_Case_Detection;
