import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    improvementsInFeedbackAndSupervision: new ValidationRuleModel(
        DataType.Object,
        false
    ),

    "improvementsInFeedbackAndSupervision.evidence": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `const evidencesForData=Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["cannotBeAssessed","cannotBeAssessedReason","hasMalariaSurReportBeenProducedInLast12Months","links"].includes(key));
    evidencesForData.every(data =>  ${Constants.Common.RootObjectNameSubstitute}[data].evidence === null)`
    ),

    "improvementsInFeedbackAndSupervision.details": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.details) `,
        "Errors.MandatoryField"
    ),

    improvementsInDataQuality: new ValidationRuleModel(
        DataType.Object,
        false
    ),

    "improvementsInDataQuality.details": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.improvementsInDataQuality.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.improvementsInDataQuality.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.improvementsInDataQuality.details) `,
        "Errors.MandatoryField"
    ),

    initiateSurveillanceTrainingAndDataAnalysis: new ValidationRuleModel(
        DataType.Object,
        false
    ),

    "initiateSurveillanceTrainingAndDataAnalysis.details":
        new ValidationRuleModel(
            DataType.String,
            false,
            `(${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.details) `,
            "Errors.MandatoryField"
        ),

    responseActivities: new ValidationRuleModel(DataType.Object, false),

    "responseActivities.details": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.responseActivities.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.responseActivities.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.responseActivities.details) `,
        "Errors.MandatoryField"
    )
};

export default ValidationRules;
