﻿import { useTranslation } from "react-i18next";
import { Step_C_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useSelector } from "react-redux";

interface IStep_C_Props {
    step_C: Step_C_Response;
    updateStep_C: (step_C: any) => void;
}

/** Renders the response for indicator 1.1.1 STEP C Line Graph */
const Indicator_1_1_1_Step_c = (props: IStep_C_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_C, updateStep_C } = props;

    const onValueChange = (fieldName: string, value: any) => {
        updateStep_C({ ...step_C, [fieldName]: value });
    };

    const errors = useSelector((state: any) => state.error);

    return (
        <div className="response-wrapper">
            <p>
                {t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:SpecificPopolationGroup"
                )}
            </p>
            <div className="col-md-12 ml-5 mt-3">
                <RadioButtonGroup
                    id="groupsNotCapturedByNSS"
                    name="groupsNotCapturedByNSS"
                    row
                    color="primary"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    value={step_C.groupsNotCapturedByNSS}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            "groupsNotCapturedByNSS",
                            e.currentTarget.value === "false" ? false : true
                        )
                    }
                    error={
                        errors["step_C.groupsNotCapturedByNSS"] &&
                        errors["step_C.groupsNotCapturedByNSS"]
                    }
                    helperText={
                        errors["step_C.groupsNotCapturedByNSS"] &&
                        errors["step_C.groupsNotCapturedByNSS"]
                    }
                />
            </div>
            <TextBox
                id="step_C_details"
                name="step_C_details"
                label={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ProvideDetail"
                )}
                fullWidth
                rows={5}
                InputLabelProps={{ shrink: true }}
                maxLength={10000}
                multiline
                value={step_C.details}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onValueChange("details", e.currentTarget.value)
                }
                error={
                    errors["step_C.details"] &&
                    errors["step_C.details"]
                }
                helperText={
                    errors["step_C.details"] &&
                    errors["step_C.details"]
                }
            />
        </div>
    );
};

export default Indicator_1_1_1_Step_c;
