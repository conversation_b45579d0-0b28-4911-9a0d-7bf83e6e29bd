﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    screenshot: new ValidationRuleModel(DataType.Object, false),

    "screenshot[file1]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file1] ? (screenshot[file1].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file1].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    "screenshot[file2]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file2] ? (screenshot[file2].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file2].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    "screenshot[file3]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file3] ? (screenshot[file3].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file3].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    "screenshot[file4]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file4] ? (screenshot[file4].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file4].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    "screenshot[file5]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file5] ? (screenshot[file5].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file5].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    "screenshot[file6]":
        new ValidationRuleModel(DataType.String, false,
            `screenshot[file6] ? (screenshot[file6].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(screenshot[file6].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),

    nameTypeExpectedOutput: new ValidationRuleModel(DataType.Object, true),
    "nameTypeExpectedOutput.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    dataSourceUsed: new ValidationRuleModel(DataType.Object, true),
    "dataSourceUsed.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    indicatorsAndVisualizations: new ValidationRuleModel(DataType.Object, true),
    "indicatorsAndVisualizations.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    toolsUsed: new ValidationRuleModel(DataType.Object, true),
    "toolsUsed.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    method: new ValidationRuleModel(DataType.Object, true),
    "method.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    frequencyOfAnalysis: new ValidationRuleModel(DataType.Object, true),
    "frequencyOfAnalysis.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    levelOfAnalysisDone: new ValidationRuleModel(DataType.Object, true),
    "levelOfAnalysisDone.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    personResponsible: new ValidationRuleModel(DataType.Object, true),
    "personResponsible.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    recipients: new ValidationRuleModel(DataType.Object, true),
    "recipients.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),

    methodOfDissemination: new ValidationRuleModel(DataType.Object, true),
    "methodOfDissemination.output1": new ValidationRuleModel(
        DataType.String,
        true,
    ),
};

export default ValidationRules;