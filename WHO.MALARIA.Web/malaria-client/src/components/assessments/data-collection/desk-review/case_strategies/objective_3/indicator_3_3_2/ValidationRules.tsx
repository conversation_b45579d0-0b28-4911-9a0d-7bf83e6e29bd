﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
    transmitMalariaVariables: new ValidationRuleModel(
        DataType.ArrayOfObject,
        true
    ),
    [`transmitMalariaVariables[${Constants.Common.IndexSubstitute}].recordedInSource`]: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.transmitMalariaVariables.some(data => data.recordedInSource === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListVariablesCount !== ${Constants.Common.RootObjectNameSubstitute}.transmitMalariaVariables.length))`
    ),
    "hasPVivaxCases": new ValidationRuleModel(DataType.Boolean, true),  
};

export const EliminationValidationRules: IValidationRuleProvider = {
   "hasMalariaInpatients": new ValidationRuleModel(DataType.Boolean, true),
};
