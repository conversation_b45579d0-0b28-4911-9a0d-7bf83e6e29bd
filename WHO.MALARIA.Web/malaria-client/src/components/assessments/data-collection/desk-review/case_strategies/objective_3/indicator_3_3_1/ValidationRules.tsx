﻿import { Constants } from '../../../../../../../models/Constants';
import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "areReportingToolsSameAsRecordingTools": new ValidationRuleModel(DataType.Boolean, false),
    "reportingTools": new ValidationRuleModel(DataType.ArrayOfObject, true),

    [`reportingTools[${Constants.Common.IndexSubstitute}].reportingToolName`]: new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].reportingToolName)`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].toolType`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].toolType) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].yearTool`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].yearTool) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].reportingHealthSystemLevel`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].reportingHealthSystemLevel) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].methodOfReporting`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].methodOfReporting) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].aggregation`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].aggregation) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].frequencyData`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].frequencyData) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].personResponsible`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].personResponsible) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].reportsRecipient`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].reportsRecipient) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].variablesReportedList`]:
        new ValidationRuleModel(DataType.String, false,
            `let index=${Constants.Common.IndexSubstitute}; index == 0 ? ${Constants.Common.RootObjectNameSubstitute}.areReportingToolsSameAsRecordingTools === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}].variablesReportedList) : false`),

    [`reportingTools[${Constants.Common.IndexSubstitute}].file`]:
        new ValidationRuleModel(DataType.String, false,
            `${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}]["file"] ? (${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}]["file"].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(${Constants.Common.RootObjectNameSubstitute}.reportingTools[${Constants.Common.IndexSubstitute}]["file"].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"),
};

export default ValidationRules;