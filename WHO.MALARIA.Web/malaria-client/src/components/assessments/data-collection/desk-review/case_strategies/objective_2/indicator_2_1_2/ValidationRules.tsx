import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const commonCondition: string = `${Constants.Common.RootObjectNameSubstitute}.healthSector.{0}.{1}.reportingMalaria === true  && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.{0}.{1}.{2})`;

const privateInformalCommunityCondition: string = `${Constants.Common.RootObjectNameSubstitute}.healthSector.{0}.reportingMalaria === true  && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.{0}.{1})`;

export const MandatedToReportValidationRuleKeys = [
  "healthSector.publicHealthSector.publicReportingDetails.mandatedToReport"
];

export const CaseBasedDataReportedValidationRuleKeys = [
  "healthSector.publicHealthSector.publicReportingDetails.caseBasedDataReported"
];

const ValidationRules: IValidationRuleProvider = {
  healthSector: new ValidationRuleModel(DataType.Object, true),
  "healthSector.publicHealthSector": new ValidationRuleModel(DataType.Object, true),
  //"healthSector.publicHealthSector.publicReportingDetails": new ValidationRuleModel(DataType.Object, true),
  "healthSector.publicHealthSector.healthFacility": new ValidationRuleModel(DataType.Object, true),
  "healthSector.publicHealthSector.hospital": new ValidationRuleModel(DataType.Object, true),
  "healthSector.publicHealthSector.laboratory": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal": new ValidationRuleModel(DataType.Object, true),
  // "healthSector.privateFormal.privateFormal": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.healthFacility": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.hospital": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.laboratory": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.faithBasedClinic": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.ngoClinic": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.military": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.police": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateFormal.prison": new ValidationRuleModel(DataType.Object, true),
  "healthSector.privateInformal": new ValidationRuleModel(DataType.Object, true),
  "healthSector.community": new ValidationRuleModel(DataType.Object, true),
  // "isOnePublicHealthSystemDescendentNotSelected":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     `${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.reportingMalaria && Object.keys(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector).filter(key => key !== 'publicReportingDetails').every(key => !${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector[key] || !${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector[key].reportingMalaria)`,
  //     "Errors.AtLeastOnePublicHealthSystemDescendentNotSelected"
  //   ),
  // "isParentPublicHealthSectorNotSelected":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     `!${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.reportingMalaria && Object.keys(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector).filter(key => key !== 'publicReportingDetails').some(key => ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector[key].reportingMalaria)`,
  //     "Errors.SelectParentPublicHealthSector"
  //   ),
  // "isOnePrivateFormalDescendentHealthSystemNotSelected":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     `${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.reportingMalaria && Object.keys(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal).filter(key => key !== 'privateFormal').every(key => !${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal[key] || !${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal[key].reportingMalaria)`,
  //     "Errors.AtLeastOnePrivateFormalDescendentHealthSystemMustBeSelected"
  //   ),
  // "isParentPrivateFormalHealthSectorNotSelected":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     `!${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.reportingMalaria && Object.keys(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal).filter(key => key !== 'privateFormal').some(key => ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal[key].reportingMalaria)`,
  //     "Errors.SelectParentPrivateFormalHealthSector"
  //   ),
  // "healthSector.publicHealthSector.publicReportingDetails.reportingMalaria":
  //   new ValidationRuleModel(
  //     DataType.Boolean,
  //     true
  //   ),
  // "healthSector.publicHealthSector.publicReportingDetails.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'publicReportingDetails').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.publicReportingDetails.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'publicReportingDetails').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.publicReportingDetails.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'publicReportingDetails').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.publicReportingDetails.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'publicReportingDetails').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.publicHealthSector.healthFacility.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // Commenting below rules as client requested.
  // "healthSector.publicHealthSector.healthFacility.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.healthFacility.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.healthFacility.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.healthFacility.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.publicHealthSector.hospital.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.publicHealthSector.hospital.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.hospital.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.hospital.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.hospital.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.publicHealthSector.laboratory.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.publicHealthSector.laboratory.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.laboratory.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.laboratory.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.publicHealthSector.laboratory.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'publicHealthSector',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.privateFormal.reportingMalaria":
  //   new ValidationRuleModel(
  //     DataType.Boolean,
  //     true
  //   ),
  // "healthSector.privateFormal.privateFormal.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'privateFormal').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.privateFormal.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'privateFormal').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.privateFormal.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'privateFormal').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.privateFormal.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'privateFormal').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.healthFacility.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.healthFacility.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.healthFacility.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.healthFacility.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.healthFacility.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'healthFacility').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.hospital.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.hospital.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.hospital.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.hospital.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.hospital.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'hospital').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.laboratory.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.laboratory.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.laboratory.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.laboratory.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.laboratory.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'laboratory').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.faithBasedClinic.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.faithBasedClinic.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'faithBasedClinic').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.faithBasedClinic.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'faithBasedClinic').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.faithBasedClinic.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'faithBasedClinic').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.faithBasedClinic.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'faithBasedClinic').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.ngoClinic.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.ngoClinic.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'ngoClinic').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.ngoClinic.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'ngoClinic').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.ngoClinic.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'ngoClinic').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.ngoClinic.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'ngoClinic').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.military.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.military.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'military').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.military.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'military').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.military.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'military').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.military.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'military').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.police.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.police.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'police').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.police.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'police').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.police.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'police').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.police.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'police').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateFormal.prison.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateFormal.prison.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'prison').replace(/\{2\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.prison.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'prison').replace(/\{2\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.prison.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'prison').replace(/\{2\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateFormal.prison.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     commonCondition.replace(/\{0\}/g, 'privateFormal',).replace(/\{1\}/g, 'prison').replace(/\{2\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.privateInformal.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.privateInformal.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'privateInformal',).replace(/\{1\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateInformal.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'privateInformal',).replace(/\{1\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateInformal.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'privateInformal',).replace(/\{1\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.privateInformal.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'privateInformal',).replace(/\{1\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.community.reportingMalaria":
    new ValidationRuleModel(
      DataType.Boolean,
      true
    ),
  // "healthSector.community.national":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'community',).replace(/\{1\}/g, 'national'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.community.subNational":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'community',).replace(/\{1\}/g, 'subNational'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.community.serviceDelivery":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'community',).replace(/\{1\}/g, 'serviceDelivery'),
  //     "Errors.MandatoryField"
  //   ),
  // "healthSector.community.notificationProcesses":
  //   new ValidationRuleModel(
  //     DataType.String,
  //     false,
  //     privateInformalCommunityCondition.replace(/\{0\}/g, 'community',).replace(/\{1\}/g, 'notificationProcesses'),
  //     "Errors.MandatoryField"
  //   ),
  "healthSector.publicHealthSector.publicReportingDetails.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.mandatedToReport === null`
    ),
  "healthSector.publicHealthSector.healthFacility.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.mandatedToReport === null`
    ),
  "healthSector.publicHealthSector.hospital.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.mandatedToReport === null`
    ),
  "healthSector.publicHealthSector.laboratory.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.mandatedToReport === null`
    ),

  "healthSector.privateFormal.privateFormal.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.mandatedToReport === null`
    ),
  "healthSector.privateFormal.healthFacility.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.mandatedToReport === null`
    ),
  "healthSector.privateFormal.hospital.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.mandatedToReport === null`
    ),
  "healthSector.privateFormal.laboratory.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.mandatedToReport === null`
    ),
  "healthSector.privateFormal.faithBasedClinic.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.mandatedToReport === null`
    ),
  "healthSector.privateFormal.ngoClinic.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.mandatedToReport === null`
    ),
  "healthSector.privateFormal.military.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.mandatedToReport === null`
    ),
  "healthSector.privateFormal.police.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.mandatedToReport === null`
    ),
  "healthSector.privateFormal.prison.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.mandatedToReport === null`
    ),
  "healthSector.privateInformal.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.mandatedToReport === null`
    ),
  "healthSector.community.mandatedToReport":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === true && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.mandatedToReport === null`
    ),
  "healthSector.publicHealthSector.publicReportingDetails.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.caseBasedDataReported === null`
    ),
  "healthSector.publicHealthSector.healthFacility.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.caseBasedDataReported === null`
    ),
  "healthSector.publicHealthSector.hospital.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.caseBasedDataReported === null`
    ),
  "healthSector.publicHealthSector.laboratory.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.privateFormal.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.healthFacility.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.hospital.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.laboratory.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.faithBasedClinic.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.ngoClinic.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.military.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.police.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.caseBasedDataReported === null`
    ),
  "healthSector.privateFormal.prison.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.caseBasedDataReported === null`
    ),
  "healthSector.privateInformal.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.caseBasedDataReported === null`
    ),
  "healthSector.community.caseBasedDataReported":
    new ValidationRuleModel(
      DataType.Boolean,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.isBurdenReductionStrategy === false && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.caseBasedDataReported === null`
    ),
};

export default ValidationRules;