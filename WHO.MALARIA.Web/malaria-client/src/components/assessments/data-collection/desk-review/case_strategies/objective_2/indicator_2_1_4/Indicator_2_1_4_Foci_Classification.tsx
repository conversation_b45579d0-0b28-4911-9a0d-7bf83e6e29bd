﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import React from "react";
import {
  Step_A_Response,
  FociClassification,
} from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_1";
import { useSelector } from "react-redux";
import parse from "html-react-parser";

type Indicator_2_1_4_FociClassificationProps = {
  step_A: Step_A_Response;
  updateStep_A: (step_A: any) => void;
  fociClassification: FociClassification;
  getMetNotMetStatus: () => void;
};

/** Renders the response for indicator 2.1.4 Foci Classification */
function Indicator_2_1_4_Foci_Classification(
  props: Indicator_2_1_4_FociClassificationProps
) {
  const { t } = useTranslation(["indicators-responses"]);
  const { fociClassification, updateStep_A, step_A, getMetNotMetStatus } = props;
  const errors = useSelector((state: any) => state.error);

  /** Excluded property that are not used in Array Map */
  const excludedProperties: Array<string> = [
    "cannotBeAssessed",
    "cannotBeAssessedReason",
  ];

  // Triggered whenever the control values are changed and update response
  const onValueChange = (
    fieldName: string,
    value: string | boolean,
    modelKeyName: string
  ) => {
    const _response = {
      ...step_A,
      fociClassification: {
        ...step_A.fociClassification,
        [modelKeyName]: {
          ...step_A.fociClassification[modelKeyName],
          [fieldName]: value,
        },
      },
    };

    updateStep_A(_response);
  };

  const headers = [
    { field: "", label: "" },
    {
      field: "WHODefinition",
      label: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:WHODefinition"
      ),
    },
    {
      field: "countryDefinition",
      label: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CountryDefinition"
      ),
    },
    {
      field: "definitionOK",
      label: parse(t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:DefinitionOK"
      )),
    },
  ];

  const columns: any = {
    active: {
      field: "active",
      labelOne: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:Active"
      ),
      labelTwo: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ActiveLabelTwo"
      ),
    },
    residualNonActive: {
      field: "residualNonActive",
      labelOne: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResidualNonActive"
      ),
      labelTwo: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResidualNonActiveLabelTwo"
      ),
    },
    cleared: {
      field: "cleared",
      labelOne: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:Cleared"
      ),
      labelTwo: t(
        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ClearedLabelTwo"
      ),
    },
  };

  return (
    <>
      <div className="response-wrapper">
        <div>
          <Table>
            <>
              <TableHeader
                headers={headers.map((header: any) => header.label)}
              />
              <TableBody>
                <>
                  {Object.keys(fociClassification)
                    .filter((key) => !excludedProperties.includes(key))
                    .map((modelKeyName: string, index: any) => (
                      <TableRow key={`row_${columns.field}_${index}`}>
                        <>
                          <TableCell>
                            <span>{columns[modelKeyName]?.labelOne}</span>
                          </TableCell>
                          <TableCell>
                            <span>{columns[modelKeyName]?.labelTwo}</span>
                          </TableCell>
                          <TableCell>
                            <TextBox
                              id={`${columns.field}_${index}_${Math.random()}`}
                              name="countryDefinition"
                              rows={2}
                              multiline
                              fullWidth
                              value={
                                fociClassification[modelKeyName]
                                  ?.countryDefinition
                              }
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) =>
                                onValueChange(
                                  e.target.name,
                                  e.currentTarget.value,
                                  modelKeyName
                                )
                              }
                              error={
                                errors[
                                `step_A.fociClassification[${modelKeyName}].countryDefinition`
                                ] &&
                                errors[
                                `step_A.fociClassification[${modelKeyName}].countryDefinition`
                                ]
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <RadioButtonGroup
                              id="definitionOK"
                              name="definitionOK"
                              row
                              color="primary"
                              options={[
                                new MultiSelectModel(
                                  true,
                                  t("indicators-responses:Common:Yes")
                                ),
                                new MultiSelectModel(
                                  false,
                                  t("indicators-responses:Common:No")
                                ),
                              ]}
                              value={
                                fociClassification[modelKeyName]?.definitionOK
                              }
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => {
                                onValueChange(
                                  e.target.name,
                                  e.currentTarget.value === "false"
                                    ? false
                                    : true,
                                  modelKeyName
                                );
                                getMetNotMetStatus();
                              }}
                              error={
                                errors[
                                `step_A.fociClassification[${modelKeyName}].definitionOK`
                                ] &&
                                errors[
                                `step_A.fociClassification[${modelKeyName}].definitionOK`
                                ]
                              }
                            />
                          </TableCell>
                        </>
                      </TableRow>
                    ))}
                </>
              </TableBody>
            </>
          </Table>
        </div>
      </div>
    </>
  );
}

export default Indicator_2_1_4_Foci_Classification;
