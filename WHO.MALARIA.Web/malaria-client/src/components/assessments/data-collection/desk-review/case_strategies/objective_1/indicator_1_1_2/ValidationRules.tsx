import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";
const currentYear = new Date().getFullYear();

const ValidationRules: IValidationRuleProvider = {
    portionOfSuspectsTestedOverTimeTab: new ValidationRuleModel(
        DataType.Object,
        true
    ),
    "portionOfSuspectsTestedOverTimeTab.values": new ValidationRuleModel(
        DataType.ArrayOfKeyValuePair,
        true
    ),
    "portionOfSuspectsTestedOverTimeTab.values.key": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.KeySubstitute} && !(${Constants.Common.KeySubstitute} >=2010 && ${Constants.Common.KeySubstitute} <=${currentYear}))`,
        "Errors.YearBetween2010ToCurrentYear"
    ),
    "portionOfSuspectsTestedOverTimeTab.values.value": new ValidationRuleModel(
        DataType.Number,
        true,
        `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100 ))`,
        "Errors.ValueBetweenZeroToHundred"
    ),

    "portionOfSuspectsTestedOverTimeTab.reasonForChangeObservedOvertime":
        new ValidationRuleModel(
            DataType.String,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.portionOfSuspectsTestedOverTimeTab.values.length > 1 && !${Constants.Common.RootObjectNameSubstitute}.portionOfSuspectsTestedOverTimeTab.reasonForChangeObservedOvertime`
        ),

    portionOfSuspectsTestedOverRegionTab: new ValidationRuleModel(
        DataType.Object,
        true
    ),
    "portionOfSuspectsTestedOverRegionTab.yearOfData": new ValidationRuleModel(
        DataType.String,
        false,
    ),
    "portionOfSuspectsTestedOverRegionTab.values": new ValidationRuleModel(
        DataType.ArrayOfKeyValuePair,
        false
    ),
    "portionOfSuspectsTestedOverRegionTab.values.key": new ValidationRuleModel(
        DataType.String,
        false
    ),
    "portionOfSuspectsTestedOverRegionTab.values.value": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100 ))`,
        "Errors.ValueBetweenOneToHundred"
    ),
};

export default ValidationRules;
