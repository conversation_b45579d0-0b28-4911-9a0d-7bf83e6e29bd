﻿import React from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Step_D_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import { useSelector } from "react-redux";

type Indicator_1_1_7_Props = {
  step_D: Step_D_Response;
  updateStep_D: (step_D: any) => void;
};

/** Renders the indicator 1.1.7 Step D response for desk review */
const Indicator_1_1_7_Step_D = (props: Indicator_1_1_7_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { updateStep_D, step_D } = props;
  const errors = useSelector((state: any) => state.error);

  // Triggers when input control's value changes
  const onValueChange = (fieldName: string, value: any) => {
    const _response = {
      ...step_D,
      [fieldName]: value,
    };
    updateStep_D(_response);
  };

  return (
    <>
      <div className="response-wrapper">
        <div className="response-content">
          <div className="row mb-3">
            <div className="col-xs-12 col-md-6">
              <div className="row mb-3">
                <div className="col-xs-12 col-md-12">
                  <label>
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:NMPHaveAccess"
                    )}
                  </label>
                  <RadioButtonGroup
                    id="molecularanalysis"
                    name="molecularanalysis"
                    row
                    color="primary"
                    options={[
                      new MultiSelectModel(
                        "H",
                        t("indicators-responses:Common:Hospital")
                      ),
                      new MultiSelectModel(
                        "C",
                        t("indicators-responses:Common:Community")
                      ),
                      new MultiSelectModel(
                        "B",
                        t("indicators-responses:Common:Both")
                      ),
                      new MultiSelectModel(
                        "N",
                        t("indicators-responses:Common:No")
                      ),
                    ]}
                    value={step_D?.isNMPRecorded}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange("isNMPRecorded", e.currentTarget.value)
                    }
                    error={
                      errors[`step_D.isNMPRecorded`] &&
                      errors[`step_D.isNMPRecorded`]
                    }
                    helperText={
                      errors[`step_D.isNMPRecorded`] &&
                      errors[`step_D.isNMPRecorded`]
                    }
                  />
                </div>
              </div>
              <div className="row">
                <div className="col-xs-12 col-md-12">
                  <TextBox
                    id="nmpDeathRecordedDetail"
                    name="nmpDeathRecordedDetail"
                    label={t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ProvideDetails"
                    )}
                    multiline
                    rows={10}
                    variant="outlined"
                    fullWidth
                    value={step_D?.nmpDeathRecordedDetail}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange(
                        "nmpDeathRecordedDetail",
                        e.currentTarget.value
                      )
                    }
                    error={
                      errors[`step_D.nmpDeathRecordedDetail`] &&
                      errors[`step_D.nmpDeathRecordedDetail`]
                    }
                    helperText={
                      errors[`step_D.nmpDeathRecordedDetail`] &&
                      errors[`step_D.nmpDeathRecordedDetail`]
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Indicator_1_1_7_Step_D;
