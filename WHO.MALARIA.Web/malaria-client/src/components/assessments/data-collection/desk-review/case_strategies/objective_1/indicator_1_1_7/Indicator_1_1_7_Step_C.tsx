﻿import React from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TableBody from "../../../responses/TableBody";
import { Step_C_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import { useSelector } from "react-redux";

type Indicator_1_1_7_Props = {
    step_C: Step_C_Response;
    updateStep_C: (step_C: any) => void;
    getMetNotMetStatus: () => void;
};

/** Renders the indicator 1.1.7 Step C response for desk review */
const Indicator_1_1_7_Step_C = (props: Indicator_1_1_7_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_C, updateStep_C, getMetNotMetStatus } = props;
    const errors = useSelector((state: any) => state.error);

    // Triggers when input control's value changes
    const onValueChange = (fieldName: string, value: any) => {
        const _response = {
            ...step_C,
            [fieldName]: value ? (value) : null,
        };

        updateStep_C(_response);
    };

    // Rows of Table
    const rows: any = {
        deathCompletenessPercentage: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:DeathRegistration"
        ),
        deathProportionPercentage: t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ProportionOfDeaths"
        ),
    };

    //Calculates death percentage and show met not-met status
    const calculateDeathPercentage = (modelKeyName: string) => {
        if (modelKeyName === "deathCompletenessPercentage") {
            return (
                <label>
                    {step_C["deathCompletenessPercentage"] as number > 89
                        ? t("indicators-responses:Common:Met")
                        : t("indicators-responses:Common:NotMet")}
                </label>
            )
        }
        else if (modelKeyName === "deathProportionPercentage") {
            return (
                <label>
                    {step_C["deathProportionPercentage"] as number <= 9 && step_C["deathProportionPercentage"] as number >= 0 && step_C["deathProportionPercentage"] as number !== null
                        ? t("indicators-responses:Common:Met")
                        : t("indicators-responses:Common:NotMet")}
                </label>
            )
        }
    }

    return (
        <>
            <div className="response-wrapper">
                <div className="response-content">
                    <div className="row mb-3">
                        <div className="col-xs-12 col-md-6">
                            <p>{t("indicators-responses:Common:IndicatePercentage")}</p>
                            <table width="60%" className="app-table">
                                <thead>
                                    <th></th>
                                    <th>
                                        <div className="fw-bold">
                                            {t("indicators-responses:Common:Percentage")}
                                        </div>
                                    </th>
                                    <th>
                                        <div className="fw-bold">
                                            {t("indicators-responses:Common:Criteria")}
                                        </div>
                                    </th>
                                </thead>
                                <TableBody>
                                    <>
                                        {Object.keys(step_C).map(
                                            (modelKeyName: string, index: any) => (
                                                <TableRow key={`row_${index}`}>
                                                    <>
                                                        <TableCell>{rows[modelKeyName]}</TableCell>
                                                        <TableCell>
                                                            <TextBox
                                                                id={modelKeyName}
                                                                name={modelKeyName}
                                                                type="number"
                                                                inputProps={{
                                                                    max: 100,
                                                                    min: 0,
                                                                    maxLength: 3,
                                                                }}
                                                                fullWidth
                                                                value={step_C[modelKeyName]}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) => {
                                                                    onValueChange(
                                                                        modelKeyName,
                                                                        e.currentTarget.value
                                                                    );
                                                                    getMetNotMetStatus();
                                                                }}
                                                                error={
                                                                    (errors[`step_C.${modelKeyName}`] &&
                                                                        errors[`step_C.${modelKeyName}`]) ||
                                                                    (errors[
                                                                        `step_C.${modelKeyName}.percentage`
                                                                    ] &&
                                                                        errors[`step_C.${modelKeyName}.percentage`])
                                                                }
                                                                helperText={
                                                                    (errors[`step_C.${modelKeyName}`] &&
                                                                        errors[`step_C.${modelKeyName}`]) ||
                                                                    (errors[
                                                                        `step_C.${modelKeyName}.percentage`
                                                                    ] &&
                                                                        errors[`step_C.${modelKeyName}.percentage`])
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            {calculateDeathPercentage(modelKeyName)}
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            )
                                        )}
                                    </>
                                </TableBody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Indicator_1_1_7_Step_C;
