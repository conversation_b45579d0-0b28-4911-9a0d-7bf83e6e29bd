﻿import { Constants } from '../../../../../../../models/Constants';
import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "jobRole": new ValidationRuleModel(DataType.String, true),
    "surveillanceStaffPerceivedAbilities": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`surveillanceStaffPerceivedAbilities[${Constants.Common.IndexSubstitute}].completionDifficulty`]:
        new ValidationRuleModel(DataType.Boolean, true),
    [`surveillanceStaffPerceivedAbilities[${Constants.Common.IndexSubstitute}].description`]: new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffPerceivedAbilities[${Constants.Common.IndexSubstitute}].completionDifficulty === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffPerceivedAbilities[${Constants.Common.IndexSubstitute}].description)`),

};

export default ValidationRules;
