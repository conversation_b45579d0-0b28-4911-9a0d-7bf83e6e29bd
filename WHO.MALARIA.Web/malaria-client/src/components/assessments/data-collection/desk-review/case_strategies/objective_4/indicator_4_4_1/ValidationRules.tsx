﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
    hasTraining: new ValidationRuleModel(DataType.Boolean, false),

    dataCollection: new ValidationRuleModel(DataType.Object, true),
    "dataCollection.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.national === null`
    ),
    "dataCollection.subNational": new ValidationRuleModel(
        DataType.Bo<PERSON>an,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.subNational === null`
    ),
    "dataCollection.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.serviceDeliveryLevel === null`
    ),

    dataReporting: new ValidationRuleModel(DataType.Object, true),
    "dataReporting.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.national === null`
    ),
    "dataReporting.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.subNational === null`
    ),
    "dataReporting.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.serviceDeliveryLevel === null`
    ),

    conductingDataQualityReview: new ValidationRuleModel(DataType.Object, true),
    "conductingDataQualityReview.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataQualityReview.national === null`
    ),
    "conductingDataQualityReview.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataQualityReview.subNational === null`
    ),
    "conductingDataQualityReview.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataQualityReview.serviceDeliveryLevel === null`
    ),

    conductingDataAnalysis: new ValidationRuleModel(DataType.Object, true),
    "conductingDataAnalysis.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataAnalysis.national === null`
    ),
    "conductingDataAnalysis.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataAnalysis.subNational === null`
    ),
    "conductingDataAnalysis.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.conductingDataAnalysis.serviceDeliveryLevel === null`
    ),

    preparingDisseminationReports: new ValidationRuleModel(DataType.Object, true),
    "preparingDisseminationReports.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.preparingDisseminationReports.national === null`
    ),
    "preparingDisseminationReports.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.preparingDisseminationReports.subNational === null`
    ),
    "preparingDisseminationReports.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.preparingDisseminationReports.serviceDeliveryLevel === null`
    ),

    supervision: new ValidationRuleModel(DataType.Object, true),
    "supervision.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.national === null`
    ),
    "supervision.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.subNational === null`
    ),
    "supervision.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.serviceDeliveryLevel === null`
    ),

    trainingInPublicPrivateSectors: new ValidationRuleModel(
        DataType.Object,
        true
    ),
    "trainingInPublicPrivateSectors.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingInPublicPrivateSectors.national === null`
    ),
    "trainingInPublicPrivateSectors.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingInPublicPrivateSectors.subNational === null`
    ),
    "trainingInPublicPrivateSectors.serviceDeliveryLevel":
        new ValidationRuleModel(
            DataType.Boolean,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingInPublicPrivateSectors.serviceDeliveryLevel === null`
        ),

    plannedTraining: new ValidationRuleModel(DataType.Object, true),
    "plannedTraining.national": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.national === null`
    ),
    "plannedTraining.subNational": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.subNational === null`
    ),
    "plannedTraining.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.serviceDeliveryLevel === null`
    ),
    previousYearTraining: new ValidationRuleModel(DataType.Object, true),
    "previousYearTraining.national": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.national === null`
    ),
    "previousYearTraining.subNational": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.subNational === null`
    ),
    "previousYearTraining.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.serviceDeliveryLevel === null`
    ),
    "nationalTrainingProportion": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.national / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.national)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.national / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.national)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),
    "subNationalTrainingProportion": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.subNational / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.subNational)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.subNational / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.subNational)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),
    "serviceDeliveryTrainingProportion": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.serviceDeliveryLevel / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.serviceDeliveryLevel)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.previousYearTraining.serviceDeliveryLevel / ${Constants.Common.RootObjectNameSubstitute}.plannedTraining.serviceDeliveryLevel)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),
};

export const EliminationValidationRules: IValidationRuleProvider = {
    caseNotification: new ValidationRuleModel(DataType.Object, true),
    "caseNotification.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseNotification.national === null`
    ),
    "caseNotification.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseNotification.subNational === null`
    ),
    "caseNotification.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseNotification.serviceDeliveryLevel === null`
    ),

    caseInvestigation: new ValidationRuleModel(DataType.Object, true),
    "caseInvestigation.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.national === null`
    ),
    "caseInvestigation.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.subNational === null`
    ),
    "caseInvestigation.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.serviceDeliveryLevel === null`
    ),

    caseClassification: new ValidationRuleModel(DataType.Object, true),
    "caseClassification.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseClassification.national === null`
    ),
    "caseClassification.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseClassification.subNational === null`
    ),
    "caseClassification.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.caseClassification.serviceDeliveryLevel === null`
    ),

    fociInvestigation: new ValidationRuleModel(DataType.Object, true),
    "fociInvestigation.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociInvestigation.national === null`
    ),
    "fociInvestigation.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociInvestigation.subNational === null`
    ),
    "fociInvestigation.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociInvestigation.serviceDeliveryLevel === null`
    ),

    fociClassification: new ValidationRuleModel(DataType.Object, true),
    "fociClassification.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociClassification.national === null`
    ),
    "fociClassification.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociClassification.subNational === null`
    ),
    "fociClassification.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.fociClassification.serviceDeliveryLevel === null`
    ),

    qualityAssuranceOfLabData: new ValidationRuleModel(DataType.Object, true),
    "qualityAssuranceOfLabData.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.qualityAssuranceOfLabData.national === null`
    ),
    "qualityAssuranceOfLabData.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.qualityAssuranceOfLabData.subNational === null`
    ),
    "qualityAssuranceOfLabData.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.qualityAssuranceOfLabData.serviceDeliveryLevel === null`
    ),

    trainingForMicroscopy: new ValidationRuleModel(DataType.Object, true),
    "trainingForMicroscopy.national": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingForMicroscopy.national === null`
    ),
    "trainingForMicroscopy.subNational": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingForMicroscopy.subNational === null`
    ),
    "trainingForMicroscopy.serviceDeliveryLevel": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.trainingForMicroscopy.serviceDeliveryLevel === null`
    ),
};
