import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  nationalData: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.nationalData !==null && !(${Constants.Common.RootObjectNameSubstitute}.nationalData >=1 && ${Constants.Common.RootObjectNameSubstitute}.nationalData <=100)`,
    "Errors.ValueBetweenOneToHundred"
  ),

  publicData: new ValidationRuleModel(
    DataType.Number,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.publicData !==null && !(${Constants.Common.RootObjectNameSubstitute}.publicData >=1 && ${Constants.Common.RootObjectNameSubstitute}.publicData <=100)`,
    "Errors.ValueBetweenOneToHundred"
  ),

  privateData: new ValidationRuleModel(
    DataType.Number,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.privateData !==null && !(${Constants.Common.RootObjectNameSubstitute}.privateData >=1 && ${Constants.Common.RootObjectNameSubstitute}.privateData <=100)`,
    "Errors.ValueBetweenOneToHundred"
  ),
};

export default ValidationRules;
