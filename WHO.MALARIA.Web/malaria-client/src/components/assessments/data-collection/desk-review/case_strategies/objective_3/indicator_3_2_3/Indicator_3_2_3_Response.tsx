import React, { useEffect, useRef, ChangeEvent, useState } from "react";
import { useTranslation } from "react-i18next";
import {
    Response_1,
    StandardizedRecordingTool,
    StandardizedFormsDetail,
} from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_3/Response_1";
import { NumberOfRecordingForm } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_1/Response_1";
import Checkbox from "../../../../../../controls/Checkbox";
import TextBox from "../../../../../../controls/TextBox";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import UserMessage from "../../../../../../common/UserMessage";
import useFormValidation from "../../../../../../common/useFormValidation";
import { CommonValidationRules, EliminationValidationRules } from "./ValidationRules";
import { useSelector } from "react-redux";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum, StrategiesEnum } from "../../../../../../../models/Enums";
import { useLocation } from "react-router-dom";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import Indicator_3_2_3_Elimination_Response from "./Indicator_3_2_3_Elimination_Response"
import Indicator_3_2_3_Recording_Tools from "./Indicator_3_2_3_Recording_Tools";

/** Renders the response indicator 3.2.3 */
function Indicator_3_2_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_2_3_Title");
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const [hasParentDataAssessed, setHasParentDataAssessed] = useState<boolean>(false);

    const rows: any = {
        recordingName: {
            field: "recordingName",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecordingName"
            ),
        },
        isStandardizedRecordingTools: {
            field: "recodringFormsAndTools",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecodringFormsAndTools"
            ),
        },
        standardizedFormsDetails: {
            field: "detailsOnPrivatePublicUseStandardisedForm",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:DetailsOnPrivatePublicUseStandardisedForm"
            ),
        },
    };

    let ValidationRules: IValidationRuleProvider;
    const validationRulesRef = useRef<IValidationRuleProvider>({ ...CommonValidationRules });

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getParentResponse,
        onChangeWithKeyInArray,
        onChangeWithKey,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);
    const eliminationResponseWithoutParentData = !response.cannotBeAssessed && hasParentDataAssessed === false;

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    useEffect(() => {
        const hasParentData: boolean = response?.parentData && response?.parentData?.recordingTools[0]?.nameOfReportingToolSourceDocument ? true : false;
        setHasParentDataAssessed(hasParentData);

        if (hasParentData === false &&
            strategyId === StrategiesEnum.Elimination.toLowerCase()) {
            ValidationRules = { ...EliminationValidationRules }
        } else {
            ValidationRules = { ...CommonValidationRules };
        }

        validationRulesRef.current = ValidationRules;

        if (hasParentData === true) {
            const recordingToolCount = response.parentData.recordingTools.length;
            onValueChange(
                "parentRecordingToolCount",
                recordingToolCount
            );
        }
    }, [response?.parentData]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    //Process data on save and calls processDataAndSave method based on the isFinalized flag
    const onResponseSave = () => {
        processDataAndSave(false)
    }

    //Process data on finalize and calls processDataAndSave method based on the isFinalized flag
    const onResponseFinalize = () => {
        processDataAndSave(true)
    }

    //Process data set the date control value then save or finalize
    const processDataAndSave = (isFinalized: boolean) => {
        let _response = response;
        response.standardizedRecordingTools
            .filter(
                (standardizedRecordingTool: StandardizedRecordingTool) =>
                    !response?.parentData?.recordingTools.some(
                        (numberOfRecordingForm: NumberOfRecordingForm) => {
                            return (
                                numberOfRecordingForm.recordingToolId ===
                                standardizedRecordingTool.recordingToolId
                            );
                        }
                    )
            )
            .forEach((standardizedRecordingTool: StandardizedRecordingTool) => {
                _response.standardizedRecordingTools =
                    _response.standardizedRecordingTools.filter(
                        (srTool: StandardizedRecordingTool) =>
                            srTool.recordingToolId !==
                            standardizedRecordingTool.recordingToolId
                    );
                _response.standardizedFormsDetails =
                    _response.standardizedFormsDetails.filter(
                        (sfDetail: StandardizedFormsDetail) =>
                            standardizedRecordingTool.recordingToolId !==
                            sfDetail.recordingToolId
                    );
            });

        onValueChange(
            "standardizedRecordingTools",
            _response.standardizedRecordingTools
        );
        onValueChange(
            "standardizedFormsDetails",
            _response.standardizedFormsDetails
        );

        // triggers on click of finalize button, performs validations and then action is performed
        if (isFinalized) {
            setTrueFlagOnFinalizeButtonClick();
            const isFormValid = validate(response);
            if (isFormValid) onFinalize();
        } else {
            onSave();
        }
    };

    useEffect(() => {
        getParentResponse();
    }, []);

    // Get control value with recording tool id and control name for different controls(Textbox and Radio)
    const generateControlValue = (
        recordingToolId: string,
        controlName: string,
        controlDataArrayName: string
    ) => {
        let value: string | null | boolean = null;
        if (
            response[controlDataArrayName].find(
                (x: any) => x.recordingToolId === recordingToolId
            ) !== undefined &&
            response[controlDataArrayName].find(
                (x: any) => x.recordingToolId === recordingToolId
            )[controlName] !== undefined
        ) {
            value = response[controlDataArrayName].find(
                (x: any) => x.recordingToolId === recordingToolId
            )[controlName];
        }
        return value;
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const hasReportingToolSourceDocument = response?.standardizedRecordingTools.every((data: StandardizedRecordingTool) => data.areStandardizedAcrossAllService);

        let areRecordingToolsStandardizedAcrossAllService: boolean = false;
        if (response?.recordingTool1.areStandardizedAcrossAllService === false || response?.recordingTool2.areStandardizedAcrossAllService === false ||
            response?.recordingTool3.areStandardizedAcrossAllService === false ||
            response?.recordingTool4.areStandardizedAcrossAllService === false) {
            areRecordingToolsStandardizedAcrossAllService = false;
        } else if (response?.recordingTool1.areStandardizedAcrossAllService === true || response?.recordingTool2.areStandardizedAcrossAllService === true ||
            response?.recordingTool3.areStandardizedAcrossAllService === true ||
            response?.recordingTool4.areStandardizedAcrossAllService === true) {
            areRecordingToolsStandardizedAcrossAllService = true;
        }

        const hasMet = hasParentDataAssessed ? hasReportingToolSourceDocument : areRecordingToolsStandardizedAcrossAllService

        onValueChange(
            "metNotMetStatus",
            hasMet ? MetNotMetEnum.Met : MetNotMetEnum.NotMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [response?.standardizedRecordingTools, response?.recordingTool1, response?.recordingTool2, response?.recordingTool3, response?.recordingTool4, hasParentDataAssessed]);

    // renders the response for the elimination strategy if parent data from indicator response 3.2.1 screen isn't assessed 
    const renderEliminationResponse = () => {
        if (eliminationResponseWithoutParentData && strategyId === StrategiesEnum.Elimination.toLowerCase()) {
            return <>
                {
                    //Show error message for atleast one of the recording tool are selected for health facility level
                    errors["recordingTool1.sourceDocumentName"] && (
                        <span className="Mui-error d-flex mb-2">
                            *
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:ResponseErrorElimination"
                            )}
                        </span>
                    )
                }

                <Indicator_3_2_3_Elimination_Response
                    rows={rows}
                    response={response}
                    onChangeWithKey={onChangeWithKey}
                />
            </>
        }
        else if (eliminationResponseWithoutParentData) {
            return <UserMessage
                renderContent={
                    <>
                        <b>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:NoRecordingToolFound")}</b>
                        <p>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:IndicatorParentDependentMessage")}</p>
                    </>
                }
            />
        }
    }

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:MetNotMetTooltip"
                )}
            />

            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed && hasParentDataAssessed ? (
                <Indicator_3_2_3_Recording_Tools
                    rows={rows}
                    response={response}
                    generateControlValue={generateControlValue}
                    onChangeWithKeyInArray={onChangeWithKeyInArray}
                />
            ) : (
                <div className="response-wrapper d-flex w-100 flex-column">
                    {renderEliminationResponse()}

                    {response.cannotBeAssessed &&
                        <TextBox
                            id="cannotBeAssessedReason"
                            name="cannotBeAssessedReason"
                            label={t(
                                "indicators-responses:Common:IndicatorNoAssessReasons"
                            )}
                            multiline
                            rows={10}
                            variant="outlined"
                            fullWidth
                            value={response?.cannotBeAssessedReason}
                            onChange={onChange}
                            error={
                                errors["cannotBeAssessedReason"] &&
                                errors["cannotBeAssessedReason"]
                            }
                            helperText={
                                errors["cannotBeAssessedReason"] &&
                                errors["cannotBeAssessedReason"]
                            }
                        />
                    }
                </div>
            )}
            <SaveFinalizeButton onSave={onResponseSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_2_3_Response;