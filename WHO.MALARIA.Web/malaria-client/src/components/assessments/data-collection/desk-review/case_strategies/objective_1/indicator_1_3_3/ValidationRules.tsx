import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    nationalLevel: new ValidationRuleModel(DataType.Object, false),

    "nationalLevel.noOfMeetingsOccured": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsOccured !==null && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsOccured >=0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsOccured <=100)`,
        "Errors.ValueBetweenZeroToHundred"
    ),

    "nationalLevel.noOfMeetingsExpected": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsExpected !=="" && (!${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsExpected > 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsOccured !==0)`,
        "Errors.DenominatorGreaterThanZero"
    ),

    regionalLevel: new ValidationRuleModel(DataType.Object, false),
    "regionalLevel.noOfMeetingsOccured": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsOccured !==null && (${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsOccured <=0 && ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsOccured >=100)`
    ),

    "regionalLevel.noOfMeetingsExpected": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsExpected !=="" && (!${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsExpected > 0 && ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsOccured !==0)`,
        "Errors.DenominatorGreaterThanZero"
    ),

    districtLevel: new ValidationRuleModel(DataType.Object, false),
    "districtLevel.noOfMeetingsOccured": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsOccured !== null && (${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsOccured <=0 && ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsOccured >=100)`
    ),

    "districtLevel.noOfMeetingsExpected": new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsExpected !== "" && (!${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsExpected > 0 && ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsOccured !==0)`,
        "Errors.DenominatorGreaterThanZero"
    ),

    healthSystemLevelValidationRuleKey: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsOccured === null && ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMeetingsExpected === null) && (${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsOccured === null && ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMeetingsExpected === null) && (${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsOccured === null && ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMeetingsExpected === null)`
    ),
};

export default ValidationRules;
