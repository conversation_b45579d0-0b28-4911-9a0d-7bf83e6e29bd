﻿import React from "react";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { Step_A_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import { useSelector } from "react-redux";

type Indicator_1_1_7_Props = {
  step_A: Step_A_Response;
  updateStep_A: (step_A: any) => void;
};

/** Renders the indicator 1.1.7 step A response for desk review */
const Indicator_1_1_7_Step_A = (props: Indicator_1_1_7_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { step_A, updateStep_A } = props;
  const errors = useSelector((state: any) => state.error);

  // Triggers when input control's value changes
  const onValueChange = (fieldName: string, value: any) => {
    const _response = {
      ...step_A,
      [fieldName]: value,
    };

    updateStep_A(_response);
  };

  return (
    <>
      <div className="response-wrapper">
        <div className="response-content">
          <div className="row mb-3">
            <div className="col-xs-12 col-md-6">
              <div className="radio-wrapper">
                {/*Show error message if hasTESBeenCarriedOut is selected NO*/}
                {errors["step_A.isVitalRegistration"] && (
                  <span className="Mui-error d-flex mb-2">
                    *
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseError"
                    )}
                  </span>
                )}
                <label>
                  {t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseDescA"
                  )}
                </label>
                <RadioButtonGroup
                  id="isVitalRegistration"
                  name="isVitalRegistration"
                  row
                  color="primary"
                  options={[
                    new MultiSelectModel(
                      true,
                      t("indicators-responses:Common:Yes")
                    ),
                    new MultiSelectModel(
                      false,
                      t("indicators-responses:Common:No")
                    ),
                  ]}
                  value={step_A?.isVitalRegistration}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onValueChange(
                      "isVitalRegistration",
                      e.currentTarget.value === "false" ? false : true
                    )
                  }
                />
                <div className="row g-0">
                  <div className="col-xs-12 col-md-6 pe-5">
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseARadioActionYes"
                    )}
                  </div>

                  <div className="col-xs-12 col-md-6">
                    {t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseARadioActionNo"
                    )}
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-xs-12 col-md-12">
                  <TextBox
                    id="vitalRegistrationDetail"
                    name="vitalRegistrationDetail"
                    label={t(
                      "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ProvideDetailsOfExistingSystem"
                    )}
                    multiline
                    rows={10}
                    variant="outlined"
                    fullWidth
                    value={step_A?.vitalRegistrationDetail || ""}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      onValueChange(
                        "vitalRegistrationDetail",
                        e.currentTarget.value
                      )
                    }
                    error={
                      errors["step_A.vitalRegistrationDetail"] &&
                      errors["step_A.vitalRegistrationDetail"]
                    }
                    helperText={
                      errors["step_A.vitalRegistrationDetail"] &&
                      errors["step_A.vitalRegistrationDetail"]
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Indicator_1_1_7_Step_A;
