import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
  guideline1: new ValidationRuleModel(DataType.Object, true),
  "guideline1.name": new ValidationRuleModel(DataType.String, true),
  "guideline1.publicationDate": new ValidationRuleModel(DataType.String, true),
  guideline2: new ValidationRuleModel(DataType.Object, false),
  /****************************************************************/

  malariaCase: new ValidationRuleModel(DataType.Object, true),
  "malariaCase.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "malariaCase.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "malariaCase.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,   
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate) && ${Constants.Common.RootObjectNameSubstitute}.malariaCase.isSecondChecklist === null`       
  ),
  "malariaCase.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCase.secondCheckListDetails)`
  ),

  "malariaCase.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaCase.isThirdChecklist === null`
  ),
  "malariaCase.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCase.thirdCheckListDetails)`
  ),

  "malariaCase.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaCase.isFourthChecklist === null`
  ),
  "malariaCase.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCase.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  proceduralGuideLine: new ValidationRuleModel(DataType.Object, true),
  "proceduralGuideLine.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "proceduralGuideLine.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "proceduralGuideLine.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.isSecondChecklist === null`
  ),
  "proceduralGuideLine.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.secondCheckListDetails)`
  ),

  "proceduralGuideLine.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.isThirdChecklist === null`
  ),
  "proceduralGuideLine.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.thirdCheckListDetails)`
  ),

  "proceduralGuideLine.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.isFourthChecklist === null`
  ),
  "proceduralGuideLine.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.proceduralGuideLine.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  dataSecurityStatement: new ValidationRuleModel(DataType.Object, true),
  "dataSecurityStatement.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "dataSecurityStatement.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "dataSecurityStatement.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.isSecondChecklist === null`
  ),
  "dataSecurityStatement.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.secondCheckListDetails)`
  ),

  "dataSecurityStatement.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.isThirdChecklist === null`
  ),
  "dataSecurityStatement.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.thirdCheckListDetails)`
  ),

  "dataSecurityStatement.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.isFourthChecklist === null`
  ),
  "dataSecurityStatement.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.dataSecurityStatement.fourthCheckListDetails)`
  ),
  }

  export const EliminationValidationRules: IValidationRuleProvider = {
  /**************************************************************************/
  nonCompliance: new ValidationRuleModel(DataType.Object, true),
  "nonCompliance.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "nonCompliance.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "nonCompliance.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.nonCompliance.isSecondChecklist === null`
  ),
  "nonCompliance.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.nonCompliance.secondCheckListDetails)`
  ),

  "nonCompliance.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.nonCompliance.isThirdChecklist === null`
  ),
  "nonCompliance.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.nonCompliance.thirdCheckListDetails)`
  ),

  "nonCompliance.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.nonCompliance.isFourthChecklist === null`
  ),
  "nonCompliance.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.nonCompliance.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  highRiskPopulation: new ValidationRuleModel(DataType.Object, true),
  "highRiskPopulation.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "highRiskPopulation.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "highRiskPopulation.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.isSecondChecklist === null`
  ),
  "highRiskPopulation.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.secondCheckListDetails)`
  ),

  "highRiskPopulation.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.isThirdChecklist === null`
  ),
  "highRiskPopulation.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.thirdCheckListDetails)`
  ),

  "highRiskPopulation.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.isFourthChecklist === null`
  ),
  "highRiskPopulation.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.highRiskPopulation.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  caseInvestigation: new ValidationRuleModel(DataType.Object, true),
  "caseInvestigation.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "caseInvestigation.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "caseInvestigation.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.isSecondChecklist === null`
  ),
  "caseInvestigation.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.secondCheckListDetails)`
  ),

  "caseInvestigation.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.isThirdChecklist === null`
  ),
  "caseInvestigation.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.thirdCheckListDetails)`
  ),

  "caseInvestigation.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.isFourthChecklist === null`
  ),
  "caseInvestigation.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseInvestigation.fourthCheckListDetails)`
  ),
  /**************************************************************************/
  caseAcquiredLocally: new ValidationRuleModel(DataType.Object, true),
  "caseAcquiredLocally.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "caseAcquiredLocally.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "caseAcquiredLocally.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.isSecondChecklist === null`
  ),
  "caseAcquiredLocally.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.secondCheckListDetails)`
  ),

  "caseAcquiredLocally.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.isThirdChecklist === null`
  ),
  "caseAcquiredLocally.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.thirdCheckListDetails)`
  ),

  "caseAcquiredLocally.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.isFourthChecklist === null`
  ),
  "caseAcquiredLocally.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.caseAcquiredLocally.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  additionalInvestigation: new ValidationRuleModel(DataType.Object, true),
  "additionalInvestigation.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "additionalInvestigation.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "additionalInvestigation.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.isSecondChecklist === null`
  ),
  "additionalInvestigation.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.secondCheckListDetails)`
  ),

  "additionalInvestigation.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.isThirdChecklist === null`
  ),
  "additionalInvestigation.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.thirdCheckListDetails)`
  ),

  "additionalInvestigation.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.isFourthChecklist === null`
  ),
  "additionalInvestigation.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.additionalInvestigation.fourthCheckListDetails)`
  ),
  /**************************************************************************/
  malariaOutbreak: new ValidationRuleModel(DataType.Object, true),
  "malariaOutbreak.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "malariaOutbreak.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "malariaOutbreak.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.isSecondChecklist === null`
  ),
  "malariaOutbreak.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.secondCheckListDetails)`
  ),

  "malariaOutbreak.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.isThirdChecklist === null`
  ),
  "malariaOutbreak.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.thirdCheckListDetails)`
  ),

  "malariaOutbreak.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.isFourthChecklist === null`
  ),
  "malariaOutbreak.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaOutbreak.fourthCheckListDetails)`
  ),

  /**************************************************************************/
  malariaCasesAnnuallyReported: new ValidationRuleModel(DataType.Object, true),
  "malariaCasesAnnuallyReported.isFirstChecklist": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "malariaCasesAnnuallyReported.firstCheckListDetails": new ValidationRuleModel(
    DataType.String,
    true
  ),

  "malariaCasesAnnuallyReported.isSecondChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.isSecondChecklist === null`
  ),
  "malariaCasesAnnuallyReported.secondCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline2.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.secondCheckListDetails)`
  ),

  "malariaCasesAnnuallyReported.isThirdChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.isThirdChecklist === null`
  ),
  "malariaCasesAnnuallyReported.thirdCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline3.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.thirdCheckListDetails)`
  ),

  "malariaCasesAnnuallyReported.isFourthChecklist": new ValidationRuleModel(
    DataType.Boolean,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && ${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.isFourthChecklist === null`
  ),
  "malariaCasesAnnuallyReported.fourthCheckListDetails": new ValidationRuleModel(
    DataType.String,
    false,
    `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.name) && ${Constants.Common.RootObjectNameSubstitute}.guideline4.publicationDate && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.malariaCasesAnnuallyReported.fourthCheckListDetails)`
  ),
  /**************************************************************************/
};


