﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { useSelector } from "react-redux";

/** Response for indicator 2.2.2 visualization capacities attribute tab */
const VisualizationCapacitiesAttribute = (props: any) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { visualizationCapacitiesResponse, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsVisualizationCapacities"
                    )}
                </label>
                <RadioButtonGroup
                    id="areCapabilitiesPresent"
                    name="areCapabilitiesPresent"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={visualizationCapacitiesResponse?.areCapabilitiesPresent}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "visualizationCapacitiesResponse"
                        )
                    }
                    error={
                        errors["step_A.visualizationCapacitiesResponse.areCapabilitiesPresent"] &&
                        errors["step_A.visualizationCapacitiesResponse.areCapabilitiesPresent"]
                    }
                    helperText={
                        errors["step_A.visualizationCapacitiesResponse.areCapabilitiesPresent"] &&
                        errors["step_A.visualizationCapacitiesResponse.areCapabilitiesPresent"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explain"
                        name="explain"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        InputLabelProps={{
                            shrink: true,
                        }}
                        rows={3}
                        value={visualizationCapacitiesResponse?.explain}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "visualizationCapacitiesResponse"
                            )
                        }
                        error={
                            errors["step_A.visualizationCapacitiesResponse.explain"] &&
                            errors["step_A.visualizationCapacitiesResponse.explain"]
                        }
                        helperText={
                            errors["step_A.visualizationCapacitiesResponse.explain"] &&
                            errors["step_A.visualizationCapacitiesResponse.explain"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="dashboardInterface"
                        name="dashboardInterface"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacitiesProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacitiesPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={visualizationCapacitiesResponse?.dashboardInterface}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "visualizationCapacitiesResponse"
                            )
                        }
                        error={
                            errors["step_A.visualizationCapacitiesResponse.dashboardInterface"] &&
                            errors["step_A.visualizationCapacitiesResponse.dashboardInterface"]
                        }
                        helperText={
                            errors["step_A.visualizationCapacitiesResponse.dashboardInterface"] &&
                            errors["step_A.visualizationCapacitiesResponse.dashboardInterface"]
                        }
                    />
                </div>
            </div>
        </>
    );
};

export default VisualizationCapacitiesAttribute;
