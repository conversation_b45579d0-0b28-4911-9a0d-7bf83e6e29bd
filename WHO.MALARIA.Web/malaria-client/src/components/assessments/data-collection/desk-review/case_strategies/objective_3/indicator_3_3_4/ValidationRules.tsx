﻿import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {   
    "reportingFrequencyCriteriaMet": new ValidationRuleModel(DataType.Boolean, true),
    "aggregationOfReportedDataCriteriaMet": new ValidationRuleModel(DataType.Boolean, true),
    "reportingOfZeroCasesCriteriaMet": new ValidationRuleModel(DataType.Boolean, true)  
}

export default ValidationRules;