import React from "react";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import useCalculation from "../../../responses/useCalculation";
import { useSelector } from "react-redux";

type MicroscopyResponse_Props = {
    response: any;
    updateStep_B: (step_B: any) => void;
};

/** Renders response for Indication 3.1.2 microscopy response*/
const MicroscopyResponse = (props: MicroscopyResponse_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { response, updateStep_B } = props;
    const { step_B } = response;
    const {
        healthFacilitiesWithStockOut,
        healthFacilitiesReporting,
        provideReason,
    } = step_B;
    const errors = useSelector((state: any) => state.error);
    const { calculatePercentage } = useCalculation();

    //Triggers on value change of input controls and updates the step b
    const onValueChange = (fieldName: string, value: string | number) => {
        updateStep_B({ ...step_B, [fieldName]: value ? Math.round(+value) : value === 0 ? 0 : null });
    };

    const rows = [
        {
            field: "healthFacilityWithStocksOut",
            label: t("indicators-responses:Common:HealthFacilitiesWithStockOuts"),
        },
        {
            field: "healthFacilitiesReporting",
            label: t("indicators-responses:Common:HealthFacilitiesReporting"),
        },
        {
            field: "healthFacilitiesWithStockOuts",
            label: t(
                "indicators-responses:Common:ProportionHealthFacilitiesWithStockOuts"
            ),
        }
    ];

    return (
        <>
            <div className="row mt-3 mb-3">
                <div className="col-md-12">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_1_2:MicroscopyResponseDesc"
                    )}
                </div>
            </div>
            <Table width="100%">
                <>
                    <TableHeader headers={rows.map((row: any) => row.label)} />
                    <TableBody>
                        <>
                            <TableRow>
                                <>
                                    <TableCell>
                                        <TextBox
                                            id="healthFacilitiesWithStockOut"
                                            name="healthFacilitiesWithStockOut"
                                            type="number"
                                            fullWidth
                                            value={healthFacilitiesWithStockOut}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onValueChange(
                                                    "healthFacilitiesWithStockOut",
                                                    e.currentTarget.value
                                                )
                                            }
                                            error={
                                                errors["step_B.healthFacilitiesWithStockOut"] &&
                                                errors["step_B.healthFacilitiesWithStockOut"]
                                            }
                                            helperText={
                                                errors["step_B.healthFacilitiesWithStockOut"] &&
                                                errors["step_B.healthFacilitiesWithStockOut"]
                                            }
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <TextBox
                                            id="healthFacilitiesReporting"
                                            name="healthFacilitiesReporting"
                                            type="number"
                                            fullWidth
                                            value={healthFacilitiesReporting}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                onValueChange(
                                                    "healthFacilitiesReporting",
                                                    e.currentTarget.value
                                                )
                                            }
                                            error={
                                                errors["step_B.healthFacilitiesReporting"] &&
                                                errors["step_B.healthFacilitiesReporting"]
                                            }
                                            helperText={
                                                errors["step_B.healthFacilitiesReporting"] &&
                                                errors["step_B.healthFacilitiesReporting"]
                                            }
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <label>                                            
                                            {errors["step_B.proportionRate"] ? <span className="Mui-error d-flex">{errors["step_B.proportionRate"]}</span>
                                                : `${calculatePercentage(
                                                    healthFacilitiesWithStockOut,
                                                    healthFacilitiesReporting
                                                )}%`}
                                        </label>
                                    </TableCell>
                                </>
                            </TableRow>
                        </>
                    </TableBody>
                </>
            </Table>
        </>
    );
};

export default MicroscopyResponse;