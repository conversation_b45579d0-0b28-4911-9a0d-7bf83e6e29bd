import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const currentYear = new Date().getFullYear();

export const CommonValidationRules: IValidationRuleProvider = {
  hasTESBeenCarriedOut: new ValidationRuleModel(
    DataType.Boolean,
    false,
    `${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false`
  ),

  noOfSentinelSites: new ValidationRuleModel(
    DataType.Number,
    true,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && !(${Constants.Common.RootObjectNameSubstitute}.noOfSentinelSites >=0 && ${Constants.Common.RootObjectNameSubstitute}.noOfSentinelSites <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),

  year: new ValidationRuleModel(
    DataType.Number,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false &&(!${Constants.Common.RootObjectNameSubstitute}.year || !(${Constants.Common.RootObjectNameSubstitute}.year >=2010 && ${Constants.Common.RootObjectNameSubstitute}.year <=${currentYear}))`,
    "Errors.YearBetween2010ToCurrentYear"
  ),
  summaryDetails: new ValidationRuleModel(
    DataType.String,
    false,
    `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.summaryDetails)`
  ),
  treatmentFailure: new ValidationRuleModel(DataType.ArrayOfObject, false),
  [`treatmentFailure[${Constants.Common.IndexSubstitute}].drugName`]:
    new ValidationRuleModel(
      DataType.String,
      true,
      `!${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].drugName)`
    ),
  [`treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay28Percent`]:
    new ValidationRuleModel(
      DataType.Number,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === true && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay28Percent !==null && !(${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay28Percent >=0 && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay28Percent <=100)`,
      "Errors.ValueBetweenZeroToHundred"
    ),

  [`treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay42Percent`]:
    new ValidationRuleModel(
      DataType.Number,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === true && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay42Percent !==null && !(${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay42Percent >=0 && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].treatmentFailureAtDay42Percent <=100)`,
      "Errors.ValueBetweenZeroToHundred"
    ),
};

export const EliminationValidationRule: IValidationRuleProvider = {
  [`treatmentFailure[${Constants.Common.IndexSubstitute}].patientsPositiveOnDay3Percent`]:
    new ValidationRuleModel(
      DataType.Number,
      true,
      `${Constants.Common.RootObjectNameSubstitute}.hasTESBeenCarriedOut === true && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].patientsPositiveOnDay3Percent !==null && !(${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].patientsPositiveOnDay3Percent >=0 && ${Constants.Common.RootObjectNameSubstitute}.treatmentFailure[${Constants.Common.IndexSubstitute}].patientsPositiveOnDay3Percent <=100)`,
      "Errors.ValueBetweenZeroToHundred"
    ),
};
