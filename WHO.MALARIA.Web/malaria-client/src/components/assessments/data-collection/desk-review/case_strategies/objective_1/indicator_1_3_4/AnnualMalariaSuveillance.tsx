import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

type Indicator_1_3_4_Props = {
    response: any;
    updateTab: (tabKey: any, response: any) => void;
    getMetNotMetStatus: () => void;
};

/** Renders the 3rd Tab of indicator 1.3.4 */
function AnnualMalariaSuveillance(props: Indicator_1_3_4_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { annualMalariaTab } = props.response;
    const { getMetNotMetStatus } = props;

    // trigger when value change on control
    const onValueChange = (fieldName: string, value: any) => {
        const _response = {
            ...annualMalariaTab,
            [fieldName]: value,
        };
        props.updateTab("annualMalariaTab", _response);
    };

    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <p className="fw-lighter">
                {t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_3_4.Tab2ResponseNote"
                )}
            </p>
            <div>
                <RadioButtonGroup
                    id="isSurveillanceReportProducedInLast12Month"
                    name="isSurveillanceReportProducedInLast12Month"
                    options={[
                        new MultiSelectModel(true, t("Common.Yes")),
                        new MultiSelectModel(false, t("Common.No")),
                    ]}
                    row
                    value={annualMalariaTab?.isSurveillanceReportProducedInLast12Month}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        onValueChange(
                            "isSurveillanceReportProducedInLast12Month",
                            e.currentTarget.value === "false" ? false : true
                        );
                        getMetNotMetStatus();
                    }}
                    error={
                        errors["annualMalariaTab.isSurveillanceReportProducedInLast12Month"] &&
                        errors["annualMalariaTab.isSurveillanceReportProducedInLast12Month"]
                    }
                    helperText={
                        errors["annualMalariaTab.isSurveillanceReportProducedInLast12Month"] &&
                        errors["annualMalariaTab.isSurveillanceReportProducedInLast12Month"]
                    }
                />
            </div>
            {
                annualMalariaTab?.isSurveillanceReportProducedInLast12Month === true &&
                <div>
                    <TextBox
                        id="links"
                        name="links"
                        label={t("indicators-responses:Common:Links")}
                        fullWidth
                        multiline
                        rows={3}
                        value={annualMalariaTab?.links || ""}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("links", e.currentTarget.value)
                        }
                        error={
                            errors["annualMalariaTab.links"] && errors["annualMalariaTab.links"]
                        }
                        helperText={
                            errors["annualMalariaTab.links"] && errors["annualMalariaTab.links"]
                        }
                    />
                </div>
            }
        </>
    );
}

export default AnnualMalariaSuveillance;
