﻿import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  healthFacilitiesWithStockOut: new ValidationRuleModel(DataType.Number, true),
  healthFacilitiesReporting: new ValidationRuleModel(DataType.Number, true),
};

export default ValidationRules;
