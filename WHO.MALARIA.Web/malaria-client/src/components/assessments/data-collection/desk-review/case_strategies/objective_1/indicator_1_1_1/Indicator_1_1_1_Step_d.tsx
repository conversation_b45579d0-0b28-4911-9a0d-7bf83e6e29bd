﻿import { useTranslation } from "react-i18next";
import { Step_D_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useSelector } from "react-redux";

interface IStep_D_Props {
  step_D: Step_D_Response;
  updateStep_D: (step_D: any) => void;
}

/** Renders the response for indicator 1.1.1 STEP D Line Graph */
const Indicator_1_1_1_Step_d = (props: IStep_D_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { step_D, updateStep_D } = props;

  //Triggers on value change of input controls and updates the step d
  const onValueChange = (fieldName: string, value: any) => {
    updateStep_D({ ...step_D, [fieldName]: value });
  };

  const errors = useSelector((state: any) => state.error);

  return (
    <div className="response-wrapper">
      <p>
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:HaveNewStrategyUsed"
        )}{" "}
      </p>
      <div className="col-md-12 ml-5 mt-3">
        <RadioButtonGroup
          id="newStrategiesUsed"
          name="newStrategiesUsed"
          row
          color="primary"
          options={[
            new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
            new MultiSelectModel(false, t("indicators-responses:Common:No")),
          ]}
          value={step_D.newStrategiesUsed}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onValueChange(
              "newStrategiesUsed",
              e.currentTarget.value === "false" ? false : true
            )
          }
          error={
            errors["step_D.newStrategiesUsed"] &&
            errors["step_D.newStrategiesUsed"]
          }
          helperText={
            errors["step_D.newStrategiesUsed"] &&
            errors["step_D.newStrategiesUsed"]
          }
        />
      </div>
      <TextBox
        id="step_D_details"
        name="step_D_details"
        label={t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ProvideDetails"
        )}
        fullWidth
        rows={5}
        maxLength={10000}
        multiline
        value={step_D.details}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          onValueChange("details", e.currentTarget.value)
        }
        error={
          errors["step_D.details"] &&
          errors["step_D.details"]
        }
        helperText={
          errors["step_D.details"] &&
          errors["step_D.details"]
        }
      />
    </div>
  );
};

export default Indicator_1_1_1_Step_d;
