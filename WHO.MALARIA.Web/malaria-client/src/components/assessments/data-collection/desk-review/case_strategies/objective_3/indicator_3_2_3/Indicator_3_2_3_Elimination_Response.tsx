﻿import * as React from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useSelector } from "react-redux";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_3/Response_1";

type EliminationResponseProps = {
    rows: any;
    response: Response_1;
    onChangeWithKey: (evt: React.ChangeEvent<HTMLInputElement>, objProperty: string) => void;
};

/** Renders the indicator 3.2.3 response if parent component 3.2.1 isn't assessed*/
function Indicator_3_2_3_Elimination_Response(props: EliminationResponseProps) {
    const { t } = useTranslation(["indicators-responses"]);
    const { rows, response, onChangeWithKey } = props;
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_2_3_Title");
    const header = t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecordingTool");
    const errors = useSelector((state: any) => state.error);

    // Excluded property that are not used in Array Map 
    const excludedProperties: Array<string> =
        [
            "cannotBeAssessed",
            "cannotBeAssessedReason",
            "standardizedRecordingTools",
            "standardizedFormsDetails",
            "metNotMetStatus",
            "parentRecordingToolCount",
            "parentData"
        ]

    // Render row controls based on the rowIndex
    const showRowControls = (rowIndex: number, columnIndex: number, modelKeyName: string) => {
        switch (rowIndex) {
            case 0:
                return <TableCell>
                    <TextBox
                        id={`sourceDocumentName${columnIndex + 1}`}
                        name="sourceDocumentName"
                        variant="outlined"
                        fullWidth
                        value={response[modelKeyName]?.sourceDocumentName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onChangeWithKey(
                                e,
                                modelKeyName
                            )
                        }
                        error={errors[`${modelKeyName}.sourceDocumentName`] && errors[`${modelKeyName}.sourceDocumentName`]}
                    />
                </TableCell>
            case 1:
                return <TableCell>
                    <>
                        <RadioButtonGroup
                            id={`areStandardizedAcrossAllService_${columnIndex + 1}`}
                            name="areStandardizedAcrossAllService"
                            row
                            color="primary"
                            options={[
                                new MultiSelectModel(
                                    true,
                                    t(
                                        "indicators-responses:Common:Yes"
                                    ),
                                    !response[modelKeyName]?.sourceDocumentName
                                ),
                                new MultiSelectModel(
                                    false,
                                    t(
                                        "indicators-responses:Common:No"
                                    ),
                                    !response[modelKeyName]?.sourceDocumentName
                                ),
                            ]}
                            value={response[modelKeyName]?.areStandardizedAcrossAllService}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onChangeWithKey(
                                    e,
                                    modelKeyName
                                )
                            }
                            error={errors[`${modelKeyName}.areStandardizedAcrossAllService`] && errors[`${modelKeyName}.areStandardizedAcrossAllService`]}
                            helperText={errors[`${modelKeyName}.areStandardizedAcrossAllService`] && errors[`${modelKeyName}.areStandardizedAcrossAllService`]}
                        />
                    </>
                </TableCell>
            case 2:
                return <TableCell>
                    <TextBox
                        id={`detail_${columnIndex + 1}`}
                        name="detail"
                        variant="outlined"
                        fullWidth
                        multiline
                        rows={2}
                        value={response[modelKeyName]?.detail}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onChangeWithKey(
                                e,
                                modelKeyName
                            )
                        }
                        error={errors[`${modelKeyName}.detail`] && errors[`${modelKeyName}.detail`]}
                        helperText={errors[`${modelKeyName}.detail`] && errors[`${modelKeyName}.detail`]}
                        disabled={!response[modelKeyName]?.sourceDocumentName}
                    />
                </TableCell>
            default: return <></>;
        }
    };

    return (
        <>
            <Table>
                <>
                    <TableHead>
                        <>
                            <TableHeaderCell>
                                <span></span>
                            </TableHeaderCell>
                            <TableHeaderCell>
                                <span>{`${header} ${1}`}</span>
                            </TableHeaderCell>
                            <TableHeaderCell>
                                <span>{`${header} ${2}`}</span>
                            </TableHeaderCell>
                            <TableHeaderCell>
                                <span>{`${header} ${3}`}</span>
                            </TableHeaderCell>
                            <TableHeaderCell>
                                <span>{`${header} ${4}`}</span>
                            </TableHeaderCell>
                        </>
                    </TableHead>
                    <TableBody>
                        <>
                            {Object.keys(rows).map(
                                (modelKeyName: string, rowIndex: number) => (
                                    <TableRow id={`${modelKeyName}_${rowIndex + 1}`}>
                                        <>
                                            <TableCell width="600px">
                                                <>{rows[modelKeyName].label}</>
                                            </TableCell>
                                            {Object.keys(response)
                                                .filter((key: string) => !excludedProperties.includes(key))
                                                .map((modelKeyName: string, columnIndex: number) => (
                                                    showRowControls(rowIndex, columnIndex, modelKeyName)
                                                ))}
                                        </>
                                    </TableRow>
                                ))}
                        </>
                    </TableBody>
                </>
            </Table>
        </>
    )
}

export default Indicator_3_2_3_Elimination_Response;