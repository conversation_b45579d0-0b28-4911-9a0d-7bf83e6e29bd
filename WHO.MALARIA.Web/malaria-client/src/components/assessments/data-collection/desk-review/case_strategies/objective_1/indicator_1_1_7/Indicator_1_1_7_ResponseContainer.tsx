﻿import React, { useEffect, useRef, useState, ChangeEvent } from "react";
import Checkbox from "../../../../../../controls/Checkbox";
import { useTranslation } from "react-i18next";
import WHOStepper from "../../../../../../controls/WHOStepper";
import { StepperModel } from "../../../../../../../models/StepperModel";
import Indicator_1_1_7_Step_A from "./Indicator_1_1_7_Step_A";
import Indicator_1_1_7_Step_B from "./Indicator_1_1_7_Step_B";
import Indicator_1_1_7_Step_C from "./Indicator_1_1_7_Step_C";
import Indicator_1_1_7_Step_D from "./Indicator_1_1_7_Step_D";
import Indicator_1_1_7_Step_E from "./Indicator_1_1_7_Step_E";
import TextBox from "../../../../../../controls/TextBox";
import { useDispatch, useSelector } from "react-redux";
import { updateStepIndex } from "../../../../../../../redux/ducks/indicator-guide";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_7/Response_1";
import useIndicatorResponseCaptureForTabs from "../../../responses/useIndicatorResponseCaptureForTabs";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import { MetNotMetStatus } from "../../../MetNotMetStatus";
import { MetNotMetEnum } from "../../../../../../../models/Enums";
import { Step_C_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";
import { showUserGuide, setTabClick } from "../../../../../../../redux/ducks/user-guide-drawer";

/** Response for indicator 1.3.4 */
function Indicator_1_1_7_ResponseContainer() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_1_1_7_Title");

    const [currentStep, setCurrentStep] = useState<number>(0);
    const dispatch = useDispatch();
    const isUserGuideShown: boolean = useSelector((state: any) => state.userGuideDrawer.isUserGuideShown);

    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);
    const errors = useSelector((state: any) => state.error);

    const {
        response,
        onChange,
        updateStep_A,
        updateStep_B,
        updateStep_C,
        updateStep_D,
        updateStep_E,
        onCannotBeAssessed,
        onChangeOfArrayWithIndex,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick,
    } = useIndicatorResponseCaptureForTabs<Response_1>(
        Response_1.init(),
        validate
    );

    useEffect(() => {
        getResponse();
        dispatch(updateStepIndex(0));      
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }
    const steps: Array<StepperModel> = [
        new StepperModel(0, "A", <></>),
        new StepperModel(1, "B", <></>),
        new StepperModel(2, "C", <></>),
        new StepperModel(3, "D", <></>),
        new StepperModel(4, "E", <></>),
    ];

    // Triggers when step is changed
    const onStepChange = (currentStep: number) => {
        setCurrentStep(currentStep);
        dispatch(updateStepIndex(currentStep));
        dispatch(showUserGuide(!isUserGuideShown));
        dispatch(setTabClick(true));
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        onValueChange(
            "metNotMetStatus",
            response?.step_C?.deathCompletenessPercentage > 89 &&
                response?.step_C?.deathProportionPercentage <= 9
                ? MetNotMetEnum.Met
                : response?.step_C?.deathCompletenessPercentage < 90 &&
                    response?.step_C?.deathProportionPercentage > 9 ||
                    response?.step_C?.deathProportionPercentage === null &&
                    response?.step_C?.deathCompletenessPercentage === null &&
                    response?.step_C?.deathCompletenessPercentage !== 0
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [
        response?.step_C?.deathCompletenessPercentage,
        response?.step_C?.deathProportionPercentage,
    ]);

    const renderStepComponent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <Indicator_1_1_7_Step_A
                        step_A={response.step_A}
                        updateStep_A={updateStep_A}
                    />
                );
            case 1:
                return (
                    <Indicator_1_1_7_Step_B
                        step_B={response.step_B}
                        updateStep_B={updateStep_B}
                    />
                );
            case 2:
                return (
                    <Indicator_1_1_7_Step_C
                        step_C={response.step_C}
                        updateStep_C={updateStep_C}
                        getMetNotMetStatus={getMetNotMetStatus}
                    />
                );
            case 3:
                return (
                    <Indicator_1_1_7_Step_D
                        step_D={response.step_D}
                        updateStep_D={updateStep_D}
                    />
                );
            case 4:
                return (
                    <Indicator_1_1_7_Step_E
                        step_E={response.step_E}
                        updateStep_E={updateStep_E}
                        onChangeOfArrayWithIndex={onChangeOfArrayWithIndex}
                        onChange={onChange}
                    />
                );
        }
    };

    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    return (
        <>
            <MetNotMetStatus
                status={response.metNotMetStatus}
                tooltip={t(
                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:MetNotMetTooltip"
                )}
            />
            <div className="response-assess-wrapper">
            <Checkbox
                id="cannotBeAssessed"
                name="cannotBeAssessed"
                label={t("indicators-responses:Common:IndicatorNoAssess")}
                onChange={onCannotBeAssessedChange}
                checked={response?.cannotBeAssessed}
                />
            </div>

            {!response.cannotBeAssessed ? (
                <>
                    {!errors["step_A.isVitalRegistration"] && (errors["step_A.vitalRegistrationDetail"] ||
                        errors["step_B.isDeathRecorded"] || errors["step_B.deathRecordedDetail"]
                        || errors["step_C.deathCompletenessPercentage"] || errors["step_C.deathProportionPercentage"]
                        || errors["step_D.isNMPRecorded"] || errors["step_D.nmpDeathRecordedDetail"]) && (
                            <span className="Mui-error d-flex mt-2">
                                *
                                {t(
                                    "indicators-responses:DRObjective_1_Responses:Indicator_1_1_7:ResponseErrorAToD"
                                )}
                            </span>
                        )}
                    <WHOStepper
                        alternativeLabel={false}
                        enableStepClick
                        steps={steps}
                        activeStep={currentStep}
                        onStepChange={onStepChange}
                    >
                        <div className="p-2">{renderStepComponent()}</div>
                    </WHOStepper>
                </>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason || ""}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_1_1_7_ResponseContainer;
