﻿import { useTranslation } from "react-i18next";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import CheckboxList from "../../../../../../controls/CheckboxList";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useSelector } from "react-redux";

enum Services {
  AllServices = "allServices",
  Consultation = "consultation",
  Diagnosis = "diagnosis",
  Treatment = "treatment",
  Hospitalisation = "hospitalisation",
}

interface IStep_B_Props {
  step_B: Step_B_Response;
  updateStep_B: (step_B: any) => void;
}

/** Renders the response for indicator 1.1.1 Step B */
const Indicator_1_1_1_Step_b = (props: IStep_B_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { step_B, updateStep_B } = props;
  const { services, differByAge, details } = step_B;

  const onValueChange = (fieldName: string, value: any) => {
    props.updateStep_B({ ...props.step_B, [fieldName]: value });
  };

  //Triggers on change of differ by age radio button, it updates the state for differByAge field
  const onDifferByAgeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const differByAge: boolean = event.currentTarget.value === "false" ? false : true;
    updateStep_B({
      ...step_B,
      differByAge,
      details: differByAge ? step_B.details : ""
    });
  }

  const errors = useSelector((state: any) => state.error);

  return (
    <div className="response-wrapper">
      <div className="col-md-12 ml-5">
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ServicesFreeForMalaria"
        )}
        {
          //Show error message if not a single checkbox will select
          errors["step_B.services"] && (
            <span className="Mui-error d-flex mb-2">
              *
              {t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ResponseErrorForCheckBox"
              )}
            </span>
          )}
        <CheckboxList
          options={[
            new MultiSelectModel(
              Services.AllServices,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:AllServices"
              )
            ),
            new MultiSelectModel(
              Services.Consultation,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Consultation"
              )
            ),
            new MultiSelectModel(
              Services.Diagnosis,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Diagnosis"
              )
            ),
            new MultiSelectModel(
              Services.Treatment,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Treatment"
              )
            ),
            new MultiSelectModel(
              Services.Hospitalisation,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:Hospitalisation"
              )
            ),
          ]}
          selectedItems={services}
          onClick={(selectedServices: Array<string | number>) => {
            onValueChange("services", selectedServices);
          }}
        />
      </div>

      <div className="col-md-12 ml-5 my-3">
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:AgeDiffer"
        )}
        <RadioButtonGroup
          id="differByAge"
          name="differByAge"
          row
          color="primary"
          options={[
            new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
            new MultiSelectModel(false, t("indicators-responses:Common:No")),
          ]}
          value={differByAge}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onDifferByAgeChange(e)
          }
          error={
            errors["step_B.differByAge"] &&
            errors["step_B.differByAge"]
          }
          helperText={
            errors["step_B.differByAge"] &&
            errors["step_B.differByAge"]
          }
        />
      </div>
      {differByAge &&
        <TextBox
          id="details"
          name="details"
          label={t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ProvideDetails"
          )}
          fullWidth
          rows={5}
          maxLength={1000}
          multiline
          value={details}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onValueChange("details", e.currentTarget.value)}
          error={
            errors["step_B.details"] &&
            errors["step_B.details"]
          }
          helperText={
            errors["step_B.details"] &&
            errors["step_B.details"]
          }
        />}
    </div>
  );
};

export default Indicator_1_1_1_Step_b;
