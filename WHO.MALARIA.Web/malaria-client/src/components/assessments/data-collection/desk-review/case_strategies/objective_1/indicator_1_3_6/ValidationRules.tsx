import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    caseClassificationRate: new ValidationRuleModel(DataType.Object, false),

    "caseClassificationRate.numerator": new ValidationRuleModel(
        DataType.Number,
        false
    ),

    "caseClassificationRate.denominator": new ValidationRuleModel(
        DataType.Number,
        false,     
        `${Constants.Common.RootObjectNameSubstitute}.caseClassificationRate.denominator !==null && ${Constants.Common.RootObjectNameSubstitute}.caseClassificationRate.denominator < 1`,
        "Errors.DenominatorGreaterThanZero"           
    ),

    "caseClassificationRate.evidenceOfFollowupOrUse": new ValidationRuleModel(
        DataType.String,
        false
    ),
    yearOfData: new ValidationRuleModel(
        DataType.Number,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.yearOfData !==null && (${Constants.Common.RootObjectNameSubstitute}.yearOfData <= 0)`,
    ),
    caseClassificationRates: new ValidationRuleModel(DataType.ArrayOfKeyValuePair, false),
    "caseClassificationRates.key": new ValidationRuleModel(DataType.String, false),
    "caseClassificationRates.value": new ValidationRuleModel(
        DataType.Number,
        false,      
        `(${Constants.Common.ValueSubstitute} && !(${Constants.Common.ValueSubstitute} >=0 && ${Constants.Common.ValueSubstitute} <=100))`,
        "Errors.ValueBetweenZeroToHundred"
    ),
};

export default ValidationRules;
