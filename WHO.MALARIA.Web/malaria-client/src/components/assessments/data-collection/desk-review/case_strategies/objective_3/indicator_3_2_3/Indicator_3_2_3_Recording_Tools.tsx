﻿import * as React from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useSelector } from "react-redux";
import { StandardizedRecordingTool, Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_3/Response_1";
import { NumberOfRecordingForm } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_1/Response_1";

type EliminationParentResponseProps = {
    rows: any;
    response: Response_1;
    generateControlValue: (recordingToolId: string, controlName: string, controlDataArrayName: string) => void;
    onChangeWithKeyInArray: (evt: React.ChangeEvent<HTMLInputElement>,
        objProperty: string,
        index: number,
        keyProperty: string,
        valueProperty: string) => void;
};

/** Renders the indicator 3.2.3 response if parent component 3.2.1 is assessed*/
function Indicator_3_2_3_Recording_Tools(props: EliminationParentResponseProps) {
    const { t } = useTranslation(["indicators-responses"]);
    const { rows, response, generateControlValue, onChangeWithKeyInArray } = props;
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_2_3_Title");
    const header = t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:RecordingTool");
    const errors = useSelector((state: any) => state.error);

    // Render row controls based on the rowIndex
    const showRowControls = (rowIndex: number, columnIndex: number, numberOfRecordingForm: NumberOfRecordingForm) => {
        switch (rowIndex) {
            case 0:
                return <TableCell>
                    <TextBox
                        id={`nameOfReportingToolSourceDocument${columnIndex + 1
                            }`}
                        variant="outlined"
                        disabled={true}
                        fullWidth
                        value={
                            numberOfRecordingForm?.nameOfReportingToolSourceDocument
                        }
                    />
                </TableCell>
            case 1:
                return <TableCell>
                    <>
                        <RadioButtonGroup
                            id={`standardizedRecordingTools${numberOfRecordingForm?.nameOfReportingToolSourceDocument
                                }_${columnIndex + 1}`}
                            name={
                                numberOfRecordingForm.recordingToolId
                            }
                            row
                            color="primary"
                            options={[
                                new MultiSelectModel(
                                    true,
                                    t(
                                        "indicators-responses:Common:Yes"
                                    )
                                ),
                                new MultiSelectModel(
                                    false,
                                    t(
                                        "indicators-responses:Common:No"
                                    )
                                ),
                            ]}
                            value={generateControlValue(
                                numberOfRecordingForm?.recordingToolId,
                                "areStandardizedAcrossAllService",
                                "standardizedRecordingTools"
                            )}
                            onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                            ) =>
                                onChangeWithKeyInArray(
                                    e,
                                    "standardizedRecordingTools",
                                    columnIndex,
                                    "recordingToolId",
                                    "areStandardizedAcrossAllService"
                                )
                            }
                            error={
                                errors[
                                `standardizedRecordingTools[${rowIndex}].areStandardizedAcrossAllService`
                                ] &&
                                errors[
                                `standardizedRecordingTools[${rowIndex}].areStandardizedAcrossAllService`
                                ]
                            }
                        />
                    </>
                </TableCell>
            case 2:
                return <TableCell>
                    <TextBox
                        id={`standardizedFormsDetails${numberOfRecordingForm?.nameOfReportingToolSourceDocument
                            }_${columnIndex + 1}`}
                        name={
                            numberOfRecordingForm.recordingToolId
                        }
                        variant="outlined"
                        fullWidth
                        multiline
                        rows={2}
                        value={generateControlValue(
                            numberOfRecordingForm?.recordingToolId,
                            "details",
                            "standardizedFormsDetails"
                        )}
                        onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                        ) =>
                            onChangeWithKeyInArray(
                                e,
                                "standardizedFormsDetails",
                                columnIndex,
                                "recordingToolId",
                                "details"
                            )
                        }
                        error={
                            errors[
                            `standardizedFormsDetails$[${rowIndex}].details`
                            ] &&
                            errors[
                            `standardizedFormsDetails[${rowIndex}].details`
                            ]
                        }
                    />
                </TableCell>
            default: return <></>;
        }
    };

    // Filters the standardizedRecordingTools and checks if recordingTool satisfies the condition for numberOfRecordingForm
    const hasRecordingToolsInParentData: boolean = response?.standardizedRecordingTools.filter(
        (standardizedFormsDetail: StandardizedRecordingTool) =>
            !response?.parentData?.recordingTools.some(
                (numberOfRecordingForm: NumberOfRecordingForm) =>
                    numberOfRecordingForm.recordingToolId === standardizedFormsDetail.recordingToolId
            )
    )?.length > 0;

    return (
        <div className="response-wrapper">
            {hasRecordingToolsInParentData &&
                <span className="text-danger">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:SaveWarning"
                    )}
                </span>
            }
            {
                // Show error message if user does not select 'Yes' or 'No' and provide details for each recording tool to finalize the indicator
                !!Object.keys(errors).length && (
                    <span className="Mui-error d-flex mb-2">
                        *{t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_3:ResponseError")}
                    </span>
                )
            }
            <Table>
                <>
                    <TableHead>
                        {response?.parentData?.recordingTools.map(
                            (
                                numberOfRecordingForm: NumberOfRecordingForm,
                                index: number
                            ) =>
                                // Here response.parentData.recordingTools is array and in UI first column is header with no label and to not miss this condition
                                index === 0 ? (
                                    <>
                                        <TableHeaderCell>
                                            <span></span>
                                        </TableHeaderCell>
                                        <TableHeaderCell>
                                            <span>{`${header} ${index + 1}`}</span>
                                        </TableHeaderCell>
                                    </>
                                ) : (
                                    <TableHeaderCell>
                                        <span>{`${header} ${index + 1}`}</span>
                                    </TableHeaderCell>
                                )
                        )}
                    </TableHead>
                    <TableBody>
                        <>
                            {Object.keys(rows).map(
                                (modelKeyName: string, rowIndex: number) => (
                                    <TableRow id={`${modelKeyName}_${rowIndex + 1}`}>
                                        <>
                                            <TableCell width="600px">
                                                <>{rows[modelKeyName].label}</>
                                            </TableCell>
                                            {response?.parentData?.recordingTools.map(
                                                (
                                                    numberOfRecordingForm: NumberOfRecordingForm,
                                                    columnIndex: number
                                                ) =>
                                                    showRowControls(rowIndex, columnIndex, numberOfRecordingForm)
                                            )}
                                        </>
                                    </TableRow>
                                )
                            )}
                        </>
                    </TableBody>
                </>
            </Table>
        </div>
    )
}

export default Indicator_3_2_3_Recording_Tools;