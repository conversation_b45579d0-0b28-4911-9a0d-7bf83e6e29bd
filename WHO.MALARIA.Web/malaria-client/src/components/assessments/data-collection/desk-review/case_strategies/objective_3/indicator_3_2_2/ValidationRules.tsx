﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
    step_A: new ValidationRuleModel(DataType.Object, true),
    "step_A.hasPVivaxCases": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.transmitMalariaVariables": new ValidationRuleModel(
        DataType.ArrayOfObject,
        true
    ),
    [`step_A.transmitMalariaVariables[${Constants.Common.IndexSubstitute}].recordedInSource`]: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.step_A.transmitMalariaVariables.some(data => data.recordedInSource === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListVariablesCount !== ${Constants.Common.RootObjectNameSubstitute}.step_A.transmitMalariaVariables.length))`
    ),

    step_B: new ValidationRuleModel(DataType.Object, true),

    "step_B.rdtConfirmCase": new ValidationRuleModel(DataType.Object, true),
    "step_B.rdtConfirmCase.checklist": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "step_B.rdtConfirmCase.details": new ValidationRuleModel(
        DataType.String,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.step_B.rdtConfirmCase.checklist === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.rdtConfirmCase.details)`
    ),

    "step_B.falciparumInfection": new ValidationRuleModel(DataType.Object, true),
    "step_B.falciparumInfection.checklist": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "step_B.falciparumInfection.details": new ValidationRuleModel(
        DataType.String,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.step_B.falciparumInfection.checklist === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.falciparumInfection.details)`
    ),
};

export const BurdenReductionValidationRules: IValidationRuleProvider = {
    "step_B.correctlyDistinguisedPresumedCase": new ValidationRuleModel(
        DataType.Object,
        true
    ),
    "step_B.correctlyDistinguisedPresumedCase.checklist": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "step_B.correctlyDistinguisedPresumedCase.details": new ValidationRuleModel(
        DataType.String,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.step_B.correctlyDistinguisedPresumedCase.checklist === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.correctlyDistinguisedPresumedCase.details)`
    ),
};

export const EliminationValidationRules: IValidationRuleProvider = {
    "step_A.hasMalariaInpatients": new ValidationRuleModel(DataType.Boolean, true),
    "step_B.indigenousCase": new ValidationRuleModel(DataType.Object, true),
    "step_B.indigenousCase.checklist": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "step_B.indigenousCase.details": new ValidationRuleModel(
        DataType.String,
        false,
        `${Constants.Common.RootObjectNameSubstitute}.step_B.indigenousCase.checklist === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.indigenousCase.details)`
    ),

    "step_B.identifiedCasesThroughProactiveCaseDetection":
        new ValidationRuleModel(DataType.Object, true),
    "step_B.identifiedCasesThroughProactiveCaseDetection.checklist":
        new ValidationRuleModel(DataType.Boolean, true),
    "step_B.identifiedCasesThroughProactiveCaseDetection.details":
        new ValidationRuleModel(
            DataType.String,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.step_B.identifiedCasesThroughProactiveCaseDetection.checklist === false && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.step_B.identifiedCasesThroughProactiveCaseDetection.details)`
        ),
};
