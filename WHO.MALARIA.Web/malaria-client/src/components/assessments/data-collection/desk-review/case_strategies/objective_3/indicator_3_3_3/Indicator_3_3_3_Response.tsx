﻿import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import TableRow from "../../../responses/TableRow";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { ReportingTool, Response_1 } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_3_3/Response_1";
import { useEffect } from "react";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response indicator 3.3.3 */
function Indicator_3_3_3_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_3_3_Title");

    const { response, onSave, onFinalize, getParentResponse, onChangeInArrayWithIndexForProperty, setDefaultValue } = useIndicatorResponseCapture<Response_1>(Response_1.init());

    useEffect(() => {
        getParentResponse();
    }, []);

    useEffect(() => {
        if (response?.parentData?.reportingTools) {
            const reportingTools = response.parentData.reportingTools.map((parentTool: any) => {
                const existingReportingtool: ReportingTool = response.reportingTools?.find((tool: ReportingTool) => tool.name?.toLowerCase().trim() === parentTool.reportingToolName?.toLowerCase().trim());

                if (existingReportingtool) {
                    existingReportingtool.name = parentTool.reportingToolName;
                    return existingReportingtool
                };

                return new ReportingTool(parentTool.reportingToolName, null, "");
            });

            setDefaultValue<Array<ReportingTool>>(["reportingTools"], reportingTools);
        }

    }, [response["parentData"]]);

    //Renders table header cells
    const renderTableHeaderCells = () => {
        if (!response?.reportingTools) return null;

        return [
            <TableHeaderCell key="headercell"><span></span></TableHeaderCell>,
            response.reportingTools.map((_: any, index: number) => (<TableHeaderCell key={`headercell${index}`}>
                <span>{`${t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_3:ReportingTool")} ${index + 1}`}</span>
            </TableHeaderCell>))
        ];
    }

    //Renders reporting tool names in a table cell
    const renderReportingToolNameTableCells = () => {
        if (!response?.reportingTools) return null;

        return response.reportingTools.map((tool: any, index: number) => (
            <TableCell key={`namecell${index}`}>
                <TextBox
                    id={`name${index + 1}`}
                    name={`name${index + 1}`}
                    variant="outlined"
                    fullWidth
                    value={tool.name}
                    disabled
                />
            </TableCell>
        ));
    }

    //Renders radio buttons in a table cell
    const renderIsStandardizedTableCells = () => {
        if (!response?.reportingTools) return null;

        return response.reportingTools.map((_: any, index: number) => (
            <TableCell key={`standardized${index}`}>
                <RadioButtonGroup
                    id={`isstandaridized${index + 1}`}
                    name={`isstandaridized${index + 1}`}
                    row
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    value={response?.reportingTools[index]?.isStandardized}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onChangeInArrayWithIndexForProperty("reportingTools", "isStandardized", e.target.value === "true" ? true : false, index)
                    }
                />
            </TableCell>
        ));
    }

    //Renders texboxes for the standardized form details in a table cell  
    const renderDetailsTableCells = () => {
        if (!response?.reportingTools) return null;

        return response.reportingTools.map((_: any, index: number) => (
            <TableCell key={`detail${index}`}>
                <TextBox
                    id={`detail${index + 1}`}
                    name={`detail${index + 1}`}
                    variant="outlined"
                    fullWidth
                    multiline
                    rows={2}
                    maxLength={2000}
                    value={response?.reportingTools[index]?.details || ""}
                    onChange={(
                        e: React.ChangeEvent<HTMLInputElement>
                    ) =>
                        onChangeInArrayWithIndexForProperty("reportingTools", "details", e.target.value, index)
                    }
                />
            </TableCell>
        ));
    }

    if (response?.reportingTools && response?.reportingTools.every((tool: ReportingTool) => !tool.name)) {
        return <>
            <span>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_3:IndicatorParentDependentMessage")}</span>
            <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />
        </>
    }

    return (
        <>
            <div className="response-wrapper">
                <Table>
                    <>
                        <TableHead>
                            {renderTableHeaderCells()}
                        </TableHead>
                        <TableBody>
                            <>
                                <TableRow>
                                    <>
                                        <TableCell width="600px">
                                            <span>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_3:ReportingName")}</span>
                                        </TableCell>
                                        {renderReportingToolNameTableCells()}
                                    </>
                                </TableRow>
                                <TableRow>
                                    <>
                                        <TableCell width="600px">
                                            <span>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_3:IsStandardized")}</span>
                                        </TableCell>
                                        {renderIsStandardizedTableCells()}
                                    </>
                                </TableRow>
                                <TableRow>
                                    <>
                                        <TableCell width="600px">
                                            <span>{t("indicators-responses:DRObjective_3_Responses:Indicator_3_3_3:DetailsOnPrivatePublicUseStandardisedForm")}</span>
                                        </TableCell>
                                        {renderDetailsTableCells()}
                                    </>
                                </TableRow>
                            </>
                        </TableBody>
                    </>
                </Table>
            </div>

            <SaveFinalizeButton onSave={onSave} onFinalize={onFinalize} />

        </>
    );
}

export default Indicator_3_3_3_Response;
