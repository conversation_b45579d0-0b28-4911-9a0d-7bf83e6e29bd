﻿import React, { useState } from "react";
import { TabModel } from "../../../../../../../models/TabModel";
import WHOTabs from "../../../../../../controls/WHOTabs";
import { useTranslation } from "react-i18next";
import Indicator_2_1_4_Burden_Case_Definition from "./Indicator_2_1_4_Burden_Case_Definition";
import { Step_A_Response } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_1_4/Response_4";

type Indicator_2_1_4_Props = {
    step_A: Step_A_Response;
    updateStep_A: (step_A: any) => void;
    getMetNotMetStatus: () => void;
};

/** Renders the response for indicator 2.1.4 Step A for burden reduction strategy*/
const Indicator_2_1_4_Burden_Step_A = (props: Indicator_2_1_4_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const [currentTab, setCurrentTab] = useState<number>(0);   
    const { step_A, updateStep_A, getMetNotMetStatus } = props;
    const { caseDefinition } = step_A;

    const tabs: Array<TabModel> = [
        new TabModel(
            1,
            t(
                "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:CaseDefinition"
            ),
            (
                <Indicator_2_1_4_Burden_Case_Definition
                    caseDefinition={caseDefinition}
                    updateStep_A={updateStep_A}
                    step_A={step_A}
                    getMetNotMetStatus={getMetNotMetStatus}
                />
            )
        ),
    ];

    // triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
    };

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseDesc"
                    )}
                </p>
                <p>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseDescTwo"
                    )}
                </p>
                <a
                    href="https://www.who.int/publications/i/item/WHO-HTM-GMP-2016.6"
                    target="_blank"
                >
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_1_4:ResponseLink"
                    )}
                </a>
                <div className="app-tab-wrapper mt-3">
                    <WHOTabs
                        tabs={tabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-3">{tabs[currentTab].children}</div>
                    </WHOTabs>
                </div>
            </div>
        </>
    );
};

export default Indicator_2_1_4_Burden_Step_A;