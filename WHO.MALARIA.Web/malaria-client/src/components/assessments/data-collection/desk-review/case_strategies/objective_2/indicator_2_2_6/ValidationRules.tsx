import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
  drugEfficacyMonitoringIntegrated: new ValidationRuleModel(DataType.Boolean, true),
  drugEfficacyMonitoringDetails:new ValidationRuleModel(DataType.String, true)
 };

export default ValidationRules;
