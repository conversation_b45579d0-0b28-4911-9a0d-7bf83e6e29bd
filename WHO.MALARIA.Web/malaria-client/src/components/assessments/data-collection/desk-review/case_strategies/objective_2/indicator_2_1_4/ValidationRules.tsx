import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const CommonValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, true),
  "step_A.caseDefinition": new ValidationRuleModel(DataType.Object, true),
  "step_A.caseDefinition.suspectedCase": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseDefinition.suspectedCase.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseDefinition.suspectedCase.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_A.caseDefinition.confirmedCase": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseDefinition.confirmedCase.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseDefinition.confirmedCase.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  step_B: new ValidationRuleModel(DataType.Object, true),
  "step_B.notification": new ValidationRuleModel(DataType.Object, true),
  "step_B.notification.national": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.notification.subNational": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.notification.timeframeOk": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
};

export const BurdenReductionValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, true),
  "step_A.caseDefinition": new ValidationRuleModel(DataType.Object, true),
  "step_A.caseDefinition.presumedCase": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseDefinition.presumedCase.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseDefinition.presumedCase.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
};

export const EliminationValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, true),
  "step_A.caseClassification": new ValidationRuleModel(DataType.Object, false),
  "step_A.caseClassification.local": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.local.countryDefinition": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_A.caseClassification.local.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.indigenous": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.indigenous.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.indigenous.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.introduced": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.introduced.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.introduced.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.imported": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.imported.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.imported.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.induced": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.induced.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.induced.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.recrudescent": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.recrudescent.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.recrudescent.definitionOK":
    new ValidationRuleModel(DataType.Boolean, true),
  "step_A.caseClassification.relapsing": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.relapsing.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.relapsing.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.caseClassification.recurrent": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.caseClassification.recurrent.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.caseClassification.recurrent.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_A.fociClassification": new ValidationRuleModel(DataType.Object, false),
  "step_A.fociClassification.active": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.fociClassification.active.countryDefinition": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_A.fociClassification.active.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  "step_A.fociClassification.residualNonActive": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.fociClassification.residualNonActive.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.fociClassification.residualNonActive.definitionOK":
    new ValidationRuleModel(DataType.Boolean, true),
  "step_A.fociClassification.cleared": new ValidationRuleModel(
    DataType.Object,
    true
  ),
  "step_A.fociClassification.cleared.countryDefinition":
    new ValidationRuleModel(DataType.String, true),
  "step_A.fociClassification.cleared.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_A.activeCaseDetection": new ValidationRuleModel(DataType.Object, false),
  "step_A.activeCaseDetection.countryDefinition": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_A.activeCaseDetection.definitionOK": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_B.caseInvestigation": new ValidationRuleModel(DataType.Object, true),
  "step_B.caseInvestigation.national": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.caseInvestigation.subNational": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.caseInvestigation.timeframeOk": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_B.caseClassification": new ValidationRuleModel(DataType.Object, true),
  "step_B.caseClassification.national": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.caseClassification.subNational": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.caseClassification.timeframeOk": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  "step_B.fociInvestigation": new ValidationRuleModel(DataType.Object, true),
  "step_B.fociInvestigation.national": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.fociInvestigation.subNational": new ValidationRuleModel(
    DataType.String,
    true
  ),
  "step_B.fociInvestigation.timeframeOk": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  step_B: new ValidationRuleModel(DataType.Object, true),
  "step_B.response": new ValidationRuleModel(DataType.Object, true),
  "step_B.response.national": new ValidationRuleModel(DataType.String, true),
  "step_B.response.subNational": new ValidationRuleModel(DataType.String, true),
  "step_B.response.timeframeOk": new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
};
