﻿import { useTranslation } from "react-i18next";
import { Step_E_Response } from "../../../../../../../models/DeskReview/Objective_1/Indicator_1_1_1/Response_1";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import CheckboxList from "../../../../../../controls/CheckboxList";
import TextBox from "../../../../../../controls/TextBox";
import { useSelector } from "react-redux";

enum TreatmentOptions {
  NoTreatMent = "noTreatMent",
  CommunityHealthWorker = "communityHealthWorker",
  PublicHealthFacility = "publicHealthFacility",
  PriateHealthProvider = "priateHealthProvider",
  Other = "other",
}

interface IStep_E_Props {
  step_E: Step_E_Response;
  updateStep_E: (step_E: any) => void;
}

/** Renders the response for indicator 1.1.1 STEP E Line Graph */
const Indicator_1_1_1_Step_e = (props: IStep_E_Props) => {
  const { t } = useTranslation(["indicators-responses"]);
  const { step_E, updateStep_E } = props;
  const { otherDetails, treatmentOptions } = step_E;
  const isOtherTreatmentOptionSelected = treatmentOptions.includes(
    TreatmentOptions.Other
  );

  //Triggers on treatment options checked/unchecked and updates the step e
  const onTreatmentOptionChange = (
    treatmentOptions: Array<string | number>
  ) => {
    updateStep_E({
      treatmentOptions,
      otherDetails: isOtherTreatmentOptionSelected ? step_E.otherDetails : "",
    });
  };

  //Triggers on other details change when other treatment option is checked and updates the step e
  const onOtherDetailsChange = (otherDetails: string) => {
    updateStep_E({ ...step_E, otherDetails });
  };

  const errors = useSelector((state: any) => state.error);

  return (
    <div className="response-wrapper">
      <p>
        {t(
          "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:SeekTreatment"
        )}
      </p>
      <div className="col-md-12 ml-5 mt-3">
        <p>
          {t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:TreatmentOptions"
          )}
        </p>

        {
          //Show error message if not a single checkbox will select
          errors["step_E.treatmentOptions"] && (
            <span className="Mui-error d-flex mb-2">
              *
              {t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:ResponseErrorForCheckBox"
              )}
            </span>
          )}

        <CheckboxList
          options={[
            new MultiSelectModel(
              TreatmentOptions.NoTreatMent,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CheckboxOption1"
              )
            ),
            new MultiSelectModel(
              TreatmentOptions.CommunityHealthWorker,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CheckboxOption2"
              )
            ),
            new MultiSelectModel(
              TreatmentOptions.PublicHealthFacility,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CheckboxOption3"
              )
            ),
            new MultiSelectModel(
              TreatmentOptions.PriateHealthProvider,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CheckboxOption4"
              )
            ),
            new MultiSelectModel(
              TreatmentOptions.Other,
              t(
                "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:CheckboxOption5"
              )
            ),
          ]}
          selectedItems={treatmentOptions}
          onClick={(selectedTreatmentOptions: Array<string | number>) => {
            onTreatmentOptionChange(selectedTreatmentOptions);
          }}
        />
      </div>
      {isOtherTreatmentOptionSelected && (
        <TextBox
          id="otherDetails"
          name="otherDetails"
          label={t(
            "indicators-responses:DRObjective_1_Responses:Indicator_1_1_1:PleaseSpecify"
          )}
          fullWidth
          rows={5}
          maxLength={1000}
          multiline
          value={otherDetails}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onOtherDetailsChange(e.currentTarget.value)
          }
          error={errors["step_E.otherDetails"] && errors["step_E.otherDetails"]}
          helperText={
            errors["step_E.otherDetails"] && errors["step_E.otherDetails"]
          }
        />
      )}
    </div>
  );
};

export default Indicator_1_1_1_Step_e;
