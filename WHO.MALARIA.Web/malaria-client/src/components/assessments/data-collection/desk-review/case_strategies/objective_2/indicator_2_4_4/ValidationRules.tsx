import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "plannedAllocation": new ValidationRuleModel(DataType.String, true),
    "procedureForTroubleShooting": new ValidationRuleModel(DataType.String, true),
    "discrepanciesInThePlan": new ValidationRuleModel(DataType.String, true)
}
export default ValidationRules;