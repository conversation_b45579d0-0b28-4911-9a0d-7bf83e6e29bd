import { DataType } from '../../../../../../../models/Enums';
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "hasTraining": new ValidationRuleModel(DataType.Boolean, false),

    "dataCollection": new ValidationRuleModel(DataType.Object, true),
    "dataCollection.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.nationalLevel === null`),
    "dataCollection.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.subNationalLevel === null`),
    "dataCollection.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataCollection.serviceDeliveryLevel === null`),

    "dataReporting": new ValidationRuleModel(DataType.Object, true),
    "dataReporting.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.nationalLevel === null`),
    "dataReporting.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.subNationalLevel === null`),
    "dataReporting.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataReporting.serviceDeliveryLevel === null`),

    "dataQualityReview": new ValidationRuleModel(DataType.Object, true),
    "dataQualityReview.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataQualityReview.nationalLevel === null`),
    "dataQualityReview.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataQualityReview.subNationalLevel === null`),
    "dataQualityReview.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataQualityReview.serviceDeliveryLevel === null`),

    "dataAnalysis": new ValidationRuleModel(DataType.Object, true),
    "dataAnalysis.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataAnalysis.nationalLevel === null`),
    "dataAnalysis.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataAnalysis.subNationalLevel === null`),
    "dataAnalysis.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.dataAnalysis.serviceDeliveryLevel === null`),

    "disseminationReport": new ValidationRuleModel(DataType.Object, true),
    "disseminationReport.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.disseminationReport.nationalLevel === null`),
    "disseminationReport.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.disseminationReport.subNationalLevel === null`),
    "disseminationReport.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.disseminationReport.serviceDeliveryLevel === null`),

    "supervision": new ValidationRuleModel(DataType.Object, true),
    "supervision.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.nationalLevel === null`),
    "supervision.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.subNationalLevel === null`),
    "supervision.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.supervision.serviceDeliveryLevel === null`),

    "publicPrivateSectorTraining": new ValidationRuleModel(DataType.Object, true),
    "publicPrivateSectorTraining.nationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.publicPrivateSectorTraining.nationalLevel === null`),
    "publicPrivateSectorTraining.subNationalLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.publicPrivateSectorTraining.subNationalLevel === null`),
    "publicPrivateSectorTraining.serviceDeliveryLevel": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}.hasTraining === true && ${Constants.Common.RootObjectNameSubstitute}.publicPrivateSectorTraining.serviceDeliveryLevel === null`),
}

export default ValidationRules;