import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_1";

type Indicator_2_2_2_Props = {
    step_B: Step_B_Response;
    updateStep_B: (step_B: Step_B_Response) => void;
};

/** Response for indicator 2.2.2 Step B */
const Indicator_2_2_2_StepB = (props: Indicator_2_2_2_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_B, updateStep_B } = props;

    const {
        informationSystemEasy,
        additionalAttributes,
        survellianceInformation,
    } = step_B;

    const errors = useSelector((state: any) => state.error);

    // Triggered whenever the HTML element values are changed and update response
    const onValueChange = (fieldName: string, value: any) => {
        updateStep_B({ ...step_B, [fieldName]: value });
    };

    return (
        <>
            <div className="response-wrapper">
                <div className="mb-4">
                    <TextBox
                        id="informationSystemEasy"
                        name="informationSystemEasy"
                        label={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBDesc1"
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBPlaceholder1"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={informationSystemEasy}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("informationSystemEasy", e.currentTarget.value)
                        }
                        error={
                            errors["step_B.informationSystemEasy"] &&
                            errors["step_B.informationSystemEasy"]
                        }
                        helperText={
                            errors["step_B.informationSystemEasy"] &&
                            errors["step_B.informationSystemEasy"]
                        }

                    />
                </div>

                <div className="mb-4">
                    <TextBox
                        id="additionalAttributes"
                        name="additionalAttributes"
                        label={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBDesc2"
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBPlaceholder2"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={additionalAttributes}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("additionalAttributes", e.currentTarget.value)
                        }
                        error={
                            errors["step_B.additionalAttributes"] &&
                            errors["step_B.additionalAttributes"]
                        }
                        helperText={
                            errors["step_B.additionalAttributes"] &&
                            errors["step_B.additionalAttributes"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default Indicator_2_2_2_StepB;
