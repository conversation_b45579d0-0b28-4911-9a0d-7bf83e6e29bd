﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    noInformationSystemExists: new ValidationRuleModel(DataType.Boolean, false),
    informationSystems: new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`informationSystems[${Constants.Common.IndexSubstitute}].informationSystem1`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.noInformationSystemExists !== true && !${Constants.Common.RootObjectNameSubstitute}.informationSystems.every((item)=>item.informationSystem1)`
        ),
};

export default ValidationRules;