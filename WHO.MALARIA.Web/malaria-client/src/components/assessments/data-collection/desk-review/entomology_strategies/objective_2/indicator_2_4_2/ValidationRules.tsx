﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    equipmentAvailabilities: new ValidationRuleModel(
        DataType.ArrayOfObject,
        false
    ),
    [`equipmentAvailabilities[${Constants.Common.IndexSubstitute}].equipment`]: new ValidationRuleModel(
        DataType.String,
        true),
    [`equipmentAvailabilities[${Constants.Common.IndexSubstitute}].nationalRequired`]: new ValidationRuleModel(
        DataType.Number,
        true,
        `${Constants.Common.RootObjectNameSubstitute}.equipmentAvailabilities[${Constants.Common.IndexSubstitute}].staffRole !==null && ${Constants.Common.RootObjectNameSubstitute}.equipmentAvailabilities[${Constants.Common.IndexSubstitute}].nationalRequired === null`
    ),
    [`equipmentAvailabilities[${Constants.Common.IndexSubstitute}].nationalCurrentlyAvailable`]: new ValidationRuleModel(
        DataType.Number,
        true,
        `${Constants.Common.RootObjectNameSubstitute}.equipmentAvailabilities[${Constants.Common.IndexSubstitute}].staffRole !==null && ${Constants.Common.RootObjectNameSubstitute}.equipmentAvailabilities[${Constants.Common.IndexSubstitute}].nationalCurrentlyAvailable === null`
    )
};

export default ValidationRules;