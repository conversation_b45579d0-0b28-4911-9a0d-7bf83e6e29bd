﻿import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "definition1": new ValidationRuleModel(DataType.Object, true),
    "definition1.country": new ValidationRuleModel(DataType.String, true),
    "definition1.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "definition2": new ValidationRuleModel(DataType.Object, true),
    "definition2.country": new ValidationRuleModel(DataType.String, true),
    "definition2.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "definition3": new ValidationRuleModel(DataType.Object, true),
    "definition3.country": new ValidationRuleModel(DataType.String, true),
    "definition3.isOk": new ValidationRuleModel(DataType.Boolean, true),
};

export default ValidationRules;