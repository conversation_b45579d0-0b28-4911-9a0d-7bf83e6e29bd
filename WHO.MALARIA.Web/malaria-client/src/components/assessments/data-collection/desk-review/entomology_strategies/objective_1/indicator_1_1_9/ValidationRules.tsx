﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    isMolecularAnalysisCarriedOutForDrugResistance: new ValidationRuleModel(DataType.Boolean, true),
    detailsOnMethodForDrugResistance: new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.isMolecularAnalysisCarriedOutForDrugResistance === true && ${Constants.Common.RootObjectNameSubstitute}.detailsOnMethodForDrugResistance === null`
    )
};

export default ValidationRules;