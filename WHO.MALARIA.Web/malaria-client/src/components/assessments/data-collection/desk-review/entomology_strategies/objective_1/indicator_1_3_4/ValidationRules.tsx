﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    dataAreNotUsed: new ValidationRuleModel(DataType.Boolean, false),
    "nationalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "regionalLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),
    "districtLevel": new ValidationRuleModel(DataType.Object, true, `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true`),

    "nationalLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMonthlyBulletinsProduced === null && ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMonthlyBulletinsProduced === null && ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMonthlyBulletinsProduced === null`
        ),

    "regionalLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false
        ),

    "districtLevel.noOfMonthlyBulletinsProduced":
        new ValidationRuleModel(
            DataType.Number,
            false
        ),

    "nationalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false
        ),

    "regionalLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false
        ),

    "districtLevel.noOfWeeksOrMonths":
        new ValidationRuleModel(
            DataType.Number,
            false
        ),

    "nationalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.nationalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "regionalLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.regionalLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),

    "districtLevel.proportionRate": new ValidationRuleModel(DataType.Number, false,
        `${Constants.Common.RootObjectNameSubstitute}.dataAreNotUsed !== true && ((${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfWeeksOrMonths)*100) > 100 ||((${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfMonthlyBulletinsProduced / ${Constants.Common.RootObjectNameSubstitute}.districtLevel.noOfWeeksOrMonths)*100) < 0`,
        "Errors.ProportionValueMessage"
    ),
};

export default ValidationRules;