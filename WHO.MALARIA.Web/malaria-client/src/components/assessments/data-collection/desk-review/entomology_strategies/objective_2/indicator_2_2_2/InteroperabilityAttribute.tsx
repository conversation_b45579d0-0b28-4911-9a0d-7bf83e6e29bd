import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { useSelector } from "react-redux";
import { InteroperabilityOrIntegrationResponse } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_1";

type InteroperabilityAttribute_Props = {
    interoperabilityOrIntegrationResponse: InteroperabilityOrIntegrationResponse;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 interoperability attribute tab */
const InteroperabilityAttribute = (props: InteroperabilityAttribute_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { interoperabilityOrIntegrationResponse, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsInteroperability"
                    )}
                </label>
                <RadioButtonGroup
                    id="isInteroperability"
                    name="isInteroperability"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={interoperabilityOrIntegrationResponse?.isInteroperability}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "interoperabilityOrIntegrationResponse"
                        )
                    }
                    error={
                        errors["step_A.interoperabilityOrIntegrationResponse.isInteroperability"] &&
                        errors["step_A.interoperabilityOrIntegrationResponse.isInteroperability"]
                    }
                    helperText={
                        errors["step_A.interoperabilityOrIntegrationResponse.isInteroperability"] &&
                        errors["step_A.interoperabilityOrIntegrationResponse.isInteroperability"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="expalin"
                        name="explain"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={interoperabilityOrIntegrationResponse?.explain}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "interoperabilityOrIntegrationResponse"
                            )
                        }
                        error={
                            errors["step_A.interoperabilityOrIntegrationResponse.explain"] &&
                            errors["step_A.interoperabilityOrIntegrationResponse.explain"]
                        }
                        helperText={
                            errors["step_A.interoperabilityOrIntegrationResponse.explain"] &&
                            errors["step_A.interoperabilityOrIntegrationResponse.explain"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="standardDataFormats"
                        name="standardDataFormats"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InteroperabilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InteroperabilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={interoperabilityOrIntegrationResponse?.standardDataFormats}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "interoperabilityOrIntegrationResponse"
                            )
                        }
                        error={
                            errors["step_A.interoperabilityOrIntegrationResponse.standardDataFormats"] &&
                            errors["step_A.interoperabilityOrIntegrationResponse.standardDataFormats"]
                        }
                        helperText={
                            errors["step_A.interoperabilityOrIntegrationResponse.standardDataFormats"] &&
                            errors["step_A.interoperabilityOrIntegrationResponse.standardDataFormats"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default InteroperabilityAttribute;
