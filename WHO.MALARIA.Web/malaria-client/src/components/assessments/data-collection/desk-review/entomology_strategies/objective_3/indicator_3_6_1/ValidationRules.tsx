﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    accessUsers: new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`accessUsers[${Constants.Common.IndexSubstitute}].national`]:
        new ValidationRuleModel(
            DataType.String,
            true,
            `isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.accessUsers[${Constants.Common.IndexSubstitute}].national)`
        ),
};

export default ValidationRules;