import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const ValidationRules: IValidationRuleProvider = {
  step_A: new ValidationRuleModel(DataType.Object, true),
  "step_A.flexibilityResponse": new ValidationRuleModel(DataType.Object, true),
  "step_A.flexibilityResponse.isFlexibility": new ValidationRuleModel(DataType.Boolean, true),
  "step_A.flexibilityResponse.explain": new ValidationRuleModel(DataType.String, true),
  "step_A.flexibilityResponse.flexibilityProcess": new ValidationRuleModel(DataType.String, true),

  "step_A.stabilityResponse": new ValidationRuleModel(DataType.Object, true),
  "step_A.stabilityResponse.isStability": new ValidationRuleModel(DataType.Boolean, true),
  "step_A.stabilityResponse.explain": new ValidationRuleModel(DataType.String, true),
  "step_A.stabilityResponse.systemOperating": new ValidationRuleModel(DataType.String, true),

  "step_A.interoperabilityOrIntegrationResponse": new ValidationRuleModel(DataType.Object, true),
  "step_A.interoperabilityOrIntegrationResponse.isInteroperability": new ValidationRuleModel(DataType.Boolean, true),
  "step_A.interoperabilityOrIntegrationResponse.explain": new ValidationRuleModel(DataType.String, true),
  "step_A.interoperabilityOrIntegrationResponse.standardDataFormats": new ValidationRuleModel(DataType.String, true),

  "step_A.visualizationCapacitiesResponse": new ValidationRuleModel(DataType.Object, true),
  "step_A.visualizationCapacitiesResponse.areCapabilitiesPresent": new ValidationRuleModel(DataType.Boolean, true),
  "step_A.visualizationCapacitiesResponse.explain": new ValidationRuleModel(DataType.String, true),
  "step_A.visualizationCapacitiesResponse.dashboardInterface": new ValidationRuleModel(DataType.String, true),

  step_B: new ValidationRuleModel(DataType.Object, true),
  "step_B.informationSystemEasy": new ValidationRuleModel(DataType.String, true),
  "step_B.additionalAttributes": new ValidationRuleModel(DataType.String, true),
};