﻿import { DataType } from '../../../../../../../models/Enums';
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

/** Genomic 3.3.2 validation rules */
const ValidationRules: IValidationRuleProvider = {
    "transmitMalariaVariables": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`transmitMalariaVariables[${Constants.Common.IndexSubstitute}].recordedInSource`]: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.transmitMalariaVariables.some(data => data.recordedInSource === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListVariablesCount !== ${Constants.Common.RootObjectNameSubstitute}.transmitMalariaVariables.length))`
    ),
}

export default ValidationRules;