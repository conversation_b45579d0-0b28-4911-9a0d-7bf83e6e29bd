﻿import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    healthManagementStructure: new ValidationRuleModel(
        DataType.ArrayOfObject,
        false
    ),
    [`healthManagementStructure[${Constants.Common.IndexSubstitute}].evidence`]: new ValidationRuleModel(
        DataType.Boolean,
        false,
        `!(${Constants.Common.RootObjectNameSubstitute}.healthManagementStructure.some(data => data.evidence === true || data.evidence === false))`
    ),

    [`healthManagementStructure[${Constants.Common.IndexSubstitute}].details`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `(${Constants.Common.RootObjectNameSubstitute}.healthManagementStructure[${Constants.Common.IndexSubstitute}].evidence === true) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthManagementStructure[${Constants.Common.IndexSubstitute}].details) `,
            "Errors.MandatoryField"
        ),
};

export default ValidationRules;