﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { useSelector } from "react-redux";
import { StabilityResponse } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_1";

type StabilityAttribute_Props = {
    stabilityResponse: StabilityResponse;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 stability attribute tab */
const StabilityAttribute = (props: StabilityAttribute_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { stabilityResponse, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsStability"
                    )}
                </label>
                <RadioButtonGroup
                    id="isStability"
                    name="isStability"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={stabilityResponse?.isStability}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "stabilityResponse"
                        )
                    }
                    error={
                        errors["step_A.stabilityResponse.isStability"] &&
                        errors["step_A.stabilityResponse.isStability"]
                    }
                    helperText={
                        errors["step_A.stabilityResponse.isStability"] &&
                        errors["step_A.stabilityResponse.isStability"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explain"
                        name="explain"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={stabilityResponse?.explain}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "stabilityResponse"
                            )
                        }
                        error={
                            errors["step_A.stabilityResponse.explain"] &&
                            errors["step_A.stabilityResponse.explain"]
                        }
                        helperText={
                            errors["step_A.stabilityResponse.explain"] &&
                            errors["step_A.stabilityResponse.explain"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="systemOperating"
                        name="systemOperating"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:StabilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:StabilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={stabilityResponse?.systemOperating}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "stabilityResponse"
                            )
                        }
                        error={
                            errors["step_A.stabilityResponse.systemOperating"] &&
                            errors["step_A.stabilityResponse.systemOperating"]
                        }
                        helperText={
                            errors["step_A.stabilityResponse.systemOperating"] &&
                            errors["step_A.stabilityResponse.systemOperating"]
                        }
                    />
                </div>
            </div>
        </>
    );
};

export default StabilityAttribute;
