﻿import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

/** Commodity tracking 1.3.7 validation rules */
const ValidationRules: IValidationRuleProvider = {
    "challengesOfUsingMalariaSurveillanceData": new ValidationRuleModel(DataType.String, true)
};

export default ValidationRules;