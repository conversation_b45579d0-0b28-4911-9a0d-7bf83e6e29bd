﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import Table from "../../../responses/Table";
import TableBody from "../../../responses/TableBody";
import TableCell from "../../../responses/TableCell";
import TableHeader from "../../../responses/TableHeader";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import { useTranslation } from "react-i18next";
import TableFooter from "../../../responses/TableFooter";
import useCalculation from "../../../responses/useCalculation";
import { useLocation } from "react-router-dom";
import { ChecklistIndicatorModel } from "../../../../../../../models/DeskReview/ChecklistIndicatorModel";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { Response_2, MalariaIndicator } from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_4_2/Response_2";
import { useSelector } from "react-redux";
import useChecklistIndicator from "../../../responses/useChecklistIndicator";
import { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import React, { ChangeEvent, useEffect, useRef } from "react";
import TextBox from "../../../../../../controls/TextBox";
import Checkbox from "../../../../../../controls/Checkbox";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the response for indicator 3.4.2  */
function Indicator_3_4_2_Response() {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_3_Indicator_3_4_2_Title");
    const { calculatePercentageOfYesNo } = useCalculation();
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const checkListIndicators = useChecklistIndicator(strategyId);

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onSave,
        onFinalize,
        getResponse,
        onValueChange,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_2>(Response_2.init(strategyId), validate);

    const errors = useSelector((state: any) => state.error);

    // Variable for checking the condition for Proportional Calculation Rate the Rate should be between 0 to 100
    let isProportionRateValid: boolean = true;

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid && isProportionRateValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        if (checkListIndicators) {
            onValueChange("checkListIndicatorsCount", checkListIndicators.length);
        }
    }, [checkListIndicators]);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    // Triggered whenever the control values are changed and update response
    const onRadioButtonValueChange = (
        fieldName: string,
        value: any,
        indicatorId: string
    ) => {
        value = value === "false" ? false : true;
        const malariaIndicators = response.malariaIndicators ? [...response.malariaIndicators] : [];
        const indicatorData: MalariaIndicator | undefined = malariaIndicators.find((v: MalariaIndicator) => v.checklistIndicatorId === indicatorId);

        if (indicatorData) {
            indicatorData[fieldName] = value;
        } else {
            const malariaIndicator = new MalariaIndicator(indicatorId, null, null, null, null, null, null, null, null);
            malariaIndicators.push({ ...malariaIndicator, [fieldName]: value })
        }

        onValueChange("malariaIndicators", malariaIndicators);
    };

    const headersMain = [
        {
            field: "indicators",
            label: `${t("indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:No.")}
            ${t("indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:Indicators")}`
        },
        {
            field: "indicatorMonitoredDocuments",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:IndicatorMonitoredInRoutineOutputs"
            ),
        },
        {
            field: "disagregation",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Disagregation"
            ),
        },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
        { field: "", label: "" },
    ];

    const headersSecondary = [
        { field: "", label: " " },
        { field: " ", label: "" },
        {
            field: "under5",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Under5"
            ),
        },
        {
            field: "over5",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Over5"
            ),
        },
        {
            field: "gender",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Gender"
            ),
        },
        {
            field: "pregnantWoman",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:PregnantWoman"
            ),
        },
        {
            field: "healthSector",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:HealthSector"
            ),
        },
        {
            field: "geography",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Geography"
            ),
        },
        {
            field: "methodOfConfirmation",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:MethodOfConfirmation"
            ),
        },
        {
            field: "other",
            label: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_3_2:Other"
            ),
        },
    ];


    //Method creates an array of properties that have checked "Yes" and returns percentage of it
    const calculateRecordedInSourceYesNoPercentage = () => {
        const propertyArray: boolean[] = checkListIndicators?.map((checkListIndicator: ChecklistIndicatorModel) => (getMalariaIndicatorValue(checkListIndicator.id, "indicatorMonitored"))
        );

        return calculatePercentageOfYesNo(propertyArray);
    };

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculateRecordedInSourcePercentage = () => {
        const proportionRateValue = calculateRecordedInSourceYesNoPercentage();
        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        if (proportionRateValue >= 0 && proportionRateValue <= 100) {
            isProportionRateValid = true;
            return proportionRateValue + '%';
        }

        isProportionRateValid = false;
        return proportionRateExceptionContent;
    }

    // get the MalariaIndicator based on the indicatorId and bind it on change event
    const getMalariaIndicatorValue = (indicatorId: string, fieldName: string) => {
        const malariaIndicator = (response.malariaIndicators?.find((v: MalariaIndicator) => v.checklistIndicatorId === indicatorId));
        if (!malariaIndicator || malariaIndicator[fieldName] === undefined) return "";

        return malariaIndicator[fieldName];
    }

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    id="cannotBeAssessed"
                    name="cannotBeAssessed"
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t("indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:CommodityResponseProportionDesc")}
                    </p>
                    <p className="fst-italic">
                        {t(
                            "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:ResponseTitle"
                        )}
                    </p>
                    <div>
                        {/*Show error message if user does not select 'Yes' or 'No' for 'Indicator Checklist' and 'Disaggregations' for variables*/}
                        {!!Object.keys(errors).length &&
                            <span className="Mui-error d-flex mb-2">
                                * {t("indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:ResponseError")}
                            </span>
                        }
                        <Table>
                            <>
                                <TableHeader
                                    headers={headersMain.map((header: any) => header.label)}
                                />
                                <TableHeader
                                    headers={headersSecondary.map((header: any) => header.label)}
                                />
                                <TableBody>
                                    <>
                                        {
                                            checkListIndicators?.map((checkListIndicator: ChecklistIndicatorModel, index: number) => (
                                                <TableRow key={`row_${checkListIndicator.id}_${index}`}>
                                                    <>

                                                        <TableCell>
                                                            <p className="d-flex mb-0">
                                                                <span className="d-inline-flex me-2">{index + 1} </span> {checkListIndicator.name}</p>
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="indicatorMonitored"
                                                                name="indicatorMonitored"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.indicatorMonitored
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.indicatorMonitored
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "indicatorMonitored")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "indicatorMonitored",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].indicatorMonitored`] && errors[`malariaIndicators[${index}].indicatorMonitored`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="underFive"
                                                                name="underFive"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.underFive
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.underFive
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "underFive")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "underFive",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].underFive`] && errors[`malariaIndicators[${index}].underFive`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="overFive"
                                                                name="overFive"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.overFive
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.overFive
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "overFive")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "overFive",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].overFive`] && errors[`malariaIndicators[${index}].overFive`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="gender"
                                                                name="gender"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.gender
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.gender
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "gender")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "gender",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].gender`] && errors[`malariaIndicators[${index}].gender`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="pregnantWoman"
                                                                name="pregnantWoman"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.pregnantWoman
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.pregnantWoman
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "pregnantWoman")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "pregnantWoman",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].pregnantWoman`] && errors[`malariaIndicators[${index}].pregnantWoman`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="healthSector"
                                                                name="healthSector"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.healthSector
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.healthSector
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "healthSector")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "healthSector",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].healthSector`] && errors[`malariaIndicators[${index}].healthSector`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="geography"
                                                                name="geography"
                                                                row
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.geography
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.geography
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "geography")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "geography",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].geography`] && errors[`malariaIndicators[${index}].geography`]}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="confirmationMethod"
                                                                name="confirmationMethod"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.confirmationMethod
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.confirmationMethod
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(
                                                                    checkListIndicator.id,
                                                                    "confirmationMethod"
                                                                )}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "confirmationMethod",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )
                                                                }
                                                                error={
                                                                    errors[
                                                                    `transmitMalariaIndicators[${index}].confirmationMethod`
                                                                    ] &&
                                                                    errors[
                                                                    `transmitMalariaIndicators[${index}].confirmationMethod`
                                                                    ]
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <RadioButtonGroup
                                                                id="other"
                                                                name="other"
                                                                color="primary"
                                                                options={[
                                                                    new MultiSelectModel(
                                                                        true,
                                                                        t("indicators-responses:Common:Yes"),
                                                                        !checkListIndicator.other
                                                                    ),
                                                                    new MultiSelectModel(
                                                                        false,
                                                                        t("indicators-responses:Common:No"),
                                                                        !checkListIndicator.other
                                                                    ),
                                                                ]}
                                                                value={getMalariaIndicatorValue(checkListIndicator.id, "other")}
                                                                onChange={(
                                                                    e: React.ChangeEvent<HTMLInputElement>
                                                                ) =>
                                                                    onRadioButtonValueChange(
                                                                        "other",
                                                                        e.currentTarget.value,
                                                                        checkListIndicator.id
                                                                    )}
                                                                error={errors[`malariaIndicators[${index}].other`] && errors[`malariaIndicators[${index}].other`]}
                                                            />
                                                        </TableCell>
                                                    </>
                                                </TableRow>
                                            ))}
                                    </>
                                </TableBody>

                                <TableFooter>
                                    <>
                                        <TableCell>
                                            <span>
                                                {t(
                                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_4_2:TableFooterTitle"
                                                )}
                                            </span>
                                        </TableCell>

                                        <TableCell colSpan={10}>
                                            <span>{calculateRecordedInSourcePercentage()}</span>
                                        </TableCell>
                                    </>
                                </TableFooter>
                            </>
                        </Table>
                    </div>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
}

export default Indicator_3_4_2_Response;
