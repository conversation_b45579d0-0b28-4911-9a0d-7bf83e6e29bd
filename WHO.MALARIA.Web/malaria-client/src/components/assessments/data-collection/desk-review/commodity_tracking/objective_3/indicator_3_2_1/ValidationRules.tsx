﻿import { DataType } from '../../../../../../../models/Enums';
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

/** Commodity tracking 3.2.1 validation rules */
const ValidationRules: IValidationRuleProvider = {
    recordingTools: new ValidationRuleModel(DataType.ArrayOfObject, true),

    [`recordingTools[${Constants.Common.IndexSubstitute}].nameOfReportingToolSourceDocument`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].nameOfReportingToolSourceDocument)`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].toolType`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].toolType) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].yearToolWasIntroduced`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].yearToolWasIntroduced) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].frequencyDataReported`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].frequencyDataReported) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].personResponsibleForReporting`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].personResponsibleForReporting) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].recipientListVariablesReported`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].recipientListVariablesReported) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].lastUpdatedDate`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `let index=${Constants.Common.IndexSubstitute}; index === 0 ? (!${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].lastUpdatedDate || isNaN(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}].lastUpdatedDate)) : false`
        ),

    [`recordingTools[${Constants.Common.IndexSubstitute}].file`]:
        new ValidationRuleModel(DataType.String, false,
            `${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}]["file"] ? (${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}]["file"].size > ${Constants.Common.MaxDiagramFileSize} || !["image/png", "image/jpg", "image/jpeg"].includes(${Constants.Common.RootObjectNameSubstitute}.recordingTools[${Constants.Common.IndexSubstitute}]["file"].type)) : false`, "Exception.FileSizeAndTypeShouldBeValid"
        ),
}

export default ValidationRules;