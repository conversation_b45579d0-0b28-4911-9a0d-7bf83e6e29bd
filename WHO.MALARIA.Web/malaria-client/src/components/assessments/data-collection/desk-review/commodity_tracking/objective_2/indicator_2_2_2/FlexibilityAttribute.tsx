﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { FlexibilityResponse } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_1";
import { useSelector } from "react-redux";

type FlexibilityAttribute_Props = {
    flexibilityResponse: FlexibilityResponse;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 flexibility attribute tab */
const FlexibilityAttribute = (props: FlexibilityAttribute_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { flexibilityResponse, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsFlexibility"
                    )}
                </label>
                <RadioButtonGroup
                    id="isFlexibility"
                    name="isFlexibility"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={flexibilityResponse?.isFlexibility}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "flexibilityResponse"
                        )
                    }
                    error={
                        errors["step_A.flexibilityResponse.isFlexibility"] &&
                        errors["step_A.flexibilityResponse.isFlexibility"]
                    }
                    helperText={
                        errors["step_A.flexibilityResponse.isFlexibility"] &&
                        errors["step_A.flexibilityResponse.isFlexibility"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="expalin"
                        name="explain"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={flexibilityResponse?.explain}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "flexibilityResponse"
                            )
                        }
                        error={
                            errors["step_A.flexibilityResponse.explain"] &&
                            errors["step_A.flexibilityResponse.explain"]
                        }
                        helperText={
                            errors["step_A.flexibilityResponse.explain"] &&
                            errors["step_A.flexibilityResponse.explain"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="flexibilityProcess"
                        name="flexibilityProcess"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:FlexibilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:FlexibilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={flexibilityResponse?.flexibilityProcess}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "flexibilityResponse"
                            )
                        }
                        error={
                            errors["step_A.flexibilityResponse.flexibilityProcess"] &&
                            errors["step_A.flexibilityResponse.flexibilityProcess"]
                        }
                        helperText={
                            errors["step_A.flexibilityResponse.flexibilityProcess"] &&
                            errors["step_A.flexibilityResponse.flexibilityProcess"]
                        }
                    />
                </div>
            </div>
        </>
    );
};

export default FlexibilityAttribute;
