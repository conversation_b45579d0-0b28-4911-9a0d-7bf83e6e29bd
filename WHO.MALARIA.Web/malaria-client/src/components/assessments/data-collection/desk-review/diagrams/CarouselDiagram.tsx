﻿import React from "react";
import { Carousel as ReactCarousel } from "react-responsive-carousel";

type CarouselProps = {
    cssClassNames: Array<string>;
};

/** Renders carousel diagram component */
function CarouselDiagram(props: CarouselProps) {
    const { cssClassNames } = props;

    return (
        <>
            {/* Carousel Component */}
            <div className="carouselWrapper">
                <div
                    id="app-diagram-carousel"
                    className="carousel slide mb-4"
                    data-ride="carousel"
                >
                    <div className="carousel-inner">
                        <ReactCarousel
                            showArrows={true}
                            showThumbs={false}
                            showIndicators={false}
                            showStatus={false}
                        >
                            {
                                cssClassNames.map((cssClassName: string, index: number) => (
                                    <div key={`carousel_${cssClassName}_${index}`} className="row justify-content-end">
                                        <div className="col-xs-12 col-sm-12">
                                            <div className={`diagram ${cssClassName}`}></div>
                                        </div>
                                    </div>
                                ))
                            }
                        </ReactCarousel>
                    </div>
                </div>
            </div>
        </>
    );
}

export default CarouselDiagram;