﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser"
import FileUploader from "../../../../controls/FileUploader";
import classNames from "classnames";
import { Constants } from "../../../../../models/Constants";
import { useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../../../../models/PermissionModel";

type DiagramUploadControlProps = {
    uploadedFile?: File | string,
    extension?: string,
    onFileUpload: (evt: React.ChangeEvent<HTMLInputElement>) => void
}

/** Renders the upload diagram section and action buttons */
const UploadDiagramControl = (props: DiagramUploadControlProps) => {
    const { t } = useTranslation();
    const [isFileSizeInValid, setIsFileSizeInValid] = useState<boolean>(false);
    const [isFileTypeInValid, setIsFileTypeInValid] = useState<boolean>(false);

    const { onFileUpload, uploadedFile, extension } = props;
    const [imageSrc, setImageSrc] = useState("");
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    useEffect(() => {
        if (typeof uploadedFile === 'string') {
            setImageSrc(`data:image/${extension || "png"};base64,${uploadedFile}`);
        } else if (uploadedFile) {
            setImageSrc(URL.createObjectURL(uploadedFile));
        }
    }, [uploadedFile]);

    //Triggers on file upload
    const onUpload = (evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.target.files) {
            const file: File = evt.target.files[0];

            if (!(["image/png", "image/jpg", "image/jpeg"].includes(file.type))) {
                setIsFileTypeInValid(true);
                return;
            }

            setIsFileTypeInValid(false);

            if (file.size > Constants.Common.MaxDiagramFileSize) {
                setIsFileSizeInValid(true);
                return;
            }

            setIsFileSizeInValid(false);
        }

        onFileUpload(evt);
    }

    return (
        <>
            <div className="upload-section">
                {uploadedFile
                    ? <img key={`img${Math.random()}`} src={imageSrc} className="img-fluid p-2" />
                    : <div className="h-400 d-flex flex-column align-items-center justify-content-center text-center">
                        <div className="grey-circle"></div>
                        <span className="mt-2">
                            {parse(t("Assessment.DataCollection.DeskReviewDiagram.UploadDiagramInfo"))}
                        </span>
                    </div>
                }
                {userPermission?.canSaveOrFinalizeDRIndicatorResponse &&
                    <div className="upload-action-wrapper text-center my-3">
                        <FileUploader
                            key={`fileuploader_${Math.random()}`}
                            id="template"
                            linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                            onChange={onUpload}
                            accept=".png, .jpg, .jpeg"
                            label={uploadedFile && t("Assessment.DataCollection.DeskReviewDiagram.ReUpload")}
                        >
                            <i className="d-inline-flex mx-2 align-items-center">{t("Assessment.DataCollection.DataQualityAssessment.Max10MBFileSizeLimit")}</i>
                        </FileUploader>
                    </div>
                }
                {isFileSizeInValid &&
                    <span className="d-flex justify-content-center Mui-error"> {t("Exception.FileSizeGreaterThan10MB")} </span>
                }
                {isFileTypeInValid &&
                    <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidDiagramFile")} </span>
                }
            </div>
        </>
    );
};

export default UploadDiagramControl;