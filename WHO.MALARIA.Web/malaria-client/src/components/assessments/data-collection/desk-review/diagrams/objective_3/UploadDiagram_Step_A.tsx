﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../../../../controls/WHOTabs";
import { TabModel } from "../../../../../../models/TabModel";
import UploadDiagramTabContainer from "./UploadDiagramTabContainer";
import CarouselDiagram from "../CarouselDiagram";
import { DiagramProps } from "./UploadObjective_3_DiagramContainer";

/** Renders the tabs for the upload diagram (Example Diagrams and Uploaded Diagrams) and all components underneath */
const UploadDiagram_Step_A = (props: DiagramProps) => {
    const { diagramDetails, onDiagramDetailChange, onFileUpload } = props;
    const { t } = useTranslation();
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
        setCurrentTab(newValue);
    };

    // Renders tabs for examples diagrams and uploaded diagrams
    const objectiveDiagramTabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DeskReviewDiagram.ExampleDiagrams")}`,
            <CarouselDiagram cssClassNames={
                [`${t("Assessment.DataCollection.DeskReviewDiagram.HMIS_Only")}`,
                `${t("Assessment.DataCollection.DeskReviewDiagram.HMIS_FullRepository_CorePrinciples")}`,
                `${t("Assessment.DataCollection.DeskReviewDiagram.HMIS_HealthManagementInformationSystem")}`,
                `${t("Assessment.DataCollection.DeskReviewDiagram.KeyDataSystemComponent")}`]} />
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DeskReviewDiagram.UploadedDiagrams")}`,
            <UploadDiagramTabContainer
                diagramDetails={diagramDetails}
                onDiagramDetailChange={onDiagramDetailChange}
                onFileUpload={onFileUpload} />
        ),
    ];

    return (
        <>
            <div className="response-wrapper">
                <p className="fw-lighter">
                    {t("Assessment.DataCollection.DeskReviewDiagram.UploadDesc")}
                </p>
                <p className="fst-italic">
                    {t("Assessment.DataCollection.DeskReviewDiagram.UploadNote")}
                </p>

                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={objectiveDiagramTabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <> {objectiveDiagramTabs[currentTab].children} </>
                    </WHOTabs>
                </div>
            </div>
        </>
    );
};

export default UploadDiagram_Step_A;