﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../../../../controls/WHOTabs";
import { TabModel } from "../../../../../../models/TabModel";
import UploadDiagramControl from "../UploadDiagramControl";
import TextBox from "../../../../../controls/TextBox";
import { DiagramProps } from "./UploadObjective_3_DiagramContainer";

/** Renders the tabs for the upload diagram */
const UploadDiagramTabContainer = (props: DiagramProps) => {
    const { t } = useTranslation();
    const [currentTab, setCurrentTab] = useState<number>(0);

    const { diagramDetails, onDiagramDetailChange, onFileUpload } = props;

    //Calls the onFileUpload function of a parent component to pass the uploaded file.
    const onDiagramUpload = (evt: React.ChangeEvent<HTMLInputElement>) => onFileUpload(currentTab, evt.target.files && evt.target.files[0])

    // renders the additional info textboxes to add details for the uploaded diagram
    const createDiagramDetailsElements = (tabIndex: number) => (
        <div className="row mx-2 my-5">
            <div className="col-xs-12 col-md-4">
                <TextBox
                    id={`dataFlow${tabIndex}`}
                    name={`dataFlow${tabIndex}`}
                    className="lp-text inputfocus"
                    label={t(
                        "Assessment.DataCollection.DeskReviewDiagram.UploadTextboxSummarize"
                    )}
                    placeholder={t(
                        "Assessment.DataCollection.DeskReviewDiagram.UploadTextboxSummarizePlaceholder"
                    )}
                    InputLabelProps={{ shrink: true }}
                    multiline
                    rows={7}
                    variant="outlined"
                    fullWidth
                    value={diagramDetails[tabIndex]?.dataFlow || ''}
                    maxLength={2000}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onDiagramDetailChange(tabIndex, "dataFlow", e.target.value)}
                />
            </div>

            <div className="col-xs-12 col-md-4">
                <TextBox
                    id={`plannedChanges${tabIndex}`}
                    name={`plannedChanges${tabIndex}`}
                    className="lp-text inputfocus"
                    label={t(
                        "Assessment.DataCollection.DeskReviewDiagram.UploadTextboxNote"
                    )}
                    InputLabelProps={{ shrink: true }}
                    multiline
                    rows={7}
                    variant="outlined"
                    fullWidth
                    value={diagramDetails[tabIndex]?.plannedChanges || ''}
                    maxLength={2000}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onDiagramDetailChange(tabIndex, "plannedChanges", e.target.value)}
                />
            </div>

            <div className="col-xs-12 col-md-4">
                <TextBox
                    id={`modifyingProcess${tabIndex}`}
                    name={`modifyingProcess${tabIndex}`}
                    className="lp-text inputfocus"
                    label={t(
                        "Assessment.DataCollection.DeskReviewDiagram.UploadTextboxProcess"
                    )}
                    InputLabelProps={{ shrink: true }}
                    multiline
                    rows={7}
                    variant="outlined"
                    fullWidth
                    value={diagramDetails[tabIndex]?.modifyingProcess || ''}
                    maxLength={2000}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onDiagramDetailChange(tabIndex, "modifyingProcess", e.target.value)}
                />
            </div>
        </div>
    );

    // Renders tabs for Data Quality Assessment
    const uploadDiagramTabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DeskReviewDiagram.Diagram")} 1`,
            (
                <>
                    <UploadDiagramControl uploadedFile={diagramDetails[0].file || ""} extension={diagramDetails[0].extension} onFileUpload={onDiagramUpload} />
                    {createDiagramDetailsElements(0)}
                </>
            )
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DeskReviewDiagram.Diagram")} 2`,
            (
                <>
                    <UploadDiagramControl uploadedFile={diagramDetails[1].file || ""} extension={diagramDetails[1].extension} onFileUpload={onDiagramUpload} />
                    {createDiagramDetailsElements(1)}
                </>
            )
        ),
    ];

    // Triggers whenever tab is changed
    const onUploadTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
        setCurrentTab(newValue);
    };

    return (
        <>
            <div className="app-tab-wrapper m-3">
                <WHOTabs
                    tabs={uploadDiagramTabs}
                    value={currentTab}
                    onChange={onUploadTabChange}
                    scrollable={true}
                >
                    <> {uploadDiagramTabs[currentTab].children} </>
                </WHOTabs>
            </div>
        </>
    );
};

export default UploadDiagramTabContainer;
