﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../../../../controls/WHOTabs";
import { TabModel } from "../../../../../../models/TabModel";
import UploadDiagramControl from "../UploadDiagramControl";
import { DiagramProps } from "./UploadDiagramTab";

/** Renders the tabs for the upload diagram */
const UploadDiagramTabContainer = (props: DiagramProps) => {
    const { t } = useTranslation();
    const [currentTab, setCurrentTab] = useState<number>(0);
    const { diagramFiles, onFileUpload } = props;

    //Calls the onFileUpload function of a parent component to pass the uploaded file.
    const onDiagramUpload = (evt: React.ChangeEvent<HTMLInputElement>) => onFileUpload(currentTab, evt.target.files && evt.target.files[0])

    // Renders tabs for Data Quality Assessment
    const uploadDiagramtabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DeskReviewDiagram.Diagram")} 1`,
            <UploadDiagramControl extension={diagramFiles[0].extension} uploadedFile={diagramFiles[0].file || ""} onFileUpload={onDiagramUpload} />
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DeskReviewDiagram.Diagram")} 2`,
            <UploadDiagramControl extension={diagramFiles[0].extension} uploadedFile={diagramFiles[1].file || ""} onFileUpload={onDiagramUpload} />
        ),
    ];

    // Triggers whenever tab is changed
    const onUploadTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
        setCurrentTab(newValue);
    };

    return (
        <>
            <div className="app-tab-wrapper m-3">
                <WHOTabs
                    tabs={uploadDiagramtabs}
                    value={currentTab}
                    onChange={onUploadTabChange}
                    scrollable={true}
                >
                    <> {uploadDiagramtabs[currentTab].children} </>
                </WHOTabs>
            </div>
        </>
    );
};

export default UploadDiagramTabContainer;