﻿import React from "react";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../controls/TextBox";

type UploadDiagram_Step_B_Props = {
    dataAccess: string | null,
    dataQualityAssurance: string | null,
    dataRecording: string | null,
    dataReporting: string | null,
    dataAnalysis: string | null,
    onValueChange: (propertyName: string, value: string) => void
}

/** Renders the tabs for the upload diagram and all components underneath */
const UploadDiagram_Step_B = (props: UploadDiagram_Step_B_Props) => {
    const { t } = useTranslation();
    const { dataAccess, dataAnalysis, dataRecording, dataQualityAssurance, dataReporting, onValueChange } = props;

    // Triggered by all HTMLInputElement whenever the values are changed
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => onValueChange(evt.target.name, evt.target.value);

    return (
        <>
            <div className="response-wrapper">
                <div className="row mx-2 my-3">
                    <div className="col-xs-12 col-md-8 mb-3">
                        <TextBox
                            id="dataAccess"
                            name="dataAccess"
                            label={t("Assessment.DataCollection.DeskReviewDiagram.SummaryDataFlow")}
                            placeholder={t("Assessment.DataCollection.DeskReviewDiagram.DescribeSummaryProcessTools")}
                            InputLabelProps={{ shrink: true }}
                            multiline
                            rows={3}
                            variant="outlined"
                            fullWidth
                            value={dataAccess}
                            maxLength={2000}
                            onChange={onChange}
                            className="inputfocus"
                        />
                    </div>
                    
                    <div className="col-xs-12 col-md-8 mb-3">
                        <TextBox
                            id="dataRecording"
                            name="dataRecording"
                            label={t("Assessment.DataCollection.DeskReviewDiagram.DataRecording")}
                            placeholder={t("Assessment.DataCollection.DeskReviewDiagram.DescribeProcessTools")}
                            InputLabelProps={{ shrink: true }}
                            multiline
                            rows={3}
                            variant="outlined"
                            fullWidth
                            value={dataRecording}
                            maxLength={2000}
                            onChange={onChange}
                            className="inputfocus"
                        />
                    </div>

                    <div className="col-xs-12 col-md-8 mb-3">
                        <TextBox
                            id="dataReporting"
                            name="dataReporting"
                            label={t("Assessment.DataCollection.DeskReviewDiagram.DataReporting")}
                            placeholder={t("Assessment.DataCollection.DeskReviewDiagram.DescribeProcessTools")}
                            InputLabelProps={{ shrink: true }}
                            multiline
                            rows={3}
                            variant="outlined"
                            fullWidth
                            value={dataReporting}
                            maxLength={2000}
                            onChange={onChange}
                            className="inputfocus"
                        />
                    </div>

                    <div className="col-xs-12 col-md-8 mb-3">
                        <TextBox
                            id="dataQualityAssurance"
                            name="dataQualityAssurance"
                            label={t("Assessment.DataCollection.DeskReviewDiagram.StrengthsChallenges")}
                            placeholder={t("Assessment.DataCollection.DeskReviewDiagram.DescribeStrengthsChallenges")}
                            InputLabelProps={{ shrink: true }}
                            multiline
                            rows={3}
                            variant="outlined"
                            fullWidth
                            value={dataQualityAssurance}
                            maxLength={2000}
                            onChange={onChange}
                            className="inputfocus"
                        />
                    </div>

                    <div className="col-xs-12 col-md-8">
                        <TextBox
                            id="dataAnalysis"
                            name="dataAnalysis"
                            label={t("Assessment.DataCollection.DeskReviewDiagram.PlannedChanges")}
                            placeholder={t("Assessment.DataCollection.DeskReviewDiagram.DescribePlannedChanges")}
                            InputLabelProps={{ shrink: true }}
                            multiline
                            rows={3}
                            variant="outlined"
                            fullWidth
                            value={dataAnalysis}
                            maxLength={2000}
                            onChange={onChange}
                            className="inputfocus"
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default UploadDiagram_Step_B;