import { DataType } from "../../../../../../models/Enums";
import { Constants } from "../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    diagramFiles: new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.diagramFiles && ${Constants.Common.RootObjectNameSubstitute}.diagramFiles.every(diagram => !diagram.file)`)
}

export default ValidationRules;