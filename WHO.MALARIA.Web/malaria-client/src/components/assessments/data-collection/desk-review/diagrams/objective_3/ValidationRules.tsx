import { DataType } from "../../../../../../models/Enums";
import { Constants } from "../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    diagramDetails: new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.diagramDetails && ${Constants.Common.RootObjectNameSubstitute}.diagramDetails.every(detail => !detail.file)`)
}

export default ValidationRules;