﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../../../../controls/WHOTabs";
import { TabModel } from "../../../../../../models/TabModel";
import UploadDiagramTabContainer from "./UploadDiagramTabContainer";
import CarouselDiagram from "../CarouselDiagram";
import parse from "html-react-parser";
import { DiagramFile } from "../../../../../../models/DeskReview/Objective_2/DiagramUploadModel";

export type DiagramProps = {
    diagramFiles: Array<DiagramFile>,
    onFileUpload: (tabIndex: number, file: File | null) => void,
}

/** Renders the tabs for the upload diagram (Example Diagrams and Uploaded Diagrams) and all components underneath */
const UploadDiagramTab = (props: DiagramProps) => {
    const { t } = useTranslation();
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: number) => {
        setCurrentTab(newValue);
    };

    // Renders tabs for examples diagrams and uploaded diagrams
    const objectiveDiagramTabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DeskReviewDiagram.ExampleDiagrams")}`,
            <CarouselDiagram cssClassNames={
                [`${t("Assessment.DataCollection.DeskReviewDiagram.HMIS")}`,
                `${t("Assessment.DataCollection.DeskReviewDiagram.HMIS_FullRepository")}`,
                `${t("Assessment.DataCollection.DeskReviewDiagram.NationalMalariaRepository")}`]} />
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DeskReviewDiagram.UploadedDiagrams")}`,
            <UploadDiagramTabContainer {...props} />
        ),
    ];

    return (
        <>
            <div className="response-wrapper">
                <p className="fw-lighter">
                    {t("Assessment.DataCollection.DeskReviewDiagram.Objective2UploadDesc")}
                </p>
                <p className="fst-italic">
                    {parse(t("Assessment.DataCollection.DeskReviewDiagram.Objective2UploadNote"))}
                </p>

                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={objectiveDiagramTabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <> {objectiveDiagramTabs[currentTab].children} </>
                    </WHOTabs>
                </div>
            </div>
        </>
    );
};

export default UploadDiagramTab;