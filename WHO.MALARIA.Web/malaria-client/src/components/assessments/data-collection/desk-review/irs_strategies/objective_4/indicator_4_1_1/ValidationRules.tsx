import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    goverenanceStructures: new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`goverenanceStructures[${Constants.Common.IndexSubstitute}].inPlace`]:
        new ValidationRuleModel(DataType.Boolean, true),
    [`goverenanceStructures[${Constants.Common.IndexSubstitute}].details`]:
        new ValidationRuleModel(
            DataType.String,
            false,
            `${Constants.Common.RootObjectNameSubstitute}.goverenanceStructures[${Constants.Common.IndexSubstitute}].inPlace === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.goverenanceStructures[${Constants.Common.IndexSubstitute}].details)`
        ),
};

export default ValidationRules;