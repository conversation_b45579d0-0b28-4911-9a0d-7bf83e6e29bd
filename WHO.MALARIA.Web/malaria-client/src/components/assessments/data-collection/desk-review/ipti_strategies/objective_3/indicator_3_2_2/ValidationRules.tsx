﻿import { DataType } from '../../../../../../../models/Enums';
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "malariaVariables": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`malariaVariables[${Constants.Common.IndexSubstitute}].recordedInSource`]: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.malariaVariables.some(data => data.recordedInSource === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListVariablesCount !== ${Constants.Common.RootObjectNameSubstitute}.malariaVariables.length))`
    ),
}

export default ValidationRules;