﻿import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const ValidationRules: IValidationRuleProvider = {
    step_A: new ValidationRuleModel(DataType.Object, true),
    "step_A.flexibility": new ValidationRuleModel(DataType.Object, true),
    "step_A.flexibility.hasFlexibility": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.flexibility.explaination": new ValidationRuleModel(DataType.String, true),
    "step_A.flexibility.process": new ValidationRuleModel(DataType.String, true),

    "step_A.stability": new ValidationRuleModel(DataType.Object, true),
    "step_A.stability.hasStability": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.stability.explaination": new ValidationRuleModel(DataType.String, true),
    "step_A.stability.answers": new ValidationRuleModel(DataType.String, true),

    "step_A.interoperabilityIntegration": new ValidationRuleModel(DataType.Object, true),
    "step_A.interoperabilityIntegration.hasInteroperabilityOrIntegration": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.interoperabilityIntegration.explaination": new ValidationRuleModel(DataType.String, true),
    "step_A.interoperabilityIntegration.answers": new ValidationRuleModel(DataType.String, true),

    "step_A.visualizationCapacity": new ValidationRuleModel(DataType.Object, true),
    "step_A.visualizationCapacity.areCapabilitiesPresent": new ValidationRuleModel(DataType.Boolean, true),
    "step_A.visualizationCapacity.explaination": new ValidationRuleModel(DataType.String, true),
    "step_A.visualizationCapacity.answers": new ValidationRuleModel(DataType.String, true),

    step_B: new ValidationRuleModel(DataType.Object, true),
    "step_B.informationSystem": new ValidationRuleModel(DataType.String, true),
    "step_B.additionalAttributes": new ValidationRuleModel(DataType.String, true),
};