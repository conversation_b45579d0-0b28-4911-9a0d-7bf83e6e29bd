import React, { useState } from "react";
import { TabModel } from "../../../../../../../models/TabModel";
import WHOTabs from "../../../../../../controls/WHOTabs";
import { useTranslation } from "react-i18next";
import FlexibilityAttribute from "./FlexibilityAttribute";
import StabilityAttribute from "./StabilityAttribute";
import InteroperabilityAttribute from "./InteroperabilityAttribute";
import VisualizationCapacitiesAttribute from "./VisualizationCapacitiesAttribute";
import { Step_A_Response } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";

type Indicator_2_2_2_Props = {
    step_A: Step_A_Response;
    updateStep_A: (step_A: Step_A_Response) => void;
    onValueChange: (field: string, value: any) => void;
};

/** Response for indicator 2.2.2 Step A */
function Indicator_2_2_2_StepA(props: Indicator_2_2_2_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_1_Indicator_2_2_2_Title");

    const { step_A, updateStep_A } = props;
    const {
        flexibility,
        stability,
        interoperabilityIntegration,
        visualizationCapacity,
    } = step_A;

    // Triggered whenever the HTML element values are changed and update response
    const onValueChange = (
        fieldName: string,
        value: any,
        objProperty: string
    ) => {
        updateStep_A({
            ...step_A,
            [objProperty]: {
                ...step_A[objProperty],
                [fieldName]: value,
            },
        });
    };

    const [currentTab, setCurrentTab] = useState<number>(0);
    const tabs: Array<TabModel> = [
        new TabModel(
            0,
            `${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:Flexibility")}
            <span class="fw-light fst-italic">${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:FlexibilityTabDesc")}</span>`,
            <FlexibilityAttribute
                flexibility={flexibility}
                onValueChange={onValueChange}
            />),
        new TabModel(
            1,
            `${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:Stability")}
            <span class="fw-light fst-italic">${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:StabilityTabDesc")}</span>`,
            <StabilityAttribute
                stability={stability}
                onValueChange={onValueChange}
            />
        ),
        new TabModel(
            2,
            `${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:Interoperability/integration")}
            <span class="fw-light fst-italic">${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InteroperabilityTabDesc")}</span>`,
            <InteroperabilityAttribute
                interoperabilityIntegration={interoperabilityIntegration}
                onValueChange={onValueChange}
            />
        ),
        new TabModel(
            3,
            `${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacities")}
            <span class="fw-light fst-italic">${t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacitiesTabDesc")}</span>`,
            <VisualizationCapacitiesAttribute
                visualizationCapacity={visualizationCapacity}
                onValueChange={onValueChange}
            />
        ),
    ];

    // triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
    };

    return (
        <>
            <div className="response-wrapper">
                <p className="fw-lighter">
                    {t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:ITNAssessment")}
                </p>
                <p className="mt-3 fst-italic">
                    {t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:ITNRoutineResponseTitle")}
                </p>
                <div className="mt-3">
                    <div className="app-tab-wrapper">
                        <WHOTabs
                            tabs={tabs}
                            value={currentTab}
                            onChange={onTabChange}
                            scrollable={false}
                        >
                            <div className="p-3">{tabs[currentTab].children}</div>
                        </WHOTabs>
                    </div>
                </div>
            </div>
        </>
    );
}

export default Indicator_2_2_2_StepA;