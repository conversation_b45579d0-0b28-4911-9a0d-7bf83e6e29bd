﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    "dataQualityActivity": new ValidationRuleModel(DataType.Object, true),
    "dataQualityActivity.dataCleaning": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityActivity.dataReviewMeeting": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityActivity.dataQualityAssessment": new ValidationRuleModel(DataType.Boolean, true),
    "dataQualityActivity.dataQualityIndicator": new ValidationRuleModel(DataType.Boolean, true),

    nationalLevelDataAssessed: new ValidationRuleModel(
        DataType.Number,
        true,
        `${Constants.Common.RootObjectNameSubstitute}.nationalValidationActivity.dataQualityAssessment === "Quarterly" && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed >= 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed <=4) || ${Constants.Common.RootObjectNameSubstitute}.nationalValidationActivity.dataQualityAssessment === "Monthly" && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed >= 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed <=12) || ${Constants.Common.RootObjectNameSubstitute}.nationalValidationActivity.dataQualityAssessment === "Weekly" && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed >= 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed <=52) || ${Constants.Common.RootObjectNameSubstitute}.nationalValidationActivity.dataQualityAssessment === "Annually" && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed >= 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed <=1) || ${Constants.Common.RootObjectNameSubstitute}.nationalValidationActivity.dataQualityAssessment === "Biannually" && !(${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed >= 0 && ${Constants.Common.RootObjectNameSubstitute}.nationalLevelDataAssessed <=2)`,
        "Errors.ValueBetweenDenominatorRange"
    ),
    nationalValidationActivity: new ValidationRuleModel(DataType.Object, false),
    "nationalValidationActivity.dataQualityAssessment": new ValidationRuleModel(
        DataType.String,
        true
    ),
};

export default ValidationRules;