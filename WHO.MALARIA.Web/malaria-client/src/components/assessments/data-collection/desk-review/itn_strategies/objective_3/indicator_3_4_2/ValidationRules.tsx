﻿import { DataType } from '../../../../../../../models/Enums';
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
    "malariaIndicators": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`malariaIndicators[${Constants.Common.IndexSubstitute}].indicatorMonitored`]: new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.malariaIndicators.some(data => data.indicatorMonitored === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListIndicatorsCount !== ${Constants.Common.RootObjectNameSubstitute}.malariaIndicators.length))`
    ),
}

export default ValidationRules;