import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { Flexibility } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";
import { useSelector } from "react-redux";

type FlexibilityAttribute_Props = {
    flexibility: Flexibility;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 flexibility attribute tab */
const FlexibilityAttribute = (props: FlexibilityAttribute_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { flexibility, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsFlexibility"
                    )}
                </label>
                <RadioButtonGroup
                    id="hasFlexibility"
                    name="hasFlexibility"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={flexibility?.hasFlexibility}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "flexibility"
                        )
                    }
                    error={
                        errors["step_A.flexibility.hasFlexibility"] &&
                        errors["step_A.flexibility.hasFlexibility"]
                    }
                    helperText={
                        errors["step_A.flexibility.hasFlexibility"] &&
                        errors["step_A.flexibility.hasFlexibility"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explaination"
                        name="explaination"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={flexibility?.explaination}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "flexibility"
                            )
                        }
                        error={
                            errors["step_A.flexibility.explaination"] &&
                            errors["step_A.flexibility.explaination"]
                        }
                        helperText={
                            errors["step_A.flexibility.explaination"] &&
                            errors["step_A.flexibility.explaination"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="process"
                        name="process"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:FlexibilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:FlexibilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={flexibility?.process}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "flexibility"
                            )
                        }
                        error={
                            errors["step_A.flexibility.process"] &&
                            errors["step_A.flexibility.process"]
                        }
                        helperText={
                            errors["step_A.flexibility.process"] &&
                            errors["step_A.flexibility.process"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default FlexibilityAttribute;
