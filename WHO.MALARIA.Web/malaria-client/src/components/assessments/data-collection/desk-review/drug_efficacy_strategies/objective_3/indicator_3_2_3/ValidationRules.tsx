﻿import { Constants } from '../../../../../../../models/Constants';
import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

/** Drug efficacy 3.2.3 validation rules */
const ValidationRules: IValidationRuleProvider = {
    "standardizedRecordingTools": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`standardizedRecordingTools[${Constants.Common.IndexSubstitute}].areStandardizedAcrossAllService`]: new ValidationRuleModel(DataType.Boolean, true, `${Constants.Common.RootObjectNameSubstitute}.parentData.recordingTools.length > ${Constants.Common.RootObjectNameSubstitute}.standardizedRecordingTools.length`),
    "standardizedFormsDetails": new ValidationRuleModel(DataType.ArrayOfObject, true),
    [`standardizedFormsDetails[${Constants.Common.IndexSubstitute}].details`]: new ValidationRuleModel(DataType.String, true, `${Constants.Common.RootObjectNameSubstitute}.parentData.recordingTools.length > ${Constants.Common.RootObjectNameSubstitute}.standardizedRecordingTools.length`),
};

export default ValidationRules;