﻿import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    guideline1: new ValidationRuleModel(DataType.Object, true),
    "guideline1.name": new ValidationRuleModel(DataType.String, true),
    "guideline1.linkToCopy": new ValidationRuleModel(DataType.String, false),
    "guideline1.internetAvailability": new ValidationRuleModel(DataType.Boolean, false),

    publicSector: new ValidationRuleModel(DataType.Object, true),
    "publicSector.hasGuidelineReceived": new ValidationRuleModel(DataType.Boolean, true),
    "publicSector.guidelineDetails": new ValidationRuleModel(DataType.String, true),

    privateFormal: new ValidationRuleModel(DataType.Object, true),
    "privateFormal.hasGuidelineReceived": new ValidationRuleModel(DataType.Boolean, true),
    "privateFormal.guidelineDetails": new ValidationRuleModel(DataType.String, true),

    privateInformal: new ValidationRuleModel(DataType.Object, true),
    "privateInformal.hasGuidelineReceived": new ValidationRuleModel(DataType.Boolean, true),
    "privateInformal.guidelineDetails": new ValidationRuleModel(DataType.String, true),

    community: new ValidationRuleModel(DataType.Object, true),
    "community.hasGuidelineReceived": new ValidationRuleModel(DataType.Boolean, true),
    "community.guidelineDetails": new ValidationRuleModel(DataType.String, true),
}

export default ValidationRules;