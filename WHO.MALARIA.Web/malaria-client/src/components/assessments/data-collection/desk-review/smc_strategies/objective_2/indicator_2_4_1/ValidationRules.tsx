﻿import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    surveillanceStaffs: new ValidationRuleModel(
        DataType.ArrayOfObject,
        false
    ),
    [`surveillanceStaffs[${Constants.Common.IndexSubstitute}].staffRole`]: new ValidationRuleModel(
        DataType.String,
        true),
    [`surveillanceStaffs[${Constants.Common.IndexSubstitute}].nationalRequired`]: new ValidationRuleModel(
        DataType.Number,
        true,
        `${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffs[${Constants.Common.IndexSubstitute}].staffRole !==null && ${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffs[${Constants.Common.IndexSubstitute}].nationalRequired === null`
    ),
    [`surveillanceStaffs[${Constants.Common.IndexSubstitute}].nationalCurrentlyAvailable`]: new ValidationRuleModel(
        DataType.Number,
        true,
        `!isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffs[${Constants.Common.IndexSubstitute}].staffRole) && ${Constants.Common.RootObjectNameSubstitute}.surveillanceStaffs[${Constants.Common.IndexSubstitute}].nationalCurrentlyAvailable === null`
    )
};

export default ValidationRules;