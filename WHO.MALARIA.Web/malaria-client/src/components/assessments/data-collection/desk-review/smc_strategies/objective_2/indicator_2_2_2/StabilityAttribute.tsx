﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { Stability } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";
import { useSelector } from "react-redux";

type StabilityAttribute_Props = {
    stability: Stability;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 stability attribute tab */
const StabilityAttribute = (props: StabilityAttribute_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { stability, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsStability"
                    )}
                </label>
                <RadioButtonGroup
                    id="hasStability"
                    name="hasStability"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={stability?.hasStability}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "stability"
                        )
                    }
                    error={
                        errors["step_A.stability.hasStability"] &&
                        errors["step_A.stability.hasStability"]
                    }
                    helperText={
                        errors["step_A.stability.hasStability"] &&
                        errors["step_A.stability.hasStability"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explaination"
                        name="explaination"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={stability?.explaination}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "stability"
                            )
                        }
                        error={
                            errors["step_A.stability.explaination"] &&
                            errors["step_A.stability.explaination"]
                        }
                        helperText={
                            errors["step_A.stability.explaination"] &&
                            errors["step_A.stability.explaination"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="answers"
                        name="answers"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:StabilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:StabilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={stability?.answers}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "stability"
                            )
                        }
                        error={
                            errors["step_A.stability.answers"] &&
                            errors["step_A.stability.answers"]
                        }
                        helperText={
                            errors["step_A.stability.answers"] &&
                            errors["step_A.stability.answers"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default StabilityAttribute;