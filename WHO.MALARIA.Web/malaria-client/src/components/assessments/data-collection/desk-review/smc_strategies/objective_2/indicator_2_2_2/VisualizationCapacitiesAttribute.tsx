﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { VisualizationCapacity } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";
import { useSelector } from "react-redux";

type VisualizationCapacities_Props = {
    visualizationCapacity: VisualizationCapacity;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 visualization capacities attribute tab */
const VisualizationCapacitiesAttribute = (props: VisualizationCapacities_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { visualizationCapacity, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsVisualizationCapacities"
                    )}
                </label>
                <RadioButtonGroup
                    id="areCapabilitiesPresent"
                    name="areCapabilitiesPresent"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={visualizationCapacity?.areCapabilitiesPresent}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "visualizationCapacity"
                        )
                    }
                    error={
                        errors["step_A.visualizationCapacity.areCapabilitiesPresent"] &&
                        errors["step_A.visualizationCapacity.areCapabilitiesPresent"]
                    }
                    helperText={
                        errors["step_A.visualizationCapacity.areCapabilitiesPresent"] &&
                        errors["step_A.visualizationCapacity.areCapabilitiesPresent"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explaination"
                        name="explaination"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        InputLabelProps={{
                            shrink: true,
                        }}
                        rows={3}
                        value={visualizationCapacity?.explaination}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "visualizationCapacity"
                            )
                        }
                        error={
                            errors["step_A.visualizationCapacity.explaination"] &&
                            errors["step_A.visualizationCapacity.explaination"]
                        }
                        helperText={
                            errors["step_A.visualizationCapacity.explaination"] &&
                            errors["step_A.visualizationCapacity.explaination"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="answers"
                        name="answers"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacitiesProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:VisualizationCapacitiesPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={visualizationCapacity?.answers}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "visualizationCapacity"
                            )
                        }
                        error={
                            errors["step_A.visualizationCapacity.answers"] &&
                            errors["step_A.visualizationCapacity.answers"]
                        }
                        helperText={
                            errors["step_A.visualizationCapacity.answers"] &&
                            errors["step_A.visualizationCapacity.answers"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default VisualizationCapacitiesAttribute;