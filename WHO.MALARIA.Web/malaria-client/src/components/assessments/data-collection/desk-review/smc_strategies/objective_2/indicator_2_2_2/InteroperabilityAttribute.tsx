﻿import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import parse from "html-react-parser";
import { useSelector } from "react-redux";
import { InteroperabilityIntegration } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";

type InteroperabilityAttribute_Props = {
    interoperabilityIntegration: InteroperabilityIntegration;
    onValueChange: (field: string, value: any, objProperty: string) => void;
};

/** Response for indicator 2.2.2 interoperability attribute tab */
function InteroperabilityAttribute(props: InteroperabilityAttribute_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { interoperabilityIntegration, onValueChange } = props;
    const errors = useSelector((state: any) => state.error);

    return (
        <>
            <div>
                <label>
                    {t(
                        "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IsInteroperability"
                    )}
                </label>
                <RadioButtonGroup
                    id="hasInteroperabilityOrIntegration"
                    name="hasInteroperabilityOrIntegration"
                    options={[
                        new MultiSelectModel(true, t("indicators-responses:Common:Yes")),
                        new MultiSelectModel(false, t("indicators-responses:Common:No")),
                    ]}
                    row
                    value={interoperabilityIntegration?.hasInteroperabilityOrIntegration}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onValueChange(
                            e.target.name,
                            e.currentTarget.value === "true",
                            "interoperabilityIntegration"
                        )
                    }
                    error={
                        errors["step_A.interoperabilityIntegration.hasInteroperabilityOrIntegration"] &&
                        errors["step_A.interoperabilityIntegration.hasInteroperabilityOrIntegration"]
                    }
                    helperText={
                        errors["step_A.interoperabilityIntegration.hasInteroperabilityOrIntegration"] &&
                        errors["step_A.interoperabilityIntegration.hasInteroperabilityOrIntegration"]
                    }
                />
            </div>
            <div className="row mt-3">
                <div className="col-md-12">
                    <TextBox
                        id="explaination"
                        name="explaination"
                        label={t("indicators-responses:Common:Explain")}
                        fullWidth
                        multiline
                        rows={3}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={interoperabilityIntegration?.explaination}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "interoperabilityIntegration"
                            )
                        }
                        error={
                            errors["step_A.interoperabilityIntegration.explaination"] &&
                            errors["step_A.interoperabilityIntegration.explaination"]
                        }
                        helperText={
                            errors["step_A.interoperabilityIntegration.explaination"] &&
                            errors["step_A.interoperabilityIntegration.explaination"]
                        }
                    />
                </div>
            </div>

            <div className="row mt-5">
                <div className="col-md-12">
                    <TextBox
                        id="answers"
                        name="answers"
                        label={parse(
                            t(
                                "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InteroperabilityProcess"
                            )
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InteroperabilityPlaceholder"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        className="lp-text inputfocus"
                        value={interoperabilityIntegration?.answers}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange(
                                e.target.name,
                                e.currentTarget.value,
                                "interoperabilityIntegration"
                            )
                        }
                        error={
                            errors["step_A.interoperabilityIntegration.answers"] &&
                            errors["step_A.interoperabilityIntegration.answers"]
                        }
                        helperText={
                            errors["step_A.interoperabilityIntegration.answers"] &&
                            errors["step_A.interoperabilityIntegration.answers"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default InteroperabilityAttribute;