import { StrategyName } from "../../../../../models/Enums";

/**Custom hook to handle all the calculations for the Indicators */
const useCalculation = () => {
    /**Calculates the percentage for the numerator and denominator value */
    const calculatePercentage = (numerator: number, denominator: number) => {
        if (numerator && denominator) {
            const rate = Math.round((numerator / denominator) * 100);

            return isNaN(rate) ? 0 : rate;
        }

        return 0;
    };

    /**Calculates the Rate for the array of data for the paticular modelKeyName */
    const calculateRate = (values: Array<number>, thresholdValue: number) => {
        let rate: number;
        const checkedColumnLength = values.filter(
            (column: number) => column !== null && 0 <= column && column < thresholdValue
        ).length;
     
        rate =
            values.length > 0
                ? Math.round((checkedColumnLength / values.length) * 100)
                : 0;

        return isNaN(rate) ? 0 : rate;
    };

    /**Calculates the percentage from YesNo" values*/
    const calculatePercentageOfYesNo = (values: Array<boolean>) => {
        const trueValuesLength = values.filter(
            (value: boolean) => value === true
        ).length;

        const rate = Math.round((trueValuesLength / values.length) * 100);

        return isNaN(rate) ? 0 : rate;
    };

    /**Calculates the Proportion Of Expected Content */
    const calculateProportionOfExpectedContent = (
        values: Array<boolean>,
        strategy: string
    ) => {
        let rate: number;

        const trueValuesLength = values.filter(
            (value: boolean) => value === true
        ).length;

        switch (strategy) {
            case StrategyName.Burden:
                rate = Math.round((trueValuesLength / 3) * 100);

                return isNaN(rate) ? 0 : rate;

            case StrategyName.Elimination:
                rate = Math.round((trueValuesLength / 10) * 100);

                return isNaN(rate) ? 0 : rate;
        }
    };

    /**Calculates the Proportion Rate for the array */
    const calculateProportionRate = (
        columnOneSum: number,
        columnTwoSum: number
    ) => {
        let rate: number;
        rate = Math.round((columnTwoSum / columnOneSum) * 100);

        return isNaN(rate) || rate === Infinity ? 0 : rate;
    };

    /**Checks if array has any null value or not */
    const calculateNullPercentageInArray = (data: Array<Array<string>>) => {
        let result: boolean[] = data.map((item: Array<string | null>) => {
            if (item.includes(null) === true || item.includes("") === true) {
                return false;
            } else {
                return true;
            }
        });

        return calculatePercentageOfYesNo(result);
    };

    /**Checks if array has any null value or not */
    const calculateNumberOfNotNullInArray = (data: Array<string>) => {
        return data.flat().filter((item) => item).length;
    };

    /**Checks if array has any null or false value or not */
    const calculateProportionOfNullAndFalseValues = (
        data: Array<Array<boolean>>
    ) => {
        let result: boolean[] = data.map((item: Array<boolean | null>) => {
            if (item.includes(false) === true || item.includes(null) === true) {
                return false;
            } else {
                return true;
            }
        });

        return calculatePercentageOfYesNo(result);
    };

    /**Calculates the percentage gap of two values */
    const calculatePercentageGap = (valueOne: number, valueTwo: number) => {
        const gap: number = Math.round(((valueOne - valueTwo) / valueOne) * 100);

        return isNaN(gap) ? 0 : gap;
    };

    return {
        calculatePercentage,
        calculateRate,
        calculateProportionRate,
        calculatePercentageOfYesNo,
        calculateProportionOfExpectedContent,
        calculateNullPercentageInArray,
        calculateProportionOfNullAndFalseValues,
        calculatePercentageGap,
        calculateNumberOfNotNullInArray,
    };
};

export default useCalculation;
