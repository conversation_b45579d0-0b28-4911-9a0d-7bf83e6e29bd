import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { StrategiesEnum } from "../../../../../models/Enums";
import { UtilityHelper } from "../../../../../utils/UtilityHelper";

export class GuideModel {
    constructor(
        public sequence: string,
        public howToAssess: string,
        public whatYouNeed: string,
        public interview: string
    ) {
        this.sequence = sequence;
        this.howToAssess = howToAssess;
        this.whatYouNeed = whatYouNeed;
        this.interview = interview;
    }
}

function useGuide(strategyId: string) {
    let translationFileName: string = "";

    switch (strategyId) {

        case StrategiesEnum.Elimination.toLowerCase():
            translationFileName = "elimination-indicator-guide"
            break;

        case StrategiesEnum.MDA.toLowerCase():
            translationFileName = "mda-indicator-guide";
            break;

        case StrategiesEnum.IPTi.toLowerCase():
            translationFileName = "ipti-indicator-guide"
            break;

        case StrategiesEnum.IPTp.toLowerCase():
            translationFileName = "iptp-indicator-guide"
            break;

        case StrategiesEnum.Genomics.toLowerCase():
            translationFileName = "genomic-indicator-guide"
            break;

        case StrategiesEnum.SMC.toLowerCase():
            translationFileName = "smc-indicator-guide"
            break;

        case StrategiesEnum.IRS.toLowerCase():
            translationFileName = "irs-indicator-guide"
            break;

        case StrategiesEnum.ITNsMassCampaign.toLowerCase():
            translationFileName = "itn-mass-indicator-guide"
            break;

        case StrategiesEnum.ITNsRoutine.toLowerCase():
            translationFileName = "itn-routine-indicator-guide"
            break;

        case StrategiesEnum.DrugEfficacy.toLowerCase():
            translationFileName = "drug-efficacy-indicator-guide"
            break;

        case StrategiesEnum.LarvalSourceManagement.toLowerCase():
            translationFileName = "larval-source-management-indicator-guide"
            break;

        case StrategiesEnum.Commodities.toLowerCase():
            translationFileName = "commodity-tracking-indicator-guide"
            break;

        case StrategiesEnum.Ento.toLowerCase():
            translationFileName = "entomology-indicator-guide"
            break;

        default:
            translationFileName = "indicator-guide";
            break;
    }

    const { t } = useTranslation(translationFileName);

    const currentStepIndex: number | null = useSelector(
        (state: any) => state.indicatorGuide.currentStepIndex
    );

    /** Generat guide
     * @param indicatorId string
     * @param strategyId string
     * @param strategyShortName string
     */
    const generateGuide = (sequence: string): React.ReactElement => {
        // check if guide has stepper then get the guide object accordingly
        // fetch the object properties normally
        const isNumber = typeof currentStepIndex === "number";

        const title = isNumber
            ? t(`${sequence}.steps.${currentStepIndex}.title`)
            : '';

        const howToAssess = isNumber
            ? t(`${sequence}.steps.${currentStepIndex}.howToAssess`)
            : t(`${sequence}.howToAssess`);

        const whatYouNeed = isNumber
            ? t(`${sequence}.steps.${currentStepIndex}.whatYouNeed`)
            : t(`${sequence}.whatYouNeed`);

        const interview = isNumber
            ? t(`${sequence}.steps.${currentStepIndex}.interview`)
            : t(`${sequence}.interview`);

        return UtilityHelper.getHowToAssessGuidelines(howToAssess, title, whatYouNeed, interview);
    };

    return { generateGuide };
}

export default useGuide;
