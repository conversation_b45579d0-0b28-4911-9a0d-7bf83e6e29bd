import { Objective } from "../useIndicatorResponseLoader";

import UserMessage from "../../../../../common/UserMessage";
import Indicator_1_3_1_Response from "../../iptp_strategies/objective_1/indicator_1_3_1/Indicator_1_3_1_Response";
import Indicator_1_3_2_Response from "../../iptp_strategies/objective_1/indicator_1_3_2/Indicator_1_3_2_Response";
import Indicator_1_3_3_Response from "../../iptp_strategies/objective_1/indicator_1_3_3/Indicator_1_3_3_Response";
import Indicator_1_3_4_Response from "../../iptp_strategies/objective_1/indicator_1_3_4/Indicator_1_3_4_Response";
import Indicator_1_3_7_Response from "../../iptp_strategies/objective_1/indicator_1_3_7/Indicator_1_3_7_Response";
import Indicator_2_1_4_Response from "../../iptp_strategies/objective_2/indicator_2_1_4/Indicator_2_1_4_Response";
import Indicator_2_2_1_Response from "../../iptp_strategies/objective_2/indicator_2_2_1/Indicator_2_2_1_Response";
import Indicator_2_2_2_ResponseContainer from "../../iptp_strategies/objective_2/indicator_2_2_2/Indicator_2_2_2_ResponseContainer";
import Indicator_2_3_1_Response from "../../iptp_strategies/objective_2/indicator_2_3_1/Indicator_2_3_1_Response";
import Indicator_2_4_1_Response from "../../iptp_strategies/objective_2/indicator_2_4_1/Indicator_2_4_1_Response";
import Indicator_2_4_2_Response from "../../iptp_strategies/objective_2/indicator_2_4_2/Indicator_2_4_2_Response";
import Indicator_2_5_1_Response from "../../iptp_strategies/objective_2/indicator_2_5_1/Indicator_2_5_1_Response";
import Indicator_3_2_1_Response from "../../iptp_strategies/objective_3/indicator_3_2_1/Indicator_3_2_1_Response";
import Indicator_3_2_2_Response from "../../iptp_strategies/objective_3/indicator_3_2_2/Indicator_3_2_2_Response";
import Indicator_3_2_3_Response from "../../iptp_strategies/objective_3/indicator_3_2_3/Indicator_3_2_3_Response";
import Indicator_3_3_2_Response from "../../iptp_strategies/objective_3/indicator_3_3_2/Indicator_3_3_2_Response";
import Indicator_3_4_2_Response from "../../iptp_strategies/objective_3/indicator_3_4_2/Indicator_3_4_2_Response";
import Indicator_3_5_1_Response from "../../iptp_strategies/objective_3/indicator_3_5_1/Indicator_3_5_1_Response";
import Indicator_3_5_2_Response from "../../iptp_strategies/objective_3/indicator_3_5_2/Indicator_3_5_2_Response";
import Indicator_3_6_1_Response from "../../iptp_strategies/objective_3/indicator_3_6_1/Indicator_3_6_1_Response";
import Indicator_4_1_1_Response from "../../iptp_strategies/objective_4/indicator_4_1_1/Indicator_4_1_1_Response";
import Indicator_4_3_1_Response from "../../iptp_strategies/objective_4/indicator_4_3_1/Indicator_4_3_1_Response";
import Indicator_4_4_1_Response from "../../iptp_strategies/objective_4/indicator_4_4_1/Indicator_4_4_1_Response";

/** Renders the Other Iptp strategy screens (Comprehensive) */
function useIPTpStrategies() {
    // get the list of objectives
    const {
        Objective1: {
            Indicator_1_3_1,
            Indicator_1_3_2,
            Indicator_1_3_3,
            Indicator_1_3_4,
            Indicator_1_3_7,
        },
        Objective2: {
            Indicator_2_1_4,
            Indicator_2_2_1,
            Indicator_2_2_2,
            Indicator_2_3_1,
            Indicator_2_4_1,
            Indicator_2_4_2,
            Indicator_2_5_1,
        },
        Objective3: {
            Indicator_3_2_1,
            Indicator_3_2_2,
            Indicator_3_2_3,
            Indicator_3_3_2,
            Indicator_3_4_2,
            Indicator_3_5_1,
            Indicator_3_5_2,
            Indicator_3_6_1,
        },
        Objective4: {
            Indicator_4_1_1,
            Indicator_4_3_1,
            Indicator_4_4_1,
        },
    } = Objective;

    /** Renders the response of the indicators
     * @param sequence A string variable of an indicator's sequence like 1.1.1, 1.1.2 and so on
     * @param label A string variable which represents indicator's name
     */
    const renderIPTpResponse = (sequence: string, label: string) => {
        switch (sequence) {
            // Objective 1     
            case Indicator_1_3_1:
                return <Indicator_1_3_1_Response />;
            case Indicator_1_3_2:
                return <Indicator_1_3_2_Response />;
            case Indicator_1_3_3:
                return <Indicator_1_3_3_Response />;
            case Indicator_1_3_4:
                return <Indicator_1_3_4_Response />;
            case Indicator_1_3_7:
                return <Indicator_1_3_7_Response />;

            // Objective 2     
            case Indicator_2_1_4:
                return <Indicator_2_1_4_Response />;
            case Indicator_2_2_1:
                return <Indicator_2_2_1_Response />;
            case Indicator_2_2_2:
                return <Indicator_2_2_2_ResponseContainer />;
            case Indicator_2_3_1:
                return <Indicator_2_3_1_Response />;
            case Indicator_2_4_1:
                return <Indicator_2_4_1_Response />;
            case Indicator_2_4_2:
                return <Indicator_2_4_2_Response />;
            case Indicator_2_5_1:
                return <Indicator_2_5_1_Response />;


            // Objective 3  
            case Indicator_3_2_1:
                return <Indicator_3_2_1_Response />;
            case Indicator_3_2_2:
                return <Indicator_3_2_2_Response />;
            case Indicator_3_2_3:
                return <Indicator_3_2_3_Response />;
            case Indicator_3_3_2:
                return <Indicator_3_3_2_Response />;
            case Indicator_3_4_2:
                return <Indicator_3_4_2_Response />;
            case Indicator_3_5_1:
                return <Indicator_3_5_1_Response />;
            case Indicator_3_5_2:
                return <Indicator_3_5_2_Response />;
            case Indicator_3_6_1:
                return <Indicator_3_6_1_Response />;

            // Objective 4
            case Indicator_4_1_1:
                return <Indicator_4_1_1_Response />;
            case Indicator_4_3_1:
                return <Indicator_4_3_1_Response />;
            case Indicator_4_4_1:
                return <Indicator_4_4_1_Response />;


            default:
                return (
                    <UserMessage
                        renderContent={
                            <h5>
                                Response for indicator{" "}
                                <strong>{`${sequence} ${label}`} </strong> is under development.
                            </h5>
                        }
                    />
                );
        }
    };

    return { renderIPTpResponse };
}

export default useIPTpStrategies;
