﻿import React from "react";
import { useTranslation } from "react-i18next";

// Language translation wrapper used for the how to assess info section headings which is in UtilityHelper 
const LanguageTranslationWrapperComponent = (props: { keyId : string }) => {
    const { keyId } = props;
    const { t } = useTranslation();

    return <>{t(keyId)}</>;
}

export default LanguageTranslationWrapperComponent;