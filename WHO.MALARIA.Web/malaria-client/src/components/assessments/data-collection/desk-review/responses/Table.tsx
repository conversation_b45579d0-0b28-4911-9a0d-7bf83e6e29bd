import React from "react";

type TableProps = React.TableHTMLAttributes<HTMLTableElement> & {
    children: React.ReactElement;
    className?: string;
};
/** Renders table
 * @param props TableProps
 */
function Table(props: TableProps) {
    const { children, className } = props;
    return (
        <div className="table-responsive">
            <table className={className || "app-table table"} {...props}>
                {children}
            </table>
        </div>
    );
}

export default Table;
