import React from "react";

type TableCellProps = React.DetailedHTMLProps<
  React.TdHTMLAttributes<HTMLTableDataCellElement>,
  HTMLTableDataCellElement
    > & {
        children: React.ReactNode;
};

/** Renders Table cell
 * @param props: TableCellProps
 */
function TableCell(props: TableCellProps) {
  const { children } = props;

  return <td {...props}>{children}</td>;
}

export default TableCell;
