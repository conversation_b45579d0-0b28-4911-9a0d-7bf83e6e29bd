﻿import { useEffect, useState } from "react";
import { ChecklistIndicatorModel } from "../../../../../models/DeskReview/ChecklistIndicatorModel";
import { assessmentService } from "../../../../../services/assessmentService";

/** Custom hook to get the checklist indicators for an indicator response
 *  @param strategyId A string guid value
 */
function useChecklistIndicator(strategyId: string) {
    const [checkListIndicators, setCheckListIndicators] = useState<Array<ChecklistIndicatorModel>>([]);

    useEffect(() => {
        getChecklistIndicators();
    }, []);

    // Bind Checklist Indicators
    const getChecklistIndicators = () => {
        assessmentService
            .getChecklistIndicators(strategyId)
            .then((checkListIndicators: Array<ChecklistIndicatorModel>) => {
                setCheckListIndicators(checkListIndicators);
            });
    };

    return checkListIndicators;
}

export default useChecklistIndicator;