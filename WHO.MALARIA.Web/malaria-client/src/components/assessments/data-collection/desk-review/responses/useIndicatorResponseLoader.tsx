import { useLocation } from "react-router-dom";
import { StrategiesEnum } from "../../../../../models/Enums";
import { GetIndicatorModel } from "../../../../../models/IndicatorModel";
import UserMessage from "../../../../common/UserMessage";
import useCaseStrategies from "./StrategyResponses/useCaseStrategies";
import useSMCStrategies from "./StrategyResponses/useSMCStrategies";
import useIPTpStrategies from "./StrategyResponses/useIPTpStrategies";
import useITPTStrategies from "./StrategyResponses/useIPTIStrategies";
import useITNRoutineChannelStrategy from "./StrategyResponses/useITNRoutineChannelStrategies";
import useGenomicStrategies from "./StrategyResponses/useGenomicStrategies";
import useMDAStrategies from "./StrategyResponses/useMDAStrategies";
import useIRSStrategies from "./StrategyResponses/useIRSStrategies";
import useITNMassCampaignStrategies from "./StrategyResponses/useITNMassCampaignStrategies";
import useLarvalStrategies from "./StrategyResponses/useLarvalStrategies";
import useEntomologyStrategies from "./StrategyResponses/useEntomologyStrategies";
import useCommodityTrackingStrategies from "./StrategyResponses/useCommodityTrackingStrategies";
import useDrugEfficacyStrategies from "./StrategyResponses/useDrugEfficacyStrategies";

/** Renders the Indicator's Response Component */
function useIndicatorResponseLoader() {
    const location: any = useLocation();
    const { renderResponse } = useCaseStrategies();
    const { renderIPTpResponse } = useIPTpStrategies();
    const { renderSMCResponse } = useSMCStrategies();
    const { renderIPTIResponse } = useITPTStrategies();
    const { renderITNRoutineChannelStrategyResponse } = useITNRoutineChannelStrategy();
    const { renderGenomicResponse } = useGenomicStrategies();
    const { renderMDAResponse } = useMDAStrategies();
    const { renderIRSResponse } = useIRSStrategies();
    const { renderITNMassCampaignStrategiesResponse } = useITNMassCampaignStrategies();
    const { renderLarvalResponse } = useLarvalStrategies();
    const { renderEntomologyStrategiesResponse } = useEntomologyStrategies();
    const { renderCommodityTrackingResponse } = useCommodityTrackingStrategies();
    const { renderDrugEfficacyStrategiesResponse } = useDrugEfficacyStrategies();

    const indicators = location?.state?.indicators;
    const strategyShortName = location?.state?.strategyShortName;
    const indicatorId = location?.state?.indicatorId;
    const strategyId = location?.state?.strategyId;
    const strategyIds = location?.state?.strategyIds;
    const indicatorIds = indicators.map(
        (indicator: GetIndicatorModel) => indicator.id
    );

    /** renders the response component based on indicator Id */
    const renderIndicatorResponseComponent = (
        sequence: string,
        label: string
    ) => {
        // Destructing all master Strategies
        const {
            BurdenReduction,
            Elimination,
            Both,
            Commodities,
            LarvalSourceManagement,
            ITNsMassCampaign,
            IPTi,
            SMC,
            Ento,
            Genomics,
            DrugEfficacy,
            ITNsRoutine,
            IPTp,
            MDA,
            IRS,
        } = StrategiesEnum;

        switch (strategyId?.trim()?.toUpperCase()) {
            case BurdenReduction:
            case Elimination:
            case Both:
                return renderResponse(sequence, label);
            case Commodities:
                return renderCommodityTrackingResponse(sequence, label);
            case LarvalSourceManagement:
                return renderLarvalResponse(sequence, label);
            case ITNsMassCampaign:
                return renderITNMassCampaignStrategiesResponse(sequence, label);
            case IPTi:
                return renderIPTIResponse(sequence, label);
            case SMC:
                return renderSMCResponse(sequence, label);
            case Ento:
                return renderEntomologyStrategiesResponse(sequence, label);
            case Genomics:
                return renderGenomicResponse(sequence, label);
            case DrugEfficacy:
                return renderDrugEfficacyStrategiesResponse(sequence, label);
            case ITNsRoutine:
                return renderITNRoutineChannelStrategyResponse(sequence, label);
            case IPTp:
                return renderIPTpResponse(sequence, label);
            case MDA:
                return renderMDAResponse(sequence, label);
            case IRS:
                return renderIRSResponse(sequence, label);

            default:
                return (
                    <UserMessage
                        renderContent={
                            <h5>
                                Response for Strategy <strong> {strategyShortName} </strong>
                                is under development.
                            </h5>
                        }
                    />
                );
        }
    };

    return {
        renderIndicatorResponseComponent,
    };
}

export default useIndicatorResponseLoader;

/** A class which holds objective wise indicators */
export class Objective {
    static Objective1 = class {
        static Indicator_1_1_1 = "1.1.1";
        static Indicator_1_1_2 = "1.1.2";
        static Indicator_1_1_3 = "1.1.3";
        static Indicator_1_1_4 = "1.1.4";
        static Indicator_1_1_5 = "1.1.5";
        static Indicator_1_1_6 = "1.1.6";
        static Indicator_1_1_7 = "1.1.7";
        static Indicator_1_1_8 = "1.1.8";
        static Indicator_1_1_9 = "1.1.9";
        static Indicator_1_3_1 = "1.3.1";
        static Indicator_1_3_2 = "1.3.2";
        static Indicator_1_3_3 = "1.3.3";
        static Indicator_1_3_4 = "1.3.4";
        static Indicator_1_3_5 = "1.3.5";
        static Indicator_1_3_6 = "1.3.6";
        static Indicator_1_3_7 = "1.3.7";
        static Indicator_1_2_11 = "1.2.11";
        static Indicator_1_2_12 = "1.2.12";
        static Indicator_1_2_13 = "1.2.13";
    };
    static Objective2 = class {
        static Indicator_2_1_1 = "2.1.1";
        static Indicator_2_1_2 = "2.1.2";
        static Indicator_2_1_3 = "2.1.3";
        static Indicator_2_1_4 = "2.1.4";
        static Indicator_2_2_1 = "2.2.1";
        static Indicator_2_2_2 = "2.2.2";
        static Indicator_2_2_3 = "2.2.3";
        static Indicator_2_2_4 = "2.2.4";
        static Indicator_2_2_5 = "2.2.5";
        static Indicator_2_2_6 = "2.2.6";
        static Indicator_2_3_1 = "2.3.1";
        static Indicator_2_3_2 = "2.3.2";
        static Indicator_2_4_1 = "2.4.1";
        static Indicator_2_4_2 = "2.4.2";
        static Indicator_2_4_4 = "2.4.4";
        static Indicator_2_5_1 = "2.5.1";
    };
    static Objective3 = class {
        static Indicator_3_1_2 = "3.1.2";
        static Indicator_3_1_3 = "3.1.3";
        static Indicator_3_2_1 = "3.2.1";
        static Indicator_3_2_2 = "3.2.2";
        static Indicator_3_2_3 = "3.2.3";
        static Indicator_3_3_1 = "3.3.1";
        static Indicator_3_3_2 = "3.3.2";
        static Indicator_3_3_4 = "3.3.4";
        static Indicator_3_4_2 = "3.4.2";
        static Indicator_3_3_3 = "3.3.3";
        static Indicator_3_4_1 = "3.4.1";
        static Indicator_3_5_1 = "3.5.1";
        static Indicator_3_5_2 = "3.5.2";
        static Indicator_3_5_3 = "3.5.3";
        static Indicator_3_6_1 = "3.6.1";
    };
    static Objective4 = class {
        static Indicator_4_1_1 = "4.1.1";
        static Indicator_4_1_2 = "4.1.2";
        static Indicator_4_1_3 = "4.1.3";
        static Indicator_4_2_1 = "4.2.1";
        static Indicator_4_2_2 = "4.2.2";
        static Indicator_4_3_1 = "4.3.1";
        static Indicator_4_4_1 = "4.4.1";
        static Indicator_4_4_2 = "4.4.2";
        static Indicator_4_4_3 = "4.4.3";
    };
}
