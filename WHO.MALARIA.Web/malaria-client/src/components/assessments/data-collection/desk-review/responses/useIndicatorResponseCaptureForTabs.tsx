import useIndicatorResponseCapture from "./useIndicatorResponseCapture";

/**
 * Use to capture indicator response for mutiple tabs
 * @param responseModel
 * @param validateFn A validate function which will be called on response change
 */
export default function useIndicatorResponseCaptureForTabs<T>(
  responseModel: T, validateFn: ((response: any) => void) | null = null
) {
  const {
    response,
    onSave,
    onFinalize,
    getResponse,
    onValueChange,
    onCannotBeAssessed,
    onChange,
    onChangeWithIndex,
    onChangeWithKey,
    onChangeOfArrayWithIndex,
    setTrueFlagOnFinalizeButtonClick
  } = useIndicatorResponseCapture<T>(responseModel, validateFn);

  /**
   * Update the STEP A of the response, name of the step field in the response model must be "step_A"
   * @param step_A
   */
  const updateStep_A = (step_A: any) => {
    onValueChange("step_A", step_A);
  };

  /**
   * Update the STEP B of the response, name of the step field in the response model must be "step_B"
   * @param step_B
   */
  const updateStep_B = (step_B: any) => {
    onValueChange("step_B", step_B);
  };

  /**
   * Update the STEP C of the response, name of the step field in the response model must be "step_C"
   * @param step_C
   */
  const updateStep_C = (step_C: any) => {
    onValueChange("step_C", step_C);
  };

  /**
   * Update the STEP D of the response, name of the step field in the response model must be "step_D"
   * @param step_D
   */
  const updateStep_D = (step_D: any) => {
    onValueChange("step_D", step_D);
  };

  /**
   * Update the STEP E of the response, name of the step field in the response model must be "step_E"
   * @param step_E
   */
  const updateStep_E = (step_E: any) => {
    onValueChange("step_E", step_E);
  };

  /**
   * Update the TAB of the response, keyname of the Tab Object name  and tabResponse is the response model
   * @param keyName
   * @param tabResponse
   */
  const updateTab = (keyName: string, tabResponse: any) => {
    onValueChange(keyName, tabResponse);
  };

  return {
    response,
    onCannotBeAssessed,
    getResponse,
    onSave,
    onChange,
    onFinalize,
    updateStep_A,
    updateStep_B,
    updateStep_C,
    updateStep_D,
    updateStep_E,
    onValueChange,
    updateTab,
    onChangeWithIndex,
    onChangeWithKey,
    onChangeOfArrayWithIndex,
    setTrueFlagOnFinalizeButtonClick
  };
}
