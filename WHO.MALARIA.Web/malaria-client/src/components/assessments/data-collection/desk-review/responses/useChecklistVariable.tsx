﻿import { useEffect, useState } from "react";
import { ChecklistVariableModel } from "../../../../../models/DeskReview/ChecklistVariableModel";
import { assessmentService } from "../../../../../services/assessmentService";

/** Custom hook to get the checklist variables for an indicator response
 *  @param strategyId A string guid value
 */
function useChecklistVariable(strategyId: string, isRecorded: boolean = false) {
    const [checkListVariables, setCheckListVariables] = useState<Array<ChecklistVariableModel>>([]);

    useEffect(() => {
        if (isRecorded) {
            getRecordedChecklistVariables();
        }
        else {
            getReportedChecklistVariables();
        }       
    }, []);

    // Bind Recorded Checklist Variables
    const getRecordedChecklistVariables = () => {
        assessmentService
            .getRecordedChecklistVariables(strategyId)
            .then((checkListVariables: Array<ChecklistVariableModel>) => {
                setCheckListVariables(checkListVariables);
            });
    };

    // Bind Reported Checklist Variables
    const getReportedChecklistVariables = () => {
        assessmentService
            .getReportedChecklistVariables(strategyId)
            .then((checkListVariables: Array<ChecklistVariableModel>) => {
                setCheckListVariables(checkListVariables);
            });
    };

    return checkListVariables;
}

export default useChecklistVariable;