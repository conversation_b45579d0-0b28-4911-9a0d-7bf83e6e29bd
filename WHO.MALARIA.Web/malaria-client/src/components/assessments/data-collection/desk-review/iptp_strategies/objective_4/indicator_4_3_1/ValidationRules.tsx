import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    descriptionOfSupervisionPlan: new ValidationRuleModel(DataType.Object, true),
    "descriptionOfSupervisionPlan.national": new ValidationRuleModel(
        DataType.String,
        true
    ),
    "descriptionOfSupervisionPlan.subNational": new ValidationRuleModel(
        DataType.String,
        true
    ),

    levelOfSupervision: new ValidationRuleModel(DataType.Object, true),
    "levelOfSupervision.national": new ValidationRuleModel(DataType.String, true),
    "levelOfSupervision.subNational": new ValidationRuleModel(
        DataType.String,
        true
    ),

    supervisionGuidelines: new ValidationRuleModel(DataType.Object, true),
    "supervisionGuidelines.national": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "supervisionGuidelines.subNational": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),

    supervisionVisitsChecklists: new ValidationRuleModel(DataType.Object, true),
    "supervisionVisitsChecklists.national": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "supervisionVisitsChecklists.subNational": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),

    supervisionVisitSchedule: new ValidationRuleModel(DataType.Object, true),
    "supervisionVisitSchedule.national": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
    "supervisionVisitSchedule.subNational": new ValidationRuleModel(
        DataType.Boolean,
        true
    ),
};

export default ValidationRules;