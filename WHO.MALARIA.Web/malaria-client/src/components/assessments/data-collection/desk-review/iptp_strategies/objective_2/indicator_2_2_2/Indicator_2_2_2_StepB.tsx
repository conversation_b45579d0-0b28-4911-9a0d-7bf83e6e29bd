﻿import TextBox from "../../../../../../controls/TextBox";
import { useTranslation } from "react-i18next";
import { Step_B_Response } from "../../../../../../../models/DeskReview/Objective_2/Indicator_2_2_2/Response_2";
import { useSelector } from "react-redux";

type Indicator_2_2_2_Props = {
    step_B: Step_B_Response;
    updateStep_B: (step_B: Step_B_Response) => void;
    onValueChange: (field: string, value: any) => void;
};

/** Response for indicator 2.2.2 Step B */
const Indicator_2_2_2_StepB = (props: Indicator_2_2_2_Props) => {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_B, updateStep_B } = props;

    const {
        informationSystem,
        additionalAttributes,
    } = step_B;

    const errors = useSelector((state: any) => state.error);

    // Triggered whenever the HTML element values are changed and update response
    const onValueChange = (fieldName: string, value: any) => {
        updateStep_B({ ...step_B, [fieldName]: value });
    };

    return (
        <>
            <div className="response-wrapper">
                <p className="fw-lighter">
                    {t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IPTPResponseDesc")}
                </p>
                <p className="mt-3 fst-italic">
                    {t("indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:IPTPAssess")}
                </p>
                <div className="mb-4">
                    <TextBox
                        id="informationSystem"
                        name="informationSystem"
                        label={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBDesc1"
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBPlaceholder1"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={informationSystem}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("informationSystem", e.currentTarget.value)
                        }
                        error={
                            errors["step_B.informationSystem"] &&
                            errors["step_B.informationSystem"]
                        }
                        helperText={
                            errors["step_B.informationSystem"] &&
                            errors["step_B.informationSystem"]
                        }
                    />
                </div>

                <div className="mb-4">
                    <TextBox
                        id="additionalAttributes"
                        name="additionalAttributes"
                        label={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBDesc2"
                        )}
                        placeholder={t(
                            "indicators-responses:DRObjective_2_Responses:Indicator_2_2_2:InformationSystemsStepBPlaceholder2"
                        )}
                        fullWidth
                        multiline
                        rows={10}
                        InputLabelProps={{
                            shrink: true,
                        }}
                        value={additionalAttributes}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                            onValueChange("additionalAttributes", e.currentTarget.value)
                        }
                        error={
                            errors["step_B.additionalAttributes"] &&
                            errors["step_B.additionalAttributes"]
                        }
                        helperText={
                            errors["step_B.additionalAttributes"] &&
                            errors["step_B.additionalAttributes"]
                        }
                    />
                </div>
            </div>
        </>
    );
}

export default Indicator_2_2_2_StepB;
