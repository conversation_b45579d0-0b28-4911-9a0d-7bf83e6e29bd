import { Constants } from "../../../../../../../models/Constants";
import { DataType } from "../../../../../../../models/Enums";
import ValidationRuleModel, { IValidationRuleProvider } from "../../../../../../../models/ValidationRuleModel";

const ValidationRules: IValidationRuleProvider = {
    improvementsInFeedbackAndSupervision: new ValidationRuleModel(
        DataType.Object,
        false
    ),

    "improvementsInFeedbackAndSupervision.evidence": new ValidationRuleModel(
        DataType.Boolean,
        false,
        `const evidencesForData=Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["cannotBeAssessed","cannotBeAssessedReason","hasMalariaSurReportBeenProducedInLast12Months","links"].includes(key));
    evidencesForData.every(data =>  ${Constants.Common.RootObjectNameSubstitute}[data].evidence === null)`
    ),

    "improvementsInFeedbackAndSupervision.details": new ValidationRuleModel(
        DataType.String,
        false,
        `(${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.improvementsInFeedbackAndSupervision.details) `,
        "Errors.MandatoryField"
    ),

    initiateSurveillanceTrainingAndDataAnalysis: new ValidationRuleModel(
        DataType.Object,
        false
    ),

    "initiateSurveillanceTrainingAndDataAnalysis.details":
        new ValidationRuleModel(
            DataType.String,
            false,
            `(${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.evidence === true || ${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.evidence === false) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.initiateSurveillanceTrainingAndDataAnalysis.details) `,
            "Errors.MandatoryField"
        ),

};

export default ValidationRules;