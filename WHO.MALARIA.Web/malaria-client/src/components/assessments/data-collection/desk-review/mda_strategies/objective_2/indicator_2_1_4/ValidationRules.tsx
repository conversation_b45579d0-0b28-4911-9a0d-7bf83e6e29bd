﻿import { Constants } from '../../../../../../../models/Constants';
import { DataType } from '../../../../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {   
    "definition1": new ValidationRuleModel(DataType.Object, true),
    "definition1.country": new ValidationRuleModel(DataType.String, true),
    "definition1.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "definition2": new ValidationRuleModel(DataType.Object, true),
    "definition2.country": new ValidationRuleModel(DataType.String, true),
    "definition2.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "adverseEvent": new ValidationRuleModel(DataType.Object, true),
    "adverseEvent.country": new ValidationRuleModel(DataType.String, true),
    "adverseEvent.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "adverseDrugReaction": new ValidationRuleModel(DataType.Object, true),
    "adverseDrugReaction.country": new ValidationRuleModel(DataType.String, true),
    "adverseDrugReaction.isOk": new ValidationRuleModel(DataType.Boolean, true),

    "seriousAdverseEvent": new ValidationRuleModel(DataType.Object, true),
    "seriousAdverseEvent.country": new ValidationRuleModel(DataType.String, true),
    "seriousAdverseEvent.isOk": new ValidationRuleModel(DataType.Boolean, true),
};

export default ValidationRules;