.wrapper {
  width: 100% !important;
}

.wrapper svg {
  /*fill: #008dc3;*/
}

.subCategoriesWrapper {
  flex: 0.2;
  border-right: 1px solid #ddd;
}
.subCategoriesWrapper ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  vertical-align: top;
  /*border-right: 1px solid #ddd;*/
  /*height: 500px;*/
  max-width: 600px;
  /*padding: 15px;*/
  padding: 0px 15px 0px 30px;
  margin-bottom: 0px;
}
.subCategoriesWrapper ul li {
  /*margin-bottom: 60px;*/
  margin-bottom: 30px;
  font-weight: 400;
  cursor: pointer;
  padding-right: 20px;
}

.subCategoriesWrapper ul li:hover {
  color: #008dc3;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}

.subCategoriesWrapper ul li.selected {
  color: #008dc3;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}

.indicatorsWrapper {
  flex: 0.8;
  margin-left: 15px;
  padding: 0px 15px;
}

.indicatorWrapper {
  table {
    tr {
      td.disabled {
        color: #000 !important;
        opacity: 0.5;
        cursor: not-allowed;
      }
      td{
          white-space: normal;
      }
    }
  }
}
