﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ShellTableController : BaseApiController
    {
        private readonly IShellTableQueries _shellTableQueries;

        public ShellTableController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IShellTableQueries shellTableQueries) : base(mediator, httpContextAccessor)
        {
            _shellTableQueries = shellTableQueries;
        }

        /// <summary>
        /// Get collection of indicators, sub-objective, objective information that are associated with the shell table question
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>Collection of shell table question indicators, sub-objectives and objectives</returns>
        [HttpGet]
        [Route("objectives/sub-objectives/indicators/{assessmentId}")]
        public async Task<ActionResult<ObjectivesSubObjectivesIndicatorsDto>> GetObjectiveSubObjectivesIndicatorsAsync(Guid assessmentId)
        {
            return Ok(await _shellTableQueries.GetObjectivesSubObjectivesIndicatorsAsync(assessmentId));
        }

        /// <summary>
        /// Get shell table respondent types and helth facility types for the given assessment id
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table data are to be fetched</param>
        /// <returns>Shell table respondent types and health facility types</returns>
        [HttpGet]
        [Route("respondent-types/health-facility-types/{assessmentId}")]
        public async Task<ActionResult<RespondentAndHealthFacilityTypeDto>> GetRespondentTypesAndHelathFacilityTypesAsync(Guid assessmentId)
        {
            return Ok(await _shellTableQueries.GetRespondentTypesAndHealthFacilityTypesAsync(assessmentId));
        }

        /// <summary>
        /// Get shell table questions by respondent type, level and health facility type
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table data are to be fetched</param>
        /// <param name="respondentType">Respondent type for which shell table data are to be fetched</param>
        /// <param name="geoGraphicLevel">Geo graphic level for which shell table data are to be fetched</param>
        /// <param name="healthFacilityType">Health facility type for which shell table data are to be fetched</param>
        /// <returns>Shell table analysis output</returns>
        [HttpGet]
        [Route("questions/{assessmentId}/{respondentType}/{geoGraphicLevel}/{healthFacilityType}")]
        public async Task<ActionResult<IEnumerable<ShellTableQuestionsDto>>> GetQuestionsAsync(Guid assessmentId, QBRespondentType respondentType, GeoGraphicLevels geoGraphicLevel, HealthFacilityType healthFacilityType)
        {
            return Ok(await _shellTableQueries.GetQuestionsAsync(assessmentId, respondentType, geoGraphicLevel, healthFacilityType));
        }

        /// <summary>
        /// Export shell table questions into excel based on respondent type and health facility type
        /// </summary>        
        /// <returns>An excel file</returns>
        [HttpGet]
        [Route("export/{assessmentId}/{respondentType}/{healthFacilityType}")]
        public async Task<FileContentResult> GetShellTableQuestionsFileResponseAsync(Guid assessmentId, QBRespondentType respondentType, HealthFacilityType healthFacilityType)
        {

            FileResponseDto fileResponseDto = await _shellTableQueries.GetShellTableQuestionsFileResponseAsync(base.GetCurrentUser().UserId, assessmentId, respondentType, healthFacilityType);

            var result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }
    }
}