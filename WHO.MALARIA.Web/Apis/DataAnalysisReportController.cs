﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DataAnalysisReportController : BaseApiController
    {
        private readonly IDataAnalysisReportQueries _dataAnalysisReportQueries;

        public DataAnalysisReportController(
            IMediator mediator,
            IHttpContextAccessor httpContextAccessor,
            IDataAnalysisReportQueries dataAnalysisReportQueries) : base(mediator, httpContextAccessor)
        {
            _dataAnalysisReportQueries = dataAnalysisReportQueries;
        }

        /// <summary>
        /// Upload Data analysis report information
        /// </summary>
        /// <param name="command">Instance of UploadDataAnalysisReportDocumentCommand class - Contains input parameters</param>
        /// <returns>return true if data saved successfully</returns>
        [HttpPost]
        [Route("upload")]
        public async Task<ActionResult> UploadReportInformation([FromForm] UploadDataAnalysisReportDocumentCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Get Data Analysis Reports
        /// </summary>
        /// <param name="assessmentId">Assessment Id </param>
        /// <returns>List of Instance of DataAnalysisReport</returns>
        [HttpGet]
        [Route("reports/{assessmentId}")]
        public async Task<ActionResult> GetReports(Guid assessmentId)
        {
            return Ok(await _dataAnalysisReportQueries.GetReportsAsync(assessmentId));
        }

        /// <summary>
        /// Download file which is saved in database
        /// </summary>
        /// <param name="assessmentId">GUID Assessment Id</param>
        /// <returns>Instance of DataAnalysisReport</returns>
        [HttpGet]
        [Route("download/{reportId}")]
        public async Task<FileContentResult> GetReportDocumentAsync(Guid reportId)
        {
            DataAnalysisReport report = await _dataAnalysisReportQueries.GetReportDocumentAsync(reportId);

            FileContentResult result = new FileContentResult(report.Content, report.ContentType)
            {
                FileDownloadName = report.Name
            };

            return result;
        }

        /// <summary>
        /// Show assessment on global dashboard
        /// </summary>
        /// <param name="command">Instance of ShowAssessmentResultOnGlobalDashboardCommand class - Contains input parameters</param>
        /// <returns>return true if data updated successfully</returns>
        [HttpPost]
        [Route("showAssessmentOnGlobalDashboard")]
        public async Task<ActionResult> ShowAssessmentResultOnGlobalDashboard([FromBody] ShowAssessmentResultOnGlobalDashboardCommand command)
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
            command.CurrentUserId = GetCurrentUser().UserId;
            command.LanguageId = language;
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Publishing an assessment
        /// </summary>
        /// <param name="command">Instance of PublishAssessmentCommand class - Contains input parameters</param>
        /// <returns>return true if data is published successfully</returns>
        [HttpPost]
        [Route("publishAssessment")]
        public async Task<ActionResult> PublishAssessment([FromBody] PublishAssessmentCommand command)
        {           
            command.CurrentUserId = GetCurrentUser().UserId;
           
            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }

        /// <summary>
        /// Deletes uploaded report.
        /// </summary>
        /// <param name="command">Instance of DeleteDataAnalysisReportDocumentCommand - Contains input parameters</param>
        /// <returns>True if deletion of report data is successful</returns>
        [HttpDelete]
        [Route("delete")]
        public async Task<ActionResult<bool>> DeleteReport([FromBody] DeleteDataAnalysisReportDocumentCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }
    }
}
