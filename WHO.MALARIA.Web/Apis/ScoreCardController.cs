﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ScoreCardController : BaseApiController
    {
        private readonly IScoreCardQueries _scoreCardQueries;
        public ScoreCardController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IScoreCardQueries scoreCardQueries) : base(mediator, httpContextAccessor)
        {
            _scoreCardQueries = scoreCardQueries;
        }

        /// <summary>
        /// Get score card details for given assessment id and strategy id 
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>        
        /// <returns>Score card details for given assessment id and strategy id </returns>
        [HttpGet]       
        [Route("details/{assessmentId}")]
        public async Task<ActionResult<ScoreCardDto>> GetScoreCardDetailsAsync(Guid assessmentId)
        {
            return Ok(await _scoreCardQueries.GetScoreCardDetailsAsync(assessmentId));
        }

        /// <summary>
        /// Checks whether the score card can be generated or not
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>      
        /// <returns>True, if score card can be generated else false</returns>
        [HttpGet]
        [Route("canScoreCardBeGenerated/{assessmentId}")]
        public async Task<ActionResult<bool>> CanScoreCardBeGeneratedAsync(Guid assessmentId)
        {
            return Ok(await _scoreCardQueries.CanScoreCardBeGeneratedAsync(assessmentId));
        }
        
        /// <summary>
        /// Set met not met status for survey indicators
        /// </summary>
        /// <param name="command">Instance of SetMetNotMetForSurveyIndicatorsCommand class - Contains input parameters</param>
        /// <returns>Returns true if request is successfully completed</returns>
        [HttpPost]
        [Route("survey/setMetNotMetForIndicators")]
        public async Task<ActionResult> SetMetNotMetForSurveryIndicatorsAsync(SetMetNotMetForSurveyIndicatorsCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }
       
        /// <summary>
        /// Finalize score card details for survey indicators
        /// </summary>
        /// <param name="command">Instance of FinalizeSurveyScoreCardCommand class - Contains input parameters</param>
        /// <returns>return true if data finalized successfully</returns>
        [HttpPost]
        [Route("survey/finalize")]
        public async Task<ActionResult> FinalizeScoreCardAsync(FinalizeSurveyScoreCardCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Export score card data into excel
        /// </summary>        
        /// <returns>An excel file</returns>
        [HttpGet]
        [Route("export/{assessmentId}")]
        public async Task<FileContentResult> ExportScoreCardDataAsync(Guid assessmentId)
        {
            FileResponseDto fileResponseDto = await _scoreCardQueries.GetScoreCardExcelFileResponse(assessmentId, GetCurrentUser().UserId);

            var result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }

        /// <summary>
        /// Checks whether the score card has data or not
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>        
        /// <returns>True, if score card data else false</returns>
        [HttpGet]
        [Route("hasScordCardData/{assessmentId}")]
        public async Task<ActionResult<bool>> HasScoreCardDataAsync(Guid assessmentId)
        {
            return Ok( await _scoreCardQueries.HasScoreCardDataAsync(assessmentId));
        }
    }
}