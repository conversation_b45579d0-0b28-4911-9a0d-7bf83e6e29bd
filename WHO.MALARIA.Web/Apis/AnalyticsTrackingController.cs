﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Domain.Queries.Models;
using WHO.MALARIA.Services.Handlers.Queries;
using WHO.MALARIA.Web.Models.User;

namespace WHO.MALARIA.Web.Apis
{
    [Authorize]
    [Route("api/[controller]")]
    public class AnalyticsTrackingController : BaseApiController
    {
        private readonly IMediator _mediator;
        private readonly IAnalyticsQueries _analyticsQueries;

        public AnalyticsTrackingController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IAnalyticsQueries analyticsQueries) : base(mediator, httpContextAccessor)
        {
            _mediator = mediator;
            _analyticsQueries = analyticsQueries;
        }       

        /// <summary>
        /// Returns the list of google analytics data 
        /// </summary>
        /// <param name="command">GetUserAnalyticsDataCommand object</param>
        /// <returns>List of google analytics data.</returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<GetGoogleAnalyticsData>), 200)]
        [ProducesResponseType(401)]
        [Route("statistics")]
        public async Task<ActionResult> GetGoogleAnalyticsResponseData(GetUserAnalyticsDataCommand command)
        {
            List<GetGoogleAnalyticsData> listOfGoogleAnalyticsData = await _analyticsQueries.GetGoogleAnalyticsResponseData(command);

            return Ok(listOfGoogleAnalyticsData);
        }


    }
}
