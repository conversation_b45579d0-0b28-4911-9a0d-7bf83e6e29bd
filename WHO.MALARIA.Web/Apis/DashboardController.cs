﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DashboardController : BaseApiController
    {
        private readonly IDashboardQueries _dashboardQueries;

        public DashboardController(
            IMediator mediator,
            IHttpContextAccessor httpContextAccessor,
            IDashboardQueries dashboardQueries) : base(mediator, httpContextAccessor)
        {
            _dashboardQueries = dashboardQueries;
        }

        /// <summary>
        /// Gets Dashboard record
        /// </summary> 
        /// <param name="countryId">Country Id of logged in user</param>
        /// <param name="year">Year of an Assessment</param>
        /// <returns>Instance of DashboardResultDto</returns>
        [HttpGet]
        [Route("records/{countryId}/{year}")]
        public async Task<ActionResult> GetDashboardRecords(Guid countryId, int year)
        {
            Guid userId = GetCurrentUser().UserId;

            DashboardResultDto result = await _dashboardQueries.GetDashboardRecordsAsync(userId, countryId, year);

            if (result == null)
            {
                return NotFound();
            }
            else
            {
                return Ok(result);
            }
        }

        /// <summary>
        /// Gets the assessment years for a country
        /// </summary>
        /// <param name="countryId">Country ID of logged in user</param>
        /// <returns>List of years</returns>
        [HttpGet]
        [Route("years/{countryId}")]
        public async Task<ActionResult> GetYears(Guid countryId)
        {
            Guid userId = GetCurrentUser().UserId;

            IEnumerable<int> result = await _dashboardQueries.GetYearsAsync(userId, countryId);

            return Ok(result);
        }

        /// <summary>
        /// Get global dashboard for regional summary details
        /// </summary>       
        /// <returns>Global dashboard for regional summary details</returns>
        [HttpGet]
        [Route("regionalsummary")]
        public async Task<ActionResult<GlobalDashboardRegionalSummaryDto>> GetRegionalSummaryDetailsAsync()
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
            return Ok(await _dashboardQueries.GetRegionalSummaryDetailsAsync(language));
        }

        /// <summary>
        /// Get global dashboard for indicator summary details
        /// </summary>       
        /// <returns>Global dashboard for indicator summary details</returns>
        [HttpGet]
        [Route("indicatorsummary")]
        public async Task<ActionResult<GlobalDashboardIndicatorSummaryDto>> GetIndicatorSummaryDetailsAsync()
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
            return Ok(await _dashboardQueries.GetIndicatorSummaryDetailsAsync(language));
        }

        /// <summary>
        /// Get global dashboard for map summary details
        /// </summary>       
        /// <returns>Global dashboard for map summary details</returns>
        [HttpGet]
        [Route("mapsummary")]
        public async Task<ActionResult<GlobalDashboardObjectiveMapSummaryDto>> GetObjectiveMapSummaryDetailsAsync()
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
            Guid userId = GetCurrentUser().UserId;
            return Ok(await _dashboardQueries.GetObjectiveMapSummaryDetailsAsync(language, userId));
        }

        /// <summary>
        /// Get assessment status and approach details for dashboard
        /// </summary>
        /// <returns>DashboardAssessmentStatusApproachDTO object</returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("assessments/status-and-approach")]
        public async Task<ActionResult<DashboardAssessmentStatusApproachDTO>> GetDashboardAssessmentStatusesAndApproachesAsync()
        {
            return Ok(await _dashboardQueries.GetDashboardAssessmentStatusesAndApproachesAsync());
        }

        /// <summary>
        ///  Get country wise assessent status details for dashboard 
        /// </summary>
        /// <param name="year">Year of an assessment</param>
        /// <returns>DashboardAssessmentStatusDTO object</returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("assessments/{year}/status")]
        public async Task<ActionResult<DashboardAssessmentStatusDTO>> GetDashboardAssessmentStatusesAsync(int year)
        {
            return Ok(await _dashboardQueries.GetDashboardAssessmentStatusesAsync(year));
        }
    }
}
