﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Domain.Queries.Models;
using WHO.MALARIA.Services.Handlers.Queries;
using WHO.MALARIA.Web.Models.User;

namespace WHO.MALARIA.Web.Apis
{
    [Authorize]
    [Route("api/[controller]")]
    public class CountryController : BaseApiController
    {
        private readonly IMediator _mediator;
        private readonly IUserQueries _userQueries;

        public CountryController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IUserQueries userQueries) : base(mediator, httpContextAccessor)
        {
            _mediator = mediator;
            _userQueries = userQueries;
        }

        /// <summary>
        /// Returns countries whose super manager is not created or not active.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdAndNameDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [Route("countries/without/supermanager")]
        public async Task<ActionResult> GetCountriesWithoutSuperManager()
        {
            IEnumerable<IdAndNameDto> countries = await _userQueries.GetCountriesWithoutSuperManager();

            return Ok(countries);
        }
    }
}
