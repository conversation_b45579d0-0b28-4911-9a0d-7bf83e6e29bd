﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features;
using WHO.MALARIA.Web.Extensions;

namespace WHO.MALARIA.Web.Apis
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class BaseApiController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public BaseApiController(IMediator mediator, IHttpContextAccessor httpContextAccessor)
        {
            _mediator = mediator ?? throw new ArgumentNullException();
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException();
        }

        /// <summary>
        /// Get login user details
        /// </summary>
        /// <returns>WHO Identity object</returns>
        protected WHOIdentity GetCurrentUser()
        {
            ClaimsPrincipal user = _httpContextAccessor.HttpContext.User;
            WHOIdentity whoIdentity = new WHOIdentity(user);

            return whoIdentity;
        }

        protected async Task<TResult> QueryAsync<TResult>(IRequest<TResult> query)
        {
            return await _mediator.Send(query);
        }

        protected ActionResult<T> Single<T>(T data)
        {
            if (data == null) return NotFound();
            return Ok(data);
        }

        protected async Task<TResult> CommandAsync<TResult>(IRequest<TResult> command)
        {
            return await _mediator.Send(command);
        }


        // De-serialize the request object to the TReqest 
        protected async Task<T> SerializeJObjectAsync<T>(T TRequest)
        {
            JsonSerializer serializer = new JsonSerializer();
            JObject jObject = await ConvertToJObjectAsync();
            serializer.Populate(jObject.CreateReader(), TRequest);

            return TRequest;
        }

        /// <summary>
        /// Create a cookie which will later be used by client application
        /// </summary>
        /// <param name="identity">IdentityDto object</param>
        protected void CreateCookie(IdentityDto identity)
        {
            Guid identityId = identity.Id;
            Guid userId = identity.User.Id;
            int userType = identity.User.UserType;
            bool isNewUser = false;
            string name = identity.User.Name;
            string email = identity.Email;

            string userInfoStringify = JsonConvert.SerializeObject(new
            {
                userId,
                identityId,
                isNewUser,
                name,
                email,
                userType,
            });

            HttpContext.Response.Cookies.Append(Constants.Common.UserInfoCookieName, Crypto.Encrypt(userInfoStringify));

        }
        private async Task<JObject> ConvertToJObjectAsync()
        {
            string rawMessage = await Request.GetRawBodyStringAsync();
            JObject jObject = JObject.Parse(rawMessage);

            return jObject;
        }

        /// <summary>
        /// Get parent assessment indicator id by current indicatorId
        /// </summary>
        /// <param name="currentAssessmentIndicatorId">current assessment indicator id </param>
        /// <returns></returns>
        protected Guid GetParentIndicatorId(Guid currenIndicatorId)
        {
            IDictionary<Guid, Guid> _parentChildIndicatorsMapper = new Dictionary<Guid, Guid>() 
            {
                { IndicatorSeedingMetadata.IND_2_3_1,IndicatorSeedingMetadata.IND_2_1_2 },
                { IndicatorSeedingMetadata.IND_3_2_3,IndicatorSeedingMetadata.IND_3_2_1 },
                { IndicatorSeedingMetadata.IND_3_3_1,IndicatorSeedingMetadata.IND_3_2_1 },
                { IndicatorSeedingMetadata.IND_3_3_3,IndicatorSeedingMetadata.IND_3_3_1 }
            };

            _parentChildIndicatorsMapper.TryGetValue(currenIndicatorId, out Guid parentAssessmentIndicatorId);

            return parentAssessmentIndicatorId;
        }
    }
}
