﻿using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Queries;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace WHO.MALARIA.Web.Apis
{
    [Authorize]
    [Route("api/[controller]")]
    public class InternalController : BaseApiController
    {
        public InternalController(IMediator mediator, IHttpContextAccessor httpContextAccessor) : base(mediator, httpContextAccessor)
        {

        }
        /// <summary>
        /// Create new Identity
        /// </summary>
        /// <param name="command">An object of CreateIdentityCommand</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdentityDto>), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("identity/create")]
        public async Task<ActionResult> CreateIdentityAsync([FromBody] CreateIdentityCommand command)
        {
            // this is generic api to create user and so the default user role should be assigned which is Viewer
            command.UserType = UserRoleEnum.Viewer;           
            IdentityDto identityDto = await CommandAsync(command);

            CreateCookie(identityDto);
            return Ok(identityDto);
        }

        /// <summary>
        /// Create new identity and user with SuperManager role
        /// </summary>
        /// <param name="command">An object of CreateIdentityCommand</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IdentityDto), 200)]
        [ProducesResponseType(302)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [Route("identity/create/supermanager")]
        public async Task<ActionResult> CreateSuperManagerAsync([FromBody] CreateIdentityCommand command)
        {
            command.UserType = UserRoleEnum.SuperManager;
            IdentityDto identityDto = await CommandAsync(command);
            return Ok(identityDto);
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdentityDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [Route("identities")]
        public async Task<ActionResult> GetIdentitiesAsync()
        {
            GetIdentityQuery query = await SerializeJObjectAsync(new GetIdentityQuery());
            return Ok(await QueryAsync(new GetIdentityQuery(query.FilterCriterias)));
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdAndNameDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [Route("{entity}/records")]
        public async Task<ActionResult> GetMultipleRecordsAsync()
        {
            GetEntityMultipleRecordsQuery query = await SerializeJObjectAsync(new GetEntityMultipleRecordsQuery());

            return Ok(await QueryAsync(new GetEntityMultipleRecordsQuery(query.Entity, query.FilterCriterias)));
        }

        /// <summary>
        ///  Upload malaria toolkit document
        /// </summary>
        /// <param name="command">Object of UploadDocumentCommand</param>  
        /// <returns>True if file uploaded successfully</returns>
        [HttpPost]
        [DisableRequestSizeLimit, RequestFormLimits(MultipartBodyLengthLimit = 52428800)]
        [Route("toolkit-document/upload")]
        public async Task<ActionResult> Upload([FromForm] UploadDocumentCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;

            bool isSuccess = await CommandAsync(command);

            return Ok(isSuccess);
        }
    }
}
