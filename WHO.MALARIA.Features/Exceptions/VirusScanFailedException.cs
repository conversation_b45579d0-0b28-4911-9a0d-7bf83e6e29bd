﻿using System;
using System.Collections;
using System.Collections.Generic;

namespace WHO.MALARIA.Features.Exceptions
{
    /// <summary>
    /// Use when virus scanner has failed to perform.
    /// </summary>
    public class VirusScanFailedException : Exception
    {
        private string _exceptionMessage;
        private string _errorCode;
        private string _errorContent;
        private string _message;
        private string _stackTrace;
        private string _source;

        public override IDictionary Data => new Dictionary<string, string>
        {
            { "ErrorCode", _errorCode },
            { "ErroContent", _errorContent },
            { "ExceptionMessage", _exceptionMessage }
        };

        public override string Message => _message;

        public override string StackTrace => _stackTrace;

        public override string Source => _source;

        public VirusScanFailedException(string message,
                                        string exceptionMessage,
                                        string errorCode,
                                        string errorContent,
                                        string stackTrace,
                                        string source)
        {
            _exceptionMessage  = exceptionMessage;
            _errorCode = errorCode;
            _errorContent = errorContent;
            _stackTrace = stackTrace;
            _source = source;
            _message = message;
        }
    }
}
