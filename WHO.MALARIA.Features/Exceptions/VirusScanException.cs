﻿using System;
using System.Collections;
using System.Collections.Generic;

namespace WHO.MALARIA.Features.Exceptions
{
    /// <summary>
    /// Throws when virus scanner detects any vulnerability in the uploaded file.
    /// </summary>
    public class VirusScanException : Exception
    {
        private string _fileName;
        private string _ipAddress;
        private string _message;
        private string _resultJson;

        public override IDictionary Data => new Dictionary<string, string>
        {
            { "FileName", _fileName },
            { "IPAddress", _ipAddress },
            { "ScanResult", _resultJson }
        };

        public override string Message => _message;

        public VirusScanException(string fileName,
                                  string ipAddress,
                                  bool containExecutables,
                                  bool containsInvalidFile,
                                  bool containsScript,
                                  bool containsPasswordProtectedFile,
                                  bool containsRestrictedFileFormat,
                                  bool containsMacros,
                                  bool containsXmlExternalEntities,
                                  bool containsInsecureDeserialization,
                                  bool containsHtml,
                                  string resultJson,
                                  Func<string, string> translateMessage)
        {
            _fileName = fileName;
            _ipAddress = ipAddress;
            _resultJson = resultJson;

            switch (true)
            {
                case true when containExecutables:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainExecutables);
                    break;
                case true when containsInvalidFile:
                    _message = translateMessage(Domain.Constants.Constants.Exception.InvalidFile);
                    break;
                case true when containsScript:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsScript);
                    break;
                case true when containsPasswordProtectedFile:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsPasswordProtectedFile);
                    break;
                case true when containsRestrictedFileFormat:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsRestrictedFileFormat);
                    break;
                case true when containsMacros:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsMacros);
                    break;
                case true when containsXmlExternalEntities:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsXmlExternalEntities);
                    break;
                case true when containsInsecureDeserialization:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsInsecureDeserialization);
                    break;
                case true when containsHtml:
                    _message = translateMessage(Domain.Constants.Constants.Exception.ContainsHtml);
                    break;
                default:
                    _message = translateMessage(Domain.Constants.Constants.Exception.InvalidFile);
                    break;
            }
        }
    }
}
