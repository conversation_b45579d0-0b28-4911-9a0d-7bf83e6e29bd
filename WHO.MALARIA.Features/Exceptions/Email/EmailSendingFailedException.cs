﻿using System;

namespace WHO.MALARIA.Features.Exceptions.Email
{
    /// <summary>
    /// Throw when the system is failed to send an email
    /// </summary>
    public class EmailSendingFailedException: Exception
    {
        public EmailSendingFailedException() : base("Email sending failed!")
        {

        }

        public EmailSendingFailedException(string message) : base(message)
        {

        }
    }
}
