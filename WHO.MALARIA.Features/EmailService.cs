﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using SendGrid;
using SendGrid.Helpers.Mail;

using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Exceptions.Email;
using WHO.MALARIA.Features.Models.Email;
using System.Linq;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Features
{
    /// <summary>
    /// Class to provide email related services
    /// </summary>
    public class EmailService : IEmailService
    {
        public readonly AppSettings _appSettings;
        private readonly ILogger<EmailService> _logger;
        private readonly ISendGridClient _sendGridClient;
        private readonly ITranslationService _translationService;

        public EmailService(AppSettings appSettings, ILogger<EmailService> logger, ISendGridClient sendGridClient, ITranslationService translationService)
        {
            _appSettings = appSettings;
            _logger = logger;
            _sendGridClient = sendGridClient;
            _translationService = translationService;
        }

        /// <summary>
        /// Send an email (Uses SMTP details to send an email)
        /// </summary>
        /// <param name="message">Object of EmailMessage class, contains Email addresses, subject and body of the email</param>
        public void SendEmail(EmailMessage message)
        {
            try
            {
                MailMessage mailMessage = new MailMessage()
                {
                    From = new MailAddress(_appSettings.Email.EmailSender),
                    Subject = message.Subject,
                    Body = message.Body,
                    IsBodyHtml = false,
                    SubjectEncoding = System.Text.Encoding.UTF8,
                    BodyEncoding = System.Text.Encoding.UTF8,
                    Priority = MailPriority.High
                };

                foreach (string to in message.To ?? new string[0])
                {
                    mailMessage.To.Add(new MailAddress(to));
                }

                foreach (string email in message.Bcc ?? new string[0])
                {
                    mailMessage.Bcc.Add(new MailAddress(email));
                }

                SmtpClient client = new SmtpClient
                {
                    Host = _appSettings.Email.SmtpHost,
                    Port = _appSettings.Email.SmtpPort,
                    EnableSsl = true,
                    Credentials = new NetworkCredential(_appSettings.Email.UserName, _appSettings.Email.Password),
                };

                try
                {
                    client.Send(mailMessage);
                }
                catch (SmtpException ex)
                {
                    _logger.LogError(ex, ex.Message);
                    throw new EmailSendingFailedException();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                throw new Exception("Something went wrong!");
            }
        }

        /// <summary>
        /// Sends an email to multiple email addresses thru SendGrid API
        /// </summary>
        /// <param name="to">Collection of to email addresses</param>
        /// <param name="subject">Email's subject</param>
        /// <param name="body">Email's body</param>
        /// <param name="isHtmlBody">Email's body is plain text or html identifier</param>
        /// <returns></returns>
        public async Task SendEmail(string[] to, string subject, string body, bool isHtmlBody = false)
        {
            try
            {
                List<EmailAddress> emails = to.Select(email => new EmailAddress(email)).ToList();

                string plainTextBody = (isHtmlBody == false) ? body : null;
                string htmlBody = (isHtmlBody == true) ? body : null;

                SendGridMessage msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    new EmailAddress(_appSettings.SendGrid.From),
                     emails, subject, plainTextBody, htmlBody);

                await _sendGridClient.SendEmailAsync(msg).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                throw new EmailSendingFailedException(_translationService.GetTranslatedMessage(Constants.Exception.InSufficientPrivilegesToSendEmail));
            }
        }
    }
}
