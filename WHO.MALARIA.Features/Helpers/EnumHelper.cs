﻿using System;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Features.Helpers
{
    public static class EnumHelper
    {
        public static string GetDisplayUserType(int userType)
        {
            if(Enum.IsDefined(typeof(UserRoleEnum), userType))
            {
                UserRoleEnum role = (UserRoleEnum)userType;

                switch(role)
                {
                    case UserRoleEnum.Viewer:
                        return "Viewer";
                    case UserRoleEnum.Manager:
                        return "Manager";
                    case UserRoleEnum.SuperManager:
                        return "Super manager";
                }
            }

            throw new InvalidCastException($"Invalid user type : {userType}");
        }
    }
}
