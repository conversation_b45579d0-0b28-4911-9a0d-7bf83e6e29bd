﻿using System;
using System.Collections.Generic;

using WHO.MALARIA.Domain.SeedingMetadata;

using Indicator_1_1_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_1;
using Indicator_1_1_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_2;
using Indicator_1_1_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_3;
using Indicator_1_1_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_4;
using Indicator_1_1_5 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_5;
using Indicator_1_1_6 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_6;
using Indicator_1_1_7 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_7;
using Indicator_1_1_8 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_8;
using Indicator_1_1_9 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_1_9;

using Indicator_1_3_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_1;
using Indicator_1_3_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_2;
using Indicator_1_3_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_3;
using Indicator_1_3_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_4;
using Indicator_1_3_5 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_5;
using Indicator_1_3_6 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_6;
using Indicator_1_3_7 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_1.Indicator_1_3_7;

using Indicator_2_1_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_1;
using Indicator_2_1_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_2;
using Indicator_2_1_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_3;
using Indicator_2_1_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_4;

using Indicator_2_2_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_1;
using Indicator_2_2_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_2;
using Indicator_2_2_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_3;
using Indicator_2_2_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_4;
using Indicator_2_2_5 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_5;
using Indicator_2_2_6 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_2_6;

using Indicator_2_3_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_3_1;
using Indicator_2_3_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_3_2;

using Indicator_2_4_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_4_1;
using Indicator_2_4_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_4_2;
using Indicator_2_4_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_4_4;

using Indicator_2_5_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_5_1;

using Indicator_3_1_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_2;
using Indicator_3_1_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_1_3;
using Indicator_3_2_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_1;
using Indicator_3_2_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_2;
using Indicator_3_2_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_2_3;

using Indicator_3_3_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_1;
using Indicator_3_3_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_2;
using Indicator_3_3_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_3;
using Indicator_3_3_4 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_4;

using Indicator_3_4_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_1;
using Indicator_3_4_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2;

using Indicator_3_5_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_1;
using Indicator_3_5_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_2;
using Indicator_3_5_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_5_3;

using Indicator_3_6_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_6_1;

using Indicator_4_1_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_1_1;
using Indicator_4_1_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_1_2;
using Indicator_4_1_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_1_3;

using Indicator_4_2_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_2_1;
using Indicator_4_2_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_2_2;

using Indicator_4_3_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_3_1;

using Indicator_4_4_1 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_4_1;
using Indicator_4_4_2 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_4_2;
using Indicator_4_4_3 = WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_4_3;

namespace WHO.MALARIA.Features.Helpers
{
    /// <summary>
    /// Contains mapping details between Indicators and Strategies for their response types
    /// </summary>
    public static class DeskReviewResponseHelper
    {
        private static Dictionary<string, Type> _responseTypes = new Dictionary<string, Type>();

        static DeskReviewResponseHelper()
        {
            MapIndicator_1_1_1();
            MapIndicator_1_1_2();
            MapIndicator_1_1_3();
            MapIndicator_1_1_4();
            MapIndicator_1_1_5();
            MapIndicator_1_1_6();
            MapIndicator_1_1_7();
            MapIndicator_1_1_8();
            MapIndicator_1_1_9();

            MapIndicator_1_3_1();
            MapIndicator_1_3_2();
            MapIndicator_1_3_3();
            MapIndicator_1_3_4();
            MapIndicator_1_3_5();
            MapIndicator_1_3_6();
            MapIndicator_1_3_7();

            MapIndicator_2_1_1();
            MapIndicator_2_1_2();
            MapIndicator_2_1_3();
            MapIndicator_2_1_4();

            MapIndicator_2_2_1();
            MapIndicator_2_2_2();
            MapIndicator_2_2_3();
            MapIndicator_2_2_4();
            MapIndicator_2_2_5();
            MapIndicator_2_2_6();

            MapIndicator_2_3_1();
            MapIndicator_2_3_2();

            MapIndicator_2_4_1();
            MapIndicator_2_4_2();
            MapIndicator_2_4_4();

            MapIndicator_2_5_1();

            MapIndicator_3_1_2();
            MapIndicator_3_1_3();
            MapIndicator_3_2_1();
            MapIndicator_3_2_2();
            MapIndicator_3_2_3();

            MapIndicator_3_3_1();
            MapIndicator_3_3_2();
            MapIndicator_3_3_3();
            MapIndicator_3_3_4();

            MapIndicator_3_4_1();
            MapIndicator_3_4_2();

            MapIndicator_3_5_1();
            MapIndicator_3_5_2();
            MapIndicator_3_5_3();

            MapIndicator_3_6_1();

            MapIndicator_4_1_1();
            MapIndicator_4_1_2();
            MapIndicator_4_1_3();

            MapIndicator_4_2_1();
            MapIndicator_4_2_2();

            MapIndicator_4_3_1();

            MapIndicator_4_4_1();
            MapIndicator_4_4_2();
            MapIndicator_4_4_3();
        }

        /// <summary>
        /// Maps the Indicator 1.1.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_4.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.5 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_5()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_5}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_5.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_5}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_5.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.6 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_6()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_6}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_6.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_6}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_6.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.7 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_7()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_7}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_7}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_7.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.8 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_8()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_8}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_8.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_8}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_8.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_8}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_8.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.1.9 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_1_9()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_9}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_1_9.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_9}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_1_9.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_9}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_1_1_9.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_1_9}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_1_9.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.3.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_1_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_1_3_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.3.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_1_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_1_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_1_3_2.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_1_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_1_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_1_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_1_3_2.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_1_3_2.Response_3));
        }

        /// <summary>
        /// Maps the Indicator 1.3.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_1_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_3}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_1_3_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.3.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_3_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_1_3_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_4}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_1_3_4.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 1.3.5 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_5()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_5}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_5.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_5}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_5.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.3.6 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_6()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_6}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_6.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_6}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_6.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 1.3.7 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_1_3_7()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_1_3_7.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_1_3_7}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_1_3_7.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.1.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_1_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_1_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.1.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_1_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_1_2.Response_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_1_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_1_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.1.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_1_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_1_3.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_1_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.1.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_1_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_1_4.Response_4));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_1_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_1_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_2_1_4.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_1_4.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_1_4.Response_5));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_1_4.Response_5));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_1_4.Response_5));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_1_4.Response_6));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_1_4.Response_6));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_1_4.Response_7));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_1_4.Response_6));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_1_4.Response_6));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_1_4}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_1_4.Response_6));
        }

        /// <summary>
        /// Maps the Indicator 2.2.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_2_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_2_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_2_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_2_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_2_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_4.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.5 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_5()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_5}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_2_5.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_5}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_5.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_5}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_5.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.6 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_2_6()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_6}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_2_6.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_6}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_2_6.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_2_6}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_2_6.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.3.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_3_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_3_1.Response_2));

            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_2_3_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_3_1.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 2.3.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_3_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_3_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_3_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.4.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_4_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_2_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_4_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.4.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_4_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_2_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_2_4_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.2.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_4_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_4_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_4_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_4_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_4_4.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 2.5.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_2_5_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_5_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_2_5_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_5_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_2_5_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_2_5_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_2_5_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.1.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_1_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_1_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_1_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_1_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.1.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_1_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_1_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_1_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.2.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_2_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_2_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.2.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_2_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_2_2.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_2_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_2}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_2_2.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 3.2.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_2_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_2_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_2_3}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_2_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.3.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_3_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_3_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.3.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_3_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_3_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_3_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_2}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_3_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.3.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_3_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_3_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_3_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.3.4 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_3_4()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_4}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_3_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_4}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_3_4.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_3_4}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_3_4.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.4.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_4_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_4_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.4.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_4_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_4_2.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_4_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_4_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_4_2.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 3.5.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_5_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_5_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_5_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_5_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 3.5.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_5_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_5_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_5_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_5_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_5_2.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_2}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_5_2.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 3.5.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_5_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_5_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_5_3.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_5_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_5_3.Response_2));
        }

        /// <summary>
        /// Maps the Indicator 3.6.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_3_6_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_3_6_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_3_6_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_3_6_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.1.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_1_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_4_1_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.1.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_1_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_1_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_1_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_1_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.1.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_1_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_1_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_1_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_1_3.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.2.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_2_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_2_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_2_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.2.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_2_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_2_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_2_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_2_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.3.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_3_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_4_3_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_3_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_4_3_1.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.4.1 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_4_1()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_4_1.Response_4));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_4_1.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.MDA_ID}", typeof(Indicator_4_4_1.Response_2));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.IPTI_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.IPTP_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.GS_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.SMC_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.ITNSMC_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.ITNSRC_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.VC_IRS_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.DES_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.CT_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.ES_ID}", typeof(Indicator_4_4_1.Response_3));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_1}|{StrategySeedingMetadata.VC_LSM_ID}", typeof(Indicator_4_4_1.Response_3));
        }

        /// <summary>
        /// Maps the Indicator 4.4.2 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_4_2()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_2}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_2}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_4_2.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_2}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_4_2.Response_1));
        }

        /// <summary>
        /// Maps the Indicator 4.4.3 responses to its associated strategies, this will be used to get correct response type at run time 
        /// </summary>
        private static void MapIndicator_4_4_3()
        {
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_3}|{StrategySeedingMetadata.BURDEN_REDUCTION_ID}", typeof(Indicator_4_4_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_3}|{StrategySeedingMetadata.ELIMINATION_ID}", typeof(Indicator_4_4_3.Response_1));
            _responseTypes.Add($"{IndicatorSeedingMetadata.IND_4_4_3}|{StrategySeedingMetadata.Both_ID}", typeof(Indicator_4_4_3.Response_1));
        }

        /// <summary>
        /// Get "Type" of the response to perform desk review for the given indicator and strategy
        /// </summary>
        /// <param name="indicatorId">To get mapped response type along with the strategy id</param>
        /// <param name="strategyId">To get mapped response type along with the indicator id</param>
        /// <returns>Type of response</returns>
        public static Type GetResponseType(Guid indicatorId, Guid strategyId)
        {
            if (_responseTypes.TryGetValue($"{indicatorId}|{strategyId}", out Type responseType))
            {
                return responseType;
            }

            throw new KeyNotFoundException("Invalid key provided to get ResponseType");
        }
    }
}
