﻿using System;
using System.Data;

namespace WHO.MALARIA.Features.Helpers
{
    /// <summary>
    /// Helper class for sql queries running related requirements
    /// </summary>
    public static class DatabaseHelper
    {
        public static DataTable BuildUniqueidentifierCollectionDataTable(Guid[] values)
        {
            DataTable table = new DataTable();
            table.Columns.Add("Value", typeof(Guid));

            foreach (var value in values)
            {
                DataRow row = table.NewRow();

                row["Value"] = value;

                table.Rows.Add(row);
            }

            return table;
        }
    }
}
