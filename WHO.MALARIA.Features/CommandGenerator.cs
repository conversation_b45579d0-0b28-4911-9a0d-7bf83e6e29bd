﻿using System;
using System.Collections.Generic;
using System.Linq;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Features
{
    public static class CommandGenerator
    {
        /// <summary>
        /// Build the sql Parameter to be passed in a query
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria </param>
        /// <returns>string of filter criteria</returns>
        public static string BuildWhereClauseParameters(List<FilterCriteria> filterCriterias)
        {
            var criterias = filterCriterias?.Select(field => $"{field.Field} {BuildOperator(field.Operator, field.Field)}").ToList();

            return criterias != null && criterias.Any() ? string.Join(" AND ", criterias) : string.Empty;
        }

        /// <summary>
        ///  Build the operator 
        /// </summary>
        /// <param name="_operator">operator as string</param>
        /// <param name="field">field (column) as a string</param>
        /// <returns>SQL formatted operator</returns>
        private static string BuildOperator(string _operator, string field)
        {
            field = field.Replace(".", "_");
            return _operator switch
            {
                "eq" => $"= @{field}",
                "contains" => $"LIKE @{field}",
                _ => $"{_operator} @{field}",
            };
        }


        /// <summary>
        /// Method to calcute file size 
        /// </summary>
        /// <param name="len">file size</param>
        /// <returns>string</returns>
        public static string CalcuateFileSize(double len)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len/1024;
            }
            // Adjust the format string to your preferences. For example "{0:0.#}{1}" would
            // show a single decimal place, and no space.
            return String.Format("{0:0.##} {1}", len, sizes[order]);
        }
    }
}
