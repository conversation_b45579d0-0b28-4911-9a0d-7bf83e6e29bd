﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace WHO.MALARIA.Features
{
    public static class Crypto
    {
        private static readonly string _key = "01234567890123456789012345678901";
        
        /// <summary>
        /// Encrypt the string which can be later used in client-side to decrypt and use it for references.
        /// </summary>
        /// <param name="text">A text to be encrypted</param>
        /// <returns>Encrypted string</returns>
        public static string Encrypt(string text)
        {
            byte[] iv = new byte[16];
            byte[] array;

            using (Aes aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(_key);
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7;
                aes.Mode = CipherMode.CBC;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using MemoryStream memoryStream = new MemoryStream();
                using CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream, encryptor, CryptoStreamMode.Write);
                using (StreamWriter streamWriter = new StreamWriter((Stream)cryptoStream))
                {
                    streamWriter.Write(text);
                }

                array = memoryStream.ToArray();
            }

            return Convert.ToBase64String(array);
        }

        /// <summary>
        /// Descypt string which was encrypted by the encrypt method
        /// </summary>
        /// <param name="text">String to be descrypted</param>
        /// <returns>Decrypted string</returns>
        public static string Decrypt(string text)
        {
            byte[] iv = new byte[16];

            using (Aes aes = Aes.Create())
            {
                aes.Key = Encoding.UTF8.GetBytes(_key);
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7;
                aes.Mode = CipherMode.CBC;

                using (ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                {
                    byte[] input = Convert.FromBase64String(text);
                    byte[] decryptedArray = decryptor.TransformFinalBlock(input, 0, input.Length);

                    return Encoding.UTF8.GetString(decryptedArray);
                }
            }
        }
    }
}
