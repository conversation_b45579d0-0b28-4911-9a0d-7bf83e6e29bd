﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace WHO.MALARIA.Features.Extensions
{
    /// <summary>
    /// Class to generate predicate dynamically based on type
    /// </summary>
    public static class PredicateExtensions
    {
        /// <summary>
        /// Begin an expression chain
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value">Default return value if the chanin is ended early</param>
        /// <returns>A lambda expression stub</returns>
        public static Expression<Func<T, bool>> Begin<T>(bool value = false)
        {
            if (value)
                return parameter => true; //value cannot be used in place of true/false

            return parameter => false;
        }

        /// <summary>
        /// And an expression chain
        /// </summary>
        /// <typeparam name="T">Type for which expression chain needs to be created</typeparam>
        /// <param name="value">Default return value if the chanin is ended early</param>
        /// <returns>A lambda expression stub</returns>
        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> left,
            Expression<Func<T, bool>> right)
        {
            return CombineLambdas(left, right, ExpressionType.AndAlso);
        }

        /// <summary>
        /// Or an expression chain
        /// </summary>
        /// <typeparam name="T">Type for which expression chain needs to be created</typeparam>
        /// <param name="left">expression for left</param>
        /// <param name="right">expression for right</param>
        /// <returns>A lambda expression stub</returns>
        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
        {
            return CombineLambdas(left, right, ExpressionType.OrElse);
        }

        #region private
        /// <summary>
        /// Combining lambda expression chain
        /// </summary>
        /// <typeparam name="T">Type for which expression chain needs to be created</typeparam>
        /// <param name="left">expression for left</param>
        /// <param name="right">expression for right</param>
        /// <param name="expressionType">type for the expression</param>
        /// <returns>A lambda expression stub</returns>
        private static Expression<Func<T, bool>> CombineLambdas<T>(this Expression<Func<T, bool>> left,
            Expression<Func<T, bool>> right, ExpressionType expressionType)
        {
            //Remove expressions created with Begin<T>()
            if (IsExpressionBodyConstant(left))
                return (right);

            ParameterExpression p = left.Parameters[0];

            SubstituteParameterVisitor visitor = new SubstituteParameterVisitor();
            visitor.Sub[right.Parameters[0]] = p;

            Expression body = Expression.MakeBinary(expressionType, left.Body, visitor.Visit(right.Body));
            return Expression.Lambda<Func<T, bool>>(body, p);
        }

        /// <summary>
        /// Check for constant expression body
        /// </summary>
        /// <typeparam name="T">Type of model</typeparam>
        /// <param name="left">expression for left</param>
        /// <returns></returns>
        private static bool IsExpressionBodyConstant<T>(Expression<Func<T, bool>> left)
        {
            return left.Body.NodeType == ExpressionType.Constant;
        }

        /// <summary>
        /// Class to create substitute for parameter
        /// </summary>
        internal class SubstituteParameterVisitor : ExpressionVisitor
        {
            public Dictionary<Expression, Expression> Sub = new Dictionary<Expression, Expression>();

            /// <summary>
            /// Method to check parameter and replace with new node if required
            /// </summary>
            /// <param name="node">expression parameter</param>
            /// <returns>returns expression</returns>
            protected override Expression VisitParameter(ParameterExpression node)
            {
                Expression newValue;
                if (Sub.TryGetValue(node, out newValue))
                {
                    return newValue;
                }
                return node;
            }
        }

        #endregion
    }
}
