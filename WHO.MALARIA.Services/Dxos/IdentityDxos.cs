﻿using System;
using AutoMapper;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Services.Dxos
{
    public class IdentityDxos : IIdentityDxos
    {
        private readonly IMapper _mapper;

        public IdentityDxos()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Identity, IdentityDto>()
                    .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
                    .ForMember(dst => dst.AuthenticatorKey, opt => opt.MapFrom(src => src.AuthenticatorKey))
                    .ForMember(dst => dst.Email, opt => opt.MapFrom(src => src.Email))
                    .ForMember(dst => dst.Language, opt => opt.MapFrom(src => src.Language))
                    .ForMember(dst => dst.Locked, opt => opt.MapFrom(src => src.Locked))
                    .ForMember(dst => dst.Email, opt => opt.MapFrom(src => src.Email))
                    .ForMember(dst => dst.Mode, opt => opt.MapFrom(src => src.Mode))
                    .ForMember(dst => dst.PasswordChangedOn, opt => opt.MapFrom(src => src.PasswordChangedOn))
                    .ForMember(dst => dst.RecoveryCodes, opt => opt.MapFrom(src => src.RecoveryCodes))
                    .ForMember(dst => dst.ShouldChangePassword, opt => opt.MapFrom(src => src.ShouldChangePassword))
                    .ForMember(dst => dst.Status, opt => opt.MapFrom(src => src.Status))
                    .ForMember(dst => dst.Theme, opt => opt.MapFrom(src => src.Theme))
                    .ForMember(dst => dst.TwoFactorEnabled, opt => opt.MapFrom(src => src.TwoFactorEnabled))
                    .ForMember(dst => dst.UpdatePasswordLink, opt => opt.MapFrom(src => src.UpdatePasswordLink))
                    .ForMember(dst => dst.UpdatePasswordLinkValidUntil, opt => opt.MapFrom(src => src.UpdatePasswordLinkValidUntil))
                    .ForMember(dst => dst.User, opt => opt.Ignore())
                    .ForMember(dst => dst.Username, opt => opt.MapFrom(src => src.Username));                                   

            });

            _mapper = config.CreateMapper();
        }

        public IdentityDto MapIdentityDto(Identity identity)
        {
            return _mapper.Map<Identity, IdentityDto>(identity);
        }

    }
}
