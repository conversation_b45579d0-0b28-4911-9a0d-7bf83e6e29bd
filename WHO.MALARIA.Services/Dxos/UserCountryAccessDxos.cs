﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Text;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Services.Dxos
{
    public class UserCountryAccessDxos : IUserCountryAccessDxos
    {
        private readonly IMapper _mapper;

        public UserCountryAccessDxos()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<UserCountryAccess, UserCountryAccessDto>()
                    .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
                    .ForMember(dst => dst.CountryId, opt => opt.MapFrom(src => src.CountryId))
                    .ForMember(dst => dst.UserId, opt => opt.MapFrom(src => src.UserId))
                    .ForMember(dst => dst.IsActive, opt => opt.MapFrom(src => src.Status));

            });

            _mapper = config.CreateMapper();
        }


        public IEnumerable<UserCountryAccessDto> MapToDto(IEnumerable<UserCountryAccess> userCountryAccesses)
        {
            return _mapper.Map<IEnumerable<UserCountryAccess>, IEnumerable<UserCountryAccessDto>>(userCountryAccesses);
        }
    }
}
