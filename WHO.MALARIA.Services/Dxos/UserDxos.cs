﻿using System;
using AutoMapper;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Services.Dxos
{
    public class UserDxos : IUserDxos
    {
        private readonly IMapper _mapper;

        public UserDxos()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<User, UserDto>()
                    .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
                    .ForMember(dst => dst.IdentityId, opt => opt.MapFrom(src => src.IdentityId))
                    .ForMember(dst => dst.Name, opt => opt.MapFrom(src => src.Name));

            });

            _mapper = config.CreateMapper();
        }

        public UserDto MapUserDto(User user)
        {
            return _mapper.Map<User, UserDto>(user);
        }

    }
}
