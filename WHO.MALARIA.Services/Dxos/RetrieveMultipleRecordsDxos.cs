﻿using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Dxos
{
    public class RetrieveMultipleRecordsDxos<TSourceEntity, TDestination> : IRetrieveMultipleRecordsDxos<TSourceEntity, TDestination>
    {
        private readonly IMapper _mapper;

        public RetrieveMultipleRecordsDxos()
        {
            var config = new MapperConfiguration(cfg =>
            {
                var configuration = new MapperConfiguration(cfg =>
                                    cfg.CreateMap(typeof(TSourceEntity), typeof(TDestination)));

            });

            _mapper = config.CreateMapper();
        }


        public TDestination MapToDto(TSourceEntity entity)
        {
            return _mapper.Map<TSourceEntity, TDestination>(entity);
        }
    }
}
