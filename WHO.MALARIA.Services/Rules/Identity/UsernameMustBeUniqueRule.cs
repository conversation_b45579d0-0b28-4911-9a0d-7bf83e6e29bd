﻿using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Identity
{
    public class UsernameMustBeUniqueRule: IBusinessRule
    {
        private readonly IIdentityRuleChecker _identityChecker;
        private readonly string _username;
        private readonly ITranslationService _translationService;
        public UsernameMustBeUniqueRule(IIdentityRuleChecker identityChecker, string username, ITranslationService translationService)
        {
            _identityChecker = identityChecker;
            _username = username;
            _translationService = translationService;
        }

        public string Message => _username +  _translationService.GetTranslatedMessage("UserNameAlreadyExists");

        public bool IsBroken() => _identityChecker.IsUniqueUsername(_username);
    }
}
