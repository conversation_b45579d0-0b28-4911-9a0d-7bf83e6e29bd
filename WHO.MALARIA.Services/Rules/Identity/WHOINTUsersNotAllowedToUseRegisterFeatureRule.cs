﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Identity
{
    /// <summary>
    /// @Who.int users are not allowed to use register feature.
    /// </summary>
    public class WHOINTUsersNotAllowedToUseRegisterFeatureRule : IBusinessRule
    {      
        private readonly string _email;
        private readonly ITranslationService _translationService; 
        public WHOINTUsersNotAllowedToUseRegisterFeatureRule(ITranslationService translationService, string email)
        {            
            _email = email;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.WHO_INT_UsersNotAllowedUseToRegister);

        public bool IsBroken() => (bool)(_email?.EndsWith("@who.int", StringComparison.OrdinalIgnoreCase));
    }
}