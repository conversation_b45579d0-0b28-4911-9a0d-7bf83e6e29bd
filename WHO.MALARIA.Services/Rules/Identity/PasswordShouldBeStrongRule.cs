﻿using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Identity
{
    public class PasswordShouldBeStrongRule: IBusinessRule
    {
        private readonly string _password;
        private readonly IIdentityRuleChecker _identityChecker;
        private readonly ITranslationService _translationService;

        public PasswordShouldBeStrongRule(IIdentityRuleChecker identityChecker, string password, ITranslationService translationService)
        {
            _identityChecker = identityChecker;
            _password = password;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage("PasswordShouldBeStrong");

        public bool IsBroken() => _identityChecker.IsStrongPassword(_password);
    }
}
