﻿using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Identity
{
    /// <summary>
    /// Checks if the string is valid email address
    /// </summary>
    public class ValidEmailRule: IBusinessRule
    {
        private readonly IIdentityRuleChecker _identityChecker;
        private readonly string _email;
        private readonly ITranslationService _translationService; 
        public ValidEmailRule(ITranslationService translationService, IIdentityRuleChecker identityChecker, string email)
        {
            _identityChecker = identityChecker;
            _email = email;
            _translationService = translationService;
        }

        public string Message => _email +  _translationService.GetTranslatedMessage(Constants.Exception.EmailIsNotValid);

        public bool IsBroken() => !_identityChecker.IsEmailValid(_email);
    }
}
