﻿using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules
{
    public class IdentityEmailMustbeUniqueRule: IBusinessRule
    {
        private string _email;
        private readonly IIdentityRuleChecker _identityChecker;
        private readonly ITranslationService _translationService;

        public IdentityEmailMustbeUniqueRule(ITranslationService translationService, IIdentityRuleChecker identityChecker, string email)
        {
            _email = email;
            _identityChecker = identityChecker;
            _translationService = translationService;
        }

        public string Message => string.Format(_translationService.GetTranslatedMessage(Constants.Exception.IdentityWithEmailAlreadyExist), _email);

        public bool IsBroken() => _identityChecker.IsUnique(_email);
    }
}
