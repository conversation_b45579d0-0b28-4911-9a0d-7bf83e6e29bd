﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is in WHO active directory
    /// </summary>
    public class UserShouldBeInWhoActiveDirectoryRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly string _currentUserEmail;
        private readonly string _userEmail;
        private readonly ITranslationService _translationService;
        private UserExistsInAzureDirectoryStatus _status;
        public UserShouldBeInWhoActiveDirectoryRule(
             ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            string currentUserEmail,
            string userEmail          
           )
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _currentUserEmail = currentUserEmail;
            _userEmail = userEmail;           
        }

        public string Message => _status == UserExistsInAzureDirectoryStatus.InSufficientPrivilegesToAddExistingWHOUser ?
                 _translationService.GetTranslatedMessage(Constants.Exception.AccessTokenExpiredWHOADUser) :
                 _translationService.GetTranslatedMessage(Constants.Exception.AccessToOnlyWHOADUser);

        public bool IsBroken()
        {
            _status = _ruleChecker.IsUserInWHOActiveDirectory(_userEmail, _currentUserEmail).Result;

            if (_status != UserExistsInAzureDirectoryStatus.Exists)
            {
                return true;
            }
            return false;
        }
    }
}
