﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Check if for a given country is there a super manager assigned except the current user.
    /// </summary>
    public class HasDifferentSuperManagerAssignedToCountryRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _countryId;
        private readonly ITranslationService _translationService;
        private readonly Guid _userId;

        public HasDifferentSuperManagerAssignedToCountryRule(ITranslationService translationService, IUserRuleChecker ruleChecker,Guid userId, Guid countryId)
        {
            _ruleChecker = ruleChecker;
            _countryId = countryId;
            _translationService = translationService;
            _userId = userId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.SuperManagerAlreadyPresent);

        public bool IsBroken() => _ruleChecker.HasDifferentSuperManagerAssignedToCountry(_userId, _countryId);
    }
}
