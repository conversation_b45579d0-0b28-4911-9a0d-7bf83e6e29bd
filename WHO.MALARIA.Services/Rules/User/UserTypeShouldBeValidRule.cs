﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    public class UserTypeShouldBeValidRule : IBusinessRule
    {
        private readonly IUserCountryAccessRuleChecker _ruleChecker;
        private readonly int _userType;
        private readonly ITranslationService _translationService;
        public UserTypeShouldBeValidRule(ITranslationService translationService, IUserCountryAccessRuleChecker ruleChecker, int userType)
        {
            _userType = userType;
            _ruleChecker = ruleChecker;
            _translationService = translationService;
        }

        public string Message => _userType + _translationService.GetTranslatedMessage(Constants.Exception.InvalidUserType);

        public bool IsBroken() => !_ruleChecker.IsUserTypeValid(_userType);
    }
}
