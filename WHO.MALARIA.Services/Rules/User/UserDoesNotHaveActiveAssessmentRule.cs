﻿using System;
using System.Collections.Generic;
using System.Text;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Check if user has active assessment(s) assigned to him/her.
    /// </summary>
    public class UserDoesNotHaveActiveAssessmentRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly bool _hasActiveAssessment;

        public UserDoesNotHaveActiveAssessmentRule(ITranslationService translationService, bool hasActiveAssessment)
        {
            _translationService = translationService;
            _hasActiveAssessment = hasActiveAssessment;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.RestrictToDeactivateManager);

        public bool IsBroken() => _hasActiveAssessment;
    }
}
