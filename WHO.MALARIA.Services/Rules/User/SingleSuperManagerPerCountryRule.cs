﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if single super manager for a country is enforced
    /// </summary>
    public class SingleSuperManagerPerCountryRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _countryId;
        private readonly ITranslationService _translationService;

        public SingleSuperManagerPerCountryRule(ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            Guid countryId)
        {
            _ruleChecker = ruleChecker;
            _countryId = countryId;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.SuperManagerAlreadyPresent);

        public bool IsBroken()
        {
           return _ruleChecker.DoesCountryHaveSuperManager(_countryId);
        }
    }
}
