﻿using System;
using System.Linq;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check that super manager should not pass country access change request
    /// </summary>
    public class SuperManagerCannotChangeCountryAccessRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly Guid[] _countryRequestedIds;
        private readonly ITranslationService _translationService;

        public SuperManagerCannotChangeCountryAccessRule(ITranslationService translationService, IUserRuleChecker ruleChecker,
            Guid userId, Guid[] countryRequestedIds)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
            _countryRequestedIds = countryRequestedIds;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserCantRequestOtherCountry);

        public bool IsBroken() => (_countryRequestedIds == null || !_countryRequestedIds.Any()) ? false : (_ruleChecker.IsUserSuperManager(_userId) ? true : false);
    }
}
