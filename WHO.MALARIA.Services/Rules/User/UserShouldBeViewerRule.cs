﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a viewer
    /// </summary>
    public class UserShouldBeViewerRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly ITranslationService _translationService;

        public UserShouldBeViewerRule(
            ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            Guid userId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.OnlyViewerAccess);
        public bool IsBroken() => !_ruleChecker.IsUserViewer(_userId);
    }
}
