﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a WHO admin
    /// </summary>
    public class UserShouldBeWhoAdminRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly ITranslationService _translationService;

        public UserShouldBeWhoAdminRule(
            ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            Guid userId)
        {
            _ruleChecker = ruleChecker;
            _userId = userId;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AccessToOnlyWHOUser);

        public bool IsBroken() => !_ruleChecker.IsUserWHOAdmin(_userId);
    }
}
