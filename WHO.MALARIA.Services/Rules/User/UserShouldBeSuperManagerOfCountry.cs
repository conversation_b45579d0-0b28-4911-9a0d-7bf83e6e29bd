﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a super manager of a country
    /// </summary>
    public class UserShouldBeSuperManagerOfCountry : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly Guid _countryId;
        private readonly string _countryName;
        private readonly ITranslationService _translationService;

        public UserShouldBeSuperManagerOfCountry(
            ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            Guid userId,
            Guid countryId,
            string countryName = ""
            )
        {
            _ruleChecker = ruleChecker;
            _userId = userId;
            _countryId = countryId;
            _countryName = countryName;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserMustBeSuperManagerOfCountry) + _countryName;

        public bool IsBroken() => !_ruleChecker.IsUserSuperManagerOfCountry(_userId, _countryId);
    }
}
