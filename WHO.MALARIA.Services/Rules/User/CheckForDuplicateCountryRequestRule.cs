﻿using System;
using System.Linq;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check whether user requested for countries are already requested or not
    /// </summary>
    public class CheckForDuplicateCountryRequestRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly Guid[] _countryRequestedIds;
        private readonly ITranslationService _translationService;

        public CheckForDuplicateCountryRequestRule(ITranslationService translationService, IUserRule<PERSON>hecker ruleChecker,
            Guid userId, Guid[] countryRequestedIds)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
            _countryRequestedIds = countryRequestedIds;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.CheckForDuplicateUserCountryAccess);

        public bool IsBroken() => (_countryRequestedIds != null && _countryRequestedIds.Any()) ?_ruleChecker.CheckForDuplicateUserCountryAccess(_userId, _countryRequestedIds) : false;
   
    }
}
