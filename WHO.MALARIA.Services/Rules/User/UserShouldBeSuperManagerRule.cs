﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a super manager
    /// </summary>
    public class UserShouldBeSuperManagerRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly ITranslationService _translationService;

        public UserShouldBeSuperManagerRule(
            ITranslationService translationService, 
            IUserRuleChecker ruleChecker,
            Guid userId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.OnlySuperManagerAccess);
        public bool IsBroken() => !_ruleChecker.IsUserSuperManager(_userId);
    }
}
