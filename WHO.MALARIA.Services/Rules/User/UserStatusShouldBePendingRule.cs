﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user status is pending
    /// </summary>
    public class UserStatusShouldBePendingRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly string _userEmail;
        private readonly ITranslationService _translationService;

        public UserStatusShouldBePendingRule(ITranslationService translationService, IUserRuleChecker ruleChecker,
            string userEmail)
        {
            _ruleChecker = ruleChecker;
            _userEmail = userEmail;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserStatusMustBePending);

        public bool IsBroken() => !_ruleChecker.IsUserStatusPending(_userEmail);
    }
}
