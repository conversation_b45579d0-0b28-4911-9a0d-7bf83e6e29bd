﻿using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    public class UpdatedUserTypeBySuperManagerShouldBeValidRule : IBusinessRule
    {
        private readonly int _userType;
        private readonly ITranslationService _translationService;
        public UpdatedUserTypeBySuperManagerShouldBeValidRule(
            ITranslationService translationService, 
            int userType)
        {
            _userType = userType;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidUserTypeUpdateBySuperManager);

        public bool IsBroken() => _userType != (int)UserRoleEnum.Viewer && _userType != (int)UserRoleEnum.Manager;
    }
}
