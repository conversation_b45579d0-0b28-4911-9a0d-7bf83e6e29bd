﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if the user is manager of any in-progress assessment
    /// </summary>
    public class UserIsManagerOfAnyInProgressAssessment : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;       
        private readonly ITranslationService _translationService;

        public UserIsManagerOfAnyInProgressAssessment(
            ITranslationService translationService,
            IUserRule<PERSON>hecker ruleChecker,
            Guid userId)
        {
            _ruleChecker = ruleChecker;
            _userId = userId;           
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserHasActiveAssessmentAsManager);

        public bool IsBroken() => _ruleChecker.IsUserManagerOfAnyInProgressAssessment(_userId);
    }
}
