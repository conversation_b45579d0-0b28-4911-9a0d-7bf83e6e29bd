﻿using System.Collections.Generic;
using System.Linq;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    // <summary>
    /// Rule to check if health facility file contains valid sheet
    /// </summary>
    public class IsHealthFacilityFileContainsValidSheetRule: IBusinessRule
    {       
        private readonly ITranslationService _translationService;
        private readonly string _translationKey;

        public IsHealthFacilityFileContainsValidSheetRule(
            ITranslationService translationService,            
            string translationKey)
        {
            _translationService = translationService;           
            _translationKey = translationKey;
        }

        public string Message => _translationService.GetTranslatedMessage(_translationKey);

        public bool IsBroken() => true;
    }
}