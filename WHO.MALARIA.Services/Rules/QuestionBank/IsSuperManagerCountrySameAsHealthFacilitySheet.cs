﻿using System;
using System.Collections.Generic;
using System.Linq;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    // <summary>
    /// Rule to check if country in uploaded health facility sheet is same as super manager country
    /// </summary>
    public class IsSuperManagerCountrySameAsHealthFacilitySheet : IBusinessRule
    {
        private readonly string _superManagerCountry;
        private readonly string _healthFacilityCountry;
        private readonly ITranslationService _translationService;
        public IsSuperManagerCountrySameAsHealthFacilitySheet(ITranslationService translationService,
            string superManagerCountry, string healthFacilityCountry)
        {
            _superManagerCountry = superManagerCountry;
            _healthFacilityCountry = healthFacilityCountry;
            _translationService = translationService;
        }
        public string Message => $@"{_translationService.GetTranslatedMessage(Constants.Exception.SuperManagerCountrySameAsHealthFacility)}";

        public bool IsBroken() => (_superManagerCountry != _healthFacilityCountry);
    }
}
