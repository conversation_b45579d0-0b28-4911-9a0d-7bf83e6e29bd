﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    // <summary>
    /// Rule to check if uploaded shell table template is valid or not.
    /// </summary>
    public class IsValidShellTableFileRule : IBusinessRule
    {
        private readonly Guid _inputAssessmentId;
        private readonly Guid _templateAssessementId;
        private readonly int _inputRespondentType;
        private readonly int _templateRespondentType;
        private readonly int _inputVersion;
        private readonly int _templateVersion;
        private readonly ITranslationService _translationService;

        public IsValidShellTableFileRule(ITranslationService translationService, Guid inputAssessmentId, Guid templateAssessementId, int inputRespondentType, int templateRespondentType, int inputVersion, int templateVersion)
        {
            _inputAssessmentId = inputAssessmentId;
            _templateAssessementId = templateAssessementId;
            _inputRespondentType = inputRespondentType;
            _templateRespondentType = templateRespondentType;
            _inputVersion = inputVersion;
            _templateVersion = templateVersion;
            _translationService = translationService;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(Constants.Exception.CanNotProcessTemplate)}";

        public bool IsBroken() => (_inputAssessmentId != _templateAssessementId || _inputRespondentType != _templateRespondentType || _inputVersion != _templateVersion);
    }
}
