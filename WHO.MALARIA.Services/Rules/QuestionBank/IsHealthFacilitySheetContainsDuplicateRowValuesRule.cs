﻿using System.Collections.Generic;
using System.Linq;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Common.Services;


namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    // <summary>
    /// Rule to check if collection contains same row value
    /// </summary>
    public class IsHealthFacilitySheetContainsDuplicateRowValuesRule<Type> : IBusinessRule
    {
        private readonly List<HealthFacilityCollectionDto> _values;
        private readonly ITranslationService _translationService;
        private readonly string _translationKey;

        public IsHealthFacilitySheetContainsDuplicateRowValuesRule(
            ITranslationService translationService,
            List<HealthFacilityCollectionDto> values,
            string translationKey)
        {
            _translationService = translationService;
            _values = values;
            _translationKey = translationKey;
        }

        public string Message => _translationService.GetTranslatedMessage(_translationKey);

        public bool IsBroken()
        {
            bool result = false;

            int count = 0;

            count = _values.GroupBy(x => new { x.Code, x.District, x.DistrictCode, x.Name, x.Region, x.Type })
                .Where(d => d.Count() > 1).Count();

            result = count >= 1 ? true : false;
           
            return result;
        }
    }
}