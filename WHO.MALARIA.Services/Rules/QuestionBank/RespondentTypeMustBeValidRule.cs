﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    /// <summary>
    /// Rule to check if all the respondent types provided are valid
    /// </summary>
    class RespondentTypeMustBeValidRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IEnumerable<byte> _respondentTypes;

        public RespondentTypeMustBeValidRule(
            ITranslationService translationService,
            IEnumerable<byte> respondentTypes)
        {
            _translationService = translationService;
            _respondentTypes = respondentTypes;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidRespondentTypeSelected);

        public bool IsBroken()
        {
            foreach (byte type in _respondentTypes)
            {
                if (!Enum.IsDefined(typeof(QBRespondentType), Convert.ToInt32(type)))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
