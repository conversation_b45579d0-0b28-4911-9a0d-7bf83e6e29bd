﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Linq;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Services.Rules.ShellTable
{
    /// <summary>
    /// Check for valid shell table file
    /// </summary>
    public class IsValidShelltTableFile : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IFormFile _file;
        private readonly ShellTableExcelSetting _shellTableExcelSetting;

        public IsValidShelltTableFile(ITranslationService translationService, IFormFile file, ShellTableExcelSetting shellTableExcelSetting)
        {
            _translationService = translationService;
            _file = file;
            _shellTableExcelSetting = shellTableExcelSetting;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidFile);

        public bool IsBroken()
        {
            using (MemoryStream memorystream = new MemoryStream())
            {
                _file.CopyTo(memorystream);
                {
                    using (XLWorkbook workbook = new XLWorkbook(memorystream))
                    {
                        bool isInValid = true;
                        int respondentTypeValue;
                        IXLWorksheet hiddenWorksheet = workbook.Worksheet(workbook.Worksheets.Count);

                        //Check for sheet name is exist or not in uploaded file
                        if (workbook.Worksheets.Any(s => s.Name == _shellTableExcelSetting.HealthFacility.Name || s.Name == _shellTableExcelSetting.HealthFacility.Name_FR) && int.TryParse(hiddenWorksheet.Cell("A3").Value.ToString(), out respondentTypeValue))
                        {
                            isInValid = false;
                        }

                        return isInValid;
                    }
                }
            }
        }
    }
}
