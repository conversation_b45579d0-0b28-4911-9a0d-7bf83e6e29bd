﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
namespace WHO.MALARIA.Services.Rules.Shared
{
    // <summary>ssmentrepo
    /// Rule to check file size should not be greater than the max limit
    /// </summary>
    public class FileSizeCheckRule : IBusinessRule
    {
        private readonly long _input;
        private readonly ITranslationService _translationService;
        private readonly long _maxFileSize;
        private readonly string _errorMessageTranslationKey;

        public FileSizeCheckRule(ITranslationService translationService, long input, long maxFileSize, string errorMessageTranslationKey)
        {
            _input = input;
            _translationService = translationService;
            _maxFileSize = maxFileSize;
            _errorMessageTranslationKey = errorMessageTranslationKey;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(_errorMessageTranslationKey)}";

        public bool IsBroken() => _input > _maxFileSize;
    }
}
