﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.QuestionBank
{
    // <summary>
    /// Rule to check when unique key violation happens
    /// </summary>
    public class UniqueKeyViolationRule: IBusinessRule
    {       
        private readonly ITranslationService _translationService;
        private readonly string _translationKey;

        public UniqueKeyViolationRule(
            ITranslationService translationService,            
            string translationKey)
        {
            _translationService = translationService;           
            _translationKey = translationKey;
        }

        public string Message => _translationService.GetTranslatedMessage(_translationKey);

        public bool IsBroken() => true;
    }
}