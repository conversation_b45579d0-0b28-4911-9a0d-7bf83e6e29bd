﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
namespace WHO.MALARIA.Services.Rules.Shared
{
    // <summary>
    /// Rule to check file name to existing file name
    /// </summary>
    public class FileNameCheckRule : IBusinessRule
    {
        private readonly string _input;
        private readonly ITranslationService _translationService;
        private readonly string _fileName;
        private readonly string _errorMessageTranslationKey;

        public FileNameCheckRule(ITranslationService translationService, string input, string fileName, string errorMessageTranslationKey)
        {
            _input = input;
            _translationService = translationService;
            _fileName = fileName;
            _errorMessageTranslationKey = errorMessageTranslationKey;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(_errorMessageTranslationKey)}";

        public bool IsBroken() => _input != _fileName;
    }
}
