﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check if array of any object types is null or empty
    /// </summary>
    public class IntArrayNotNullOrEmptyRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly int[] _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public IntArrayNotNullOrEmptyRule(ITranslationService translationService, ICommonRuleChecker commonRuleChecker, int[] input, string field)
        {
            _commonRuleChecker = commonRuleChecker;
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.FieldShouldNotBeEmpty)}";

        public bool IsBroken() => _commonRuleChecker.IntArrayIsNullOrEmpty(_input);
    }
}
