﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Checks if a user has permission to work on the assessment
    /// </summary>
    public class UserShouldHavePermissionToWorkOnAssessment : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _ruleChecker;
        private readonly Guid _assessmentId;
        private readonly Guid _userId;
        private readonly UserAssessmentPermission _permission;

        public UserShouldHavePermissionToWorkOnAssessment(
             ITranslationService translationService,
             IAssessmentRuleChecker ruleChecker,
             Guid assessmentId,
             Guid userId,
             UserAssessmentPermission permission)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
            _userId = userId;
            _permission = permission;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserDoesNotHavePermission);

        public bool IsBroken() => !_ruleChecker.HasUserPermissionOnAssessment(_assessmentId, _userId, _permission).Result;
    }
}
