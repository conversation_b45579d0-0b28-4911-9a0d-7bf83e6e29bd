﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;

using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Check if a constant in enumType has a value equal to input
    /// </summary>
    public class IsEnumValueValidRule : IBusinessRule
    {
        private readonly Type _enumType;
        private readonly object _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public IsEnumValueValidRule(ITranslationService translationService, Type enumType, object input, string field)
        {
            _enumType = enumType;
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(Constants.Exception.InvalidValue)}{_field}";

        public bool IsBroken() => !Enum.IsDefined(_enumType, _input);
    }
}
