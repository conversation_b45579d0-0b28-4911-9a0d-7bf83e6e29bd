﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// "Check whether a value 1 is greater than the value 2"
    /// </summary>
    public class IsValueGreaterThanRule : IBusinessRule
    {
        private readonly int _value1;
        private readonly int _value2;
        private readonly string _message;
        private readonly ITranslationService _translationService;
        public IsValueGreaterThanRule(ITranslationService translationService, int value1, int value2, string message)
        {
            _value1 = value1;
            _value2 = value2;
            _message = message;
            _translationService = translationService;
        }
        public string Message => $@"{_translationService.GetTranslatedMessage(_message)}";

        public bool IsBroken() => (_value1 > _value2);
    }
}
