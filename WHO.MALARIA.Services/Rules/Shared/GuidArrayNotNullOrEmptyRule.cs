﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check if array of guid is null or empty
    /// </summary>
    public class GuidArrayNotNullOrEmptyRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly Guid[] _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public GuidArrayNotNullOrEmptyRule(ITranslationService translationService, ICommonRuleChecker commonRuleChecker, Guid[] input, string field)
        {
            _commonRuleChecker = commonRuleChecker;
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.FieldShouldNotBeEmpty)}";

        public bool IsBroken() => _commonRuleChecker.GuidArrayIsNullOrEmpty(_input);
    }
}
