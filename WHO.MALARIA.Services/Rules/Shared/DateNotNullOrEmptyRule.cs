﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// This class is for date field is null or empty rule check for mandatory date field
    /// </summary>
    public class DateNotNullOrEmptyRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly DateTime _input;
        private readonly string _field;
        private readonly ITranslationService _translationService ;

        public DateNotNullOrEmptyRule(ITranslationService translationService, ICommonRuleChecker commonRuleChecker, DateTime input, string field)
        {
            _commonRuleChecker = commonRuleChecker;
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.FieldShouldNotBeEmpty)}";

        public bool IsBroken() => _commonRuleChecker.IsDateNotValid(_input);
    }
}
