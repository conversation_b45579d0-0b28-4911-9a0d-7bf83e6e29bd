﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to show failed message
    /// </summary>
    public class FailedRule : IBusinessRule
    {       
        private readonly ITranslationService _translationService;
        private readonly string _translationErrorMessageKey;

        public FailedRule(ITranslationService translationService, string translationErrorMessageKey)
        {            
            _translationService = translationService;
            _translationErrorMessageKey = translationErrorMessageKey;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(_translationErrorMessageKey)}";

        public bool IsBroken()
        {
            return true;
        }
    }
}
