﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to display validation message when desk review response is invalid.
    /// </summary>
    public class DeskReviewResponseValidationRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;

        public DeskReviewResponseValidationRule(string message)
        {
            Message = message;
        }

        public string Message { get; }

        public bool IsBroken() => true;
    }
}

