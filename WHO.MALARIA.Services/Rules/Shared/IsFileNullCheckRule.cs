﻿using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check if the requested file has the null value
    /// </summary>
    public class IsFileNullCheckRule : IBusinessRule
    {
        private readonly IFormFile _input;
        private readonly ITranslationService _translationService;
        private readonly string _translationErrorMessageKey;

        public IsFileNullCheckRule(ITranslationService translationService, IFormFile input, string translationErrorMessageKey)
        {
            _input = input;
            _translationService = translationService;
            _translationErrorMessageKey = translationErrorMessageKey;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(_translationErrorMessageKey)}";

        public bool IsBroken()
        {
            return _input == null;
        }
    }
}
