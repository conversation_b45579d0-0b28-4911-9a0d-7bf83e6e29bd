﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check requested  file extension is valid or not
    /// </summary>
    public class IsFileExtensionValidRule : IBusinessRule
    {
        private readonly string _input;     
        private readonly ITranslationService _translationService;
        private readonly string[] _validFileExtensions;

        public IsFileExtensionValidRule(ITranslationService translationService, string input, string[] validFileExtensions)
        {
            _input = input;       
            _translationService = translationService;
            _validFileExtensions = validFileExtensions;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(Constants.Exception.InvalidFile)}";

        public bool IsBroken()
        {
            return (!Array.Exists(_validFileExtensions, extension => string.Equals(extension, _input, StringComparison.InvariantCultureIgnoreCase)));
        }
    }
}
