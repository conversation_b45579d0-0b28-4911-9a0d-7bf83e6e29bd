﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    public class StringNotNullOrEmptyRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly string _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public StringNotNullOrEmptyRule(ITranslationService translationService, ICommonRuleChecker commonRuleChecker, string input, string field)
        {
            _commonRuleChecker = commonRuleChecker;
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.FieldShouldNotBeEmpty)}";

        public bool IsBroken() => _commonRuleChecker.StringIsNullOrEmpty(_input);
    }
}
