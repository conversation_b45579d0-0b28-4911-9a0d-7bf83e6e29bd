﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if assessment exists
    /// </summary>
    public class AssessmentShouldExistRule : IBusinessRule
    {
        private readonly IAssessmentRuleChecker _ruleChecker;

        private readonly Guid _assessmentId;
        private readonly ITranslationService _translationService;

        public AssessmentShouldExistRule(
            ITranslationService translationService,
            IAssessmentRuleChecker ruleChecker, 
            Guid assessmentId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AssessmentDoesNotExist);

        public bool IsBroken() => !_ruleChecker.DoesAssessmentExist(_assessmentId);
    }
}
