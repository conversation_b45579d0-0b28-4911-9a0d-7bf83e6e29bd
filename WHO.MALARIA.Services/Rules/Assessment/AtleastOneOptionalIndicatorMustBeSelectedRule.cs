﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if at least one indicator is optional indicator
    /// </summary>
    public class AtleastOneOptionalIndicatorMustBeSelectedRule : IBusinessRule
    {
        private readonly IAssessmentRuleChecker _ruleChecker;
        private readonly Guid[] _indicatorIds;
        private readonly Guid _assessmentId;
        private readonly ITranslationService _translationService;

        public AtleastOneOptionalIndicatorMustBeSelectedRule(ITranslationService translationService, IAssessmentRuleChecker ruleChecker,
            Guid[] indicatorIds, Guid assessmentId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _indicatorIds = indicatorIds;
            _assessmentId = assessmentId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AtleastOneOptionalIndicatorBeSelected);

        public bool IsBroken()
        {
            return !_ruleChecker.IsAnyOptionalIndicatorSelected(_indicatorIds,_assessmentId);
        }
    }
}
