﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if only allowed indicators are selected for assessment strategies
    /// </summary>
    public class ValidIndicatorSelectionForAssessmentRule : IBusinessRule
    {
        private readonly IAssessmentRuleChecker _ruleChecker;

        private readonly Guid _assessmentId;
        private readonly Guid[] _indicatorIds;
        private readonly ITranslationService _translationService;

        public ValidIndicatorSelectionForAssessmentRule(ITranslationService translationService,IAssessmentRuleChecker ruleChecker, 
            Guid assessmentId,
            Guid[] indicatorIds)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
            _indicatorIds = indicatorIds;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidIndicatorsSelectedForStrategy);

        public bool IsBroken() => !_ruleChecker.AreAllowedIndicatorsSelectedForAssessment(_assessmentId, _indicatorIds);
    }
}
