﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if user has permission to upload template 
    /// </summary>
    public class UserShouldHaveUploadFilePermissionRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _ruleChecker;
        private readonly Guid _assessmentId;
        private readonly Guid _userId;
        private readonly UserAssessmentPermission _permission;
        private readonly string _languageKey;

        public UserShouldHaveUploadFilePermissionRule(
            ITranslationService translationService,
            IAssessmentRuleChecker ruleChecker,
            Guid assessmentId,
            Guid userId,
            UserAssessmentPermission permission,
            string languageKey)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
            _userId = userId;
            _permission = permission;
            _languageKey = languageKey;
        }

        public string Message => _translationService.GetTranslatedMessage(_languageKey);

        public bool IsBroken() => !_ruleChecker.HasUserPermissionOnAssessment(_assessmentId, _userId, _permission).Result;
    }
}
