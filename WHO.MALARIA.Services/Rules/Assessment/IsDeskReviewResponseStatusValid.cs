﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if the desk review response status provided is valid
    /// </summary>
    class IsDeskReviewResponseStatusValid : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly int _status;

        public IsDeskReviewResponseStatusValid(
            ITranslationService translationService,
            int status)
        {
            _translationService = translationService;
            _status = status;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidDRResponseStatus);

        public bool IsBroken()
        {
            return !Enum.IsDefined(typeof(DeskReviewAssessmentResponseStatus), _status);
        }
    }
}
