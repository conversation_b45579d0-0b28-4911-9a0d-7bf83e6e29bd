﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Services.Rules.Assessment

{
    /// <summary>
    /// Rule to check if user can create new assessment for country for selected time duration
    /// </summary>
    public class ShouldBeAllowedToCreateAssessment : IBusinessRule
    {
        private readonly IAssessmentRuleChecker _ruleChecker;

        private readonly Guid _countryId;
        private readonly DateTime _startDate;
        private readonly DateTime _endDate;
        private readonly ITranslationService _translationService;

        public ShouldBeAllowedToCreateAssessment(ITranslationService translationService, IAssessmentRuleChecker ruleChecker, 
            Guid countryId, DateTime startDate, DateTime endDate)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _countryId= countryId;
            _startDate = startDate;
            _endDate= endDate;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AssessmentAlreadyCreatedForTheCountryForYear);

        public bool IsBroken() => !_ruleChecker.CanCreateAssessment(_countryId,_startDate,_endDate);
    }
}
