﻿using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if user has specific permission on assessment
    /// </summary>
    public class UserShouldHaveOperationPermissionOnAssessmentRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _ruleChecker;
        private readonly Guid _assessmentId;
        private readonly Guid _userId;
        private readonly UserAssessmentPermission _permission;

        public UserShouldHaveOperationPermissionOnAssessmentRule(
            ITranslationService translationService,
            IAssessmentRuleChecker ruleChecker,
            Guid assessmentId,
            Guid userId,
            UserAssessmentPermission permission)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
            _userId = userId;
            _permission = permission;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.NoEditPermissionOnAssessment);

        public bool IsBroken() => !_ruleChecker.HasUserPermissionOnAssessment(_assessmentId, _userId, _permission).Result;
    }
}
