﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.Assessment
{
    /// <summary>
    /// Rule to check if the dates proposed to be updated in assessment are valid or not
    /// </summary>
    public class UpdatedAssessmentDatesShouldBeValidRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _ruleChecker;
        private readonly Guid _assessmentId;
        private readonly DateTime _startDate;
        private readonly DateTime _endDate;
        private int _failureType;

        public UpdatedAssessmentDatesShouldBeValidRule(
            ITranslationService translationService,
            IAssessmentRuleChecker ruleChecker, 
            Guid assessmentId,
            DateTime startDate,
            DateTime endDate)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _assessmentId = assessmentId;
            _startDate = startDate;
            _endDate = endDate;
        }

        public string Message => _failureType == 1 ? 
            _translationService.GetTranslatedMessage(Constants.Exception.UpdateAssessmentDateIsInvalid) :
            _translationService.GetTranslatedMessage(Constants.Exception.AssessmentAlreadyCreatedForTheCountryForYear);

        public bool IsBroken() => !_ruleChecker.AreDatesToUpdateOnAssessmentValid(_assessmentId, _startDate, _endDate,ref _failureType);
    }
}
