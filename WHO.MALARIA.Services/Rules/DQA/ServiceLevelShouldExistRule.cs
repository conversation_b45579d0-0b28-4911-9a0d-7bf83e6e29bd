﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Rule to check if service level exists
    /// </summary>
    public class ServiceLevelShouldExistRule : IBusinessRule
    {
        private readonly IDQARuleChecker _ruleChecker;

        private readonly Guid _serviceLevelId;
        private readonly ITranslationService _translationService;

        public ServiceLevelShouldExistRule(
            ITranslationService translationService,
            IDQARuleChecker ruleChecker,
            Guid serviceLevelId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _serviceLevelId = serviceLevelId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.ServiceLevelDoesNotExist);

        public bool IsBroken() => !_ruleChecker.DoesServiceLevelExist(_serviceLevelId);
    }
}
