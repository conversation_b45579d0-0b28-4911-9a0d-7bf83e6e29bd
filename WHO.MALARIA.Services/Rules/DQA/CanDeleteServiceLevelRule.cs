﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Checks if service level record of an assessment can be deleted
    /// </summary>
    public class CanDeleteServiceLevelRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IDQARuleChecker _ruleChecker;
        private readonly Guid _assessmentId;

        public CanDeleteServiceLevelRule(
            ITranslationService translationService,
             IDQARuleChecker ruleChecker,
            Guid assessmentId)
        {
            _translationService = translationService;
            _assessmentId = assessmentId;
            _ruleChecker = ruleChecker;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.OnlyServiceLevelRecordPresent);

        public bool IsBroken()
        {
            return (!_ruleChecker.IsLastServiceLevelRecord(_assessmentId));
        }

    }
}
