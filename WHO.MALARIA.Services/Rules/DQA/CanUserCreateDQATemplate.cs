﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    ///  Use to check whether a logged in user has access to create a DQA excel template
    /// </summary>
    public class CanUserCreateDQATemplate : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly int _userType;

        public CanUserCreateDQATemplate(
            ITranslationService translationService,
            int userType)
        {
            _translationService = translationService;
            _userType = userType;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.NoPermissionToCreateTemplate);

        public bool IsBroken()
        {
            return (_userType == (int)UserRoleEnum.Viewer);
        }
    }
}
