﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Rule to display validation message when DQA template file is invalid.
    /// </summary>
    public class DQAFileExceptionMessageRule : IBusinessRule
    {
        private readonly ICommonRuleChecker _commonRuleChecker;

        public DQAFileExceptionMessageRule(string message)
        {
            Message = message;
        }

        public string Message { get; }

        public bool IsBroken() => true;
    }
}

