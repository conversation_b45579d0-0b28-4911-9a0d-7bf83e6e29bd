﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Rule to check if all the register types provided are valid
    /// </summary>
    class RegisterTypeMustBeValidRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IEnumerable<byte> _registerType;

        public RegisterTypeMustBeValidRule(
            ITranslationService translationService,
            IEnumerable<byte> registerType)
        {
            _translationService = translationService;
            _registerType = registerType;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidRegisterTypeSelected);

        public bool IsBroken()
        {
            foreach (var type in _registerType)
            {
                if(!Enum.IsDefined(typeof(DQASLRegisterType),Convert.ToInt32(type)))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
