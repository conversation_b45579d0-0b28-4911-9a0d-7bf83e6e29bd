﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    // <summary>
    /// Rule to check if uploaded template service level id and db service level id is same or not.
    /// </summary>
    public class IsUploadedServiceLevelFileValidRule : IBusinessRule
    {
        private readonly Guid _inputServiceLevelId;
        private readonly Guid _serviceLevelTemplateId;
        private readonly Int16 _inputVersion;
        private readonly Int16 _templateVersion;
        private readonly ITranslationService _translationService;

        public IsUploadedServiceLevelFileValidRule(ITranslationService translationService, Guid inputServiceLevelId, Guid serviceLevelTemplateId, Int16 inputVersion, Int16 templateVersion)
        {
            _inputServiceLevelId = inputServiceLevelId;
            _serviceLevelTemplateId = serviceLevelTemplateId;
            _inputVersion = inputVersion;
            _templateVersion = templateVersion;
            _translationService = translationService;
        }

        public string Message => $@"{_translationService.GetTranslatedMessage(Constants.Exception.CanNotProcessTemplate)}";

        public bool IsBroken() => (_inputServiceLevelId != _serviceLevelTemplateId || _inputVersion != _templateVersion);
    }
}
