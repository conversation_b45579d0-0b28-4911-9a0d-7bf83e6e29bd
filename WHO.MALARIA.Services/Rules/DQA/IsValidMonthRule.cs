﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    ///  Rule to check requested  month is valid or not
    /// </summary>
    public class IsValidMonthRule : IBusinessRule
    {
        private readonly int _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public IsValidMonthRule(ITranslationService translationService, int input, string field)
        {            
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.InvalidMonth)}";

        public bool IsBroken()
        {
            return _input <= 0 || _input > 12;
        }
    }
}
