﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    // <summary>
    /// Rule to check requested  year is valid or not
    /// </summary>
    public class IsYearValidRule : IBusinessRule
    {
        private readonly int _input;
        private readonly string _field;
        private readonly ITranslationService _translationService;

        public IsYearValidRule(ITranslationService translationService, int input, string field)
        {
            _input = input;
            _field = field;
            _translationService = translationService;
        }

        public string Message => $@"{_field}{_translationService.GetTranslatedMessage(Constants.Exception.InvalidYear)}";

        public bool IsBroken()
        {
            return _input < 2010 || _input > DateTime.Now.Year;
        }
    }
}
