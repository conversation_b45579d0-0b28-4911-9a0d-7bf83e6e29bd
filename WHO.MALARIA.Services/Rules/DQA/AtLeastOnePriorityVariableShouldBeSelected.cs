﻿using System;
using System.Collections.Generic;
using System.Linq;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Check at-least one priority variable of DQA is selected or not
    /// </summary>
    public class AtLeastOnePriorityVariableShouldBeSelected : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IDQARuleChecker _dqaRuleChecker;
        private readonly IEnumerable<Guid> _variableIds;

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AtLeastOnePriorityVariableShouldBeSelected);

        public AtLeastOnePriorityVariableShouldBeSelected(ITranslationService translationService, IDQARuleChecker dqaRuleChecker, IEnumerable<Guid> variableIds)
        {
            _translationService = translationService;
            _dqaRuleChecker = dqaRuleChecker;
            _variableIds = variableIds;
        }

        public bool IsBroken() => !_dqaRuleChecker.AtLeastOnePriorityVariableShouldBeSelected(_variableIds);
    }
}
