﻿using System;
using System.Collections.Generic;
using System.Linq;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// At least one variable should be selected for concordance when data system 2 is selected
    /// </summary>
    public class AtLeastOneVariableShouldBeSelectedForConcordance : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly IEnumerable<KeyValuePair<Guid, bool?>> _variables;
        private readonly Guid? _dataSourceId;

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.AtLeastOneVariableShouldBeSelectedForConcordance);

        public AtLeastOneVariableShouldBeSelectedForConcordance(ITranslationService translationService, IEnumerable<KeyValuePair<Guid, bool?>> variables, Guid? dataSourceId)
        {
            _translationService = translationService;
            _variables = variables;
            _dataSourceId = dataSourceId;
        }

        public bool IsBroken()
        {
            if (_dataSourceId != null && _dataSourceId != Guid.Empty)
                return !_variables.Any(x => x.Value == true);

            return false;
        }
    }
}
