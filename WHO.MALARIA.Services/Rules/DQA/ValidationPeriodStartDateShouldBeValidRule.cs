﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;

using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Checks if validation period start date is valid 
    /// </summary>
    public class ValidationPeriodStartDateShouldBeValidRule : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly DateTime _start;
        private readonly DateTime _end;
        private readonly DateTime _validationPeriodStartDate;

        public ValidationPeriodStartDateShouldBeValidRule(
            ITranslationService translationService, DateTime start, DateTime end, DateTime validationPeriodStartDate)
        {
            _start = start;
            _end = end;
            _validationPeriodStartDate = validationPeriodStartDate;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidStartPeriodDate);

        public bool IsBroken()
        {
            // Checking if validation period start date is between from date and to date
            bool isStartPeriodValid = _validationPeriodStartDate.Year < _start.Year && _validationPeriodStartDate.Month < _start.Month;
            bool isEndPeriodValid = _validationPeriodStartDate.Year > _end.Year && _validationPeriodStartDate.Month > _end.Month;           
            return (isStartPeriodValid || isEndPeriodValid);
        }
    }
}
