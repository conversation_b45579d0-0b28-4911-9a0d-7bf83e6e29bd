﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    ///  Rule to check requested  From and To Date Range is valid or not
    /// </summary>
    class FromAndToDateShouldNotBeSameRule : IBusinessRule
    {
        private readonly DateTime _fromDate;
        private readonly DateTime _toDate;
        private readonly ITranslationService _translationService;

        public FromAndToDateShouldNotBeSameRule(ITranslationService translationService, DateTime fromDate, DateTime toDate)
        {
            _fromDate = fromDate;
            _toDate = toDate;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidYearMonthRange);

        public bool IsBroken()
        {
           return (_toDate.Year - _fromDate.Year) == 0 && (_toDate.Month - _fromDate.Month) == 0;
        }
    }
}
