﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Rule to check if service level is finalized
    /// </summary>
    public class ServiceLevelShouldNotBeFinalizedRule : IBusinessRule
    {
        private readonly IDQARuleChecker _ruleChecker;

        private readonly Guid _serviceLevelId;
        private readonly ITranslationService _translationService;

        public ServiceLevelShouldNotBeFinalizedRule(
            ITranslationService translationService,
            IDQ<PERSON>ule<PERSON>he<PERSON> ruleChecker,
            Guid serviceLevelId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _serviceLevelId = serviceLevelId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.ServiceLevelIsFinalized);

        public bool IsBroken() => _ruleChecker.IsServiceLevelFinalized(_serviceLevelId);
    }
}
