﻿using System;

using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    /// Two data source can not be the same
    /// </summary>
    public class DataSourceCannotBeSame : IBusinessRule
    {
        private readonly ITranslationService _translationService;
        private readonly Guid _dataSourceId1;
        private readonly Guid? _dataSourceId2;
        private readonly string _dataSystem1_OtherDataSource;
        private readonly string _dataSystem2_OtherDataSource;

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.DataSourceCannotBeSame);

        public DataSourceCannotBeSame(ITranslationService translationService, Guid dataSourceId1, Guid? dataSourceId2, string dataSystem1_OtherDataSource, string dataSystem2_OtherDataSource)
        {
            _translationService = translationService;
            _dataSourceId1 = dataSourceId1;
            _dataSourceId2 = dataSourceId2;
            _dataSystem1_OtherDataSource = dataSystem1_OtherDataSource;
            _dataSystem2_OtherDataSource = dataSystem2_OtherDataSource;
        }

        public bool IsBroken()
        {
            if(_dataSourceId1 == DQADataSourceSeedingMetadata.HMIS_DataSystem_1_Id && _dataSourceId2 == DQADataSourceSeedingMetadata.HMIS_DataSystem_2_Id)
            {
                return true;
            }

            if (_dataSourceId1 == DQADataSourceSeedingMetadata.DataSystem_1_OtherDataSourceId 
                && _dataSourceId2 == DQADataSourceSeedingMetadata.DataSystem_2_OtherDataSourceId
                && string.Equals(_dataSystem1_OtherDataSource?.Trim(), _dataSystem2_OtherDataSource?.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            return false;
        }
    }
}
