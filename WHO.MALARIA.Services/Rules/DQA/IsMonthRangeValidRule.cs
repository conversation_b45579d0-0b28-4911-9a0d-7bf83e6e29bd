﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.DQA
{
    /// <summary>
    ///  Rule to check requested  Month Range is valid or not
    /// </summary>
    class IsMonthRangeValidRule : IBusinessRule
    {
        private readonly int _startMonth;
        private readonly int _endMonth;
        private readonly ITranslationService _translationService;

        public IsMonthRangeValidRule(ITranslationService translationService, int startMonth, int endMonth)
        {
            _startMonth = startMonth;
            _endMonth = endMonth;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidMonthRange);

        public bool IsBroken() => _startMonth >= _endMonth;
    }
}
