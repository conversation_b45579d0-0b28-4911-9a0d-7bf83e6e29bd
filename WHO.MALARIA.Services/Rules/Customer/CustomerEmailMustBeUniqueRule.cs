﻿using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Services.Rules.Customer
{
    public class CustomerEmailMustBeUniqueRule: IBusinessRule
    {
        private readonly ICustomerRuleChecker _ruleChecker;

        private readonly string _email;
        private readonly ITranslationService _translationService;

        public CustomerEmailMustBeUniqueRule(ITranslationService translationService, ICustomerRuleChecker ruleChecker,
            string emai)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _email = emai;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.CustomerEmailAlreadyExist);

        public bool IsBroken() => !_ruleChecker.IsCustomerEmailUnique(_email);
    }
}
