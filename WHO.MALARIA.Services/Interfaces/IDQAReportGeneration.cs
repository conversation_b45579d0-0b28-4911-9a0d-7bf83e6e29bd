﻿using System;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;

namespace WHO.MALARIA.Services.Interfaces
{
    public interface IDQAReportGeneration
    {
        List<DQAReportDto> GenerateReport(int indicator, int reportType, List<TemplateDataSourceCombinedVariablesDto> mainSheetData, List<TemplateDataSourceCombinedVariablesDto> concordanceSheetData,Guid AssessmentId, SelectedVariableDto selectedVariable = null);
    }
}
