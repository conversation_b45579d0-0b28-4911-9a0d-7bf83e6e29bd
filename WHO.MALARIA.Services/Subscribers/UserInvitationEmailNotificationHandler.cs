﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Features;
using DocumentFormat.OpenXml.Spreadsheet;
using System;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the notification invitation email to be sent to the user
    /// </summary>
    public class UserInvitationEmailNotificationHandler : INotificationHandler<UserInvitationEmailNotification>
    {
        private readonly ILogger<UserInvitationEmailNotification> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGraphService _graphService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITranslationService _translationService;
        private readonly IEmailService _emailService;

        public UserInvitationEmailNotificationHandler(ILogger<UserInvitationEmailNotification> logger,
                                                      IUnitOfWork unitOfWork,
                                                      IGraphService graphService,
                                                      IHttpContextAccessor httpContextAccessor,
                                                      ITranslationService translationService,
                                                      IEmailService emailService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _graphService = graphService;
            _httpContextAccessor = httpContextAccessor;
            _translationService = translationService;
            _emailService = emailService;
        }

        /// <summary>
        /// Fetches the email template, formats the email body, and sends the email to the user.
        /// </summary>
        /// <param name="notification">Object of UserInvitationEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserInvitationEmailNotification notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation(Constants.SeriLog.InformationLogMessageTemplate, this.GetType().Name, UtilityHelper.GetCallerMethodName(), notification);


            EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                                           .Queryable(et => et.Type == (int)EmailTemplateType.UserActivation)
                                                           .SingleAsync();

            string applicationUrl = _httpContextAccessor.HttpContext.GetApplicationURL();

            string callBackUrl = $"{applicationUrl}/user/invitation/accept/{notification.UserCountryAccessId}";

            Tuple<SendInvitationStatus, string> invitationDetails = await _graphService.SendInvitation(notification.Name, string.Empty, notification.Email, emailTemplate.Body, callBackUrl, notification.CurrentUserName, CultureInfo.CurrentCulture.Name);

            switch (invitationDetails.Item1)
            {
                case SendInvitationStatus.InsufficientPrivileges:
                    throw new InSufficiantPermissionException(_translationService.GetTranslatedMessage(Constants.Exception.InSufficientPrivilegesToSendInvite));
                case SendInvitationStatus.BadRequest:
                    throw new InSufficiantPermissionException(_translationService.GetTranslatedMessage(Constants.Exception.SameDomainAsAzureADCannotBeInvited));
                case SendInvitationStatus.InvitedSuccesfully:
                    {
                        if (!string.IsNullOrEmpty(invitationDetails.Item2))
                        {
                            emailTemplate.Body = string.Format(emailTemplate.Body, notification.Name, notification.Country, invitationDetails.Item2);

                            await _emailService.SendEmail(new string[] { notification.Email }, Domain.Constants.Constants.Common.InvitationSubject, emailTemplate.Body, true);
                        }
                        else
                        {
                            throw new Exception("Graph service client is found null.");
                        }
                    }
                    break;
            }
        }
    }
}
