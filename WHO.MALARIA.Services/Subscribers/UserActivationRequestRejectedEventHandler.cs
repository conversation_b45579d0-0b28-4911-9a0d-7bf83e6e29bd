﻿using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Serilog;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Features.Models.Email;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles user activation request rejection event
    /// </summary>
    public class UserActivationRequestRejectedEventHandler : INotificationHandler<UserActivationRequestRejectedEvent>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;
        private readonly IEmailService _emailService;

        public UserActivationRequestRejectedEventHandler(IUnitOfWork unitOfWork, ILogger logger, IEmailService emailService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _emailService = emailService;
        }

        public async Task Handle(UserActivationRequestRejectedEvent eventData, CancellationToken cancellationToken)
        {
            _logger.Information($"User activation request rejected UserEmail= {eventData.Email}, UserType={EnumHelper.GetDisplayUserType(eventData.UserType)}, Country={eventData.Country}, Reason={eventData.Reason}");

            EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                             .Queryable(et => et.Type == (int)EmailTemplateType.UserActivationRequestRejection)
                                             .SingleAsync();

            string emailBody = string.Format(emailTemplate.Body, eventData.Country, EnumHelper.GetDisplayUserType(eventData.UserType), eventData.Reason);

            await _emailService.SendEmail(new string[] { eventData.Email }, emailTemplate.Subject, emailBody);
        }
    }
}
