﻿using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Serilog;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Events;

namespace WHO.MALARIA.Services.Subscribers
{
    public class IdentityCreatedHandler : INotificationHandler<IdentityCreatedEvent>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;

        public IdentityCreatedHandler(IUnitOfWork unitOfWork, ILogger logger)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        public async Task Handle(IdentityCreatedEvent notification, CancellationToken cancellationToken)
        {
            _logger.Information($"Identity created with IdentityId= {notification.IdentityId}");

            // send email 
        }
    }
}
