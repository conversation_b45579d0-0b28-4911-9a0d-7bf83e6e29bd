﻿using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Features.Models.Email;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the assessment assignment/un-assignment notification event
    /// </summary>
    public class AssessmentAssignmentEmailNotificationHandler : INotificationHandler<AssessmentAssignmentEmailNotification>
    {
        private readonly ILogger<AssessmentAssignmentEmailNotificationHandler> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmailService _emailService;

        public AssessmentAssignmentEmailNotificationHandler(ILogger<AssessmentAssignmentEmailNotificationHandler> logger,
                                                            IUnitOfWork unitOfWork,
                                                            IEmailService emailService)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _emailService = emailService;
        }

        /// <summary>
        /// Fetch the country name, format the email body and send the notification email to the users
        /// </summary>
        /// <param name="notification">Object of AssessmentAssignmentEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(AssessmentAssignmentEmailNotification notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation(Constants.SeriLog.InformationLogMessageTemplate, this.GetType().Name, UtilityHelper.GetCallerMethodName(), notification);

            Country country = await _unitOfWork.CountryRepository.GetAsync(x => x.Id == notification.CountryId);

            EmailTemplateType templateType = notification.AreUsersRemoved
                ? EmailTemplateType.UserRemovedFromAssessment
                : EmailTemplateType.UserAssignedToAssessment;

            EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                            .Queryable(et => et.Type == (int)templateType)
                                            .SingleAsync();

            string role = notification.UserRole.ToString();
            string body = string.Format(emailTemplate.Body, country.Name, role, role.ToLower());

            await _emailService.SendEmail(notification.EmailAddresses, emailTemplate.Subject, body);
        }
    }
}
