﻿using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Features.Models.Email;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the email notification request whenever access request for country from who user
    /// </summary>
   public class UserCountryAccessRequestEmailNotificationHandler : INotificationHandler<UserCountryAccessRequestEmailNotification>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserCountryAccessRequestEmailNotificationHandler> _logger;
        private readonly IEmailService _emailService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserCountryAccessRequestEmailNotificationHandler(IUnitOfWork unitOfWork, ILogger<UserCountryAccessRequestEmailNotificationHandler> logger, IEmailService emailService, IHttpContextAccessor httpContextAccessor)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _emailService = emailService;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Fetches the email template, formats the body and sends the email to the viewer user
        /// </summary>
        /// <param name="notification">Object of UserCountryAccessRequestEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserCountryAccessRequestEmailNotification notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"User country access requested. UserEmail= {notification.Email}, Country={notification.Country}");

            EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                             .Queryable(et => et.Type == (int)EmailTemplateType.AccessRequestForCountryFromWHOUser)
                                             .SingleAsync();

            string emailBody = string.Format(emailTemplate.Body, notification.Country, _httpContextAccessor.HttpContext.GetApplicationURL());

            await _emailService.SendEmail(new string[] { notification.Email }, emailTemplate.Subject, emailBody);
        }
    }
}
