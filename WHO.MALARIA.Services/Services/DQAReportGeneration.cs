﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Features.Extensions;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.Interfaces;

namespace WHO.MALARIA.Services.Services
{
    /// <summary>
    /// This class contains all methods regarding calculations and generating DQA Desk analysis report
    /// </summary>
    public class DQAReportGeneration : IDQAReportGeneration
    {
        private readonly ICacheDataService _cacheService;
        private readonly IDQARepository _dQARepository;
        private readonly string PriorityVariable = "Priority Variable";
        private readonly string OptionalVariable = "Optional Variable";
        private readonly Dictionary<string, int> PriorityVariables = UtilityHelper.GetEnumerableData<DQAPriorityVariables>();
        private readonly Dictionary<string, int> DQAVariableList = UtilityHelper.GetEnumerableData<DQAVariables>();
        private readonly Dictionary<string, int> ConsistencyOverTimeIndicators = UtilityHelper.GetEnumerableData<ConsistencyOverTimeKeyIndicator>();
        private readonly Dictionary<string, int> ConsistencyBetweenVariableDictionary = UtilityHelper.GetEnumerableData<ConsistencyBetweenVariables>();

        public DQAReportGeneration(ICacheDataService cacheService, IDQARepository dQARepository)
        {
            _cacheService = cacheService;
            _dQARepository = dQARepository;
        }

        /// <summary>
        /// Method to generate report
        /// </summary>
        /// <param name="dqaIndicator">DQA Indicator value from enum</param>
        /// <param name="reportType">Type of report</param>
        /// <param name="mainSheetData">Data from main sheet, an instance of list of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="concordanceSheetData">Data from concordance sheet</param>
        /// <param name="assessmentId">Guid</param>
        /// <param name="selectedVariables">selectedVariable DTO</param>
        /// <returns>Object as response</returns>
        public List<DQAReportDto> GenerateReport(int dqaIndicator, int reportType, List<TemplateDataSourceCombinedVariablesDto> mainSheetData, List<TemplateDataSourceCombinedVariablesDto> concordanceSheetData, Guid assessmentId, SelectedVariableDto selectedVariables = null)
        {
            var reports = new List<DQAReportDto>();

            List<int> years = mainSheetData.Select(x => x.Year).Distinct().OrderBy(x => x).ToList();

            List<string> provinces = mainSheetData.Select(x => x.Province).Distinct().ToList();

            List<string> districts = mainSheetData.Select(x => x.District).Distinct().ToList();

            List<string> healthFacilities = mainSheetData.Select(x => x.HealthFacilityType).Distinct().OrderByDescending(p => p).ToList();

            healthFacilities.Add(Constants.Common.TotalColumnName);

            List<string> healthFacilityLevels = mainSheetData.Select(x => x.HealthFacilityName).Distinct().ToList();

            var reportRequest = new DQAReportRequestDto()
            {
                AssessmentId = assessmentId,
                Indicator = dqaIndicator,
                ReportType = reportType,
                Years = years,
                Provinces = provinces,
                Districts = districts,
                HealthFacilities = healthFacilities,
                HealthFacilityLevels = healthFacilityLevels,
                MainSheetData = mainSheetData,
                ConcordanceSheetData = concordanceSheetData,
                SelectedVariable = selectedVariables
            };

            switch (reportType)
            {
                case (int)DQAReportType.NationalReport:

                case (int)DQAReportType.NationalReport1:

                case (int)DQAReportType.NationalReport2:

                    reports = GetNationalLevelReport(reportRequest);
                    break;

                case (int)DQAReportType.ProvinceReport:

                case (int)DQAReportType.ProvinceReport1:

                case (int)DQAReportType.ProvinceReport2:

                    reports = GetProvinceLevelReport(reportRequest);
                    break;

                case (int)DQAReportType.DistrictReport1:

                case (int)DQAReportType.DistrictReport2:
                    reports = GetDistrictLevelReport(reportRequest);
                    break;

                case (int)DQAReportType.HealthFacilityReport1:

                case (int)DQAReportType.HealthFacilityReport2:
                    reports = GetHealthFacilityLevelReport(reportRequest);
                    break;

                case (int)DQAReportType.NationalReport3:

                case (int)DQAReportType.ProvinceReport3:

                case (int)DQAReportType.DistrictReport3:

                case (int)DQAReportType.HealthFacilityReport3:

                    reports = GetSummaryReport(reportRequest);
                    break;
            }

            return reports;
        }

        /// <summary>
        /// Method to generate national level report
        /// </summary>
        /// <param name="reportRequest">Accpets the instance of DQAReportRequestDto</param>
        /// <returns>Object of List of DQAReportDto model as a report response </returns>
        private List<DQAReportDto> GetNationalLevelReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            switch (reportRequest.Indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                case (int)DQAIndicatorReport.ReportingTimeliness:

                    reports = GetNationalReportForCompletenessTimeliness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                    reports = GetNationalReportVariableCompleteness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;

                    reports = GetNationalReportConsistencyBtwVariables(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                    reports = GetNationalReportConsistencyOverTime(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:

                    List<TemplateDataSourceCombinedVariablesDto> discrepancyData = GetDiscrepancyForSameVariablesWithConcordanceData(reportRequest.AssessmentId);
                    reportRequest.DiscrepancyData = discrepancyData;

                    reports = GetNationalReportConcordance(reportRequest);
                    break;
            }

            return reports;
        }

        /// <summary>
        /// Generates province level report
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceLevelReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            switch (reportRequest.Indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                case (int)DQAIndicatorReport.ReportingTimeliness:
                    reports = GetProvinceReportForCompletenessTimeliness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:
                    reports = GetProvinceReportVariableCompleteness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:
                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;
                    reports = GetProvinceReportConsistencyBtwVariables(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:
                    reports = GetProvinceReportConsistencyOverTime(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:
                    List<TemplateDataSourceCombinedVariablesDto> discrepancyData = GetDiscrepancyForSameVariablesWithConcordanceData(reportRequest.AssessmentId);
                    reportRequest.DiscrepancyData = discrepancyData;
                    reports = GetProvinceReportConcordance(reportRequest);
                    break;
            }

            return reports;
        }

        /// <summary>
        /// Generates district level report
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictLevelReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            switch (reportRequest.Indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                case (int)DQAIndicatorReport.ReportingTimeliness:
                    reports = GetDistrictReportForCompletenessTimeliness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:
                    reports = GetDistrictReportVariableCompleteness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:
                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;
                    reports = GetDistrictReportConsistencyBtwVariables(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:
                    reports = GetDistrictReportConsistencyOverTime(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:
                    List<TemplateDataSourceCombinedVariablesDto> discrepancyData = GetDiscrepancyForSameVariablesWithConcordanceData(reportRequest.AssessmentId);
                    reportRequest.DiscrepancyData = discrepancyData;
                    reports = GetDistrictReportConcordance(reportRequest);
                    break;
            }

            return reports;
        }

        /// <summary>
        /// Generates health facility level report
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityLevelReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            switch (reportRequest.Indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                case (int)DQAIndicatorReport.ReportingTimeliness:
                    reports = GetHealthFacilityReportForCompletenessTimeliness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:
                    reports = GetHealthFacilityReportVariableCompleteness(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:
                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;
                    reports = GetHealthFacilityReportConsistencyBtwVeriables(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:
                    reports = GetHealthFacilityReportConsistencyOverTime(reportRequest);
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:
                    List<TemplateDataSourceCombinedVariablesDto> discrepancyData = GetDiscrepancyForSameVariablesWithConcordanceData(reportRequest.AssessmentId);
                    reportRequest.DiscrepancyData = discrepancyData;
                    reports = GetHealthFacilityReportConcordance(reportRequest);
                    break;
            }

            return reports;
        }

        /// <summary>
        /// Generates summary report
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetSummaryReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            switch (reportRequest.Indicator)
            {
                case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;

                    switch (reportRequest.ReportType)
                    {
                        case (int)DQAReportType.NationalReport3:

                            reports = GetNationalLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reports = GetProvinceLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reports = GetDistrictLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reports = GetHealthFacilityLevelSummaryReport(reportRequest);
                            break;
                    }
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                    reportRequest.DiscrepancyData = reportRequest.MainSheetData;
                    switch (reportRequest.ReportType)
                    {
                        case (int)DQAReportType.NationalReport3:

                            reports = GetNationalLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reports = GetProvinceLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reports = GetDistrictLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reports = GetHealthFacilityLevelSummaryReport(reportRequest);
                            break;
                    }
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:

                    List<TemplateDataSourceCombinedVariablesDto> discrepancyData = GetDiscrepancyForSameVariablesWithConcordanceData(reportRequest.AssessmentId);
                    reportRequest.DiscrepancyData = discrepancyData;

                    switch (reportRequest.ReportType)
                    {
                        case (int)DQAReportType.NationalReport3:

                            reports = GetNationalLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reports = GetProvinceLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reports = GetDistrictLevelSummaryReport(reportRequest);
                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reports = GetHealthFacilityLevelSummaryReport(reportRequest);
                            break;
                    }
                    break;
            }

            return reports;
        }

        #region NationalLevelReports

        /// <summary>
        /// Generates national report regarding completeness and timeliness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetNationalReportForCompletenessTimeliness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.HealthFacilities.ForEach((string facilityType) =>
                {
                    DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                    {
                        DQAIndicator = reportRequest.Indicator,
                        ReportType = reportRequest.ReportType,
                        DataFromSheet = reportRequest.MainSheetData,
                        Year = year,
                        HealthFacilityType = facilityType,
                        SelectedVariable=reportRequest.SelectedVariable
                    };

                    numerator = GetReportNumerator(calculationRequest);
                    denominator = GetReportDenominator(calculationRequest);

                    if (numerator >= 0 && denominator > 0 && numerator != -1)
                    {
                        percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                        DQAReportDto report = new DQAReportDto
                        {
                            Year = year,
                            ReportType = "National Report 2",
                            HealthFacilityType = facilityType,
                            PercentageValue = percentageOfReport
                        };

                        reports.Add(report);
                    }
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates national report regarding variable completeness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetNationalReportVariableCompleteness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                foreach (KeyValuePair<string, int> variable in DQAVariableList)
                {
                    DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                    {
                        DQAIndicator = reportRequest.Indicator,
                        ReportType = reportRequest.ReportType,
                        DataFromSheet = reportRequest.MainSheetData,
                        Year = year,
                        DQAVariable = variable.Value,
                        SelectedVariable = reportRequest.SelectedVariable
                    };

                    numerator = GetReportNumerator(calculationRequest);

                    denominator = GetReportDenominator(calculationRequest);

                    if (numerator >= 0 && denominator > 0 && numerator != -1)
                    {
                        percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                        DQAReportDto report = new DQAReportDto
                        {
                            Year = year,
                            ReportParameterType = variable.Key,
                            ReportType = "National Report 2",
                            PercentageValue = percentageOfReport,
                            VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                        };

                        reports.Add(report);
                    }
                }
            });

            return reports;
        }

        /// <summary>
        /// Generates national report regarding consistency between variables
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetNationalReportConsistencyBtwVariables(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                foreach (KeyValuePair<string, int> discrepancyVariable in ConsistencyBetweenVariableDictionary)
                {
                    DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                    {
                        DQAIndicator = reportRequest.Indicator,
                        ReportType = reportRequest.ReportType,
                        DataFromSheet = reportRequest.DiscrepancyData,
                        Year = year,
                        ConsistencyBetweenVariable = discrepancyVariable.Value,
                        SelectedVariable = reportRequest.SelectedVariable
                    };

                    numerator = GetReportNumerator(calculationRequest);

                    denominator = GetReportDenominator(calculationRequest);

                    if (numerator >= 0 && denominator > 0 && numerator != -1)
                    {
                        percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                        DQAReportDto report = new DQAReportDto
                        {
                            Year = year,
                            ReportParameterType = discrepancyVariable.Key,
                            ReportType = "National Report 2",
                            PercentageValue = percentageOfReport
                        };

                        reports.Add(report);
                    }
                }
            });

            return reports;
        }

        /// <summary>
        /// Generates national report regarding consistency over time
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetNationalReportConsistencyOverTime(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.HealthFacilities.ForEach((string facilityType) =>
                {
                    foreach (KeyValuePair<string, int> consistancyIndicator in ConsistencyOverTimeIndicators)
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {
                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.MainSheetData,
                            Year = year,
                            HealthFacilityType = facilityType,
                            ConsistancyIndicator = consistancyIndicator.Value,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator >= 0 && denominator > 0 && numerator != -1)
                        {
                            percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                            DQAReportDto report = new DQAReportDto
                            {
                                Year = year,
                                ReportParameterType = consistancyIndicator.Key,
                                HealthFacilityType = facilityType,
                                ReportType = "National Report 2",
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }
                    }
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates national report regarding actual data and concordance data
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetNationalReportConcordance(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                foreach (KeyValuePair<string, int> variable in DQAVariableList)
                {
                    DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                    {
                        DQAIndicator = reportRequest.Indicator,
                        ReportType = reportRequest.ReportType,
                        DataFromSheet = reportRequest.DiscrepancyData,
                        Year = year,
                        DQAVariable = variable.Value,
                        SelectedVariable = reportRequest.SelectedVariable
                    };

                    numerator = GetReportNumerator(calculationRequest);

                    denominator = GetReportDenominator(calculationRequest);

                    if (numerator >= 0 && denominator > 0 && numerator != -1)
                    {
                        percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                        DQAReportDto report = new DQAReportDto
                        {
                            Year = year,
                            ReportParameterType = variable.Key,
                            ReportType = "National Report 2",
                            VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable,
                            PercentageValue = percentageOfReport
                        };

                        reports.Add(report);
                    }
                }
            });

            return reports;
        }

        #endregion

        #region ProvinceLevelReports

        /// <summary>
        /// Generates Province report regarding completeness and timeliness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceReportForCompletenessTimeliness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.HealthFacilities.ForEach((string facilityType) =>
                {
                    reportRequest.Provinces.ForEach((string province) =>
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {
                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.MainSheetData,
                            Year = year,
                            ProvinceType = province,
                            HealthFacilityType = facilityType,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator >= 0 && denominator > 0 && numerator != -1)
                        {
                            percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                            DQAReportDto report = new DQAReportDto
                            {
                                Year = year,
                                ReportType = "Province Report 2",
                                Province = province,
                                HealthFacilityType = facilityType,
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates province report regarding variable completeness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceReportVariableCompleteness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.HealthFacilities.ForEach((string facilityType) =>
                {
                    reportRequest.Provinces.ForEach((string province) =>
                    {
                        foreach (KeyValuePair<string, int> variable in DQAVariableList)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.MainSheetData,
                                Year = year,
                                ProvinceType = province,
                                HealthFacilityType = facilityType,
                                DQAVariable = variable.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "Province Report 2",
                                    ReportParameterType = variable.Key,
                                    Province = province,
                                    HealthFacilityType = facilityType,
                                    PercentageValue = percentageOfReport,
                                    VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates province report regarding consistency between variables
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceReportConsistencyBtwVariables(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    foreach (KeyValuePair<string, int> discrepancyVariable in ConsistencyBetweenVariableDictionary)
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {
                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.DiscrepancyData,
                            Year = year,
                            ProvinceType = province,
                            ConsistencyBetweenVariable = discrepancyVariable.Value,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator >= 0 && denominator > 0 && numerator != -1)
                        {
                            percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                            DQAReportDto report = new DQAReportDto
                            {
                                Year = year,
                                ReportParameterType = discrepancyVariable.Key,
                                Province = province,
                                ReportType = "Province Report 2",
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }
                    }
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates province report regarding consistency over time
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceReportConsistencyOverTime(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.HealthFacilities.ForEach((string facilityType) =>
                {
                    reportRequest.Provinces.ForEach((string province) =>
                    {
                        foreach (KeyValuePair<string, int> consistancyIndicator in ConsistencyOverTimeIndicators)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.MainSheetData,
                                Year = year,
                                ProvinceType = province,
                                HealthFacilityType = facilityType,
                                ConsistancyIndicator = consistancyIndicator.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);
                                
                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportParameterType = consistancyIndicator.Key,
                                    Province = province,
                                    HealthFacilityType = facilityType,
                                    ReportType = "National Report 2",
                                    PercentageValue = percentageOfReport
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates province report regarding actual data and concordance data
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetProvinceReportConcordance(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    foreach (KeyValuePair<string, int> variable in DQAVariableList)
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {
                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.DiscrepancyData,
                            Year = year,
                            ProvinceType = province,
                            DQAVariable = variable.Value,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator >= 0 && denominator > 0 && numerator != -1)
                        {
                            percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                            DQAReportDto report = new DQAReportDto
                            {
                                Year = year,
                                ReportParameterType = variable.Key,
                                Province = province,
                                ReportType = "Province Report 2",
                                VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable,
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }
                    }
                });
            });

            return reports;
        }

        #endregion

        #region DistrictLevelReports

        /// <summary>
        /// Generates District report regarding completeness and timeliness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictReportForCompletenessTimeliness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {

                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.MainSheetData,
                            Year = year,
                            ProvinceType = province,
                            DistrictType = district,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator >= 0 && denominator > 0 && numerator != -1)
                        {
                            percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                            DQAReportDto report = new DQAReportDto()
                            {
                                Year = year,
                                ReportType = "District Level",
                                Province = province,
                                District = district,
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }

                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates district report regarding variable completeness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictReportVariableCompleteness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        foreach (KeyValuePair<string, int> variable in DQAVariableList)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.MainSheetData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                DQAVariable = variable.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "District Level",
                                    ReportParameterType = variable.Key,
                                    Province = province,
                                    District = district,
                                    PercentageValue = percentageOfReport,
                                    VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates district report regarding consistency between variables
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictReportConsistencyBtwVariables(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        foreach (KeyValuePair<string, int> discrepancyVariable in ConsistencyBetweenVariableDictionary)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.DiscrepancyData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                ConsistencyBetweenVariable = discrepancyVariable.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "District Level",
                                    ReportParameterType = discrepancyVariable.Key,
                                    Province = province,
                                    District = district,
                                    PercentageValue = percentageOfReport
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates district report regarding consistency over time
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictReportConsistencyOverTime(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        foreach (KeyValuePair<string, int> consistancyIndicator in ConsistencyOverTimeIndicators)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.MainSheetData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                ConsistancyIndicator = consistancyIndicator.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "District Level",
                                    ReportParameterType = consistancyIndicator.Key,
                                    Province = province,
                                    District = district,
                                    PercentageValue = percentageOfReport
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates district report regarding actual data and concordance data
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetDistrictReportConcordance(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        foreach (KeyValuePair<string, int> variable in DQAVariableList)
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.DiscrepancyData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                DQAVariable = variable.Value,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "District Level",
                                    ReportParameterType = variable.Key,
                                    Province = province,
                                    District = district,
                                    PercentageValue = percentageOfReport,
                                    VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                                };

                                reports.Add(report);
                            }
                        }
                    });
                });
            });

            return reports;
        }

        #endregion

        #region HealthFacilityLevelReports

        /// <summary>
        /// Generates Helath facility level report regarding completeness and timeliness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityReportForCompletenessTimeliness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.MainSheetData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                HealthFacilityName = facilitylevel,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator >= 0 && denominator > 0 && numerator != -1)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "Health Facility Level",
                                    Province = province,
                                    District = district,
                                    HealthFacilityName = facilitylevel,
                                    PercentageValue = percentageOfReport
                                };

                                reports.Add(report);
                            }
                        });
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates Helath facility level report regarding variable completeness
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityReportVariableCompleteness(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                        {
                            foreach (KeyValuePair<string, int> variable in DQAVariableList)
                            {
                                DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                                {
                                    DQAIndicator = reportRequest.Indicator,
                                    ReportType = reportRequest.ReportType,
                                    DataFromSheet = reportRequest.MainSheetData,
                                    Year = year,
                                    ProvinceType = province,
                                    DistrictType = district,
                                    HealthFacilityName = facilitylevel,
                                    DQAVariable = variable.Value,
                                    SelectedVariable = reportRequest.SelectedVariable
                                };

                                numerator = GetReportNumerator(calculationRequest);

                                denominator = GetReportDenominator(calculationRequest);

                                if (numerator >= 0 && denominator > 0 && numerator != -1)
                                {
                                    percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                    DQAReportDto report = new DQAReportDto
                                    {
                                        Year = year,
                                        ReportType = "Health Facility Level",
                                        ReportParameterType = variable.Key,
                                        Province = province,
                                        District = district,
                                        HealthFacilityName = facilitylevel,
                                        PercentageValue = percentageOfReport,
                                        VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                                    };

                                    reports.Add(report);
                                }
                            }
                        });
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates Helath facility level report regarding consistency between variables
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityReportConsistencyBtwVeriables(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                        {
                            foreach (KeyValuePair<string, int> discrepancyVariable in ConsistencyBetweenVariableDictionary)
                            {
                                DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                                {
                                    DQAIndicator = reportRequest.Indicator,
                                    ReportType = reportRequest.ReportType,
                                    DataFromSheet = reportRequest.DiscrepancyData,
                                    Year = year,
                                    ProvinceType = province,
                                    DistrictType = district,
                                    HealthFacilityName = facilitylevel,
                                    ConsistencyBetweenVariable = discrepancyVariable.Value,
                                    SelectedVariable = reportRequest.SelectedVariable
                                };

                                numerator = GetReportNumerator(calculationRequest);

                                denominator = GetReportDenominator(calculationRequest);

                                if (numerator >= 0 && denominator > 0 && numerator != -1)
                                {
                                    percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                    DQAReportDto report = new DQAReportDto
                                    {
                                        Year = year,
                                        ReportType = "Health Facility Level",
                                        ReportParameterType = discrepancyVariable.Key,
                                        Province = province,
                                        District = district,
                                        HealthFacilityName = facilitylevel,
                                        PercentageValue = percentageOfReport
                                    };

                                    reports.Add(report);
                                }
                            }
                        });
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates Helath facility level report regarding consistency over time
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityReportConsistencyOverTime(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                        {
                            foreach (KeyValuePair<string, int> consistancyIndicator in ConsistencyOverTimeIndicators)
                            {
                                DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                                {
                                    DQAIndicator = reportRequest.Indicator,
                                    ReportType = reportRequest.ReportType,
                                    DataFromSheet = reportRequest.MainSheetData,
                                    Year = year,
                                    ProvinceType = province,
                                    DistrictType = district,
                                    HealthFacilityName = facilitylevel,
                                    ConsistancyIndicator = consistancyIndicator.Value,
                                    SelectedVariable = reportRequest.SelectedVariable
                                };

                                numerator = GetReportNumerator(calculationRequest);

                                denominator = GetReportDenominator(calculationRequest);

                                if (numerator >= 0 && denominator > 0 && numerator != -1)
                                {
                                    percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                    DQAReportDto report = new DQAReportDto
                                    {
                                        Year = year,
                                        ReportType = "Health Facility Level",
                                        ReportParameterType = consistancyIndicator.Key,
                                        Province = province,
                                        District = district,
                                        HealthFacilityName = facilitylevel,
                                        PercentageValue = percentageOfReport
                                    };

                                    reports.Add(report);
                                }
                            }
                        });
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// Generates Helath facility level report regarding actual data and concordance data
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportRequest model as parameter</param>
        /// <returns>List of DQAReportDto model as response</returns>
        private List<DQAReportDto> GetHealthFacilityReportConcordance(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                reportRequest.Provinces.ForEach((string province) =>
                {
                    reportRequest.Districts.ForEach((string district) =>
                    {
                        reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                        {
                            foreach (KeyValuePair<string, int> variable in DQAVariableList)
                            {
                                DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                                {
                                    DQAIndicator = reportRequest.Indicator,
                                    ReportType = reportRequest.ReportType,
                                    DataFromSheet = reportRequest.DiscrepancyData,
                                    Year = year,
                                    ProvinceType = province,
                                    DistrictType = district,
                                    HealthFacilityName = facilitylevel,
                                    DQAVariable = variable.Value,
                                    SelectedVariable = reportRequest.SelectedVariable
                                };

                                numerator = GetReportNumerator(calculationRequest);

                                denominator = GetReportDenominator(calculationRequest);

                                if (numerator >= 0 && denominator > 0 && numerator != -1)
                                {
                                    percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                    DQAReportDto report = new DQAReportDto
                                    {
                                        Year = year,
                                        ReportType = "Health Facility Level",
                                        ReportParameterType = variable.Key,
                                        Province = province,
                                        District = district,
                                        HealthFacilityName = facilitylevel,
                                        PercentageValue = percentageOfReport,
                                        VariableType = PriorityVariables.Any(x => x.Key == variable.Key) ? PriorityVariable : OptionalVariable
                                    };

                                    reports.Add(report);
                                }
                            }
                        });
                    });
                });
            });

            return reports;
        }

        #endregion

        #region SummaryReports

        /// <summary>
        /// To get national level summary report
        /// </summary>
        /// <param name="reportRequest">Instance of DQAReportRequestDto</param>
        /// <returns>List of instance of DQAReportDto</returns>
        private List<DQAReportDto> GetNationalLevelSummaryReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Years.ForEach((int year) =>
            {
                DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                {
                    DQAIndicator = reportRequest.Indicator,
                    ReportType = reportRequest.ReportType,
                    DataFromSheet = reportRequest.DiscrepancyData,
                    Year = year,
                    SelectedVariable = reportRequest.SelectedVariable
                };

                numerator = GetReportNumerator(calculationRequest);

                denominator = GetReportDenominator(calculationRequest);

                if(numerator > 0 || denominator > 0)
                {
                    percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                    DQAReportDto report = new DQAReportDto
                    {
                        Year = year,
                        ReportType = "Summary Report - National",
                        NumberOfReport = numerator,
                        NumberOfReportReceived = denominator,
                        PercentageValue = percentageOfReport,
                        VariableType = ""
                    };

                    reports.Add(report);
                }
            });

            return reports;
        }

        /// <summary>
        /// To get province level summary report
        /// </summary>
        /// <param name="reportRequest">Instance of DQAReportRequestDto</param>
        /// <returns>List of instance of DQAReportDto</returns>
        private List<DQAReportDto> GetProvinceLevelSummaryReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Provinces.ForEach((string province) =>
            {
                reportRequest.Years.ForEach((int year) =>
                {
                    DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                    {
                        DQAIndicator = reportRequest.Indicator,
                        ReportType = reportRequest.ReportType,
                        DataFromSheet = reportRequest.DiscrepancyData,
                        Year = year,
                        ProvinceType = province,
                        SelectedVariable = reportRequest.SelectedVariable
                    };

                    numerator = GetReportNumerator(calculationRequest);

                    denominator = GetReportDenominator(calculationRequest);

                    if (numerator > 0 || denominator > 0)
                    {
                        percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                        DQAReportDto report = new DQAReportDto
                        {
                            Year = year,
                            ReportType = "Summary Report - Province",
                            Province = province,
                            NumberOfReport = numerator,
                            NumberOfReportReceived = denominator,
                            PercentageValue = percentageOfReport
                        };

                        reports.Add(report);
                    }
                });
            });

            return reports;
        }

        /// <summary>
        /// To get district level summary report
        /// </summary>
        /// <param name="reportRequest">Instance of DQAReportRequestDto</param>
        /// <returns>List of instance of DQAReportDto</returns>
        private List<DQAReportDto> GetDistrictLevelSummaryReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Provinces.ForEach((string province) =>
            {
                reportRequest.Districts.ForEach((string district) =>
                {
                    reportRequest.Years.ForEach((int year) =>
                    {
                        DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                        {
                            DQAIndicator = reportRequest.Indicator,
                            ReportType = reportRequest.ReportType,
                            DataFromSheet = reportRequest.DiscrepancyData,
                            Year = year,
                            ProvinceType = province,
                            DistrictType = district,
                            SelectedVariable = reportRequest.SelectedVariable
                        };

                        numerator = GetReportNumerator(calculationRequest);

                        denominator = GetReportDenominator(calculationRequest);

                        if (numerator > 0 || denominator > 0)
                        {
                            percentageOfReport = 0;
                            if (denominator > 0) {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);
                            }


                            DQAReportDto report = new DQAReportDto
                            {
                                Year = year,
                                ReportType = "Summary Report - District",
                                Province = province,
                                District = district,
                                NumberOfReport = numerator,
                                NumberOfReportReceived = denominator,
                                PercentageValue = percentageOfReport
                            };

                            reports.Add(report);
                        }
                    });
                });
            });

            return reports;
        }

        /// <summary>
        /// To get health facility level summary report
        /// </summary>
        /// <param name="reportRequest">Instance of DQAReportRequestDto</param>
        /// <returns>List of instance of DQAReportDto</returns>
        private List<DQAReportDto> GetHealthFacilityLevelSummaryReport(DQAReportRequestDto reportRequest)
        {
            var reports = new List<DQAReportDto>();

            int numerator = 0;
            int denominator = 0;
            double percentageOfReport = 0;

            reportRequest.Provinces.ForEach((string province) =>
            {
                reportRequest.Districts.ForEach((string district) =>
                {
                    reportRequest.HealthFacilityLevels.ForEach((string facilitylevel) =>
                    {
                        reportRequest.Years.ForEach((int year) =>
                        {
                            DQAReportCalculationRequestDto calculationRequest = new DQAReportCalculationRequestDto
                            {
                                DQAIndicator = reportRequest.Indicator,
                                ReportType = reportRequest.ReportType,
                                DataFromSheet = reportRequest.DiscrepancyData,
                                Year = year,
                                ProvinceType = province,
                                DistrictType = district,
                                HealthFacilityName = facilitylevel,
                                SelectedVariable = reportRequest.SelectedVariable
                            };

                            numerator = GetReportNumerator(calculationRequest);

                            denominator = GetReportDenominator(calculationRequest);

                            if (numerator > 0 || denominator > 0)
                            {
                                percentageOfReport = Math.Round((100.0 * numerator) / denominator, 2);

                                DQAReportDto report = new DQAReportDto
                                {
                                    Year = year,
                                    ReportType = "Summary Report - Health Facility Level",
                                    Province = province,
                                    District = district,
                                    HealthFacilityName = facilitylevel,
                                    NumberOfReport = numerator,
                                    NumberOfReportReceived = denominator,
                                    PercentageValue = percentageOfReport
                                };

                                reports.Add(report);
                            }
                        });
                    });
                });
            });

            return reports;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Generates numerator value for report calculation
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportCalculationRequest model as parameter</param>
        /// <returns>Numerator for calculation as integer</returns>
        private int GetReportNumerator(DQAReportCalculationRequestDto numeratorRequest)
        {
            int numerator = 0;
            switch (numeratorRequest.ReportType)
            {
                case (int)DQAReportType.NationalReport:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsOnTime);
                            break;

                    }
                    break;

                case (int)DQAReportType.NationalReport1:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.HealthFacilityType == numeratorRequest.HealthFacilityType && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.HealthFacilityType == numeratorRequest.HealthFacilityType && x.IsReportsOnTime);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (numeratorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.TotalMalariaCases));

                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatientDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive ?? 0) + Convert.ToInt32(p.RDTPositive ?? 0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.RDTPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year &&
                                        (numeratorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == numeratorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.NationalReport2:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.TotalMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.ConfirmMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyTested:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.MicroscopyTested >= 0);
                                    break;

                                case (int)DQAVariables.RDTTested:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.RDTTested >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.MicroscopyPositive >= 0);
                                    break;

                                case (int)DQAVariables.RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.RDTPositive >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.AllCauseOutpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.AllCauseInpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.AllCauseDeaths >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.MalariaInpatients >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.MalariaInpatientDeaths >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.ConfirmedMalariaCasesTreated >= 0);
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.SuspectedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.PresumedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.IPTp:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IPTp >= 0);
                                    break;

                                case (int)DQAVariables.ANC:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.ANC >= 0);
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (numeratorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive);

                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && (x.MicroscopyTested >= x.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year 
                                                && x.AllCauseOutpatients > x.TotalMalariaCases);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                 && x.AllCauseInpatients > x.MalariaInpatients);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                && x.AllCauseDeaths > x.MalariaInpatientDeaths);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                 && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0 && x.ConfirmMalariaCases >= x.ConfirmedMalariaCasesTreated);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                 && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested >=0 || x.RDTTested >=0) && x.SuspectedMalariaCases >= (x.MicroscopyTested ?? 0 + x.RDTTested ?? 0));
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year  && x.IsReportsReceived && x.IsExcludeTotalMalariaCases != 1 
                                                    && x.TotalMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeConfirmMalariaCases != 1 && x.ConfirmMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeMicroscopyTested != 1 && x.MicroscopyTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeRDTTested != 1 && x.RDTTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenClinic)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeMicroscopyPositive != 1 && x.MicroscopyPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeRDTPositive != 1 && x.RDTPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeAllCauseOutpatients != 1 && x.AllCauseOutpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeAllCauseInpatients != 1 && x.AllCauseInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeAllCauseDeaths != 1 && x.AllCauseDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeMalariaInpatients != 1 && x.MalariaInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeMalariaInpatientDeaths != 1  && x.MalariaInpatientDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmedMalariaCasesAct)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeConfirmedMalariaCasesTreated != 1 && x.ConfirmedMalariaCasesTreated == 0);
                                    }
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.SuspectedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeSuspectedMalariaCases != 1 && x.SuspectedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.PresumedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludePresumedMalariaCases != 1 && x.PresumedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.IPTp:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenIPTP)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeIPTp != 1 && x.IPTp == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ANC:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenClinic)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived && x.IsExcludeANC != 1 && x.ANC == 0);
                                    }
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.NationalReport3:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                  .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived &&
                                  (numeratorRequest.SelectedVariable.TotalMalariaCases ? x.TotalMalariaCases >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.ConfirmMalariaCases ? x.ConfirmMalariaCases >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MicroscopyTested ? x.MicroscopyTested >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.RDTTested ? x.RDTTested >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MicroscopyPositive ? x.MicroscopyPositive >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.AllCauseOutpatients ? x.AllCauseOutpatients >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.AllCauseInpatients ? x.AllCauseInpatients >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.AllCauseDeaths ? x.AllCauseDeaths >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MalariaInpatients ? x.MalariaInpatients >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.MalariaInpatientDeaths ? x.MalariaInpatientDeaths >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.RDTPositive ? x.RDTPositive >= 0 : true));

                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            numerator = numeratorRequest.DataFromSheet
                                    .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived &&

                                        ((numeratorRequest.SelectedVariable.RDTTested && numeratorRequest.SelectedVariable.RDTPositive) ? (x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive) : true) &&
                                        ((numeratorRequest.SelectedVariable.MicroscopyTested && numeratorRequest.SelectedVariable.MicroscopyPositive) ? (x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && x.MicroscopyTested >= x.MicroscopyPositive) : true) &&
                                        ((numeratorRequest.SelectedVariable.AllCauseOutpatients && numeratorRequest.SelectedVariable.TotalMalariaCases) ? (x.AllCauseOutpatients > x.TotalMalariaCases) : true) &&
                                        ((numeratorRequest.SelectedVariable.AllCauseInpatients && numeratorRequest.SelectedVariable.MalariaInpatients) ? (x.AllCauseInpatients > x.MalariaInpatients) : true) &&
                                        ((numeratorRequest.SelectedVariable.AllCauseDeaths && numeratorRequest.SelectedVariable.MalariaInpatientDeaths) ? (x.AllCauseDeaths > x.MalariaInpatientDeaths) : true)
                                       );


                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            numerator = numeratorRequest.DataFromSheet
                                       .Count(x => x.Year == numeratorRequest.Year   && x.IsReportsReceived
                                                    // priority variables checks
                                                    && (numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases ? x.TotalMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases ? x.ConfirmMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyTested ? x.MicroscopyTested == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTTested ? x.RDTTested == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive ? x.MicroscopyPositive == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTPositive ? x.RDTPositive == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients ? x.AllCauseOutpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients ? x.AllCauseInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths ? x.AllCauseDeaths == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatients ? x.MalariaInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths ? x.MalariaInpatientDeaths == 0 : true)
                                                    );
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.IsReportsOnTime);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (numeratorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.TotalMalariaCases));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatientDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive ?? 0) + Convert.ToInt32(p.RDTPositive ?? 0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.RDTPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport1:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                            && x.HealthFacilityType == numeratorRequest.HealthFacilityType && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                            && x.HealthFacilityType == numeratorRequest.HealthFacilityType && x.IsReportsOnTime);
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport2:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.TotalMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.ConfirmMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.MicroscopyTested >= 0);
                                    break;

                                case (int)DQAVariables.RDTTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.RDTTested >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.MicroscopyPositive >= 0);
                                    break;

                                case (int)DQAVariables.RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.RDTPositive >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.AllCauseOutpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.AllCauseInpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.AllCauseDeaths >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.MalariaInpatients >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.MalariaInpatientDeaths >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.ConfirmedMalariaCasesTreated >= 0);
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.SuspectedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.PresumedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.IPTp:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.IPTp >= 0);
                                    break;

                                case (int)DQAVariables.ANC:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.ANC >= 0);
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (numeratorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                         && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && (x.MicroscopyTested >= x.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.AllCauseOutpatients > x.TotalMalariaCases);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                         && x.AllCauseInpatients > x.MalariaInpatients);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet.Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                     && x.AllCauseDeaths > x.MalariaInpatientDeaths);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                          && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0 && x.ConfirmMalariaCases >= x.ConfirmedMalariaCasesTreated);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                 && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested >=0 || x.RDTTested >=0) && x.SuspectedMalariaCases >= (x.MicroscopyTested ?? 0 + x.RDTTested ?? 0));
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                        && x.IsReportsReceived && x.IsExcludeTotalMalariaCases != 1
                                                        && x.Province == numeratorRequest.ProvinceType
                                                        && x.TotalMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                         && x.IsReportsReceived && x.IsExcludeConfirmMalariaCases != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.ConfirmMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                         && x.IsReportsReceived && x.IsExcludeMicroscopyTested != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.MicroscopyTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                          && x.IsReportsReceived && x.IsExcludeRDTTested != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.RDTTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeMicroscopyPositive != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.MicroscopyPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeRDTPositive != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.RDTPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeAllCauseOutpatients != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.AllCauseOutpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeAllCauseInpatients != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.AllCauseInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                          && x.IsReportsReceived && x.IsExcludeAllCauseDeaths != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.AllCauseDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                    && x.IsReportsReceived && x.IsExcludeMalariaInpatients != 1
                                                                    && x.Province == numeratorRequest.ProvinceType 
                                                                    && x.MalariaInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                    && x.IsReportsReceived && x.IsExcludeMalariaInpatientDeaths != 1
                                                                    && x.Province == numeratorRequest.ProvinceType && x.MalariaInpatientDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmedMalariaCasesAct)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeConfirmedMalariaCasesTreated != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.ConfirmedMalariaCasesTreated == 0);
                                    }
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.SuspectedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludeSuspectedMalariaCases != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.SuspectedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.PresumedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                        && x.IsReportsReceived && x.IsExcludePresumedMalariaCases != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.PresumedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.IPTp:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenIPTP)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year 
                                        && x.IsReportsReceived && x.IsExcludeIPTp != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.IPTp == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ANC:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenClinic)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year 
                                        && x.IsReportsReceived && x.IsExcludeANC != 1
                                        && x.Province == numeratorRequest.ProvinceType && x.ANC == 0);
                                    }
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport3:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                  .Count(x => x.Year == numeratorRequest.Year
                                   && x.Province == numeratorRequest.ProvinceType
                                   && x.IsReportsReceived &&
                                  (numeratorRequest.SelectedVariable.TotalMalariaCases ? x.TotalMalariaCases >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.ConfirmMalariaCases ? x.ConfirmMalariaCases >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MicroscopyTested ? x.MicroscopyTested >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.RDTTested ? x.RDTTested >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MicroscopyPositive ? x.MicroscopyPositive >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.AllCauseOutpatients ? x.AllCauseOutpatients >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.AllCauseInpatients ? x.AllCauseInpatients >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.AllCauseDeaths ? x.AllCauseDeaths >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.MalariaInpatients ? x.MalariaInpatients >= 0 : true) &&

                                  (numeratorRequest.SelectedVariable.MalariaInpatientDeaths ? x.MalariaInpatientDeaths >= 0 : true) &&
                                  (numeratorRequest.SelectedVariable.RDTPositive ? x.RDTPositive >= 0 : true));
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            numerator = numeratorRequest.DataFromSheet
                                       .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType &&

                                                    ((numeratorRequest.SelectedVariable.RDTTested && numeratorRequest.SelectedVariable.RDTPositive) ? (x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.MicroscopyTested && numeratorRequest.SelectedVariable.MicroscopyPositive) ? (x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && x.MicroscopyTested >= x.MicroscopyPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseOutpatients && numeratorRequest.SelectedVariable.TotalMalariaCases) ? (x.AllCauseOutpatients > x.TotalMalariaCases) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseInpatients && numeratorRequest.SelectedVariable.MalariaInpatients) ? (x.AllCauseInpatients > x.MalariaInpatients) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseDeaths && numeratorRequest.SelectedVariable.MalariaInpatientDeaths) ? (x.AllCauseDeaths > x.MalariaInpatientDeaths) : true)

                                                    );
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.IsReportsReceived

                                                    // priority variables checks
                                                    && (numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases ? x.TotalMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases ? x.ConfirmMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyTested ? x.MicroscopyTested == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTTested ? x.RDTTested == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive ? x.MicroscopyPositive == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTPositive ? x.RDTPositive == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients ? x.AllCauseOutpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients ? x.AllCauseInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths ? x.AllCauseDeaths == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatients ? x.MalariaInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths ? x.MalariaInpatientDeaths == 0 : true)
                                                    );
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport1:
                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                        && x.District == numeratorRequest.DistrictType && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                        && x.District == numeratorRequest.DistrictType && x.IsReportsOnTime);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (numeratorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.TotalMalariaCases));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatientDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive ?? 0) + Convert.ToInt32(p.RDTPositive ?? 0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.RDTPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType && x.District == numeratorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport2:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.TotalMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.ConfirmMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.MicroscopyTested >= 0);
                                    break;

                                case (int)DQAVariables.RDTTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.RDTTested >= 0);
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.MicroscopyPositive >= 0);
                                    break;

                                case (int)DQAVariables.RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.RDTPositive >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.AllCauseOutpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.AllCauseInpatients >= 0);
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.AllCauseDeaths >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.MalariaInpatients >= 0);
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.MalariaInpatientDeaths >= 0);
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.ConfirmedMalariaCasesTreated >= 0);
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.SuspectedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.PresumedMalariaCases >= 0);
                                    break;

                                case (int)DQAVariables.IPTp:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.IPTp >= 0);
                                    break;

                                case (int)DQAVariables.ANC:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType && x.ANC >= 0);
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (numeratorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                         && x.Province == numeratorRequest.ProvinceType
                                                         && x.District == numeratorRequest.DistrictType
                                                         && x.RDTTested >= 0 &&  x.RDTPositive >= 0  && x.RDTTested >= x.RDTPositive);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                            && x.Province == numeratorRequest.ProvinceType
                                                            && x.District == numeratorRequest.DistrictType
                                                            && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && (x.MicroscopyTested >= x.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType
                                                && x.AllCauseOutpatients > x.TotalMalariaCases);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.AllCauseInpatients > x.MalariaInpatients);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.AllCauseDeaths > x.MalariaInpatientDeaths);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0 && x.ConfirmMalariaCases >= x.ConfirmedMalariaCasesTreated);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                && x.Province == numeratorRequest.ProvinceType
                                                && x.District == numeratorRequest.DistrictType
                                                && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested >=0 || x.RDTTested >=0) && x.SuspectedMalariaCases >= (x.MicroscopyTested ?? 0 + x.RDTTested ?? 0));
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                        && x.IsReportsReceived
                                                        && x.Province == numeratorRequest.ProvinceType
                                                        && x.District == numeratorRequest.DistrictType
                                                        && x.IsExcludeTotalMalariaCases != 1
                                                        && x.TotalMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                        && x.IsReportsReceived
                                                        && x.Province == numeratorRequest.ProvinceType
                                                        && x.District == numeratorRequest.DistrictType
                                                        && x.IsExcludeConfirmMalariaCases != 1
                                                        && x.ConfirmMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                       && x.IsReportsReceived
                                                       && x.Province == numeratorRequest.ProvinceType
                                                       && x.District == numeratorRequest.DistrictType
                                                       && x.IsExcludeMicroscopyTested != 1
                                                       && x.MicroscopyTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeRDTTested != 1
                                                                && x.RDTTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                    && x.IsReportsReceived
                                                                    && x.Province == numeratorRequest.ProvinceType
                                                                    && x.District == numeratorRequest.DistrictType
                                                                    && x.IsExcludeMicroscopyPositive != 1
                                                                    && x.MicroscopyPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                         && x.IsReportsReceived
                                                         && x.Province == numeratorRequest.ProvinceType
                                                         && x.District == numeratorRequest.DistrictType
                                                         && x.IsExcludeRDTPositive != 1
                                                         && x.RDTPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeAllCauseOutpatients != 1
                                                                && x.AllCauseOutpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeAllCauseInpatients != 1
                                                                && x.AllCauseInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeAllCauseDeaths != 1
                                                                && x.AllCauseDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                    && x.IsReportsReceived
                                                                    && x.Province == numeratorRequest.ProvinceType
                                                                    && x.District == numeratorRequest.DistrictType
                                                                    && x.IsExcludeMalariaInpatients != 1
                                                                    && x.MalariaInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                  && x.IsReportsReceived
                                                                  && x.Province == numeratorRequest.ProvinceType
                                                                  && x.District == numeratorRequest.DistrictType
                                                                  && x.IsExcludeMalariaInpatientDeaths != 1
                                                                  && x.MalariaInpatientDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmedMalariaCasesAct)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                  && x.IsReportsReceived
                                                                  && x.Province == numeratorRequest.ProvinceType
                                                                  && x.District == numeratorRequest.DistrictType
                                                                  && x.IsExcludeConfirmedMalariaCasesTreated != 1
                                                                  && x.ConfirmedMalariaCasesTreated == 0);
                                    }
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.SuspectedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeSuspectedMalariaCases != 1
                                                                && x.SuspectedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.PresumedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                               && x.IsReportsReceived
                                                               && x.Province == numeratorRequest.ProvinceType
                                                               && x.District == numeratorRequest.DistrictType
                                                               && x.IsExcludePresumedMalariaCases != 1
                                                               && x.PresumedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.IPTp:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenIPTP)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                                && x.IsReportsReceived
                                                                && x.Province == numeratorRequest.ProvinceType
                                                                && x.District == numeratorRequest.DistrictType
                                                                && x.IsExcludeIPTp != 1
                                                                && x.IPTp == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ANC:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenClinic)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                               && x.IsReportsReceived
                                                               && x.Province == numeratorRequest.ProvinceType
                                                               && x.District == numeratorRequest.DistrictType
                                                               && x.IsExcludeANC != 1
                                                               && x.ANC == 0);
                                    }
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport3:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                      .Count(x => x.Year == numeratorRequest.Year
                                            && x.Province == numeratorRequest.ProvinceType
                                            && x.District == numeratorRequest.DistrictType
                                            &&  x.IsReportsReceived &&
                                            (numeratorRequest.SelectedVariable.TotalMalariaCases ? x.TotalMalariaCases >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.ConfirmMalariaCases ? x.ConfirmMalariaCases >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.MicroscopyTested ? x.MicroscopyTested >= 0 : true) &&

                                            (numeratorRequest.SelectedVariable.RDTTested ? x.RDTTested >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.MicroscopyPositive ? x.MicroscopyPositive >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.AllCauseOutpatients ? x.AllCauseOutpatients >= 0 : true) &&

                                            (numeratorRequest.SelectedVariable.AllCauseInpatients ? x.AllCauseInpatients >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.AllCauseDeaths ? x.AllCauseDeaths >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.MalariaInpatients ? x.MalariaInpatients >= 0 : true) &&

                                            (numeratorRequest.SelectedVariable.MalariaInpatientDeaths ? x.MalariaInpatientDeaths >= 0 : true) &&
                                            (numeratorRequest.SelectedVariable.RDTPositive ? x.RDTPositive >= 0 : true));
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType &&
                                                    ((numeratorRequest.SelectedVariable.RDTTested && numeratorRequest.SelectedVariable.RDTPositive) ? (x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.MicroscopyTested && numeratorRequest.SelectedVariable.MicroscopyPositive) ? (x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && x.MicroscopyTested >= x.MicroscopyPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseOutpatients && numeratorRequest.SelectedVariable.TotalMalariaCases) ? (x.AllCauseOutpatients > x.TotalMalariaCases) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseInpatients && numeratorRequest.SelectedVariable.MalariaInpatients) ? (x.AllCauseInpatients > x.MalariaInpatients) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseDeaths && numeratorRequest.SelectedVariable.MalariaInpatientDeaths) ? (x.AllCauseDeaths > x.MalariaInpatientDeaths) : true)
                                                    );
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.IsReportsReceived

                                                    // priority variables checks
                                                    && (numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases ? x.TotalMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases ? x.ConfirmMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyTested ? x.MicroscopyTested == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTTested ? x.RDTTested == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive ? x.MicroscopyPositive == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTPositive ? x.RDTPositive == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients ? x.AllCauseOutpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients ? x.AllCauseInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths ? x.AllCauseDeaths == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatients ? x.MalariaInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths ? x.MalariaInpatientDeaths == 0 : true)
                                                   );
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport1:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year
                                            && x.Province == numeratorRequest.ProvinceType
                                            && x.District == numeratorRequest.DistrictType
                                            && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                            && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            numerator = numeratorRequest.DataFromSheet
                                .Count(x => x.Year == numeratorRequest.Year
                                            && x.Province == numeratorRequest.ProvinceType
                                            && x.District == numeratorRequest.DistrictType
                                            && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                            && x.IsReportsOnTime);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (numeratorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.TotalMalariaCases));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MalariaInpatientDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive ?? 0) + Convert.ToInt32(p.RDTPositive ?? 0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.RDTPositive));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Where(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport2:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:
                                    
                                        numerator = numeratorRequest.DataFromSheet
                                            .Count(x => x.Year == numeratorRequest.Year
                                                        && x.Province == numeratorRequest.ProvinceType
                                                        && x.District == numeratorRequest.DistrictType
                                                        && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                        && x.TotalMalariaCases >= 0);
                                        break;

                                case (int)DQAVariables.ConfirmMalariaCases:

                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.ConfirmMalariaCases >= 0);
                                        break;

                                case (int)DQAVariables.MicroscopyTested:
                                   
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.MicroscopyTested >= 0);
                                        break;

                                case (int)DQAVariables.RDTTested:

                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.RDTTested >= 0);
                                        break;

                                case (int)DQAVariables.MicroscopyPositive:
                                    
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.MicroscopyPositive >= 0);
                                    break;

                                case (int)DQAVariables.RDTPositive:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.RDTPositive >= 0);
                                        break;

                                case (int)DQAVariables.AllCauseOutpatients:
                                   
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseOutpatients >= 0);
                                        break;

                                case (int)DQAVariables.AllCauseInpatients:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseInpatients >= 0);
                                        break;

                                case (int)DQAVariables.AllCauseDeaths:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseDeaths >= 0);
                                        break;

                                case (int)DQAVariables.MalariaInpatients:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.MalariaInpatients >= 0);
                                        break;
                                
                                case (int)DQAVariables.MalariaInpatientDeaths:
                                   
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.MalariaInpatientDeaths >= 0);
                                        break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:
                                
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.ConfirmedMalariaCasesTreated >= 0);
                                        break;

                                case (int)DQAVariables.SuspectedMalariaCases:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.SuspectedMalariaCases >= 0);
                                        break;

                                case (int)DQAVariables.PresumedMalariaCases:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.PresumedMalariaCases >= 0);
                                        break;

                                case (int)DQAVariables.IPTp:
                                    
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IPTp >= 0);
                                        break;
                                    
                                case (int)DQAVariables.ANC:
                                     
                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.ANC >= 0);
                                        break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (numeratorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.RDTTested >= 0 &&  x.RDTPositive >= 0  && x.RDTTested >= x.RDTPositive);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && (x.MicroscopyTested >= x.MicroscopyPositive));

                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseOutpatients > x.TotalMalariaCases);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseInpatients > x.MalariaInpatients);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.AllCauseDeaths > x.MalariaInpatientDeaths);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0 && x.ConfirmMalariaCases >= x.ConfirmedMalariaCasesTreated);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    numerator = numeratorRequest.DataFromSheet
                                                .Count(x => x.Year == numeratorRequest.Year
                                                 && x.Province == numeratorRequest.ProvinceType
                                                 && x.District == numeratorRequest.DistrictType
                                                 && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                 && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested >=0 || x.RDTTested >=0) && x.SuspectedMalariaCases >= (x.MicroscopyTested ?? 0 + x.RDTTested ?? 0));
                                    break;
                            }
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            switch (numeratorRequest.DQAVariable)
                            {
                                case (int)DQAVariables.TotalMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeTotalMalariaCases != 1
                                                    && x.TotalMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeConfirmMalariaCases != 1
                                                    && x.ConfirmMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year  && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeMicroscopyTested != 1
                                                    && x.MicroscopyTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTTested:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTTested)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeRDTTested != 1
                                                    && x.RDTTested == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MicroscopyPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeMicroscopyPositive  != 1
                                                    && x.MicroscopyPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.RDTPositive:
                                    if (!numeratorRequest.SelectedVariable.Concordance.RDTPositive)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeRDTPositive != 1
                                                    && x.RDTPositive == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseOutpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeAllCauseOutpatients != 1
                                                    && x.AllCauseOutpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeAllCauseInpatients != 1
                                                    && x.AllCauseInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.AllCauseDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeAllCauseDeaths  != 1
                                                    && x.AllCauseDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatients:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatients)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeMalariaInpatients  != 1
                                                    && x.MalariaInpatients == 0);
                                    }
                                    break;

                                case (int)DQAVariables.MalariaInpatientDeaths:
                                    if (!numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeMalariaInpatientDeaths  != 1
                                                    && x.MalariaInpatientDeaths == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ConfirmedMalariaCasesTreated:
                                    if (!numeratorRequest.SelectedVariable.Concordance.ConfirmedMalariaCasesAct)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeConfirmedMalariaCasesTreated  != 1
                                                    && x.ConfirmedMalariaCasesTreated == 0);
                                    }
                                    break;

                                case (int)DQAVariables.SuspectedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.SuspectedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeSuspectedMalariaCases != 1
                                                    && x.SuspectedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.PresumedMalariaCases:
                                    if (!numeratorRequest.SelectedVariable.Concordance.PresumedMalariaCase)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludePresumedMalariaCases  != 1
                                                    && x.PresumedMalariaCases == 0);
                                    }
                                    break;

                                case (int)DQAVariables.IPTp:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenIPTP)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeIPTp  != 1
                                                    && x.IPTp == 0);
                                    }
                                    break;

                                case (int)DQAVariables.ANC:
                                    if (!numeratorRequest.SelectedVariable.Concordance.NumberOfPregnantWomenClinic)
                                        numerator = -1;
                                    else
                                    {
                                        numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year && x.IsReportsReceived
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsExcludeANC != 1
                                                    && x.ANC == 0);
                                    }
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport3:

                    switch (numeratorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName  && x.IsReportsReceived &&
                                                    (numeratorRequest.SelectedVariable.TotalMalariaCases ? x.TotalMalariaCases >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.ConfirmMalariaCases ? x.ConfirmMalariaCases >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.MicroscopyTested ? x.MicroscopyTested >= 0 : true) &&

                                                    (numeratorRequest.SelectedVariable.RDTTested ? x.RDTTested >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.MicroscopyPositive ? x.MicroscopyPositive >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.AllCauseOutpatients ? x.AllCauseOutpatients >= 0 : true) &&

                                                    (numeratorRequest.SelectedVariable.AllCauseInpatients ? x.AllCauseInpatients >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.AllCauseDeaths ? x.AllCauseDeaths >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.MalariaInpatients ? x.MalariaInpatients >= 0 : true) &&

                                                    (numeratorRequest.SelectedVariable.MalariaInpatientDeaths ? x.MalariaInpatientDeaths >= 0 : true) &&
                                                    (numeratorRequest.SelectedVariable.RDTPositive ? x.RDTPositive >= 0 : true));
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName &&
                                                    ((numeratorRequest.SelectedVariable.RDTTested && numeratorRequest.SelectedVariable.RDTPositive) ? (x.RDTTested >= 0 &&  x.RDTPositive >= 0 && x.RDTTested >=  x.RDTPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.MicroscopyTested && numeratorRequest.SelectedVariable.MicroscopyPositive) ? (x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0 && x.MicroscopyTested >= x.MicroscopyPositive) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseOutpatients && numeratorRequest.SelectedVariable.TotalMalariaCases) ? (x.AllCauseOutpatients > x.TotalMalariaCases) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseInpatients && numeratorRequest.SelectedVariable.MalariaInpatients) ? (x.AllCauseInpatients > x.MalariaInpatients) : true) &&
                                                    ((numeratorRequest.SelectedVariable.AllCauseDeaths && numeratorRequest.SelectedVariable.MalariaInpatientDeaths) ? (x.AllCauseDeaths > x.MalariaInpatientDeaths) : true)
                                                    );
                            break;

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            numerator = numeratorRequest.DataFromSheet
                                        .Count(x => x.Year == numeratorRequest.Year
                                                    && x.Province == numeratorRequest.ProvinceType
                                                    && x.District == numeratorRequest.DistrictType
                                                    && x.HealthFacilityName == numeratorRequest.HealthFacilityName
                                                    && x.IsReportsReceived

                                                    // priority variables checks
                                                    && (numeratorRequest.SelectedVariable.Concordance.TotalMalariaCases ? x.TotalMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.ConfirmMalariaCases ? x.ConfirmMalariaCases == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyTested ? x.MicroscopyTested == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTTested ? x.RDTTested == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MicroscopyPositive ? x.MicroscopyPositive == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.RDTPositive ? x.RDTPositive == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseOutpatients ? x.AllCauseOutpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseInpatients ? x.AllCauseInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.AllCauseDeaths ? x.AllCauseDeaths == 0 : true)

                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatients ? x.MalariaInpatients == 0 : true)
                                                    && (numeratorRequest.SelectedVariable.Concordance.MalariaInpatientDeaths ? x.MalariaInpatientDeaths == 0 : true)
                                                    );
                            break;
                    }
                    break;
            }

            return numerator;
        }

        /// <summary>
        /// Generates denominator value for report calculation
        /// </summary>
        /// <param name="reportRequest">Accepts DQAReportCalculationRequest model as parameter</param>
        /// <returns>Denominator for calculation as integer</returns>
        private int GetReportDenominator(DQAReportCalculationRequestDto denominatorRequest)
        {
            int denominator = 0;
            switch (denominatorRequest.ReportType)
            {
                case (int)DQAReportType.NationalReport:
                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year && x.IsExpectedReports);
                            break;
                    }
                    break;

                case (int)DQAReportType.NationalReport1:
                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year && x.HealthFacilityType == denominatorRequest.HealthFacilityType && x.IsExpectedReports);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (denominatorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                            (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.AllCauseOutpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                           (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.AllCauseInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                            (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.AllCauseDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                            (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested??0) + Convert.ToInt32(p.RDTTested??0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                            (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                           (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => Convert.ToInt32(p.RDTTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year &&
                                            (denominatorRequest.HealthFacilityType == Constants.Common.TotalColumnName ? true : (x.HealthFacilityType == denominatorRequest.HealthFacilityType)))
                                        .Sum(p => denominatorRequest.SelectedVariable.SuspectedMalariaCase ?
                                          Convert.ToInt32(p.PresumedMalariaCases ?? 0) + Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0):
                                         Convert.ToInt32(p.SuspectedMalariaCases ?? 0)) ;
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.NationalReport2:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (denominatorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.RDTTested >= 0 && x.RDTPositive >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.AllCauseOutpatients >= 0 &&  x.TotalMalariaCases >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.AllCauseInpatients >= 0 && x.MalariaInpatients >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.AllCauseDeaths >=0 && x.MalariaInpatientDeaths >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                    && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested>=0 || x.RDTTested >=0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.NationalReport3:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year && x.IsReportsReceived);
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.IsExpectedReports);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (denominatorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseOutpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested??0) + Convert.ToInt32(p.RDTTested??0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                        .Sum(p => Convert.ToInt32(p.RDTTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType)
                                         .Sum(p => denominatorRequest.SelectedVariable.SuspectedMalariaCase ?
                                          Convert.ToInt32(p.PresumedMalariaCases ?? 0) + Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0)
                                          : Convert.ToInt32(p.SuspectedMalariaCases ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport1:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                            && x.HealthFacilityType == denominatorRequest.HealthFacilityType && x.IsExpectedReports);
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport2:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                         .Count(x => x.Year == denominatorRequest.Year && x.IsReportsReceived && x.Province == denominatorRequest.ProvinceType);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (denominatorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                                 .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                 && x.RDTTested >= 0 && x.RDTPositive >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                                 .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                 && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    denominator = denominatorRequest.DataFromSheet
                                                 .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                 && x.AllCauseOutpatients >= 0 &&  x.TotalMalariaCases >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                                 .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                 && x.AllCauseInpatients >= 0 && x.MalariaInpatients >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                                  .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                  && x.AllCauseDeaths >=0 && x.MalariaInpatientDeaths >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    denominator = denominatorRequest.DataFromSheet
                                                 .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                 && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                                  .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                  && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested>=0 || x.RDTTested >=0));
                                    break;

                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.ProvinceReport3:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType && x.IsReportsReceived);
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport1:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType && x.IsExpectedReports);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (denominatorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseOutpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.AllCauseDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested??0) + Convert.ToInt32(p.RDTTested??0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => Convert.ToInt32(p.RDTTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType && x.District == denominatorRequest.DistrictType)
                                        .Sum(p => denominatorRequest.SelectedVariable.SuspectedMalariaCase ?
                                          Convert.ToInt32(p.PresumedMalariaCases ?? 0) + Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0)
                                          : Convert.ToInt32(p.SuspectedMalariaCases ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport2:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                        && x.District == denominatorRequest.DistrictType && x.IsReportsReceived);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (denominatorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.RDTTested >= 0 && x.RDTPositive >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.AllCauseOutpatients >= 0 &&  x.TotalMalariaCases >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.AllCauseInpatients >= 0 && x.MalariaInpatients >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.AllCauseDeaths >=0 && x.MalariaInpatientDeaths >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                                && x.Province == denominatorRequest.ProvinceType
                                                && x.District == denominatorRequest.DistrictType
                                                && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested>=0 || x.RDTTested >=0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.DistrictReport3:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.IsReportsReceived
                                                    );
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport1:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year
                                            && x.Province == denominatorRequest.ProvinceType
                                            && x.District == denominatorRequest.DistrictType
                                            && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                            && x.IsExpectedReports);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            switch (denominatorRequest.ConsistancyIndicator)
                            {
                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaOutpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.AllCauseOutpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.AllCauseInpatients));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfMalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.AllCauseDeaths));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.TestPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested??0) + Convert.ToInt32(p.RDTTested??0));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.SlidePositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.MicroscopyTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.RDTPositivityRate:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                        .Sum(p => Convert.ToInt32(p.RDTTested));
                                    break;

                                case (int)ConsistencyOverTimeKeyIndicator.ProportionOfSuspectsTested:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Where(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName)
                                         .Sum(p => denominatorRequest.SelectedVariable.SuspectedMalariaCase ?
                                          Convert.ToInt32(p.PresumedMalariaCases ?? 0) + Convert.ToInt32(p.MicroscopyTested ?? 0) + Convert.ToInt32(p.RDTTested ?? 0)
                                          : Convert.ToInt32(p.SuspectedMalariaCases ?? 0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport2:

                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                .Count(x => x.Year == denominatorRequest.Year
                                            && x.Province == denominatorRequest.ProvinceType
                                            && x.District == denominatorRequest.DistrictType
                                            && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                            && x.IsReportsReceived); //2 means both A B IsReportsReceived 1);
                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                            switch (denominatorRequest.ConsistencyBetweenVariable)
                            {
                                case (int)ConsistencyBetweenVariables.RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.RDTTested >= 0 && x.RDTPositive >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.MicroscopyTested_MicroscopyPositive:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.MicroscopyTested>= 0 &&  x.MicroscopyPositive >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseOutpatients_TotalMalariaCases:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.AllCauseOutpatients >= 0 &&  x.TotalMalariaCases >= 0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseInpatients_MalariaInpatients:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.AllCauseInpatients >= 0 && x.MalariaInpatients >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.AllCauseDeaths_MalariaInpatientDeaths:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.AllCauseDeaths >=0 && x.MalariaInpatientDeaths >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.ConfirmMalariaCases_ConfirmedMalariaCasesTreated:

                                    denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.ConfirmMalariaCases >= 0 && x.ConfirmedMalariaCasesTreated >=0);
                                    break;

                                case (int)ConsistencyBetweenVariables.Suspectedcases_RDTTested_RDTPositive:

                                    denominator = denominatorRequest.DataFromSheet.Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.SuspectedMalariaCases >=0  && (x.MicroscopyTested>=0 || x.RDTTested >=0));
                                    break;
                            }
                            break;
                    }
                    break;

                case (int)DQAReportType.HealthFacilityReport3:
                    switch (denominatorRequest.DQAIndicator)
                    {
                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            denominator = denominatorRequest.DataFromSheet
                                        .Count(x => x.Year == denominatorRequest.Year
                                                    && x.Province == denominatorRequest.ProvinceType
                                                    && x.District == denominatorRequest.DistrictType
                                                    && x.HealthFacilityName == denominatorRequest.HealthFacilityName
                                                    && x.IsReportsReceived); //2 means both A B IsReportsReceived 1
                            break;
                    }
                    break;
            }

            return denominator;
        }

        /// <summary>
        /// Method to find difference between 2 variables
        /// </summary>
        /// <param name="value1">An int value</param>
        /// <param name="value2">An int value</param>
        /// <returns>Difference in int</returns>
        private int FindDifference(int? value1, int? value2)
        {
            if (value1 != null || value2 != null)
            {
                if (value1 > 0 && value2 > 0)
                {
                    return (value1 ?? 0) - (value2 ?? 0);
                }
            }
            return 0;
        }

        /// <summary>
        /// Method to get discrepancy data for variables with concordance data
        /// </summary>
        /// <param name="assessmentId">Guid</param>
        /// <returns>List of discrepancy of variable data with concordance data as response</returns>
        private List<TemplateDataSourceCombinedVariablesDto> GetDiscrepancyForSameVariablesWithConcordanceData(Guid assessmentId)
        {
            var modifiedDataSource = new List<TemplateDataSourceCombinedVariablesDto>();
            modifiedDataSource = _dQARepository.GetDiscrepancyForSameVariablesWithConcordanceData(assessmentId).Result.ToList();
            _cacheService.SetDataIntoCache(DQAConstants.DiscrepancyForSameVariablesWithConcordance + "_" + assessmentId, modifiedDataSource);

            return modifiedDataSource;
        }

        /// <summary>
        /// Generates predicate for the input class
        /// </summary>
        /// <param name="requestVariables">An instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <returns>Report object as response</returns>
        private Expression<Func<TemplateDataSourceCombinedVariablesDto, bool>> GetMainAndConcordanceDataDifferencePredicate(TemplateDataSourceCombinedVariablesDto requestVariables)
        {
            var predicate = PredicateExtensions.Begin<TemplateDataSourceCombinedVariablesDto>();

            if (requestVariables.Year > 0)
            {
                predicate = predicate.And(x => x.Year == requestVariables.Year);
            }

            if (!string.IsNullOrEmpty(requestVariables.Province))
            {
                predicate = predicate.And(x => x.Province == requestVariables.Province);
            }

            if (!string.IsNullOrEmpty(requestVariables.District))
            {
                predicate = predicate.And(x => x.District == requestVariables.District);
            }

            if (!string.IsNullOrEmpty(requestVariables.HealthFacilityName))
            {
                predicate = predicate.And(x => x.HealthFacilityName == requestVariables.HealthFacilityName);
            }

            if (!string.IsNullOrEmpty(requestVariables.HealthFacilityType))
            {
                predicate = predicate.And(x => x.HealthFacilityType == requestVariables.HealthFacilityType);
            }

            if (requestVariables.Month > 0)
            {
                predicate = predicate.And(x => x.Month == requestVariables.Month);
            }

            if (requestVariables.IsReportsOnTime)
            {
                predicate = predicate.And(x => x.IsReportsOnTime == requestVariables.IsReportsOnTime);
            }

            if (requestVariables.IsReportsReceived)
            {
                predicate = predicate.And(x => x.IsReportsReceived == requestVariables.IsReportsReceived);
            }

            if (requestVariables.IsExpectedReports)
            {
                predicate = predicate.And(x => x.IsExpectedReports == requestVariables.IsExpectedReports);
            }

            return predicate;
        }

        #endregion

    }
}
