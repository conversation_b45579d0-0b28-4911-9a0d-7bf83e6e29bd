﻿using System;
using System.Collections.Generic;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Interfaces
{
    /// <summary>
    /// This interface contains methods related to DQA for business validations
    /// </summary>
    public interface IDQARuleChecker
    {
        bool IsServiceLevelFinalized(Guid serviceLevelId);

        bool DoesServiceLevelExist(Guid serviceLevelId);

        bool IsLastServiceLevelRecord(Guid assessmentId);

        bool AtLeastOnePriorityVariableShouldBeSelected(IEnumerable<Guid> variableIds);
    }
}
