﻿using System;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Interfaces
{
    public interface IAssessmentRuleChecker
    {
        bool AreAllowedIndicatorsSelectedForAssessment(Guid assessmentId, Guid[] indicatorIds);
        bool DoesAssessmentExist(Guid assessmentId);
        Task<bool> HasUserPermissionOnAssessment(Guid assessmentId, Guid userId, UserAssessmentPermission permission);
        bool AreDatesToUpdateOnAssessmentValid(Guid assessmentId, DateTime startDate, DateTime endDate,ref int failureType);
        bool CanCreateAssessment(Guid countryId, DateTime startDate, DateTime endDate);
        bool IsAnyOptionalIndicatorSelected(Guid[] indicatorIds, Guid assessmentId);
    }
}
