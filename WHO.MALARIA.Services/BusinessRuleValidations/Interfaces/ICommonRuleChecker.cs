﻿using System;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Interfaces
{
    /// <summary>
    /// This interface contains common methods for business validations
    /// </summary>
    public interface ICommonRuleChecker
    {
        bool StringIsNullOrEmpty(string input);
        bool GuidArrayIsNullOrEmpty(Guid[] input);
        bool GuidIsEmpty(Guid input);
        bool IsDateNotValid(DateTime input);
        bool IntArrayIsNullOrEmpty(int[] input);       
    }
}
