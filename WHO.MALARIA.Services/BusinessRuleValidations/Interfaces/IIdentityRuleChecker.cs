﻿using System;
namespace WHO.MALARIA.Services.BusinessRuleValidations.Interfaces
{
    public interface IIdentityRuleChecker
    {
        bool IsUnique(string email);
        bool IsUniqueUsername(string email);
        bool PasswordMustNotEmpty(string password);
        bool EmailMustNotEmpty(string email);
        bool UsernameMustNotEmpty(string username);
        bool IsStrongPassword(string password);
        bool IsEmailValid(string email);
    }
}
