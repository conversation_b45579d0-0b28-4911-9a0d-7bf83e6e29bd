﻿using System;
using System.Text.RegularExpressions;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    public class IdentityRuleChecker: IIdentityRuleChecker
    {
        private readonly IDbManager _dbManager;

        public IdentityRuleChecker(IDbManager dbManager)
        {
            _dbManager = dbManager;
        }

        public bool EmailMustNotEmpty(string email)
        {
            return string.IsNullOrWhiteSpace(email);
        }

        public bool IsStrongPassword(string password)
        {
            // Expressions to check strong password. The password should be,
            // At least one upper case character
            // At least one lower case character
            // At least one number
            // At least one special character
            // Length should be between 8 to 30 
            string strongPasswordExpression = "^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,30}$";

            // To check strong password
            bool isStrongPassword = Regex.IsMatch(password, strongPasswordExpression);

            return !isStrongPassword;

        }

        public bool IsUnique(string email)
        {
            string sql = $"SELECT COUNT(1) FROM [{MalariaSchemas.Internal}].[Identity] " +
                "WHERE Email = @Email";

            int identityCount = _dbManager.QuerySingleOrDefault<int>(sql,
                           new
                           {
                               Email = email
                           });

            return identityCount > 0;

        }

        public bool IsUniqueUsername(string username)
        {
            string sql = $"SELECT COUNT(1) FROM [{MalariaSchemas.Internal}].[Identity] " +
                "WHERE Username = @Username";

            int identityCount = _dbManager.QuerySingleOrDefault<int>(sql,
                           new
                           {
                               Username = username
                           });

            return identityCount > 0;
        }

        public bool PasswordMustNotEmpty(string password)
        {
            return string.IsNullOrWhiteSpace(password);
        }

        public bool UsernameMustNotEmpty(string username)
        {
            return string.IsNullOrWhiteSpace(username);
        }

        public bool IsEmailValid(string email)
        {
            return Regex.IsMatch(email, @"\A(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)\Z", RegexOptions.IgnoreCase);
        }
    }
}
