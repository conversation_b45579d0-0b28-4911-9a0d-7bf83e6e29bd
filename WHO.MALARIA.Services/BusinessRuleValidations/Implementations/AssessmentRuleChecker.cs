﻿using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    public class AssessmentRuleChecker : IAssessmentRuleChecker
    {
        private readonly IDbManager _dbManager;
        private readonly IUnitOfWork _unitOfWork;

        public AssessmentRuleChecker(IDbManager dbManager, IUnitOfWork unitOfWork)
        {
            _dbManager = dbManager;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Checks if only allowed indicator is selected for assessment strategies
        /// </summary>
        /// <param name="assessmentId">Id for the Assessment for which indicators selection is being validated</param>
        /// <param name="indicatorIds">Array of indicator ids</param>
        /// <returns>True; if all the indicators selected are allowed to be selected for assessment strategies else false</returns>
        public bool AreAllowedIndicatorsSelectedForAssessment(Guid assessmentId, Guid[] indicatorIds)
        {
            string sql = @$"SELECT TOP 1 1 FROM {MalariaSchemas.Internal}.IndicatorStrategyMapping ISM
                            JOIN {MalariaSchemas.ScopeDefinition}.AssessmentStrategy AST ON ISM.StrategyId=AST.StrategyId AND AST.AssessmentId=@AssessmentId
                            RIGHT JOIN {MalariaSchemas.Internal}.Indicator I ON ISM.IndicatorId=I.Id
                            WHERE I.Id IN @IndicatorIds AND ISM.IndicatorId IS NULL";

            var invalidIndicatorSelection = _dbManager.QuerySingleOrDefault<int?>(sql,
                                    new
                                    {
                                        AssessmentId = assessmentId,
                                        IndicatorIds = indicatorIds
                                    });

            return !invalidIndicatorSelection.HasValue;
        }

        /// <summary>
        /// Check if assessment exists or not for assessment id
        /// </summary>
        /// <param name="assessmentId">Id of assessment </param>
        /// <returns> True if assessment exists; else false </returns>
        public bool DoesAssessmentExist(Guid assessmentId)
        {
            return _unitOfWork.AssessmentRepository.Queryable(x => x.Id == assessmentId).Any();
        }

        /// <summary>
        /// Checks if user has specific permission on assessment
        /// </summary>
        /// <param name="assessmentId">Id for the Assessment</param>
        /// <param name="userId">Id of user</param>
        /// <param name="permission">Permission to check for</param>
        /// <returns>Returns true if user has passed permission on assessment; else false</returns>
        public async Task<bool> HasUserPermissionOnAssessment(Guid assessmentId, Guid userId, UserAssessmentPermission permission)
        {
            UserAssessmentPermissionDto userPermissions = await _dbManager.QuerySingleAsync<UserAssessmentPermissionDto>($"{MalariaSchemas.ScopeDefinition}.GetUserPermissionsOnAssessment", new { AssessmentId = assessmentId, UserId = userId }, null, null, CommandType.StoredProcedure);

            switch (permission)
            {
                case UserAssessmentPermission.CanViewDetails:
                    return userPermissions.CanViewDetails;
                case UserAssessmentPermission.CanConfigure:
                    return userPermissions.CanConfigure;
                case UserAssessmentPermission.CanChangeCountry:
                    return userPermissions.CanChangeCountry;
                case UserAssessmentPermission.CanUpdateStrategies:
                    return userPermissions.CanUpdateStrategies;
                case UserAssessmentPermission.CanUpdateIndicators:
                    return userPermissions.CanUpdateIndicators;
                case UserAssessmentPermission.CanFinalize:
                    return userPermissions.CanFinalize;
                case UserAssessmentPermission.CanCollectData:
                    return userPermissions.CanCollectData;
                case UserAssessmentPermission.CanPublish:
                    return userPermissions.CanPublish;
                case UserAssessmentPermission.CanUploadFile:
                    return userPermissions.CanUploadFile;
                case UserAssessmentPermission.CanCreateOrUpdateServiceLevel:
                    return userPermissions.CanCreateOrUpdateServiceLevel;
                case UserAssessmentPermission.CanDeleteServiceLevel:
                    return userPermissions.CanDeleteServiceLevel;
                case UserAssessmentPermission.CanFinalizeServiceLevel:
                    return userPermissions.CanFinalizeServiceLevel;
                case UserAssessmentPermission.CanSaveOrFinalizeDRIndicatorResponse:
                    return userPermissions.CanSaveOrFinalizeDRIndicatorResponse;
                case UserAssessmentPermission.CanCreateOrUpdateQuestionBank:
                    return userPermissions.CanCreateOrUpdateQuestionBank;
                case UserAssessmentPermission.CanFinalizeQuestionBank:
                    return userPermissions.CanFinalizeQuestionBank;
                case UserAssessmentPermission.CanEditQuestionBankRespondentType:
                    return userPermissions.CanEditQuestionBankRespondentType;
                case UserAssessmentPermission.CanSaveOrFinalizeDeskLevelDQAVariables:
                    return userPermissions.CanSaveOrFinalizeDeskLevelDQAVariables;
                case UserAssessmentPermission.CanGenerateOrUploadDeskLevelDQATemplate:
                    return userPermissions.CanGenerateOrUploadDeskLevelDQATemplate;
                case UserAssessmentPermission.CanEditAndSaveDeskLevelDQASummaryResult:
                    return userPermissions.CanEditAndSaveDeskLevelDQASummaryResult;
                case UserAssessmentPermission.CanFinalizeDeskLevelDQASummaryResult:
                    return userPermissions.CanFinalizeDeskLevelDQASummaryResult;
                case UserAssessmentPermission.CanExportDeskLevelDQASummary:
                    return userPermissions.CanExportDeskLevelDQASummary;
                case UserAssessmentPermission.CanGenerateQuestionBank:
                    return userPermissions.CanGenerateQuestionBank;
                case UserAssessmentPermission.CanExportShellTable:
                    return userPermissions.CanExportShellTable;
                case UserAssessmentPermission.CanExportScoreCard:
                    return userPermissions.CanExportScoreCard;
                case UserAssessmentPermission.CanSaveScoreCard:
                    return userPermissions.CanSaveScoreCard;
                case UserAssessmentPermission.CanFinalizeScoreCard:
                    return userPermissions.CanFinalizeScoreCard;
                case UserAssessmentPermission.ShowResultsOnGlobalDashboard:
                    return userPermissions.ShowResultsOnGlobalDashboard;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Checks if the dates proposed to update in assessment valid or not
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <param name="startDate">Updated start date</param>
        /// <param name="endDate">Updated end date</param>
        /// <returns>Returns true if the dates to be updated on assessment are valid; else false</returns>
        public bool AreDatesToUpdateOnAssessmentValid(Guid assessmentId, DateTime startDate, DateTime endDate, ref int failureType)
        {
            string sql = @$"SELECT TOP 1 1 FROM {MalariaSchemas.ScopeDefinition}.Assessment A
                            WHERE CONVERT(DATE,A.EndDate) > CONVERT(DATE,@UpdatedStartDate) AND CONVERT(DATE,A.StartDate) < CONVERT(DATE,@UpdatedEndDate) AND A.Id = @AssessmentId";

            int? result = _dbManager.QuerySingleOrDefault<int?>(sql,
                                    new
                                    {
                                        AssessmentId = assessmentId,
                                        UpdatedStartDate = startDate,
                                        UpdatedEndDate = endDate
                                    });

            if (!result.HasValue)
            {
                failureType = 1;
                return false;
            }

            sql = @$"SELECT TOP 1 1 FROM {MalariaSchemas.ScopeDefinition}.[Assessment] A
                            WHERE A.CountryId = (SELECT AI.CountryId FROM {MalariaSchemas.ScopeDefinition}.Assessment AI
                            WHERE AI.Id = @AssessmentId)
                           AND YEAR(A.StartDate)=@StartDateYear AND YEAR(A.EndDate)=@EndDateYear AND A.Id != @AssessmentId";

            result = _dbManager.QuerySingleOrDefault<int?>(sql,
                                    new
                                    {
                                        StartDateYear = startDate.Year,
                                        EndDateYear = endDate.Year,
                                        AssessmentId = assessmentId
                                    });

            if (result.HasValue)
            {
                failureType = 2;
                return false;
            }
            return true;
        }

        /// <summary>
        /// Checks that user can create new assessment for country with in selected duration
        /// </summary>
        /// <param name="countryId">Assessment country id</param>
        /// <param name="startDate">Assessment start date</param>
        /// <param name="endDate">Assessment end date</param>
        /// <returns>Returns true if user allow to create new assessment else false</returns>
        public bool CanCreateAssessment(Guid countryId, DateTime startDate, DateTime endDate)
        {
            //TODO: Need to remove this commented code later
            //string sql = @$"SELECT TOP 1 1 FROM {MalariaSchemas.ScopeDefinition}.[Assessment] A
            //                WHERE A.CountryId = @CountryId
            //               AND YEAR(A.StartDate)=@StartDateYear AND YEAR(A.EndDate)=@EndDateYear";

            string sql = @$"SELECT TOP 1 1 FROM {MalariaSchemas.ScopeDefinition}.[Assessment] A
                            WHERE A.CountryId = @CountryId AND YEAR(A.EndDate)=@EndDateYear";

            int? assessmentAlreadyExists = _dbManager.QuerySingleOrDefault<int?>(sql,
                                    new
                                    {
                                        CountryId = countryId,
                                        StartDateYear = startDate.Year,
                                        EndDateYear = endDate.Year
                                    });

            return !assessmentAlreadyExists.HasValue;
        }

        /// <summary>
        /// Checks if indicators contain any of the optional indicator
        /// </summary>
        /// <param name="indicatorIds">Array of indicator ids</param>
        /// <param name="assessmentId">Assessment id of the assessment whose indicators are to be checked</param>
        /// <returns>True if list contains optional indicator else false</returns>
        public bool IsAnyOptionalIndicatorSelected(Guid[] indicatorIds, Guid assessmentId)
        {
            string sql = $@";WITH initialData AS
                            (
								SELECT ISM.IndicatorPriority,ISM.IndicatorId FROM .{MalariaSchemas.Internal}.IndicatorStrategyMapping ISM
								JOIN {MalariaSchemas.ScopeDefinition}.AssessmentStrategy AST on ISM.StrategyId = AST.StrategyId 
								WHERE AST.AssessmentId = @AssessmentId
								AND ISM.IndicatorId IN @IndicatorIds
                            ),
                            partitionedData AS
                            (
                                SELECT ROW_NUMBER() OVER(PARTITION BY IndicatorId ORDER BY IndicatorPriority ASC) AS RowNumber, * FROM initialData
                            )
                            SELECT (CASE WHEN EXISTS(SELECT * FROM partitionedData WHERE RowNumber = 1 AND IndicatorPriority = @IndicatorPriority) THEN 1 ELSE 0 END) AS RESULT";

            bool isOptionalIndicatorPresent = _dbManager.QuerySingleOrDefault<bool>(sql,
                                    new
                                    {
                                        IndicatorIds = indicatorIds,
                                        AssessmentId = assessmentId,
                                        IndicatorPriority = (int)Priority.Optional
                                    });

            return isOptionalIndicatorPresent;
        }
    }
}
