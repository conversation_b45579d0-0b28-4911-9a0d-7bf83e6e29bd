﻿using System;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    /// <summary>
    /// Class to contain all the common business rules
    /// </summary>
    public class CommonRuleChecker : ICom<PERSON><PERSON>uleChecker
    {
        public CommonRuleChecker()
        {
        }

        /// <summary>
        /// Checks if string is null or empty or white space
        /// </summary>
        /// <param name="input">Input string to check rule on</param>
        /// <returns>Returns true if rule is valid else false </returns>
        public bool StringIsNullOrEmpty(string input)
        {
            return string.IsNullOrWhiteSpace(input);
        }

        /// <summary>
        /// Checks if array of guid is null or empty
        /// </summary>
        /// <param name="input">Input array of guid to check rule on</param>
        /// <returns>Returns true if rule is valid else false </returns>
        public bool GuidArrayIsNullOrEmpty(Guid[] input)
        {
            return input == null || input.Length == 0;
        }

        /// <summary>
        /// Checks if guid is empty
        /// </summary>
        /// <param name="input">Input guid to check rule on</param>
        /// <returns>bool</returns>
        public bool GuidIsEmpty(Guid input)
        {
            return input == Guid.Empty;
        }

        /// <summary>
        /// Checks if date field contains minimum value of date
        /// </summary>
        /// <param name="input">Input date to check rule on</param>
        /// <returns>Returns true if rule is valid else false </returns>
        public bool IsDateNotValid(DateTime input)
        {
            return input == DateTime.MinValue;
        }

        /// <summary>
        /// Checks if array of any object is null or empty
        /// </summary>
        /// <param name="input">Input array of int to check rule on</param>
        /// <returns>Returns true if rule is valid else false </returns>
        public bool IntArrayIsNullOrEmpty(int[] input)
        {
            return input == null || input.Length == 0;
        }        
    }
}
