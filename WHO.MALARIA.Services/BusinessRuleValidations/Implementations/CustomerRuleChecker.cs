﻿using WHO.MALARIA.Database;
using WHO.MALARIA.Services.BusinessRuleValidations.Interaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    /// <summary>
    /// Contains methods to check rules related to customers
    /// </summary>
    public class CustomerRuleChecker : ICustomerRuleChecker
    {
        private readonly IDbManager _dbManager;

        public CustomerRuleChecker(IDbManager dbManager)
        {
            _dbManager = dbManager;
        }

        /// <summary>
        /// Checks if email passed is not already used
        /// </summary>
        /// <param name="email">Email</param>
        /// <returns>Returns true if email is unique; else false</returns>
        public bool IsCustomerEmailUnique(string email)
        {
            string sql = "SELECT TOP 1 1 " +
                "FROM [customers] AS C " +
                "WHERE [C].[email] = @Email";

            var customersNumber = _dbManager.QuerySingleOrDefault<int?>(sql,
                            new
                            {
                                Email = email
                            });

            return !customersNumber.HasValue;
        }
    }
}
