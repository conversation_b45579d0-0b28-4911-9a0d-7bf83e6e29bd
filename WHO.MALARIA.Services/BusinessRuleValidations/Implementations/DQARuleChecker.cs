﻿using System;
using System.Collections.Generic;
using System.Linq;

using WHO.MALARIA.Database;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    /// <summary>
    /// Contains methods related to DQA for business validations
    /// </summary>
    public class DQ<PERSON><PERSON><PERSON>hecker : ID<PERSON><PERSON><PERSON><PERSON>hecker
    {
        private readonly IUnitOfWork _unitOfWork;

        public DQARuleChecker(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Checks if service level record is finalized or not
        /// </summary>
        /// <param name="serviceLevelId">Service level id of the record which is to be checked for finalization</param>
        /// <returns>True if service level is finalized</returns>
        public bool IsServiceLevelFinalized(Guid serviceLevelId)
        {
            return _unitOfWork.ServiceLevelRepository.Queryable(x => x.Id == serviceLevelId).SingleOrDefault().IsFinalized;
        }

        /// <summary>
        /// Check if service level exists or not for given service level id
        /// </summary>
        /// <param name="serviceLevelId">Id of service level </param>
        /// <returns> True if service level exists; else false </returns>
        public bool DoesServiceLevelExist(Guid serviceLevelId)
        {
            return _unitOfWork.ServiceLevelRepository.Queryable(x => x.Id == serviceLevelId).Any();
        }

        /// <summary>
        /// Checks if multiple service level records are present for given assessment
        /// </summary>
        /// <param name="assessmentId">Id of the assessment</param>
        /// <returns>true if multiple service level records present else false</returns>
        public bool IsLastServiceLevelRecord(Guid assessmentId)
        {
            int totalRecords = _unitOfWork.ServiceLevelRepository.Queryable(sl => sl.AssessmentId == assessmentId).Count();

            return totalRecords > 1;
        }

        /// <summary>
        /// Check at-least one priority variable should be selected or not
        /// </summary>
        /// <param name="variableIds">List of Guid</param>
        /// <returns>TRUE if one priority vairble is selected else FALSE</returns>
        public bool AtLeastOnePriorityVariableShouldBeSelected(IEnumerable<Guid> variableIds)
        {
            IEnumerable<Guid> priorityVariableIds = _unitOfWork.DQARepository.GetPriorityVariableIds().Result;
            return variableIds.Any(x => priorityVariableIds.Any(z => z == x));
        }
    }
}
