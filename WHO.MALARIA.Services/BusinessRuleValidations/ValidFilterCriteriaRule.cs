﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Services.BusinessRuleValidations
{
    public class ValidFilterCriteriaRule : IBusinessRule
    {
        private readonly List<FilterCriteria> _filterCriterias;

        public ValidFilterCriteriaRule(List<FilterCriteria> filterCriterias)
        {
            _filterCriterias = filterCriterias;
        }

        public string Message => $"Filter criteria is not valid.";

        public bool IsBroken() => _filterCriterias != null  && _filterCriterias.Any() ? _filterCriterias
                 .Any(filterCriteria => CheckNullOrEmpty(filterCriteria.Field, filterCriteria.Operator)) : false;


        /// <summary>
        /// CheckNullOrEmpty for filter criteria
        /// </summary>
        /// <param name="field">An entity attribute(field)</param>
        /// <param name="sqlOperator">A Sql operator</param>
        /// <returns></returns>
        private bool CheckNullOrEmpty(string field, string sqlOperator)
        {
            return string.IsNullOrWhiteSpace(field) || string.IsNullOrWhiteSpace(sqlOperator);
        }

        public int StatusCode => (int)HttpStatusCode.PreconditionFailed;
    }
}
