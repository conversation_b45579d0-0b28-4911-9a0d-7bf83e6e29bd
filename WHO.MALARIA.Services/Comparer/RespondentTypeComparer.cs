﻿using System.Collections.Generic;
using WHO.MALARIA.Domain.Models.QuestionBank;

namespace WHO.MALARIA.Services.Comparer
{
    /// <summary>
    /// It contains an equality check method for assessment respondent type.
    /// </summary>
    public class RespondentTypeComparer : IEqualityComparer<AssessmentRespondentType>
    {
        /// <summary>
        /// Check respondent type is equal or not
        /// </summary>
        /// <param name="x">First AssessmentRespondentType class's object to compare respondent type</param>
        /// <param name="y">Second AssessmentRespondentType class's object to compare respondent type</param>
        /// <returns>True or False</returns>
        public bool Equals(AssessmentRespondentType x, AssessmentRespondentType y)
        {
            if (x.RespondentType == y.RespondentType)
                return true;

            return false;
        }

        /// <summary>
        /// Get respondent type's hash code
        /// </summary>
        /// <param name="obj">Object of AssessmentRespondentType</param>
        /// <returns>Hash code of respondent type"</returns>
        public int GetHashCode(AssessmentRespondentType obj)
        {
            return obj.RespondentType.GetHashCode();
        }
    }
}
