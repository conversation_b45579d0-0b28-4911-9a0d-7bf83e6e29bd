﻿using MediatR;

using System;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>  
    /// Finalize the observed data quality reason associated with the service level using FinalizeObservedDataQualityResultReasonCommand
    /// </summary>
    public class FinalizeObservedDataQualityResultReasonCommandHandler : RuleBase, IRequestHandler<FinalizeObservedDataQualityResultReasonCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;     

        public FinalizeObservedDataQualityResultReasonCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;    
        }

        /// <summary>
        /// Finalize observed data quality reason using FinalizeObservedDataQualityResultReasonCommand
        /// </summary>
        /// <param name="request"> Contains request parameters to finalize observed data quality reason </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if finalization of observed data quality results is successful </returns>
        public async Task<bool> Handle(FinalizeObservedDataQualityResultReasonCommand request, CancellationToken cancellationToken)
        {        
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ServiceLevelId, nameof(request.ServiceLevelId)));

            // Only those users who can upload files can finalize the observed data quality result reason.
            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUpdateObservedDataQualityReason));

            ServiceLevelVariableCompletness existingServiceLevelVariableCompletness = await _unitOfWork.VariableCompletenessResponseRepository.GetAsync(x => x.ServiceLevelId == request.ServiceLevelId);

            if (existingServiceLevelVariableCompletness == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            existingServiceLevelVariableCompletness.IsFinalized = true;

            _unitOfWork.VariableCompletenessResponseRepository.Update(existingServiceLevelVariableCompletness);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
