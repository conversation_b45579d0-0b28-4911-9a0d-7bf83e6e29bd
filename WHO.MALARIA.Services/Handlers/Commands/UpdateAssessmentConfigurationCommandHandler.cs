﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle creation of assessment using UpdateAssessmentCommand
    /// </summary>
    public class UpdateAssessmentConfigurationCommandHandler : RuleBase, IRequestHandler<UpdateAssessmentConfigurationCommand, Guid>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IDbManager _dbManager;

        public UpdateAssessmentConfigurationCommandHandler(
            IMediator mediator,
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService,
            IDbManager dbManager)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _translationService = translationService;
            _dbManager = dbManager;
        }

        /// <summary>
        /// Update assessment using UpdateAssessmentCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to assessment update operation </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> Id of Assessment </returns>
        public async Task<Guid> Handle(UpdateAssessmentConfigurationCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.Country, "Country"));
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.StartDate, "StartDate"));
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.EndDate, "EndDate"));
            CheckRule(new DateRangeShouldBeValidRule(_translationService, request.StartDate, request.EndDate));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanConfigure));
            CheckRule(new UpdatedAssessmentDatesShouldBeValidRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.StartDate, request.EndDate));

            Assessment assessment = await _unitOfWork.AssessmentRepository.Queryable(x => x.Id == request.AssessmentId)
                                          .Include(x => x.AssessmentUsers)
                                          .FirstOrDefaultAsync();

            if (assessment == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, "Assessment");
            }

            UserAssessmentPermissionDto userPermissions = await _dbManager.QuerySingleAsync<UserAssessmentPermissionDto>($"{MalariaSchemas.ScopeDefinition}.GetUserPermissionsOnAssessment", new { AssessmentId = request.AssessmentId, UserId = request.CurrentUserId }, null, null, CommandType.StoredProcedure);

            IEnumerable<Guid> editorsUserIds = new List<Guid>();
            IEnumerable<Guid> reviewersUserIds = new List<Guid>();
            IEnumerable<Guid> removedEditorsUserIds = new List<Guid>();
            IEnumerable<Guid> removedReviewersUserIds = new List<Guid>();

            // update assessment configuration if user has permission
            if (userPermissions.CanConfigure)
            {
                assessment.StartDate = request.StartDate;
                assessment.EndDate = request.EndDate;
                assessment.CountryId = userPermissions.CanChangeCountry ? request.Country : assessment.CountryId;

                // updated editors
                (IEnumerable<AssessmentUser>, IEnumerable<AssessmentUser>) updatedEditors = assessment.GetUpdatedAssessmentEditors(request.Editors);

                IEnumerable<AssessmentUser> deletedEditors = updatedEditors.Item1;
                _unitOfWork.AssessmentUserRepository.RemoveRange(deletedEditors);

                IEnumerable<AssessmentUser> addedEditors = updatedEditors.Item2;
                _unitOfWork.AssessmentUserRepository.AddRange(addedEditors);

                //editorUserIds will need to fetch the email addressess of the editors to send them email notification. Send only to the new editors.
                editorsUserIds = addedEditors.Select(x => x.UserId).Except(deletedEditors.Select(x => x.UserId));
                removedEditorsUserIds = deletedEditors.Select(x => x.UserId);

                // updated reviewers
                (IEnumerable<AssessmentUser>, IEnumerable<AssessmentUser>) updatedReviewers = assessment.GetUpdatedAssessmentReviewers(request.Reviewers);

                IEnumerable<AssessmentUser> deletedReviewers = updatedReviewers.Item1;
                _unitOfWork.AssessmentUserRepository.RemoveRange(deletedReviewers);

                IEnumerable<AssessmentUser> addedReviewers = updatedReviewers.Item2;
                _unitOfWork.AssessmentUserRepository.AddRange(addedReviewers);

                reviewersUserIds = addedReviewers.Select(x => x.UserId).Except(deletedReviewers.Select(x => x.UserId));
                removedReviewersUserIds = deletedReviewers.Select(x => x.UserId);
            }

            // update manager if user has permission
            if (userPermissions.CanChangeManager)
            {
                if (request.Manager != Guid.Empty && request.Manager.HasValue)
                {
                    AssessmentUser updateManager = assessment.GetUpdatedAssessmentManager(request.Manager.Value);
                    if (updateManager != null)
                    {
                        _unitOfWork.AssessmentUserRepository.Update(updateManager);
                    }
                    else
                    {
                        AssessmentUser assessmentManagerUser = assessment.GetAssessmentManager();

                        if (assessmentManagerUser == null)
                        {
                            AssessmentUser assessmentUser = new AssessmentUser(request.AssessmentId, request.Manager.Value, (int)AssessmentUserRole.Manager);

                            _unitOfWork.AssessmentUserRepository.Add(assessmentUser);
                        }
                    }
                }
                else
                {
                    AssessmentUser assessmentManagerUser = assessment.GetAssessmentManager();

                    if (assessmentManagerUser != null)
                    {
                        _unitOfWork.AssessmentUserRepository.Remove(assessmentManagerUser);
                    }
                }
            }

            _unitOfWork.AssessmentRepository.Update(assessment);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            if (editorsUserIds.Any())
            {
                IEnumerable<string> editorsEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(editorsUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(editorsEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Editor,
                                                                                  assessment.CountryId,
                                                                                  false));
            }

            if (reviewersUserIds.Any())
            {
                IEnumerable<string> reviewersEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(reviewersUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(reviewersEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Reviewer,
                                                                                  assessment.CountryId,
                                                                                  false));
            }

            if (removedEditorsUserIds.Any())
            {
                IEnumerable<string> removedEditorsEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(removedEditorsUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(removedEditorsEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Editor,
                                                                                  assessment.CountryId,
                                                                                  true));
            }

            if (removedReviewersUserIds.Any())
            {
                IEnumerable<string> removedReviewersEmailAddresses = await _unitOfWork.UserRepository.GetEmailAddresses(removedReviewersUserIds);
                await _mediator.Publish(new AssessmentAssignmentEmailNotification(removedReviewersEmailAddresses.ToArray(),
                                                                                  AssessmentUserRole.Reviewer,
                                                                                  assessment.CountryId,
                                                                                  true));
            }

            return assessment.Id;
        }
    }
}
