﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles process to show assessment on global dashboard command
    /// </summary>
    public class ShowAssessmentResultOnGlobalDashboardCommandHandler : RuleBase, IRequestHandler<ShowAssessmentResultOnGlobalDashboardCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ICacheDataService _cacheService;

        public ShowAssessmentResultOnGlobalDashboardCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker,
            ICacheDataService cacheService)
        {

            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
            _cacheService = cacheService;
        }

        /// <summary>
        /// Show assessment on global dashboard using ShowAssessmentResultOnGlobalDashboardCommand
        /// </summary>
        /// <param name="request">Instance of ShowAssessmentResultOnGlobalDashboardCommand : Input Properties related to show assessment result on global dashboard</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>return true on successful action</returns>
        public async Task<bool> Handle(ShowAssessmentResultOnGlobalDashboardCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.ShowResultsOnGlobalDashboard));

            Assessment assessment = await _unitOfWork.AssessmentRepository.GetAsync(x => x.Id == request.AssessmentId);

            if (assessment == null)
            {
                return false;
            }

            assessment.ShowResultsOnGlobalDashboard = request.ShowResultOnGlobalDashboard;

            _unitOfWork.AssessmentRepository.Update(assessment);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
            string currentCulture = _translationService.GetCurrentCulture();

            _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardIndicatorSummaryResponsesJson + currentCulture.ToLower());
            _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardObjectiveMapSummaryResponseJson + currentCulture.ToLower());
            _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardRegionalSummaryResponseJson + currentCulture.ToLower());
            _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardObjectiveMapSummaryResponseJson + currentCulture.ToLower());

            return true;
        }
    }
}
