﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle add user country access data using AddUserCountryAccessRequestCommand
    /// </summary>
    public class AddUserCountryAccessRequestCommandHandler : RuleBase, IRequestHandler<AddUserCountryAccessRequestCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;

        public AddUserCountryAccessRequestCommandHandler(IMediator mediator,
                                                         IUnitOfWork unitOfWork,
                                                         ICommonRuleChecker commonRuleChecker,
                                                         IUserRuleChecker userRuleChecker,
                                                         ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
        }

        /// <summary>
        /// Add user country access request details
        /// </summary>
        /// <param name="request">Instance of AddUserCountryAccessRequestCommand : Input Properties related to user country access request</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>return true on successful action</returns>
        public async Task<bool> Handle(AddUserCountryAccessRequestCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CurrentUserId, "UserId"));
            CheckRule(new CheckForDuplicateCountryRequestRule(_translationService, _userRuleChecker, request.CurrentUserId, request.CountryRequestedForIds));

            // Get current user
            User currentUser = await _unitOfWork.UserRepository.Queryable(x => x.Id == request.CurrentUserId)
                                                        .Include(x => x.UserCountryAccesses)
                                                        .SingleOrDefaultAsync();

            // Get super managers
            List<UserCountryAccess> superManagerEmailIdsOfCountries = await _unitOfWork.UserCountryAccessRepository.Queryable(u => u.Status == (int)UserCountryAccessRightsEnum.Accepted
                                                                                             && u.UserType == (int)UserRoleEnum.SuperManager)
                                                                                            .Include(uca => uca.User)
                                                                                            .ThenInclude(u => u.Identity).ToListAsync();
            // Get active countries
            IEnumerable<Country> countries = await _unitOfWork.CountryRepository.GetListAsync(u => u.IsActive);


            if (currentUser == null)
            {
                throw new RecordNotFoundException(request.CurrentUserId, "User");
            }

            // Check for new country request
            if (request.CountryRequestedForIds != null && request.CountryRequestedForIds.Any())
            {
                List<UserCountryAccess> userCountryAccesses = new List<UserCountryAccess>();

                request.CountryRequestedForIds.ToList().ForEach(countryId =>
                {
                    UserCountryAccess userCountryAccess = new UserCountryAccess(currentUser.Id, countryId, (int)UserRoleEnum.Viewer);

                    userCountryAccesses.Add(userCountryAccess);
                });

                _unitOfWork.UserCountryAccessRepository.AddRange(userCountryAccesses);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            // Each super manager in the requested countries received an email
            if (request.CountryRequestedForIds != null && request.CountryRequestedForIds.Any())
            {
                request.CountryRequestedForIds.ToList().ForEach(countryId =>
                {
                    Country country = countries.Where(c => c.Id == countryId).SingleOrDefault();

                    UserCountryAccess superManagerEmailIdOfCountry = superManagerEmailIdsOfCountries.Where(e => e.CountryId == countryId).SingleOrDefault();
                    if (superManagerEmailIdOfCountry != null)
                        _mediator.Publish(new UserCountryAccessRequestEmailNotification(superManagerEmailIdOfCountry.User.Identity.Email, country.Name));
                });
            }

            return true;
        }
    }
}
