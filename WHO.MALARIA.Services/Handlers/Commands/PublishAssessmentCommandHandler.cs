﻿using MediatR;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles publish assessment activity command
    /// </summary>
    public class PublishAssessmentCommandHandler : RuleBase, IRequestHandler<PublishAssessmentCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ICacheDataService _cacheService;
        private readonly IDbManager _dbManager;

        public PublishAssessmentCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker,
            ICacheDataService cacheService,
            IDbManager dbManager)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
            _cacheService = cacheService;
            _dbManager = dbManager;
        }

        /// <summary>
        /// Publishing an assessment
        /// </summary>
        /// <param name="request">Instance of PublishAssessmentCommand - Contains input parameters</param>
        /// <param name="cancellationToken">A cancellation token to cancel the operation</param>
        /// <returns>TRUE If it is successful Else FALSE</returns>
        public async Task<bool> Handle(PublishAssessmentCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanPublish));

            bool isEligibleForPublish = false;
            int shellTableDataCount = 0;

            //Assessment
            AssessmentApproachStrategyDto assessment = await _unitOfWork.AssessmentRepository.GetAssessmentApproachStrategy(request.AssessmentId);

            bool hasDeskReviewIndicatorsCompleted = false;

            //Desk review priority indicators status
            IEnumerable<int> deskReviewIndicatorsStatuses = await _unitOfWork.AssessmentDRResponseRepository.GetResponseStatusesAsync(request.AssessmentId, assessment.StrategyId);

            if (deskReviewIndicatorsStatuses.All(d => d == (int)DeskReviewAssessmentResponseStatus.Completed))
            {
                hasDeskReviewIndicatorsCompleted = true;
            }

            if (hasDeskReviewIndicatorsCompleted == false)
            {
                return isEligibleForPublish;
            }

            //DQA
            Domain.Models.DQA.DeskLevel.DeskLevel deskLevel = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

            //Elimination national summary
            Domain.Models.DQA.Elimination.Summary eliminationSummary = new Domain.Models.DQA.Elimination.Summary();

            //Desk level national summary
            IEnumerable<Domain.Models.DQA.DeskLevel.Summary> deskLevelSummary = null;

            //Service Level DQA
            Domain.Models.DQA.ServiceLevel.ServiceLevel serviceLevelSummary = new Domain.Models.DQA.ServiceLevel.ServiceLevel();

            //Score card
            ScoreCard scoreCard = await _unitOfWork.ScoreCardRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

            //Question bank
            IEnumerable<Domain.Models.QuestionBank.AssessmentRespondentType> assessmentRespondentTypes = null;

            switch (assessment.Approach)
            {
                case (int)AssessmentApproach.Rapid:

                    //Elimination national summary 
                    eliminationSummary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //Desk level national summary
                    deskLevelSummary = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId && x.Type == (byte)DQADLSummaryResultType.NationalLevelTarget);

                    //Service Level DQA
                    serviceLevelSummary = await _unitOfWork.ServiceLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //For elimination strategy
                    if (assessment.StrategyId == StrategySeedingMetadata.ELIMINATION_ID)
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (eliminationSummary != null && eliminationSummary.IsFinalized)
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    }
                    //For burden reduction strategy
                    else if (assessment.StrategyId == StrategySeedingMetadata.BURDEN_REDUCTION_ID)
                    {

                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (deskLevelSummary != null && deskLevelSummary.Any())
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    }
                    else
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (deskLevel != null && deskLevel.IsFinalized) &&
                            (eliminationSummary != null && eliminationSummary.IsFinalized) &&
                            (deskLevelSummary != null && deskLevelSummary.Any()))
                        {
                            isEligibleForPublish = true;
                        }
                    }

                    break;

                case (int)AssessmentApproach.Tailored:

                    //Desk level national summary
                    deskLevelSummary = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId && x.Type == (byte)DQADLSummaryResultType.NationalLevelTarget);

                    //Elimination national summary 
                    eliminationSummary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //Service Level DQA
                    serviceLevelSummary = await _unitOfWork.ServiceLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //Question bank
                    assessmentRespondentTypes = await _unitOfWork.AssessmentRespondentTypeRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId);

                    foreach (Domain.Models.QuestionBank.AssessmentRespondentType respondentType in assessmentRespondentTypes)
                    {
                        //Shell Table
                        bool doesShellTableRecordExist = await _unitOfWork.ShellTableRepository.DoesShellTableDataExistAsync(respondentType.Id);

                        if (doesShellTableRecordExist)
                        {
                            shellTableDataCount++;
                        }
                    }
                    //For elimination strategy
                    if (assessment.StrategyId == StrategySeedingMetadata.ELIMINATION_ID)
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (scoreCard != null && scoreCard.IsFinalized) &&
                            (eliminationSummary != null && eliminationSummary.IsFinalized) &&
                             (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount))
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    } //For burden reduction strategy
                    else if (assessment.StrategyId == StrategySeedingMetadata.BURDEN_REDUCTION_ID)
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                              (deskLevel != null && deskLevel.IsFinalized) &&
                              (scoreCard != null && scoreCard.IsFinalized) &&
                            (deskLevelSummary != null && deskLevelSummary.Any()) &&
                            (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount))
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    }
                    else
                        if (
                        (hasDeskReviewIndicatorsCompleted) &&
                        (deskLevel != null && deskLevel.IsFinalized) &&
                        (scoreCard != null && scoreCard.IsFinalized) &&
                        (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount)) &&
                        (eliminationSummary != null && eliminationSummary.IsFinalized) &&
                         (deskLevelSummary != null && deskLevelSummary.Any())
                        )
                    {
                        isEligibleForPublish = true;
                    }

                    break;

                case (int)AssessmentApproach.Comprehensive:

                    //Elimination national summary 
                    eliminationSummary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //Service Level DQA
                    serviceLevelSummary = await _unitOfWork.ServiceLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

                    //Question bank
                    assessmentRespondentTypes = await _unitOfWork.AssessmentRespondentTypeRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId);

                    //Desk level national summary
                    deskLevelSummary = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId && x.Type == (byte)DQADLSummaryResultType.NationalLevelTarget);

                    foreach (Domain.Models.QuestionBank.AssessmentRespondentType respondentType in assessmentRespondentTypes)
                    {
                        //Shell Table
                        bool doesShellTableRecordsExist = await _unitOfWork.ShellTableRepository.DoesShellTableDataExistAsync(respondentType.Id);

                        if (doesShellTableRecordsExist)
                        {
                            shellTableDataCount++;
                        }
                    }
                    //For elimination strategy
                    if (assessment.StrategyId == StrategySeedingMetadata.ELIMINATION_ID)
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (scoreCard != null && scoreCard.IsFinalized) &&
                            (eliminationSummary != null && eliminationSummary.IsFinalized) &&
                             (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount))
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    } //For burden reduction strategy
                    else if (assessment.StrategyId == StrategySeedingMetadata.BURDEN_REDUCTION_ID)
                    {
                        if (
                            hasDeskReviewIndicatorsCompleted &&
                            (scoreCard != null && scoreCard.IsFinalized) &&
                            (deskLevel != null && deskLevel.IsFinalized) &&
                            (deskLevelSummary != null && deskLevelSummary.Any() &&
                            (serviceLevelSummary != null && serviceLevelSummary.IsFinalized)) &&
                             (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount))
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    }
                    else
                    {
                        if (
                            (hasDeskReviewIndicatorsCompleted) &&
                            (deskLevel != null && deskLevel.IsFinalized) &&
                            (serviceLevelSummary != null && serviceLevelSummary.IsFinalized) &&
                            (scoreCard != null && scoreCard.IsFinalized) &&
                            (eliminationSummary != null && eliminationSummary.IsFinalized) &&
                            (assessmentRespondentTypes != null && assessmentRespondentTypes.Any() && (assessmentRespondentTypes.Count() == shellTableDataCount))
                            )
                        {
                            isEligibleForPublish = true;
                        }
                    }

                    break;
            }

            if (isEligibleForPublish)
            {
                Domain.Models.AssessmentStatus publishedAssessment = await _unitOfWork.AssessmentStatusRepository.GetAsync(x => x.AssessmentId == request.AssessmentId && x.Status == (int)Domain.Enum.AssessmentStatus.Published);

                if (publishedAssessment == null)
                {
                    Domain.Models.AssessmentStatus assessmentStatus = new Domain.Models.AssessmentStatus(request.AssessmentId, (int)Domain.Enum.AssessmentStatus.Published);

                    _unitOfWork.AssessmentStatusRepository.Add(assessmentStatus);

                    await _dbManager.ExecuteAsync($"{MalariaSchemas.Assessment}.SaveAssessmentHealthFacilityMapping",
                                                new
                                                {
                                                    AssessmentId = request.AssessmentId,
                                                    CurrentUserId = request.CurrentUserId
                                                }, null, null, CommandType.StoredProcedure);

                    if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                    {
                        throw new ApplicationException();
                    }
                }
                isEligibleForPublish = true;
            }

            if (isEligibleForPublish)
            {
                string currentCulture = _translationService.GetCurrentCulture();

                _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardIndicatorSummaryResponsesJson + currentCulture.ToLower());
                _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardObjectiveMapSummaryResponseJson + currentCulture.ToLower());
                _cacheService.RemoveDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardRegionalSummaryResponseJson + currentCulture.ToLower());
            }

            return isEligibleForPublish;
        }
    }
}
