﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.DQA;
using WHO.MALARIA.Services.Rules.Shared;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle save service level DQA data using CreateServiceLevelDQACommand
    /// </summary>
    public class CreateServiceLevelDQACommandHandler : RuleBase, IRequestHandler<CreateServiceLevelDQACommand, Guid>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public CreateServiceLevelDQACommandHandler(IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Save service level data using CreateServiceLevelDQACommand
        /// </summary>
        /// <param name="request"> Command includes properties related to saving service level data </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> Id of service level record </returns>
        public async Task<Guid> Handle(CreateServiceLevelDQACommand request, CancellationToken cancellationToken)
        { 
            // Check Business Rules
            CheckRule(new IntArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.RegisterTypes.Select(x => (int)x).ToArray(), "Register type"));
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.ValidationPeriodStartDate, "ValidationPeriodStartDate"));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanCreateOrUpdateServiceLevel));
            CheckRule(new IsValidMonthRule(_translationService, request.From.Month, "From"));
            CheckRule(new IsValidMonthRule(_translationService, request.To.Month, "To"));
            CheckRule(new FromAndToDateShouldNotBeSameRule(_translationService, request.From, request.To));
            CheckRule(new ValidationPeriodStartDateShouldBeValidRule(_translationService, request.From, request.To, request.ValidationPeriodStartDate));
            CheckRule(new RegisterTypeMustBeValidRule(_translationService, request.RegisterTypes));
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.VariableIds, "Variable Ids"));

            Guid serviceLevelId = Guid.NewGuid();
            SaveServiceLevel(request, serviceLevelId);

            try
            {
                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }
            catch (DbUpdateException ex)
            {

                if (ex.InnerException is SqlException sqlExp)
                {
                    if (sqlExp.Number == SqlExceptionErrorCodes.ForeignKeyConflict) // Constant - 547  
                    {
                        throw new ApplicationException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidVariableIds));
                    }
                }
                throw;
            }
            return serviceLevelId;
        }

        /// <summary>
        /// Saves service level data.
        /// </summary>
        /// <param name="request">Object of CreateServiceLevelDQACommand object with parameters which are saved </param>
        /// <param name="serviceLevelId">Service level id against of which data is saved</param>
        private void SaveServiceLevel(CreateServiceLevelDQACommand request, Guid serviceLevelId)
        {

            ServiceLevel serviceLevel = new ServiceLevel(serviceLevelId, request.From, request.To, request.ValidationPeriodStartDate, Convert.ToInt16(request.From.Year), request.AssessmentId);

            foreach (Guid variableId in request.VariableIds)
            {
                serviceLevel.AddVariables(serviceLevelId, variableId);
            }

            //add system defined variable
            serviceLevel.AddVariables(serviceLevelId, DQAVariableSeedingMetadata.OverallConcordance_ID); //Guid for system defined DQA variable 

            foreach (int registerType in request.RegisterTypes)
            {
                serviceLevel.AddRegisterTypes(serviceLevelId, Convert.ToByte(registerType));
            }

            _unitOfWork.ServiceLevelRepository.Add(serviceLevel);
        }
    }
}
