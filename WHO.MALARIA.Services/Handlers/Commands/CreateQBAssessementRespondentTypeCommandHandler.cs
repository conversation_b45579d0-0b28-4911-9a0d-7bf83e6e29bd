﻿using MediatR;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.QuestionBank;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Comparer;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.QuestionBank;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Adds new respondent types and removes old respondent types for assessment using CreateQBAssessementRespondentTypeCommand
    /// </summary>
    public class CreateQBAssessementRespondentTypeCommandHandler : RuleBase, IRequestHandler<CreateQBAssessementRespondentTypeCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker; 

        public CreateQBAssessementRespondentTypeCommandHandler(IUnitOfWork unitOfWork, ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;        
        }

        /// <summary>
        /// Create respondent types data for assessment using CreateQBAssessementRespondentTypeCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to saving respondent types data</param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if save data of respondent types is successful </returns>
        public async Task<bool> Handle(CreateQBAssessementRespondentTypeCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new IntArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.RespondentTypes.Select(x => (int)x).ToArray(), "Respondent type"));
            CheckRule(new RespondentTypeMustBeValidRule(_translationService, request.RespondentTypes));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanEditQuestionBankRespondentType));

            await SaveRespondentTypes(request);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
            return true;
        }

        /// <summary>
        /// Saves respondent types.
        /// </summary>
        /// <param name="request">Object of CreateQBAssessementRespondentTypeCommand with parameters which are saved </param>    
        private async Task SaveRespondentTypes(CreateQBAssessementRespondentTypeCommand request)
        {
            IEnumerable<AssessmentRespondentType> existingAssessmentRespondentTypes = await _unitOfWork.AssessmentRespondentTypeRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId);

            List<AssessmentRespondentType> newAssessmentRespondentTypes = new List<AssessmentRespondentType>();

            foreach (byte respondentType in request.RespondentTypes)
            {
                newAssessmentRespondentTypes.Add(new AssessmentRespondentType(Guid.NewGuid(), request.AssessmentId, respondentType, false, request.HasSelfAssessmentQuestions));
            }

            IEnumerable<AssessmentRespondentType> existingAssessmentRespondentTypesToRemove = existingAssessmentRespondentTypes.Except(newAssessmentRespondentTypes, new RespondentTypeComparer());

            _unitOfWork.AssessmentRespondentTypeRepository.RemoveRange(existingAssessmentRespondentTypesToRemove);


            IEnumerable<AssessmentRespondentType> newAssessmentRespondentTypesToAdd = newAssessmentRespondentTypes.Except(existingAssessmentRespondentTypes, new RespondentTypeComparer());
            
            if (existingAssessmentRespondentTypes.Any())
            {
                foreach (AssessmentRespondentType existingRecord in existingAssessmentRespondentTypes.Except(existingAssessmentRespondentTypesToRemove))
                {
                    existingRecord.HasSelfAssessmentQuestions = request.HasSelfAssessmentQuestions;
                    _unitOfWork.AssessmentRespondentTypeRepository.Update(existingRecord);
                }
            }
            _unitOfWork.AssessmentRespondentTypeRepository.AddRange(newAssessmentRespondentTypesToAdd);
        }
    }
}
