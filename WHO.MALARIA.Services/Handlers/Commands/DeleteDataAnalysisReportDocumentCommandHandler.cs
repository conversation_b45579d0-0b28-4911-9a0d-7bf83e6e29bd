﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the incoming request to delete report
    /// </summary>
    public class DeleteDataAnalysisReportDocumentCommandHandler : RuleBase, IRequestHandler<DeleteDataAnalysisReportDocumentCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public DeleteDataAnalysisReportDocumentCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Deletes Data analysis report document using DeleteDataAnalysisReportDocumentCommand.
        /// </summary>
        /// <param name="request">Instance of DeleteDataAnalysisReportDocumentCommand - Contains input parameters</param>
        /// <param name="cancellationToken">Notifies the cancellation request</param>
        /// <returns>True if data deleted successfully</returns>
        public async Task<bool> Handle(DeleteDataAnalysisReportDocumentCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ReportId, "ReportId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.ShowResultsOnGlobalDashboard));

            DataAnalysisReport report = await _unitOfWork.DataAnalysisReportRepository.GetAsync(x => x.Id == request.ReportId && x.AssessmentId == request.AssessmentId); ;

            if (report != null)
            {
                _unitOfWork.DataAnalysisReportRepository.Remove(report);

                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }

            return true;
        }
    }
}
