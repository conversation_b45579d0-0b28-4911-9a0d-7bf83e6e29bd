﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Exceptions;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.QuestionBank;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Exceptions;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.QuestionBank;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the incoming request to Add/Update health facility data
    /// </summary>
    public class CreateOrUpdateHealthFacilityDataCommandHandler : RuleBase, IRequestHandler<UploadHealthFacilitiesCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IQuestionBank _questionBank;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ILogger<CreateOrUpdateHealthFacilityDataCommandHandler> _logger;

        public CreateOrUpdateHealthFacilityDataCommandHandler(IUnitOfWork unitOfWork,
                                                              IQuestionBank questionBank,
                                                              ITranslationService translationService,
                                                              IUserRuleChecker userRuleChecker,
                                                              ICommonRuleChecker commonRuleChecker,
                                                              ILogger<CreateOrUpdateHealthFacilityDataCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _questionBank = questionBank;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _logger = logger;
        }

        /// <summary>
        /// Performs validations on the request, adds the new health facility data if it does not exist else updates it
        /// </summary>
        /// <param name="request">Contains health facility data to add/update</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>TRUE if health facility gets saved/updated else throw an error if any validation fails</returns>
        public async Task<bool> Handle(UploadHealthFacilitiesCommand request, CancellationToken cancellationToken)
        {
            IdAndNameDto superManagerCountry = _unitOfWork.UserCountryAccessRepository.GetCountryOfSuperManager(request.CurrentUserId, request.CountryId);

            CheckRule(new UserShouldBeSuperManagerRule(_translationService, _userRuleChecker, request.CurrentUserId));

            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, superManagerCountry.Id, nameof(superManagerCountry.Id)));

            CheckRule(new IsFileNullCheckRule(_translationService, request.File, Constants.Exception.SelectFile));

            CheckRule(new FileSizeCheckRule(_translationService, request.File.Length, Constants.Common.ExcelFileSizeBytes, Constants.Exception.InvalidFileSize));

            string fileExtension = Path.GetExtension(request.File.FileName);

            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidExcelExtensions));

            CheckRule(new FileSizeCheckRule(_translationService, request.File.Length, Constants.Common.ExcelFileSizeBytes, Constants.Exception.InvalidFileSize));

            List<HealthFacilityDto> healthFacilities = new List<HealthFacilityDto>();

            try
            {
                healthFacilities = _questionBank.ReadHealthFacilitySheet(request.File);
            }
            catch (WrongSheetNameException ex)
            {
                _logger.LogError(ex, ex.Message);

                CheckRule(new IsHealthFacilityFileContainsValidSheetRule(_translationService, Constants.Exception.InvalidHealthFacilitySheet));
            }

            List<HealthFacilityCollectionDto> healthFacilityCollection = new List<HealthFacilityCollectionDto>();

            if (healthFacilities != null && healthFacilities.Any())
            {

                CheckRule(new IsValueGreaterThanRule(_translationService, healthFacilities.Where(x => x.CountryName != string.Empty).Select(c => c.CountryName).Distinct().Count(), 1, Constants.Exception.MultipleCountriesNotAllowedInHealthFacilitySheet));

                CheckRule(new IsSuperManagerCountrySameAsHealthFacilitySheet(_translationService, superManagerCountry.Name, healthFacilities.Where(x => x.CountryName != string.Empty).Select(c => c.CountryName).Distinct().First()));

                List<HealthFacility> healthFacilityEntities = new List<HealthFacility>();
            }
            else
            {
                throw new InvalidHealthFacilityException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacilitySheetData));
            }

            string districtCode = string.Empty;
            string code = string.Empty;
            string isoCountrycode = string.Empty;
            int latestDistrictCodeNumber = 0;
            int latestHealthFacilityCodeNumber = 0;

            if (healthFacilities != null && healthFacilities.Any())
            {
                if (healthFacilities.Any(d => string.IsNullOrWhiteSpace(d.DistrictCode) || string.IsNullOrWhiteSpace(d.Code)))
                {
                    Country country = await _unitOfWork.CountryRepository.GetAsync(c => c.Id == superManagerCountry.Id);
                    isoCountrycode = country.ISO;
                }

                if (healthFacilities.Any(d => string.IsNullOrWhiteSpace(d.DistrictCode)))
                    latestDistrictCodeNumber = await _unitOfWork.HealthFacilityRepository.GetMaxDistrictCode(superManagerCountry.Id);

                if (healthFacilities.Any(d => string.IsNullOrWhiteSpace(d.Code)))
                    latestHealthFacilityCodeNumber = await _unitOfWork.HealthFacilityRepository.GetMaxCode(superManagerCountry.Id);

                healthFacilities.ForEach(healthFacilityDto =>
                    {
                        byte healthFacilityTypeValue = 0;

                        if (Enum.TryParse<HealthFacilityType>(healthFacilityDto.Type, out HealthFacilityType healthFacilityType))
                        {
                            healthFacilityTypeValue = Convert.ToByte(healthFacilityType);
                        }
                        else
                        {
                            throw new InvalidHealthFacilityException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacilitySheetData));
                        }

                        if (string.IsNullOrEmpty(healthFacilityDto.Name) || string.IsNullOrEmpty(healthFacilityDto.CountryName)
                        || string.IsNullOrEmpty(healthFacilityDto.DistrictName) || string.IsNullOrEmpty(healthFacilityDto.Region)
                        || string.IsNullOrEmpty(healthFacilityDto.Type))
                        {

                            throw new InvalidHealthFacilityException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacilitySheetData));
                        }
                        if (string.IsNullOrWhiteSpace(healthFacilityDto.DistrictCode))
                        {
                            //if DistrictCode is null then DistrictCode will be ISOCountryCode_DIS_index
                            districtCode = $"{isoCountrycode}_DIS_{(latestDistrictCodeNumber + 1)}";

                            latestDistrictCodeNumber = (latestDistrictCodeNumber + 1);
                        }
                        else
                            districtCode = healthFacilityDto.DistrictCode;

                        if (string.IsNullOrEmpty(healthFacilityDto.Code))
                        {
                            //if Code is null then Code will be ISOCountryCode_HF_index
                            code = $"{isoCountrycode}_HF_{(latestHealthFacilityCodeNumber + 1)}";

                            latestHealthFacilityCodeNumber = (latestHealthFacilityCodeNumber) + 1;
                        }
                        else
                            code = healthFacilityDto.Code;

                        HealthFacilityCollectionDto healthFacility = new HealthFacilityCollectionDto
                        {
                            Code = code,
                            Name = healthFacilityDto.Name,
                            Type = healthFacilityTypeValue,
                            Region = healthFacilityDto.Region,
                            DistrictCode = districtCode,
                            District = healthFacilityDto.DistrictName
                        };

                        healthFacilityCollection.Add(healthFacility);
                    });

                CheckRule(new IsHealthFacilitySheetContainsDuplicateRowValuesRule<string>(_translationService, healthFacilityCollection.ToList()
                    , Constants.Exception.SameValuesPresentForMultipleRowsInHealthFacilitySheet));

                CheckRule(new IsHealthFacilitySheetContainsDuplicateCodeRule<string>(_translationService, healthFacilityCollection.ToList()
                    , Constants.Exception.MultipleHealthFacilitiesHaveSameCode));

                DataTable dtHealthFacilities = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityCollectionDto), healthFacilityCollection, "HealthFacility", null);

                try
                {
                    await _unitOfWork.HealthFacilityRepository.SaveHealthFacilities(superManagerCountry.Id, request.CurrentUserId, dtHealthFacilities, $"{request.File.FileName} ({CommandGenerator.CalcuateFileSize(request.File.Length)})");
                }
                catch (SqlException ex)
                {
                    if (ex.Number == Constants.SqlExceptionErrorCodes.UniqueKeyViolation)
                    {
                        // throw user friendly database exception                       
                        if (ex.Message.ToLower().Contains("code"))
                        {
                            CheckRule(new UniqueKeyViolationRule(_translationService, Constants.Exception.MultipleHealthFacilitiesHaveSameCode));
                        }
                    }
                    throw;
                }

                return true;
            }
            else
            {
                return false;
            }
        }
    }
}