﻿using MediatR;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle update of assessment strategies using CreateOrUpdateAssessmentStrategiesCommand
    /// </summary>
    public class CreateOrUpdateAssessmentStrategiesCommandHandler : RuleBase, IRequestHandler<CreateOrUpdateAssessmentStrategiesCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IDbManager _dbManager;

        public CreateOrUpdateAssessmentStrategiesCommandHandler(
            IMediator mediator,
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService,
            IDbManager dbManager)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _dbManager = dbManager;
            _translationService = translationService;
        }

        /// <summary>
        /// Adds/Updates/Deletes assessment strategies based on the list of strategies passed
        /// </summary>
        /// <param name="request">Command includes properties related to assessment strategies creation operation</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if records are created</returns>
        public async Task<bool> Handle(CreateOrUpdateAssessmentStrategiesCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.StrategyIds, "StrategyIds"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUpdateStrategies));

            DataTable assessmentStrategiesTable = DatabaseHelper.BuildUniqueidentifierCollectionDataTable(request.StrategyIds);

            await _dbManager.ExecuteAsync($"{MalariaSchemas.ScopeDefinition}.SaveAssessmentStrategies",
             new
             {
                 AssessmentId = request.AssessmentId,
                 StrategyIds = assessmentStrategiesTable,
                 CurrentUserId = request.CurrentUserId
             }, null, null, CommandType.StoredProcedure);

            return true;
        }
    }
}
