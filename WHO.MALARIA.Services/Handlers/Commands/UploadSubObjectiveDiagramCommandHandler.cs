﻿using MediatR;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DeskReview;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles upload sub objective diagram command request to upload the diagram
    /// </summary>
    public class UploadSubObjectiveDiagramCommandHandler : RuleBase, IRequestHandler<UploadSubObjectiveDiagramCommand, string[]>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public UploadSubObjectiveDiagramCommandHandler(IUnitOfWork unitOfWork, ITranslationService translationService, ICommonRuleChecker commonRuleChecker,  IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;           
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request and add/update the sub objective diagram
        /// </summary>
        /// <param name="request">Contains input fields of the upload sub objective diagram comamnd class</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>Array of uploaded diagram files id</returns>
        public async Task<string[]> Handle(UploadSubObjectiveDiagramCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(UploadObjectiveDiagramCommand.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.StrategyId, nameof(UploadObjectiveDiagramCommand.StrategyId)));
            CheckRule(new IsEnumValueValidRule(_translationService, typeof(DeskReviewAssessmentResponseStatus), Convert.ToInt32(request.Status), nameof(UploadObjectiveDiagramCommand.Status)));
            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUploadDiagram));

            if (request.DiagramFiles == null || request.DiagramFiles.All(x => x.File == null))
            {
                CheckRule(new IsFileNullCheckRule(_translationService, null, Constants.Exception.InvalidDiagramFile));
            }

            foreach (DiagramFile detail in request.DiagramFiles.Where(x => x.File != null))
            {
                CheckRule(new IsFileExtensionValidRule(_translationService, Path.GetExtension(detail.File.FileName), Constants.Common.AllowedDiagramFileTypes));
                CheckRule(new FileSizeCheckRule(_translationService, detail.File.Length, Constants.Common.DiagramMaxFileSize, Constants.Exception.FileSizeGreaterThan10MB));
            }

            Guid assessmentStrategyId = _unitOfWork.AssessmentRepository.GetAssessmentStrategyId(request.AssessmentId, request.StrategyId);

            List<SubObjectiveDiagram> existingSubObjectiveDiagrams = _unitOfWork.SubObjectiveDiagramRepository.Queryable(x => x.AssessmentStrategyId == assessmentStrategyId)
                                                                                                              .ToList();

            string[] diagramFileIds = new string[2];

            //If sub objective doesn't have existing diagrams the upload new else update the existings.
            if (!existingSubObjectiveDiagrams.Any())
            {
                foreach (DiagramFile diagramFile in request.DiagramFiles)
                {
                    if (diagramFile.File == null)
                    {
                        continue;
                    }

                    SubObjectiveDiagram newSubObjectiveDiagram = new SubObjectiveDiagram(assessmentStrategyId,
                                                                                         SubObjectiveSeedingMetadata.SO_2_2_ID,
                                                                                         request.Status,
                                                                                         diagramFile.File.GetBytesAsync().Result,
                                                                                         Path.GetExtension(diagramFile.File.FileName),
                                                                                         diagramFile.File.FileName,
                                                                                         diagramFile.Order);

                    _unitOfWork.SubObjectiveDiagramRepository.Add(newSubObjectiveDiagram);
                    diagramFileIds[diagramFile.Order - 1] = newSubObjectiveDiagram.Id.ToString();
                }
            }
            else
            {
                byte currentStatus = existingSubObjectiveDiagrams.Select(x => x.Status).First();

                //Once status is set to completed it cannot be set 'InProgress' even though request contains it.
                byte updatedStatus = currentStatus == (int)DeskReviewAssessmentResponseStatus.Completed ? currentStatus : request.Status;

                foreach (DiagramFile diagramFile in request.DiagramFiles)
                {
                    SubObjectiveDiagram subObjectiveDiagram = existingSubObjectiveDiagrams.SingleOrDefault(x => x.Id == diagramFile.Id);
                    byte[] content = diagramFile.File?.GetBytesAsync().Result;
                    string extension = Path.GetExtension(diagramFile.File?.FileName);

                    if (subObjectiveDiagram == null && diagramFile.File != null)
                    {
                        SubObjectiveDiagram newSubObjectiveDiagram = new SubObjectiveDiagram(assessmentStrategyId,
                                                                                             SubObjectiveSeedingMetadata.SO_2_2_ID,
                                                                                             updatedStatus,
                                                                                             content,
                                                                                             extension,
                                                                                             diagramFile.File.FileName,
                                                                                             diagramFile.Order);

                        _unitOfWork.SubObjectiveDiagramRepository.Add(newSubObjectiveDiagram);
                        diagramFileIds[diagramFile.Order - 1] = newSubObjectiveDiagram.Id.ToString();
                    }
                    else if (diagramFile.File != null)
                    {
                        subObjectiveDiagram.Update(updatedStatus,
                                                   content,
                                                   extension,
                                                   diagramFile.File.FileName);

                        _unitOfWork.SubObjectiveDiagramRepository.Update(subObjectiveDiagram);
                        diagramFileIds[diagramFile.Order - 1] = subObjectiveDiagram.Id.ToString();
                    }
                }
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return diagramFileIds;
        }
    }
}
