﻿using MediatR;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.QuestionBank;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.QuestionBank;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>  
    /// Command handler to handle Finalize questionnaire associated with the assessment using FinalizeQuestionnaireCommand
    /// </summary>
    public class FinalizeQuestionnaireCommandHandler : RuleBase, IRequestHandler<FinalizeQuestionnaireCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeQuestionnaireCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// FinalizeQuestionnaire handler to handle the incoming request to update the status of questionarie with a finalised status using FinalizeQuestionnaireCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to questionarie finalize operation </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if finalization of questionarie is successful </returns>
        public async Task<bool> Handle(FinalizeQuestionnaireCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new RespondentTypeMustBeValidRule(_translationService, new List<byte> { request.RespondentType }));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalizeQuestionBank));

            AssessmentRespondentType existingAssessmentRespondentTypeRecord = await _unitOfWork.AssessmentRespondentTypeRepository.GetAsync(x => x.AssessmentId == request.AssessmentId && x.RespondentType == request.RespondentType);

            if (existingAssessmentRespondentTypeRecord == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            existingAssessmentRespondentTypeRecord.IsFinalized = true;

            existingAssessmentRespondentTypeRecord.ShellTableFileVersion = Convert.ToInt16(existingAssessmentRespondentTypeRecord.ShellTableFileVersion + 1);

            _unitOfWork.AssessmentRespondentTypeRepository.Update(existingAssessmentRespondentTypeRecord);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
