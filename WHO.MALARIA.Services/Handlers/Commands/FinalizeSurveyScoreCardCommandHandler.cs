﻿using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle finalization of score card for survey using FinalizeSurveyScoreCardCommand
    /// </summary>
    public class FinalizeSurveyScoreCardCommandHandler : RuleBase, IRequestHandler<FinalizeSurveyScoreCardCommand, bool>
    {    
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeSurveyScoreCardCommandHandler(            
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {          
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Finalize survey indicators in score card using FinalizeSurveyScoreCardCommand
        /// </summary>
        /// <param name="request"> Command includes input parameters</param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> return true if saved successfully </returns>
        public async Task<bool> Handle(FinalizeSurveyScoreCardCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalizeScoreCard));

            IEnumerable<ScoreCard> existingScoreCards = await _unitOfWork.ScoreCardRepository.GetListAsync(x => x.AssessmentId == request.AssessmentId);

            if (!existingScoreCards.Any())
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            existingScoreCards.ToList().ForEach(existingScoreCard =>
            {
                existingScoreCard.IsFinalized = true;
            });

            _unitOfWork.ScoreCardRepository.UpdateRange(existingScoreCards);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}