﻿using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles SetMetNotMetForSurveyIndicatorsCommand request to set met not met status of survey indicators
    /// </summary>
    public class SetMetNotMetForSurveyIndicatorsCommandHandler : RuleBase, IRequestHandler<SetMetNotMetForSurveyIndicatorsCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public SetMetNotMetForSurveyIndicatorsCommandHandler(IUnitOfWork unitOfWork, ITranslationService translationService, ICommonRuleChecker commonRuleChecker, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request, creates entities that are going to save in the DB.
        /// </summary>
        /// <param name="request">Instance of SetMetNotMetForSurveyIndicatorsCommand - Contains input parameters</param>
        /// <param name="cancellationToken">A cancellation token to cancel the operation</param>
        /// <returns>TRUE If it is successfull Else FALSE</returns>
        public async Task<bool> Handle(SetMetNotMetForSurveyIndicatorsCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CurrentUserId, nameof(request.CurrentUserId)));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanSaveScoreCard));

            List<ScoreCard> scoreCards = new List<ScoreCard>();

            IEnumerable<ScoreCard> existingScoreCardDetails = await _unitOfWork.ScoreCardRepository.GetListAsync(a => a.AssessmentId == request.AssessmentId && a.Indicator.SubObjectiveId == request.SubObjectiveId);

            if (existingScoreCardDetails.Any())
            {
                request.IndicatorDetails.ForEach(kvpIndicatorMetNotMetStatus =>
                {
                    ScoreCard existingIndicatorDetails = existingScoreCardDetails.FirstOrDefault(d => d.IndicatorId == kvpIndicatorMetNotMetStatus.IndicatorId && d.AssessmentId == request.AssessmentId);

                    if (existingIndicatorDetails != null)
                    {
                        existingIndicatorDetails.MetNotMetStatus = kvpIndicatorMetNotMetStatus.IndicatorMetNotMetStatus;
                        existingIndicatorDetails.ReasonForResult = kvpIndicatorMetNotMetStatus.IndicatorReasonForResult;
                        existingIndicatorDetails.Recommendation = kvpIndicatorMetNotMetStatus.IndicatorRecommendation;
                    }
                    else
                    {
                        ScoreCard scoreCard = new ScoreCard()
                        {
                            Id = Guid.NewGuid(),
                            AssessmentId = request.AssessmentId,
                            IndicatorId = kvpIndicatorMetNotMetStatus.IndicatorId,
                            MetNotMetStatus = kvpIndicatorMetNotMetStatus.IndicatorMetNotMetStatus,
                            ReasonForResult = kvpIndicatorMetNotMetStatus.IndicatorReasonForResult,
                            Recommendation = kvpIndicatorMetNotMetStatus.IndicatorRecommendation
                        };
                        scoreCards.Add(scoreCard);
                    }

                });

                _unitOfWork.ScoreCardRepository.AddRange(scoreCards);
                _unitOfWork.ScoreCardRepository.UpdateRange(existingScoreCardDetails);
            }
            else
            {
                request.IndicatorDetails.ForEach(scoreCardDetails =>
                {
                    ScoreCard scoreCard = new ScoreCard()
                    {
                        Id = Guid.NewGuid(),
                        AssessmentId = request.AssessmentId,
                        IndicatorId = scoreCardDetails.IndicatorId,
                        MetNotMetStatus = scoreCardDetails.IndicatorMetNotMetStatus,
                        ReasonForResult = scoreCardDetails.IndicatorReasonForResult,
                        Recommendation = scoreCardDetails.IndicatorRecommendation
                    };
                    scoreCards.Add(scoreCard);
                });

                _unitOfWork.ScoreCardRepository.AddRange(scoreCards);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}