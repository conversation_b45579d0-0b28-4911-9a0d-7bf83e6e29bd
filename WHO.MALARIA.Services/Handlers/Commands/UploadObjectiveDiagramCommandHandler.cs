﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DeskReview;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles upload objective diagram command request to upload the diagram
    /// </summary>
    public class UploadObjectiveDiagramCommandHandler : RuleBase, IRequestHandler<UploadObjectiveDiagramCommand, string[]>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public UploadObjectiveDiagramCommandHandler(IUnitOfWork unitOfWork, ITranslationService translationService, ICommonRuleChecker commonRuleChecker, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request and add/update the objective diagram
        /// </summary>
        /// <param name="request">Contains input fields of the upload objective diagram comamnd class</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>Array of uploaded diagram files id</returns>
        public async Task<string[]> Handle(UploadObjectiveDiagramCommand request, CancellationToken cancellationToken)
        {
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(UploadObjectiveDiagramCommand.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.StrategyId, nameof(UploadObjectiveDiagramCommand.StrategyId)));
            CheckRule(new IsEnumValueValidRule(_translationService, typeof(DeskReviewAssessmentResponseStatus), Convert.ToInt32(request.Status), nameof(UploadObjectiveDiagramCommand.Status)));
            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUploadDiagram));

            if (request.DiagramDetails == null || request.DiagramDetails.All(x => x.File == null && !x.Id.HasValue))
            {
                CheckRule(new IsFileNullCheckRule(_translationService, null, Constants.Exception.InvalidDiagramFile));
            }

            foreach (DiagramDetail detail in request.DiagramDetails.Where(x => x.File != null))
            {
                CheckRule(new IsFileExtensionValidRule(_translationService, Path.GetExtension(detail.File.FileName), Constants.Common.AllowedDiagramFileTypes));
                CheckRule(new FileSizeCheckRule(_translationService, detail.File.Length, Constants.Common.DiagramMaxFileSize, Constants.Exception.FileSizeGreaterThan10MB));
            }

            Guid assessmentStrategyId = _unitOfWork.AssessmentRepository.GetAssessmentStrategyId(request.AssessmentId, request.StrategyId);

            ObjectiveDiagram existingObjectiveDiagram = _unitOfWork.ObjectiveDiagramRepository.Queryable(x => x.AssessmentStrategyId == assessmentStrategyId)
                                                                                              .Include(x => x.ObjectiveDiagramFiles)
                                                                                              .SingleOrDefault();

            string[] diagramFileIds = new string[2];

            //Add new diagram
            if (existingObjectiveDiagram == null)
            {
                ObjectiveDiagram newObjectiveDiagram = new ObjectiveDiagram(assessmentStrategyId,
                                                                            ObjectiveSeedingMetadata.TECH_A_PROCESS_ID,
                                                                            request.Status,
                                                                            request.DataAccess,
                                                                            request.DataQualityAssurance,
                                                                            request.DataRecording,
                                                                            request.DataReporting,
                                                                            request.DataAnalysis);

                int index = 0;

                foreach (DiagramDetail detail in request.DiagramDetails)
                {
                    //TODO: Check valid file extension from bytes - should be done in the xss middleware

                    if (HasNotDiagramFileAndDetails(detail))
                    {
                        index++;
                        continue;
                    }

                    newObjectiveDiagram.AddFile(detail.File?.GetBytesAsync().Result,
                                                Path.GetExtension(detail.File?.FileName),
                                                detail.File?.FileName,
                                                detail.DataFlow,
                                                detail.PlannedChanges,
                                                detail.ModifyingProcess,
                                                (byte)(index + 1));

                    index++;
                }

                _unitOfWork.ObjectiveDiagramRepository.Add(newObjectiveDiagram);

                //Returning the diagram files Ids to client to help to maintain the order of diagrams.
                foreach (ObjectiveDiagramFile file in newObjectiveDiagram.ObjectiveDiagramFiles)
                {
                    diagramFileIds[file.Order - 1] = file.Id.ToString();
                }
            }
            else
            {
                existingObjectiveDiagram.Update(
                    existingObjectiveDiagram.Status == (int)DeskReviewAssessmentResponseStatus.Completed ? existingObjectiveDiagram.Status : request.Status,
                    request.DataAccess,
                    request.DataQualityAssurance,
                    request.DataRecording,
                    request.DataReporting,
                    request.DataAnalysis);

                int index = 0;

                foreach (DiagramDetail detail in request.DiagramDetails)
                {
                    //TODO: Check valid file extension from bytes - should be done in the xss middleware
                    ObjectiveDiagramFile existingDiagramFile = existingObjectiveDiagram.ObjectiveDiagramFiles.SingleOrDefault(x => x.Id == detail.Id);
                    string extension = Path.GetExtension(detail.File?.FileName);

                    if (existingDiagramFile == null)
                    {
                        if (HasNotDiagramFileAndDetails(detail))
                        {
                            index++;
                            continue;
                        }

                        existingObjectiveDiagram.AddFile(detail.File?.GetBytesAsync().Result,
                                                         extension,
                                                         detail.File?.FileName,
                                                         detail.DataFlow,
                                                         detail.PlannedChanges,
                                                         detail.ModifyingProcess,
                                                         (byte)(index + 1));
                    }
                    else
                    {
                        if (detail.File != null)
                        {
                            existingDiagramFile.Update(detail.File.GetBytesAsync().Result,
                                                       extension,
                                                       detail.File.FileName,
                                                       detail.DataFlow,
                                                       detail.PlannedChanges,
                                                       detail.ModifyingProcess);
                        }
                        else
                        {
                            existingDiagramFile.Update(detail.DataFlow,
                                                       detail.PlannedChanges,
                                                       detail.ModifyingProcess);
                        }
                    }

                    index++;
                }

                _unitOfWork.ObjectiveDiagramRepository.Update(existingObjectiveDiagram);

                foreach (ObjectiveDiagramFile file in existingObjectiveDiagram.ObjectiveDiagramFiles)
                {
                    diagramFileIds[file.Order - 1] = file.Id.ToString();
                }
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return diagramFileIds;
        }

        /// <summary>
        /// Checks if diagram details has data including the file
        /// </summary>
        /// <param name="detail">Object of DiagramDetail class</param>
        /// <returns>True if diagram details doesn't have data else false</returns>
        private static bool HasNotDiagramFileAndDetails(DiagramDetail detail) => detail.File == null
                                                                                 && string.IsNullOrWhiteSpace(detail.DataFlow)
                                                                                 && string.IsNullOrWhiteSpace(detail.PlannedChanges)
                                                                                 && string.IsNullOrWhiteSpace(detail.ModifyingProcess);
    }
}
