﻿using MediatR;

using Microsoft.EntityFrameworkCore;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.DQA;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the incoming request to delete service level for DQA.
    /// </summary>
    public class DeleteServiceLevelCommandHandler : RuleBase, IRequestHandler<DeleteServiceLevelCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly IDQARuleChecker _dqaRuleChecker;

        public DeleteServiceLevelCommandHandler(IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker, IDQARuleChecker dqaRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
            _dqaRuleChecker = dqaRuleChecker;
        }

        /// <summary>
        /// Deletes service level record along with its registers, data system sources, variables etc using DeleteServiceLevelCommand.
        /// </summary>
        /// <param name="request">Object of DeleteServiceLevelCommand. Which includes properties to delete service level record</param>
        /// <param name="cancellationToken">Notifies the cancellation request</param>
        /// <returns>True if service level is deleted successfully</returns>
        public async Task<bool> Handle(DeleteServiceLevelCommand request, CancellationToken cancellationToken)
        {          
            //check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ServiceLevelId, "ServiceLevelId"));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new ServiceLevelShouldExistRule(_translationService, _dqaRuleChecker, request.ServiceLevelId));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanDeleteServiceLevel));
            CheckRule(new ServiceLevelShouldNotBeFinalizedRule(_translationService, _dqaRuleChecker, request.ServiceLevelId));

            int assessmentApproach = _unitOfWork.AssessmentRepository.Queryable(a => a.Id == request.AssessmentId).SingleOrDefault().Approach;

            if(assessmentApproach == (int)AssessmentApproach.Comprehensive)
            {
                CheckRule(new CanDeleteServiceLevelRule(_translationService, _dqaRuleChecker, request.AssessmentId));
            }

            ServiceLevel serviceLevel = await _unitOfWork.ServiceLevelRepository.Queryable(sl => sl.Id == request.ServiceLevelId).SingleOrDefaultAsync();

            if (serviceLevel == null)
            {
                throw new RecordNotFoundException(request.ServiceLevelId,"ServiceLevel");
            }

            _unitOfWork.ServiceLevelRepository.Remove(serviceLevel);
           

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
