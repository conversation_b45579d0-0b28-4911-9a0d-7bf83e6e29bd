﻿using MediatR;

using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Handlers.Queries;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.DQA;
using WHO.MALARIA.Services.Rules.Shared;

using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle update of service level DQA data using UpdateServiceLevelDQACommand
    /// </summary>
    public class UpdateServiceLevelDQACommandHandler : RuleBase, IRequestHandler<UpdateServiceLevelDQACommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly IDQAQueries _dqaQueries;

        public UpdateServiceLevelDQACommandHandler(IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker, IDQAQueries dqaQueries)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
            _dqaQueries = dqaQueries;
        }

        /// <summary>
        /// Updates service level data using UpdateServiceLevelDQACommand
        /// </summary>
        /// <param name="request"> Command includes properties related to saving service level data </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        public async Task<bool> Handle(UpdateServiceLevelDQACommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new DateNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.ValidationPeriodStartDate, "ValidationPeriodStartDate"));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanCreateOrUpdateServiceLevel));
            CheckRule(new IsValidMonthRule(_translationService, request.From.Month, "From"));
            CheckRule(new IsValidMonthRule(_translationService, request.To.Month, "To"));
            CheckRule(new FromAndToDateShouldNotBeSameRule(_translationService, request.From, request.To));
            CheckRule(new ValidationPeriodStartDateShouldBeValidRule(_translationService, request.From, request.To, request.ValidationPeriodStartDate));
            CheckRule(new IntArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.RegisterTypes.Select(x => (int)x).ToArray(), "Register type"));
            CheckRule(new RegisterTypeMustBeValidRule(_translationService, request.RegisterTypes));

            //service level data
            await SaveServiceLevelAsync(request);

            //registers
            await SaveRegisterTypesAsync(request.RegisterTypes, request.ServiceLevelId);

            //variables
            await SaveVariablesAsync(request.VariableIds, request.ServiceLevelId);

            try
            {
                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }
            catch (DbUpdateException ex)
            {
                if (ex.InnerException is SqlException sqlExp)
                {
                    if (sqlExp.Number == SqlExceptionErrorCodes.ForeignKeyConflict) // Constant - 547  
                    {
                        throw new ApplicationException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidVariableIds));
                    }
                }
                throw;
            }

            return true;
        }

        /// <summary>
        /// Saves service level data.
        /// </summary>
        /// <param name="request">Object of UpdateServiceLevelDQACommand object with parameters which are to be updated or saved </param>
        private async Task SaveServiceLevelAsync(UpdateServiceLevelDQACommand request)
        {
            ServiceLevel existingServiceLevelRecord = await _unitOfWork.ServiceLevelRepository.GetAsync(x => x.Id == request.ServiceLevelId);

            if (existingServiceLevelRecord == null)
            {
                throw new RecordNotFoundException(request.ServiceLevelId, "ServiceLevel");
            }

            Int16 version = request.IsFinalized ? Convert.ToInt16(existingServiceLevelRecord.Version + 1) : existingServiceLevelRecord.Version;

            existingServiceLevelRecord.Update(request.From, request.To, request.ValidationPeriodStartDate, Convert.ToInt16(request.From.Year), version);

            _unitOfWork.ServiceLevelRepository.Update(existingServiceLevelRecord);
        }

        /// <summary>
        /// Saves register types.
        /// </summary>
        /// <param name="registerTypes">Array of integers for containing register types</param>
        /// <param name="serviceLevelId">Service level id for which register types are to be updated</param>
        private async Task SaveRegisterTypesAsync(IEnumerable<byte> registerTypes, Guid serviceLevelId)
        {
            IEnumerable<ServiceLevelRegister> existingRecords = await _unitOfWork.ServiceLevelRegisterRepository.GetListAsync(x => x.ServiceLevelId == serviceLevelId);

            _unitOfWork.ServiceLevelRegisterRepository.RemoveRange(existingRecords);

            List<ServiceLevelRegister> serviceLevelRegisters = new List<ServiceLevelRegister>();

            foreach (byte registerType in registerTypes)
            {
                serviceLevelRegisters.Add
                    (new ServiceLevelRegister
                    {
                        ServiceLevelId = serviceLevelId,
                        Type = registerType
                    });
            }

            _unitOfWork.ServiceLevelRegisterRepository.AddRange(serviceLevelRegisters);
        }

        /// <summary>
        /// Saves variables for a service level.
        /// </summary>
        /// <param name="variableIds">List of variable ids to be stored</param>
        /// <param name="serviceLevelId">Service level id for which variables are to be updated</param>
        private async Task SaveVariablesAsync(Guid[] variableIds, Guid serviceLevelId)
        {
            IEnumerable<ServiceLevelVariable> existingRecords = await _unitOfWork.ServiceLevelVariableRepository.GetListAsync(x => x.ServiceLevelId == serviceLevelId);

            _unitOfWork.ServiceLevelVariableRepository.RemoveRange(existingRecords);

            List<ServiceLevelVariable> serviceLevelVariables = new List<ServiceLevelVariable>();

            //adding system defined variable as system defined variable is not actual variable, it is required for 
            //holding calculation for overall concordance. Its added directly from code. No front end input is there for this variable.
            serviceLevelVariables.Add(new ServiceLevelVariable
            {
                ServiceLevelId = serviceLevelId,
                DQAVariableId = DQAVariableSeedingMetadata.OverallConcordance_ID //Guid for system defined DQA variable
            });

            foreach (Guid variableId in variableIds)
            {
                serviceLevelVariables.Add
                    (new ServiceLevelVariable
                    {
                        ServiceLevelId = serviceLevelId,
                        DQAVariableId = variableId
                    });
            }

            _unitOfWork.ServiceLevelVariableRepository.AddRange(serviceLevelVariables);
        }
    }
}
