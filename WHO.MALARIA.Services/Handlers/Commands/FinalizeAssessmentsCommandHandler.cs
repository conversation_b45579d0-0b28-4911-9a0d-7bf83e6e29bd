﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// FinalizeAssessments handler to handle creation of assessment status with finalize status using FinalizeAssessmentCommand
    /// </summary>
    public class FinalizeAssessmentsCommandHandler : RuleBase, IRequestHandler<FinalizeAssessmentCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ITranslationService _translationService;
        public FinalizeAssessmentsCommandHandler(
            IMediator mediator,
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _translationService = translationService;
        }

        /// <summary>
        /// Finalize assessment using FinalizeAssessmentCommand
        /// </summary>
        /// <param name="request"> Command includes assesment id </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> return true if saved successfully </returns>
        public async Task<bool> Handle(FinalizeAssessmentCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessementId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalize));

            //Set assessment status as finalized
            Domain.Models.AssessmentStatus assessmentStatus = new Domain.Models.AssessmentStatus(request.AssessmentId, (int)Domain.Enum.AssessmentStatus.Finalized);
            _unitOfWork.AssessmentStatusRepository.Add(assessmentStatus);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
            return true;
        }
    }
}
