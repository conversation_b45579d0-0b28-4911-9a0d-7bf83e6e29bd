﻿using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.DQA;
using WHO.MALARIA.Services.Rules.Shared;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles SaveDQADeskLevelCommand request to save DQA desk level parameters
    /// </summary>
    public class SaveDQADeskLevelCommandHandler : RuleBase, IRequestHandler<SaveDQADeskLevelCommand, bool>
    {      
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IDQARuleChecker _dqaRuleChecker;

        public SaveDQADeskLevelCommandHandler(ILogger<SaveDQADeskLevelCommandHandler> logger, IUnitOfWork unitOfWork, ITranslationService translationService, ICommonRuleChecker commonRuleChecker, IDQARuleChecker dqaRuleChecker)
        {            
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _dqaRuleChecker = dqaRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request, creates entities that are going to save in the DB.
        /// </summary>
        /// <param name="request">Instance of SaveDQADeskLevelCommand - Contains input parameters</param>
        /// <param name="cancellationToken">A cancellation token to cancel the operation</param>
        /// <returns>TRUE If it is successfull Else FALSE</returns>
        public async Task<bool> Handle(SaveDQADeskLevelCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.DataSystem_1_DataSourceId, nameof(request.DataSystem_1_DataSourceId)));
            CheckRule(new DataSourceCannotBeSame(_translationService, request.DataSystem_1_DataSourceId, request.DataSystem_2_DataSourceId, request.DataSystem_1_OtherDataSourceName, request.DataSystem_2_OtherDataSourceName));
            CheckRule(new AtLeastOneVariableShouldBeSelectedForConcordance(_translationService, request.VariableIds, request.DataSystem_2_DataSourceId));
            CheckRule(new AtLeastOnePriorityVariableShouldBeSelected(_translationService, _dqaRuleChecker, request.VariableIds.Select(x => x.Key).ToList()));

            //Save desk level parameters
            DeskLevel deskLevel = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);
            Guid deskLevelId;

            if (deskLevel is null)
            {
                deskLevelId = Guid.NewGuid();
                DeskLevel newDeskLevel = new DeskLevel(deskLevelId, request.AssessmentId);
                _unitOfWork.DeskLevelRepository.Add(newDeskLevel);
            }
            else
                deskLevelId = deskLevel.Id;

            //Remove existing data sources before adding new from the request.
            List<DataSource> existingDataSources = _unitOfWork.DataSourceRepository.Queryable(x => x.DeskLevelId == deskLevelId).ToList();
            _unitOfWork.DataSourceRepository.RemoveRange(existingDataSources);

            //Save data source for data system 1
            DataSource dataSystem_1_dataSource;

            if (request.DataSystem_1_DataSourceId == DQADataSourceSeedingMetadata.DataSystem_1_OtherDataSourceId)
            {
                CheckRule(new StringNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.DataSystem_1_OtherDataSourceName, "Data system 1 - Other data source"));
                dataSystem_1_dataSource = new DataSource(deskLevelId, request.DataSystem_1_DataSourceId, request.DataSystem_1_OtherDataSourceName);
            }
            else
                dataSystem_1_dataSource = new DataSource(deskLevelId, request.DataSystem_1_DataSourceId);

            _unitOfWork.DataSourceRepository.Add(dataSystem_1_dataSource);

            //Save data source for data system 2
            if (request.DataSystem_2_DataSourceId != null && request.DataSystem_2_DataSourceId != Guid.Empty)
            {
                DataSource dataSystem_2_dataSource;

                if (request.DataSystem_2_DataSourceId == DQADataSourceSeedingMetadata.DataSystem_2_OtherDataSourceId)
                {
                    CheckRule(new StringNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.DataSystem_2_OtherDataSourceName, "Data system 2 - Other data source"));
                    dataSystem_2_dataSource = new DataSource(deskLevelId, request.DataSystem_2_DataSourceId.Value, request.DataSystem_2_OtherDataSourceName);
                }
                else
                    dataSystem_2_dataSource = new DataSource(deskLevelId, request.DataSystem_2_DataSourceId.Value);

                _unitOfWork.DataSourceRepository.Add(dataSystem_2_dataSource);
            }

            //Remove existing variable mappings before adding new from the request.
            List<VariableMapping> existingVariableMappings = _unitOfWork.VariableMappingRepository.Queryable(x => x.DeskLevelId == deskLevelId).ToList();
            _unitOfWork.VariableMappingRepository.RemoveRange(existingVariableMappings);

            //Add variable mappings from the request
            foreach (KeyValuePair<Guid, bool?> variable in request.VariableIds)
            {
                VariableMapping variableMapping = new VariableMapping(deskLevelId, variable.Key, variable.Value);
                _unitOfWork.VariableMappingRepository.Add(variableMapping);
            }

            try
            {
                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }
            catch (DbUpdateException ex)
            {
                if (ex.InnerException is SqlException sqlExp)
                {
                    if (sqlExp.Number == SqlExceptionErrorCodes.ForeignKeyConflict)
                    {
                        throw new ApplicationException(_translationService.GetTranslatedMessage(Domain.Constants.Constants.Exception.InvalidVariableIds));
                    }
                }

                throw;
            }

            return true;
        }
    }
}
