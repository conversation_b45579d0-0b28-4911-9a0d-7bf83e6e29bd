﻿using MediatR;

using System;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle finalization of service level using FinalizeServiceLevelCommand
    /// </summary>
    public class FinalizeServiceLevelCommandHandler : RuleBase, IRequestHandler<FinalizeServiceLevelCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeServiceLevelCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Finalize service level using FinalizeServiceLevelCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to service level finalize operation </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if finalize of service level is successful </returns>
        public async Task<bool> Handle(FinalizeServiceLevelCommand request, CancellationToken cancellationToken)
        { 
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ServiceLevelId, "ServiceLevelId"));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalizeServiceLevel));

            ServiceLevel existingServiceLevelRecord = await _unitOfWork.ServiceLevelRepository.GetAsync(x => x.Id == request.ServiceLevelId);

            if (existingServiceLevelRecord == null)
            {
                throw new RecordNotFoundException(request.ServiceLevelId, "ServiceLevel");
            }

            existingServiceLevelRecord.IsFinalized = true;
            existingServiceLevelRecord.Version = 1;
            _unitOfWork.ServiceLevelRepository.Update(existingServiceLevelRecord);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
