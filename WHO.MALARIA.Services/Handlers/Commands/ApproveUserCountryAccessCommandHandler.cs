﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles user country access request approval command
    /// </summary>
    public class ApproveUserCountryAccessCommandHandler : RuleBase, IRequestHandler<ApproveUserCountryAccessCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IUserRuleChecker _userRuleChecker;

        public ApproveUserCountryAccessCommandHandler(IMediator mediator, IUnitOfWork unitOfWork, ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IUserRuleChecker userRuleChecker)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _userRuleChecker = userRuleChecker;
        }

        /// <summary>
        /// Approved user using ApproveUserCountryAccessCommand
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns>It returns true if request status set as accepted successfully</returns>
        public async Task<bool> Handle(ApproveUserCountryAccessCommand request, CancellationToken cancellationToken)
        {
            Country country = await _unitOfWork.CountryRepository.GetAsync(u => u.Id == request.CountryId);

            if (country == null)
            {
                throw new RecordNotFoundException(request.CountryId, "Country");
            }

            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserCountryAccessId, "UserCountryAccessId"));
            CheckRule(new UserShouldBeSuperManagerOfCountry(_translationService, _userRuleChecker, request.CurrentUserId, request.CountryId, country.Name));

            UserCountryAccess userCountryAccess = await _unitOfWork.UserCountryAccessRepository.Queryable(u => u.Id == request.UserCountryAccessId)
                                                                                               .Include(uca => uca.User)
                                                                                               .ThenInclude(u => u.Identity)
                                                                                               .SingleOrDefaultAsync();

            if (userCountryAccess == null)
            {
                throw new RecordNotFoundException(request.UserCountryAccessId, "UserCountryAccess");
            }

            //Update user country access to accept
            userCountryAccess.Status = (int)UserCountryAccessRightsEnum.Accepted;

            _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            await _mediator.Publish(new UserCountryAccessGrantedEmailNotification(userCountryAccess.User.Identity.Email, country.Name));

            return true;
        }
    }
}
