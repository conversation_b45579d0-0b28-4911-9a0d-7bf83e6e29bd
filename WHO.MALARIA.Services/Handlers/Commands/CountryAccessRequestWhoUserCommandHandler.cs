﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles country access request from who user
    /// </summary>
    public class CountryAccessRequestWhoUserCommandHandler : RuleBase, IRequestHandler<CountryAccessRequestCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IUserRuleChecker _userRuleChecker;

        public CountryAccessRequestWhoUserCommandHandler(IMediator mediator, IUnitOfWork unitOfWork, 
                                                         ICommonRuleChecker commonRuleChecker, ITranslationService translationService, 
                                                         IUserRuleChecker userRuleChecker)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _userRuleChecker = userRuleChecker;
        }

        /// <summary>
        /// Handles country access request from who user using CountryAccessRequestCommand
        /// </summary>
        /// <param name="request">Instance of CountryAccessRequestCommand class that contains the request parameters</param>
        /// <param name="cancellationToken">CancellationToken token to cancel current task</param>     
        public async Task<bool> Handle(CountryAccessRequestCommand request, CancellationToken cancellationToken)
        {
            IEnumerable<Country> countries = await _unitOfWork.CountryRepository.GetListAsync(u => u.IsActive);

            foreach (Guid requestedCountryId in request.CountryRequestedForIds)
            {
                Country country = countries.Where(c => c.Id == requestedCountryId).SingleOrDefault();

                // Check Business Rules
                CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, country.Id, "CountryId"));               

                if (country == null)
                {
                    throw new RecordNotFoundException(country.Id, "Country");
                }
            }

            List<UserCountryAccess> userCountriesAccess = await _unitOfWork.UserCountryAccessRepository.Queryable(u => u.UserId == request.CurrentUserId 
                                                                                               && u.Status == (int)UserCountryAccessRightsEnum.Pending)
                                                                                               .Include(uca => uca.User)
                                                                                               .ThenInclude(u => u.Identity).ToListAsync();

            List<UserCountryAccess> superManagerEmailIdsOfCountries = await _unitOfWork.UserCountryAccessRepository.Queryable(u => u.Status == (int)UserCountryAccessRightsEnum.Accepted
                                                                                               && u.UserType == (int)UserRoleEnum.SuperManager)
                                                                                              .Include(uca => uca.User)
                                                                                              .ThenInclude(u => u.Identity).ToListAsync();
            foreach (UserCountryAccess countryRequest in userCountriesAccess)
            {
                Country country = countries.Where(c => c.Id == countryRequest.CountryId).SingleOrDefault();

                if (countryRequest  == null)
                {
                    throw new RecordNotFoundException(countryRequest.Id, "UserCountryAccess");
                }
                else
                {
                    UserCountryAccess superManagerEmailIdOfCountry = superManagerEmailIdsOfCountries.Where(e => e.CountryId == countryRequest.CountryId).SingleOrDefault();
                    if (superManagerEmailIdOfCountry != null)
                          await _mediator.Publish(new UserCountryAccessRequestEmailNotification(superManagerEmailIdOfCountry.User.Identity.Email, country.Name));
                }
            }

            return true;
        }
    }
}
