﻿using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle update the default user country status for landing page
    /// </summary>
    public class UpdateDefaultCountryOnLandingPageCommandHandler : RuleBase, IRequestHandler<UpdateDefaultCountryOnLandingPageCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;

        #endregion

        #region Constructor
        public UpdateDefaultCountryOnLandingPageCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            ITranslationService translationService
           )
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
        }
        #endregion

        #region Command Handler

        /// <summary>
        /// Update the default user country status for landing page
        /// </summary>
        public async Task<bool> Handle(UpdateDefaultCountryOnLandingPageCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId, nameof(request.CountryId)));

            IEnumerable<UserCountryAccess> existingUserCountryRecords = await _unitOfWork.UserCountryAccessRepository.GetListAsync(x => x.UserId == request.CurrentUserId);

            if (existingUserCountryRecords == null && !existingUserCountryRecords.Any())
            {
                throw new RecordNotFoundException(request.CountryId, "UserCountryAccess");
            }

            existingUserCountryRecords.ToList().ForEach(record =>
            {
                record.IsDefault = record.CountryId == request.CountryId;
            });

            _unitOfWork.UserCountryAccessRepository.UpdateRange(existingUserCountryRecords);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }

        #endregion
    }
}
