﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles user invitation acceptance command
    /// </summary>
    public class AcceptUserCommandHandler : RuleBase, IRequestHandler<AcceptUserCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;

        #endregion

        #region Constructor
        public AcceptUserCommandHandler(IUnitOfWork unitOfWork,
                                        ICommonRuleChecker commonRuleChecker,
                                        ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
        }
        #endregion

        /// <summary>
        /// Accepts user using AcceptUserCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to user accept operation </param>
        /// <param name="cancellationToken">Notifies the cancellation request</param>
        /// <returns>true if operation is successful otherwise false</returns>~
        public async Task<bool> Handle(AcceptUserCommand request, CancellationToken cancellationToken)
        {
            UserCountryAccess userCountryAccess = await _unitOfWork.UserCountryAccessRepository.Queryable(u => u.Id == request.ucaId)
                                                                                               .AsNoTracking()
                                                                                               .FirstOrDefaultAsync();

            if (userCountryAccess == null)
            {
                throw new RecordNotFoundException(request.ucaId, "UserCountryAccessId");
            }

            User user = await _unitOfWork.UserRepository.Queryable(u => u.Id == userCountryAccess.UserId)
                                                        .Include(u => u.Identity)
                                                        .FirstOrDefaultAsync();


            IEnumerable<UserCountryAccess> userCountriesAccess = await _unitOfWork.UserCountryAccessRepository.GetListAsync(u => u.UserId == userCountryAccess.UserId && u.Status == (int)UserCountryAccessRightsEnum.InvitationNotAccepted && u.UserType == (int)UserRoleEnum.Viewer);

            // if user is not who admin and his role is viewer then this code block is executed 
            if (!user.IsWhoAdmin && userCountryAccess.UserType == (int)UserRoleEnum.Viewer && userCountriesAccess.Any())
            {
                List<UserCountryAccess> countriesAccess = new List<UserCountryAccess>();

                userCountriesAccess.ToList().ForEach(countryAccess =>
                {
                    countryAccess.Status = (int)UserCountryAccessRightsEnum.Accepted;
                    countriesAccess.Add(countryAccess);
                });

                _unitOfWork.UserCountryAccessRepository.UpdateRange(countriesAccess);
            }
            else
            {
                userCountryAccess.Status = (int)UserCountryAccessRightsEnum.Accepted;

                _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);
            }

            user.Status = (int)UserStatus.Active;
            user.Identity.Status = true;

            _unitOfWork.UserRepository.Update(user);

            _unitOfWork.IdentityRepository.Update(user.Identity);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
