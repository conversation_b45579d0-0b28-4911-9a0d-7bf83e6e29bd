﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the UpdateUserStatusAndTypeBySuperManagerCommand and changes the user's status and user type performed by super manager
    /// </summary>
    public class UpdateUserStatusAndTypeBySuperManagerCommandHandler : RuleBase, IRequestHandler<UpdateUserStatusAndTypeBySuperManagerCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserCountryAccessRuleChecker _userCountryAccessRulechecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IMediator _mediator;

        #endregion

        #region Constructor
        public UpdateUserStatusAndTypeBySuperManagerCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IUserCountryAccessRuleChecker userCountryAccessRuleChecker,
            IUserRuleChecker userRuleChecker,
            ITranslationService translationService,
            IMediator mediator)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userCountryAccessRulechecker = userCountryAccessRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
            _mediator = mediator;
        }
        #endregion

        #region Command Handler

        /// <summary>
        /// Change user status and user type
        /// </summary>
        public async Task<bool> Handle(UpdateUserStatusAndTypeBySuperManagerCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserId, "User Id"));
            CheckRule(new UpdatedUserTypeBySuperManagerShouldBeValidRule(_translationService, request.UserType));
            CheckRule(new UserShouldBeSuperManagerOfCountry(_translationService, _userRuleChecker, request.CurrentUserId, request.CountryId));

            User user = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.UserId)
                                                        .Include(u => u.Identity)
                                                        .Include(u => u.UserCountryAccesses)
                                                        .ThenInclude(uc => uc.Country)
                                                        .FirstOrDefaultAsync();

            if (user == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            UserCountryAccess countryAccess = user.UserCountryAccesses.Single(uca => uca.CountryId == request.CountryId);

            bool isUserTypeChanged = countryAccess.UserType != request.UserType;

            countryAccess.UserType = request.UserType;

            bool isUserCountryAccessRightChanged = false;
            bool isUserDeactivatedForAllAssignedCountries = false;

            if (request.Status == (int)UserStatus.Active)
            {
                user.Status = (int)UserStatus.Active;
                user.Identity.Status = true;

                if (countryAccess.Status != (int)UserCountryAccessRightsEnum.Accepted) isUserCountryAccessRightChanged = true;

                countryAccess.Status = (int)UserCountryAccessRightsEnum.Accepted;

                _unitOfWork.UserCountryAccessRepository.Update(countryAccess);
                _unitOfWork.IdentityRepository.Update(user.Identity);
                _unitOfWork.UserRepository.Update(user);
            }
            else
            {
                //Restrict from deactivating manager whose assessment(s) is/are in active state.
                if (request.UserType == (int)UserRoleEnum.Manager)
                {
                    //check if user has active assessments associated with it as manager
                    bool isAssignedToAssessmentAsManager = _unitOfWork.AssessmentUserRepository.IsAssignedToAssessmentAsManager(user.Id, countryAccess.CountryId);

                    CheckRule(new UserDoesNotHaveActiveAssessmentRule(_translationService, isAssignedToAssessmentAsManager));
                }

                if (countryAccess.Status != (int)UserCountryAccessRightsEnum.InActive) isUserCountryAccessRightChanged = true;

                countryAccess.Status = (int)UserCountryAccessRightsEnum.InActive;
                _unitOfWork.UserCountryAccessRepository.Update(countryAccess);

                //If user is not active for all the assigned countries then deactivate it from the system
                IEnumerable<UserCountryAccess> countryAccessesExceptRejected = user.UserCountryAccesses.Where(uca => uca.Status != (int)UserCountryAccessRightsEnum.Rejected);
                if (countryAccessesExceptRejected.Any() && countryAccessesExceptRejected.All(uca => uca.Status == (int)UserCountryAccessRightsEnum.InActive))
                {
                    // if the user is rejected by all the countries then inactivate user
                    user.Status = (int)UserStatus.InActive;
                    _unitOfWork.UserRepository.Update(user);

                    user.Identity.Status = false;
                    _unitOfWork.IdentityRepository.Update(user.Identity);
                    isUserDeactivatedForAllAssignedCountries = true;
                }
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            if (isUserTypeChanged || isUserCountryAccessRightChanged)
            {
                await _mediator.Publish(new UserCountryAccessRightChangeEmailNotification(user.Identity.Email,
                                                                                          countryAccess.Country.Name,
                                                                                          (UserRoleEnum)countryAccess.UserType,
                                                                                          (UserCountryAccessRightsEnum)countryAccess.Status));
            }

            if (isUserDeactivatedForAllAssignedCountries)
            {
                await _mediator.Publish(new UserDeactivatedForAllAssignedCountriesEmailNotification(user.Identity.Email));
            }

            return true;
        }
        #endregion
    }
}
