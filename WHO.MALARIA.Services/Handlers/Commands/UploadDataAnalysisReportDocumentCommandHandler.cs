﻿using MediatR;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles Insertion of data analysis report document data command
    /// </summary>
    public class UploadDataAnalysisReportDocumentCommandHandler : RuleBase, IRequestHandler<UploadDataAnalysisReportDocumentCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public UploadDataAnalysisReportDocumentCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Upload data analysis report document data in database using UploadDataAnalysisReportDocumentCommand
        /// </summary>
        /// <param name="request">Input Properties related to data Analysis report document</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>return true if saved successfully</returns>
        public async Task<bool> Handle(UploadDataAnalysisReportDocumentCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.ShowResultsOnGlobalDashboard));

            string fileExtension = Path.GetExtension(request.File.FileName);

            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidDataAnalyisFileExtensions));

            DataAnalysisReport fileWithSameFullName = await _unitOfWork.DataAnalysisReportRepository.GetAsync(x => x.AssessmentId==request.AssessmentId && x.Name == request.File.FileName && x.Extension == fileExtension);

            if (fileWithSameFullName == null)
            {
                byte[] fileContent = null;

                if (request.File.Length > 0)
                {
                    using (var ms = new MemoryStream())
                    {
                        request.File.CopyTo(ms);
                        fileContent = ms.ToArray();
                    }
                }

                DataAnalysisReport dataAnalysisReport = new DataAnalysisReport()
                {
                    AssessmentId = request.AssessmentId,
                    Name = request.File.FileName,
                    Extension = fileExtension,
                    Size = CommandGenerator.CalcuateFileSize(request.File.Length),
                    Content = fileContent,
                    ContentType = request.File.ContentType
                };

                _unitOfWork.DataAnalysisReportRepository.Add(dataAnalysisReport);

                if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
                {
                    throw new ApplicationException();
                }
            }

            return true;
        }
    }
}
