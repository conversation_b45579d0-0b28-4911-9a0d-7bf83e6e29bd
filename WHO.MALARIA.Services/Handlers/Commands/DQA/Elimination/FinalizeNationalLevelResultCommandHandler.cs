﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands.DQA.Elimination;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.Elimination;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Elimination
{
    /// <summary>
    /// Handles finalizing process of National Level Result and data quality reason command
    /// </summary>
    public class FinalizeNationalLevelResultCommandHandler : RuleBase, IRequestHandler<FinalizeNationalLevelResultCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeNationalLevelResultCommandHandler(           
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {            
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Saves National Level Result and data quality reason in database
        /// </summary>
        /// <param name="request">Input Properties related National Level Result</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if saved successfully</returns>
        public async Task<bool> Handle(FinalizeNationalLevelResultCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.Summary.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.Summary.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.Summary.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalizeDeskLevelDQASummaryResult));

            Summary existingNationalResultSummary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == request.Summary.AssessmentId);

            if (existingNationalResultSummary == null)
            {
                throw new RecordNotFoundException(request.Summary.AssessmentId, nameof(request.Summary.AssessmentId));
            }

            existingNationalResultSummary.Year = request.Summary.Year;
            existingNationalResultSummary.AssessmentId = request.Summary.AssessmentId;
            existingNationalResultSummary.IsFinalized = request.Summary.IsFinalized;
            existingNationalResultSummary.ReportCompleteness = request.Summary.ReportCompleteness;
            existingNationalResultSummary.ReportTimeliness = request.Summary.ReportTimeliness;
            existingNationalResultSummary.CaseInvestigationReportsCompleteness = request.Summary.CaseInvestigationReportsCompleteness;
            existingNationalResultSummary.CaseNotificationReportsTimeliness = request.Summary.CaseNotificationReportsTimeliness;
            existingNationalResultSummary.CaseInvestigationReportsTimeliness = request.Summary.CaseInvestigationReportsTimeliness;
            existingNationalResultSummary.FociInvestigationReportsTimeliness = request.Summary.FociInvestigationReportsTimeliness;
            existingNationalResultSummary.CoreVariableCompletenessWithinReport = request.Summary.CoreVariableCompletenessWithinReport;
            existingNationalResultSummary.ConsistencyBetweenCoreVariables = request.Summary.ConsistencyBetweenCoreVariables;
            existingNationalResultSummary.ConsistencyOverTimeCoreIndicators = request.Summary.ConsistencyOverTimeCoreIndicators;
            existingNationalResultSummary.ConfirmMalariaCasesNotified = request.Summary.ConfirmMalariaCasesNotified;
            existingNationalResultSummary.ConfirmMalariaCasesInvestigated = request.Summary.ConfirmMalariaCasesInvestigated;
            existingNationalResultSummary.ConfirmMalariaCasesClassified = request.Summary.ConfirmMalariaCasesClassified;
            existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsLocal = request.Summary.ConfirmMalariaCasesClassifiedAsLocal;
            existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsIndigenous = request.Summary.ConfirmMalariaCasesClassifiedAsIndigenous;
            existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsIntroduced = request.Summary.ConfirmMalariaCasesClassifiedAsIntroduced;
            existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsImported = request.Summary.ConfirmMalariaCasesClassifiedAsImported;
            existingNationalResultSummary.MalariaCasesDueToPF = request.Summary.MalariaCasesDueToPF;
            existingNationalResultSummary.MalariaCasesDueToPK = request.Summary.MalariaCasesDueToPK;
            existingNationalResultSummary.MalariaCasesDueToPM = request.Summary.MalariaCasesDueToPM;
            existingNationalResultSummary.MalariaCasesDueToPO = request.Summary.MalariaCasesDueToPO;
            existingNationalResultSummary.MalariaCasesDueToPV = request.Summary.MalariaCasesDueToPV;
            existingNationalResultSummary.KeyVariableConcordanceBtwTwoReportingSystem = request.Summary.KeyVariableConcordanceBtwTwoReportingSystem;
            existingNationalResultSummary.CoreVariableCompletenessWithinRegister = request.Summary.CoreVariableCompletenessWithinRegister;
            existingNationalResultSummary.CoreVariableConcordanceBtwRegister = request.Summary.CoreVariableConcordanceBtwRegister;

            _unitOfWork.EliminationSummaryRepository.Update(existingNationalResultSummary);

            SummaryDataQualityResultReason existingResultReason = await _unitOfWork.EliminationSummaryDataQualityReasonRepository.GetAsync(x => x.AssessmentId == request.Summary.AssessmentId);

            if (existingResultReason == null)
            {
                throw new RecordNotFoundException(request.Summary.AssessmentId, nameof(request.Summary.AssessmentId));
            }

            existingResultReason.AssessmentId = request.Summary.AssessmentId;
            existingResultReason.Year = request.Summary.Year;
            existingResultReason.IsFinalized = request.Summary.IsFinalized;
            existingResultReason.Reason = request.Summary.DataQualityResultReason;

            _unitOfWork.EliminationSummaryDataQualityReasonRepository.Update(existingResultReason);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
