﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands.DQA.Elimination;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.Elimination;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Elimination
{
    /// <summary>
    /// Handles Insertion of National Level Result and data quality reason command
    /// </summary>
    public class SaveNationalLevelResultCommandHandler : RuleBase, IRequestHandler<SaveNationalLevelResultCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public SaveNationalLevelResultCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Saves National Level Result and data quality reason in database
        /// </summary>
        /// <param name="request">Input Properties related National Level Result</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if saved successfully</returns>
        public async Task<bool> Handle(SaveNationalLevelResultCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.Summary.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.Summary.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.Summary.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanEditAndSaveDeskLevelDQASummaryResult));

            Summary existingNationalResultSummary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == request.Summary.AssessmentId);
            SummaryDataQualityResultReason existingResultReason = await _unitOfWork.EliminationSummaryDataQualityReasonRepository.GetAsync(x => x.AssessmentId == request.Summary.AssessmentId);

            if (existingNationalResultSummary == null && existingResultReason == null)
            {
                Summary nationalResultSummary = new Summary
                {
                    Year = request.Summary.Year,
                    AssessmentId = request.Summary.AssessmentId,
                    IsFinalized = request.Summary.IsFinalized,
                    ReportCompleteness = request.Summary.ReportCompleteness,
                    ReportTimeliness = request.Summary.ReportTimeliness,
                    CaseInvestigationReportsCompleteness = request.Summary.CaseInvestigationReportsCompleteness,
                    CaseNotificationReportsTimeliness = request.Summary.CaseNotificationReportsTimeliness,
                    CaseInvestigationReportsTimeliness = request.Summary.CaseInvestigationReportsTimeliness,
                    FociInvestigationReportsTimeliness = request.Summary.FociInvestigationReportsTimeliness,
                    CoreVariableCompletenessWithinReport = request.Summary.CoreVariableCompletenessWithinReport,
                    ConsistencyBetweenCoreVariables = request.Summary.ConsistencyBetweenCoreVariables,
                    ConsistencyOverTimeCoreIndicators = request.Summary.ConsistencyOverTimeCoreIndicators,
                    ConfirmMalariaCasesNotified = request.Summary.ConfirmMalariaCasesNotified,
                    ConfirmMalariaCasesInvestigated = request.Summary.ConfirmMalariaCasesInvestigated,
                    ConfirmMalariaCasesClassified = request.Summary.ConfirmMalariaCasesClassified,
                    ConfirmMalariaCasesClassifiedAsLocal = request.Summary.ConfirmMalariaCasesClassifiedAsLocal,
                    ConfirmMalariaCasesClassifiedAsIndigenous = request.Summary.ConfirmMalariaCasesClassifiedAsIndigenous,
                    ConfirmMalariaCasesClassifiedAsIntroduced = request.Summary.ConfirmMalariaCasesClassifiedAsIntroduced,
                    ConfirmMalariaCasesClassifiedAsImported = request.Summary.ConfirmMalariaCasesClassifiedAsImported,
                    MalariaCasesDueToPF = request.Summary.MalariaCasesDueToPF,
                    MalariaCasesDueToPK = request.Summary.MalariaCasesDueToPK,
                    MalariaCasesDueToPM = request.Summary.MalariaCasesDueToPM,
                    MalariaCasesDueToPO = request.Summary.MalariaCasesDueToPO,
                    MalariaCasesDueToPV = request.Summary.MalariaCasesDueToPV,
                    KeyVariableConcordanceBtwTwoReportingSystem = request.Summary.KeyVariableConcordanceBtwTwoReportingSystem,
                    CoreVariableCompletenessWithinRegister = request.Summary.CoreVariableCompletenessWithinRegister,
                    CoreVariableConcordanceBtwRegister = request.Summary.CoreVariableConcordanceBtwRegister
                };

                _unitOfWork.EliminationSummaryRepository.Add(nationalResultSummary);

                SummaryDataQualityResultReason resultReason = new SummaryDataQualityResultReason
                {
                    AssessmentId = request.Summary.AssessmentId,
                    Year = request.Summary.Year,
                    IsFinalized = request.Summary.IsFinalized,
                    Reason = request.Summary.DataQualityResultReason,
                };

                _unitOfWork.EliminationSummaryDataQualityReasonRepository.Add(resultReason);
            }
            else
            {
                existingNationalResultSummary.Year = request.Summary.Year;
                existingNationalResultSummary.AssessmentId = request.Summary.AssessmentId;
                existingNationalResultSummary.IsFinalized = request.Summary.IsFinalized;
                existingNationalResultSummary.ReportCompleteness = request.Summary.ReportCompleteness;
                existingNationalResultSummary.ReportTimeliness = request.Summary.ReportTimeliness;
                existingNationalResultSummary.CaseInvestigationReportsCompleteness = request.Summary.CaseInvestigationReportsCompleteness;
                existingNationalResultSummary.CaseNotificationReportsTimeliness = request.Summary.CaseNotificationReportsTimeliness;
                existingNationalResultSummary.CaseInvestigationReportsTimeliness = request.Summary.CaseInvestigationReportsTimeliness;
                existingNationalResultSummary.FociInvestigationReportsTimeliness = request.Summary.FociInvestigationReportsTimeliness;
                existingNationalResultSummary.CoreVariableCompletenessWithinReport = request.Summary.CoreVariableCompletenessWithinReport;
                existingNationalResultSummary.ConsistencyBetweenCoreVariables = request.Summary.ConsistencyBetweenCoreVariables;
                existingNationalResultSummary.ConsistencyOverTimeCoreIndicators = request.Summary.ConsistencyOverTimeCoreIndicators;
                existingNationalResultSummary.ConfirmMalariaCasesNotified = request.Summary.ConfirmMalariaCasesNotified;
                existingNationalResultSummary.ConfirmMalariaCasesInvestigated = request.Summary.ConfirmMalariaCasesInvestigated;
                existingNationalResultSummary.ConfirmMalariaCasesClassified = request.Summary.ConfirmMalariaCasesClassified;
                existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsLocal = request.Summary.ConfirmMalariaCasesClassifiedAsLocal;
                existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsIndigenous = request.Summary.ConfirmMalariaCasesClassifiedAsIndigenous;
                existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsIntroduced = request.Summary.ConfirmMalariaCasesClassifiedAsIntroduced;
                existingNationalResultSummary.ConfirmMalariaCasesClassifiedAsImported = request.Summary.ConfirmMalariaCasesClassifiedAsImported;
                existingNationalResultSummary.MalariaCasesDueToPF = request.Summary.MalariaCasesDueToPF;
                existingNationalResultSummary.MalariaCasesDueToPK = request.Summary.MalariaCasesDueToPK;
                existingNationalResultSummary.MalariaCasesDueToPM = request.Summary.MalariaCasesDueToPM;
                existingNationalResultSummary.MalariaCasesDueToPO = request.Summary.MalariaCasesDueToPO;
                existingNationalResultSummary.MalariaCasesDueToPV = request.Summary.MalariaCasesDueToPV;
                existingNationalResultSummary.KeyVariableConcordanceBtwTwoReportingSystem = request.Summary.KeyVariableConcordanceBtwTwoReportingSystem;
                existingNationalResultSummary.CoreVariableCompletenessWithinRegister = request.Summary.CoreVariableCompletenessWithinRegister;
                existingNationalResultSummary.CoreVariableConcordanceBtwRegister = request.Summary.CoreVariableConcordanceBtwRegister;

                _unitOfWork.EliminationSummaryRepository.Update(existingNationalResultSummary);

                existingResultReason.AssessmentId = request.Summary.AssessmentId;
                existingResultReason.Year = request.Summary.Year;
                existingResultReason.IsFinalized = request.Summary.IsFinalized;
                existingResultReason.Reason = request.Summary.DataQualityResultReason;

                _unitOfWork.EliminationSummaryDataQualityReasonRepository.Update(existingResultReason);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
