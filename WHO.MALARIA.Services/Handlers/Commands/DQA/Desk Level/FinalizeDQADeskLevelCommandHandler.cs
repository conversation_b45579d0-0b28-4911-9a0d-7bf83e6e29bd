﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands.DQA.Desk_Level;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Desk_Level
{
    /// <summary>
    /// Finalize handler to handle finalize desk level parameters
    /// </summary>
    public class FinalizeDQADeskLevelCommandHandler : RuleBase, IRequestHandler<FinalizeDQADeskLevelCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeDQADeskLevelCommandHandler(          
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
          
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Finalize desk level parameters using FinalizeDQADeskLevelCommand
        /// </summary>
        /// <param name="request"> Command includes input parameters</param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> return true if saved successfully </returns>
        public async Task<bool> Handle(FinalizeDQADeskLevelCommand request, CancellationToken cancellationToken)
        {        
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanSaveOrFinalizeDeskLevelDQAVariables));

            DeskLevel existingDeskLevel = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == request.AssessmentId);

            if (existingDeskLevel == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            existingDeskLevel.IsFinalized = true;

            _unitOfWork.DeskLevelRepository.Update(existingDeskLevel);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;

        }
    }
}
