﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands.DQA.Desk_Level;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands.DQA.Desk_Level
{
    /// <summary>
    /// Finalize National Level Result handler to handle finalize status using FinalizeNationalLevelResultCommand
    /// </summary>
    public class FinalizeNationalLevelResultCommandHandler : RuleBase, IRequestHandler<FinalizeNationalLevelResultCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public FinalizeNationalLevelResultCommandHandler(
            IUnitOfWork unitOfWork,
            ITranslationService translationService,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assesmentRuleChecker;
        }

        /// <summary>
        /// Finalize National Level Result using FinalizeNationalLevelResultCommand
        /// </summary>
        /// <param name="request"> Command includes assessment id </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> return true if saved successfully </returns>
        public async Task<bool> Handle(FinalizeNationalLevelResultCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.SummaryData.AssessmentId, "AssessmentId"));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.SummaryData.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.SummaryData.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanFinalizeDeskLevelDQASummaryResult));

            Summary existingNationalResultSummary = await _unitOfWork.SummaryRepository.GetAsync(x => x.AssessmentId == request.SummaryData.AssessmentId && x.Year == request.SummaryData.Year && x.Type == (byte)DQADLSummaryResultType.NationalLevelTarget);

            if (existingNationalResultSummary == null)
            {
                throw new RecordNotFoundException(request.SummaryData.AssessmentId, nameof(request.SummaryData.AssessmentId));
            }
            existingNationalResultSummary.Year = request.SummaryData.Year;
            existingNationalResultSummary.AssessmentId = request.SummaryData.AssessmentId;
            existingNationalResultSummary.Type = (byte)DQADLSummaryResultType.NationalLevelTarget;
            existingNationalResultSummary.IsFinalized = request.SummaryData.IsFinalized;
            existingNationalResultSummary.ReportCompleteness = request.SummaryData.ReportCompleteness;
            existingNationalResultSummary.ReportTimeliness = request.SummaryData.ReportTimeliness;
            existingNationalResultSummary.VariableCompleteness = request.SummaryData.VariableCompleteness;
            existingNationalResultSummary.VariableConsistency = request.SummaryData.VariableConsistency;
            existingNationalResultSummary.VariableConcordance = request.SummaryData.VariableConcordance;
            existingNationalResultSummary.MalariaOutpatientProportion = request.SummaryData.MalariaOutpatientProportion;
            existingNationalResultSummary.MalariaInPatientProportion = request.SummaryData.MalariaInPatientProportion;
            existingNationalResultSummary.MalariaInPatientDeathProportion = request.SummaryData.MalariaInPatientDeathProportion;
            existingNationalResultSummary.TestPositivityRate = request.SummaryData.TestPositivityRate;
            existingNationalResultSummary.SlidePositivityRate = request.SummaryData.SlidePositivityRate;
            existingNationalResultSummary.RDTPositivityRate = request.SummaryData.RDTPositivityRate;
            existingNationalResultSummary.SuspectedTestProportion = request.SummaryData.SuspectedTestProportion;

            _unitOfWork.SummaryRepository.Update(existingNationalResultSummary);

            SummaryDataQualityResultReason existingResultReason = await _unitOfWork.SummaryDataQualityResultReasonRepository.GetAsync(x => x.AssessmentId == request.SummaryData.AssessmentId && x.Year == request.SummaryData.Year);

            if (existingResultReason == null)
            {
                throw new RecordNotFoundException(request.SummaryData.AssessmentId, nameof(request.SummaryData.AssessmentId));
            }

            existingResultReason.AssessmentId = request.SummaryData.AssessmentId;
            existingResultReason.Year = request.SummaryData.Year;
            existingResultReason.IsFinalized = request.SummaryData.IsFinalized;
            existingResultReason.Reason = request.SummaryData.DataQualityResultReason;

            _unitOfWork.SummaryDataQualityResultReasonRepository.Update(existingResultReason);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}

