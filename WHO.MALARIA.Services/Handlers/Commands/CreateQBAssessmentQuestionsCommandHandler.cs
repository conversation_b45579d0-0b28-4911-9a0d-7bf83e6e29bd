﻿using MediatR;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.QuestionBank;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.QuestionBank;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Adds new questions and removes old questions for assessment respondent types using CreateQBAssessmentQuestionsCommand
    /// </summary>
    public class CreateQBAssessmentQuestionsCommandHandler : RuleBase, IRequestHandler<CreateQBAssessmentQuestionsCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        public CreateQBAssessmentQuestionsCommandHandler(IUnitOfWork unitOfWork, ICommonRuleChecker commonRuleChecker, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Create questions data for assessment using CreateQBAssessmentQuestionsCommand
        /// </summary>
        /// <param name="request"> Command includes properties related to saving questions data</param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if data saved successfully else 500 status code or validation message if any </returns>
        public async Task<bool> Handle(CreateQBAssessmentQuestionsCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new UserShouldHavePermissionToWorkOnAssessment(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanCreateOrUpdateQuestionBank));
            CheckRule(new RespondentTypeMustBeValidRule(_translationService, new List<byte> { request.RespondentType }));
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.Questions?.Select(q => q.Id)?.ToArray(), "Questions"));

            AssessmentRespondentType existingAssessmentRespondentType = await _unitOfWork.AssessmentRespondentTypeRepository.GetAsync(x => x.RespondentType == request.RespondentType && x.AssessmentId == request.AssessmentId);

            if (existingAssessmentRespondentType == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            await SaveAssessmentQuestions(request, existingAssessmentRespondentType.Id);

            Int16 version = existingAssessmentRespondentType.IsFinalized ? Convert.ToInt16(existingAssessmentRespondentType.ShellTableFileVersion + 1) : existingAssessmentRespondentType.ShellTableFileVersion;

            existingAssessmentRespondentType.ShellTableFileVersion = version;

            await DeleteShellTableData(existingAssessmentRespondentType.Id);

            _unitOfWork.AssessmentRespondentTypeRepository.Update(existingAssessmentRespondentType);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }

        /// <summary>
        /// Save questions that are selected for an assessment and for the particular respondent type.
        /// </summary>
        /// <param name="request">Object of CreateQBAssessmentQuestionsCommand with parameters which are saved </param>    
        private async Task SaveAssessmentQuestions(CreateQBAssessmentQuestionsCommand request, Guid assessmentRespondentTypeId)
        {
            IEnumerable<AssessmentQuestion> existingRecords = await _unitOfWork.AssessmentQuestionRepository.GetListAsync(x => x.AssessmentRespondentTypeId == assessmentRespondentTypeId);

            if (existingRecords.Any())
            {
                _unitOfWork.AssessmentQuestionRepository.RemoveRange(existingRecords);
            }

            IEnumerable<AssessmentQuestion> assessmentQuestions = request.Questions.Select(x => new AssessmentQuestion
                                                                                           (
                                                                                             Guid.NewGuid(),
                                                                                             assessmentRespondentTypeId,
                                                                                             x.Id,
                                                                                             x.ModifiedQuestion,
                                                                                             x.ResponseOption
                                                                                            )
                                                                                         );

            _unitOfWork.AssessmentQuestionRepository.AddRange(assessmentQuestions);
        }

        /// <summary>
        /// Delete shell table data
        /// </summary>
        /// <param name="assessmentRespondentTypeId"></param>       
        private async Task DeleteShellTableData(Guid assessmentRespondentTypeId)
        {
            IEnumerable<ShellTable> existingRecords = await _unitOfWork.ShellTableRepository.GetListAsync(x => x.AssessmentRespondentTypeId == assessmentRespondentTypeId);

            if (existingRecords.Any())
            {
                _unitOfWork.ShellTableRepository.RemoveRange(existingRecords);
            }
        }
    }
}
