﻿using MediatR;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>  
    /// Update the observed data quality reason associated with the service level using UpdateObservedDataQualityResultReasonCommand
    /// </summary>
    public class UpdateObservedDataQualityResultReasonCommandHandler : RuleBase, IRequestHandler<UpdateObservedDataQualityResultReasonCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public UpdateObservedDataQualityResultReasonCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Update observed data quality reason using UpdateObservedDataQualityResultReasonCommand
        /// </summary>
        /// <param name="request"> Contains request parameters to update observed data quality reason </param>
        /// <param name="cancellationToken"> Notify the cancellation request </param>
        /// <returns> True if the data quality reason for the service level is successfully updated</returns>
        public async Task<bool> Handle(UpdateObservedDataQualityResultReasonCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.ServiceLevelId, nameof(request.ServiceLevelId)));
            CheckRule(new StringNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.ObservedDataQualityResultReason, nameof(request.ObservedDataQualityResultReason)));

            // Only those users who can upload files can update the observed data quality result reason.
            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUpdateObservedDataQualityReason));

            ServiceLevelVariableCompletness existingServiceLevelVariableCompletness = await _unitOfWork.VariableCompletenessResponseRepository.GetAsync(x => x.ServiceLevelId == request.ServiceLevelId);

            if (existingServiceLevelVariableCompletness == null)
            {
                throw new RecordNotFoundException(request.AssessmentId, nameof(request.AssessmentId));
            }

            existingServiceLevelVariableCompletness.ObservedDataQualityResultReason = request.ObservedDataQualityResultReason;

            _unitOfWork.VariableCompletenessResponseRepository.Update(existingServiceLevelVariableCompletness);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }
    }
}
