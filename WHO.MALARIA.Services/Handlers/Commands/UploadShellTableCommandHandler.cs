﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.QuestionBank;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.QuestionBank;
using WHO.MALARIA.Services.Rules.Shared;
using ShellTable = WHO.MALARIA.Domain.Models.QuestionBank.ShellTable;
using WHO.MALARIA.Services.Rules.ShellTable;
using Microsoft.Extensions.Options;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Add shell table excel sheet data to shell table
    /// </summary>
    public class UploadShellTableCommandHandler : RuleBase, IRequestHandler<UploadShellTableCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IShellTable _shellTable;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;
        private readonly ShellTableExcelSetting _shellTableExcelSetting;

        public UploadShellTableCommandHandler(IUnitOfWork unitOfWork,
                                                          IShellTable shellTable,
                                                          ITranslationService translationService,
                                                          ICommonRuleChecker commonRuleChecker,
                                                          IAssessmentRuleChecker assesmentRuleChecker,
                                                          ILogger<UploadShellTableCommandHandler> logger,
                                                          IOptions<ShellTableExcelSetting> shellTableExcelSetting)
        {
            _unitOfWork = unitOfWork;
            _shellTable = shellTable;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _assesmentRuleChecker = assesmentRuleChecker;
            _shellTableExcelSetting = shellTableExcelSetting.Value;
        }

        /// <summary>
        /// Performs validations on the request, adds the new shell table data if it does not exist else updates it
        /// </summary>
        /// <param name="request">Contains health facility data to add/update</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>TRUE if shell table gets saved/updated else throw an error if any validation fails</returns>
        public async Task<bool> Handle(UploadShellTableCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, nameof(request.AssessmentId)));

            string fileExtension = Path.GetExtension(request.File.FileName);

            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidExcelExtensions));

            CheckRule(new IsValidShelltTableFile(_translationService, request.File, _shellTableExcelSetting));

            CheckRule(new FileSizeCheckRule(_translationService, request.File.Length, Constants.Common.ExcelFileSizeBytes, Constants.Exception.InvalidFileSize));

            CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assesmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToUpload));

            Assessment currentUser = await _unitOfWork.AssessmentRepository.Queryable(u => u.Id == request.AssessmentId)
                                                                           .Include(u => u.Country)
                                                                           .SingleAsync();

            bool hasHealthFacilities = _unitOfWork.HealthFacilityRepository.HasLatestHealthFacilitiesByCountryAsync(currentUser.Country.Id);

            if (!hasHealthFacilities)
            {
                CheckRule(new FailedRule(_translationService, Constants.Exception.NoHealthFacilityData));
            }

            IEnumerable<QBQuestionIndicatorSubObjectiveObjectiveDto> assessmentGenerateQuestionDtos = await _unitOfWork.AssessmentQuestionRepository.GetWithIndicatorsSubObjctivesObjectivesAsync(request.AssessmentId);

            IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings = await _unitOfWork.ShellTableRepository.GetQuestionsBankMappingAsync();

            shellTableQuestionBankMappings.ToList().ForEach(q =>
            {
                q.IndicatorMapSetting = JsonConvert.DeserializeObject<IndicatorMapSetting>(q.ShellTableExcelTemplateSetting);
            });

            QuestionDocumentInputModel questionDocumentInputModel = new QuestionDocumentInputModel();

            questionDocumentInputModel.ShellTableQuestionBankMappings = shellTableQuestionBankMappings;

            questionDocumentInputModel.Questions = assessmentGenerateQuestionDtos.Select(x => new DocumentManager.Models.Question
            {
                RespondentType = x.RespondentType,
                ObjectiveId = x.ObjectiveId,
                ObjectiveName = x.ObjectiveName,
                ObjectiveSequence = x.ObjectiveSequence,
                SubObjectiveId = x.SubObjectiveId,
                SubObjectiveName = x.SubObjectiveName,
                SubObjectiveSequence = x.SubObjectiveSequence,
                IndicatorName = x.IndicatorName,
                IndicatorSequence = x.IndicatorSequence,
                QuestionName = x.Question,
                Notes = x.Notes,
                ResponseOption = x.ResponseOption,
                ForSelfAssessment = x.ForSelfAssessment,
                Code = x.Code,
                ShellTableFileVersion = x.ShellTableFileVersion,
                AssessmentRespondentTypeId = x.AssessmentRespondentTypeId,
                QuestionId = x.QuestionId
            });

            ShellTableDto shellTable = _shellTable.ReadShellTableSheet(request.File, questionDocumentInputModel);

            if (shellTable == null)
            {
                return false;
            }

            CheckRule(new IsValidShellTableFileRule(_translationService, request.AssessmentId,
            shellTable.TemplateAssessementId, shellTable.InputRespondentType,
            shellTable.TemplateRespondentType, shellTable.InputVersion, shellTable.TemplateVersion));

            CheckRule(new IsValueLessThanRule(_translationService, shellTable.NoOfRecordsInHealthFacilitySheet, 1, Constants.Exception.NoRecordsPresentInHealthFacilitySheet));

            CheckRule(new IsValueGreaterThanRule(_translationService, shellTable.NoOfRecordsInHealthFacilitySheet, 1, Constants.Exception.MultipleRecordsPresentInHealthFacilitySheet));

            List<Domain.Models.QuestionBank.ShellTable> shellTableEntities = new List<Domain.Models.QuestionBank.ShellTable>();

            string healthFacilityCode = shellTable.shellTableQuestions.First().HealthFacilityCode;

            Guid assessmentRespondentTypeId = shellTable.shellTableQuestions.First().RespondentType;

            ShellTableQuestionDto existingShellTableQuestion = shellTable.shellTableQuestions.First();

            int? heathFacilityId = null;

            string healthFacilityName = shellTable.shellTableQuestions.First().HealthFacilityName;

            string healthFacilityTypeName = shellTable.shellTableQuestions.First().HealthFacilityType;

            string regionName = shellTable.shellTableQuestions.First().RegionName;

            string districtCode = shellTable.shellTableQuestions.First().DistrictCode;

            string districtName = shellTable.shellTableQuestions.First().DistrictName;

            if (shellTable.TemplateRespondentType != (int)QBRespondentType.SubnationalLevel)
            {
                byte healthFacilityTypeEnumValue = 0;

                if (Enum.TryParse<HealthFacilityType>(healthFacilityTypeName, out HealthFacilityType healthFacilityTypeValue))
                {
                    healthFacilityTypeEnumValue = Convert.ToByte(healthFacilityTypeValue);
                }
                else
                {
                    throw new ApplicationException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacilityType));
                }

                IEnumerable<HealthFacility> healthFacilities = await _unitOfWork.HealthFacilityRepository.GetListAsync(d => d.Code == healthFacilityCode && d.Name == healthFacilityName && d.Type == healthFacilityTypeEnumValue && d.District == districtName && d.DistrictCode == districtCode && d.Region == regionName && d.IsObsolete == false && d.CountryId == currentUser.CountryId);

                if (healthFacilities.Any())
                {
                    heathFacilityId = healthFacilities.First().Id;
                }
                else
                {
                    throw new ApplicationException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacility));
                }
            }
            else
            {
                IEnumerable<HealthFacility> healthFacilities = await _unitOfWork.HealthFacilityRepository.GetListAsync(d => d.District == districtName && d.DistrictCode == districtCode && d.Region == regionName && d.IsObsolete == false && d.CountryId == currentUser.CountryId);

                if (healthFacilities.Any())
                {
                    heathFacilityId = healthFacilities.First().Id;
                }
                else
                {
                    throw new ApplicationException(_translationService.GetTranslatedMessage(Constants.Exception.InvalidHealthFacility));
                }
            }

            await DeleteShellTableData(assessmentRespondentTypeId, shellTable.TemplateRespondentType, existingShellTableQuestion.DistrictCode, heathFacilityId);

            shellTable.shellTableQuestions.ForEach(shellTableDto =>
            {
                Domain.Models.QuestionBank.ShellTable shellTable = new Domain.Models.QuestionBank.ShellTable
                {
                    DistrictCode = districtCode,
                    HealthFacilityId = heathFacilityId,
                    Option = shellTableDto.Option,
                    Value = shellTableDto.Value,
                    AssessmentRespondentTypeId = shellTableDto.RespondentType,
                    ShellTableQuestionBankMappingId = shellTableDto.ShellTableQuestionBankMappingId,
                    OptionOrder = shellTableDto.OptionOrder
                };

                shellTableEntities.Add(shellTable);
            });

            _unitOfWork.ShellTableRepository.AddRange(shellTableEntities);

            await SaveShellTableReport(request.AssessmentId, request.File);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }

        /// <summary>
        /// Saves excel upload data details to database for shell report
        /// </summary>
        /// <param name="assessmentId">GUID of the Assessment Id</param>
        /// <param name="file">Uploaded file</param>
        /// <returns>true if successfully saved else false</returns>
        private async Task<bool> SaveShellTableReport(Guid assessmentId, IFormFile file)
        {
            string fileExtension = Path.GetExtension(file.FileName);

            ShellTableReport fileWithSameFullName = await _unitOfWork.ShellTableReportRepository.GetAsync(x => x.AssessmentId == assessmentId && x.Name == file.FileName && x.Extension == fileExtension);

            if (fileWithSameFullName == null)
            {
                byte[] fileContent = null;

                if (file.Length > 0)
                {
                    using (var ms = new MemoryStream())
                    {
                        file.CopyTo(ms);
                        fileContent = ms.ToArray();
                    }
                }

                ShellTableReport shellTableReport = new ShellTableReport()
                {
                    AssessmentId = assessmentId,
                    Name = file.FileName,
                    Extension = fileExtension,
                    Size = CommandGenerator.CalcuateFileSize(file.Length),
                    Content = fileContent,
                    ContentType = file.ContentType
                };

                _unitOfWork.ShellTableReportRepository.Add(shellTableReport);

                return true;
            }
            return false;
        }

        /// <summary>
        /// Delete shell table data
        /// </summary>
        /// <param name="assessmentRespondentTypeId">Respondent type id</param>
        /// <param name="repondentType">Respondent type</param>
        /// <param name="districtCode">District code of health facility</param>
        /// <param name="healthFacilityId">Health facility id</param>     
        private async Task DeleteShellTableData(Guid assessmentRespondentTypeId, int repondentType, string districtCode, int? healthFacilityId)
        {
            if (repondentType == (int)QBRespondentType.SubnationalLevel)
            {
                IEnumerable<ShellTable> existingRecords = await _unitOfWork.ShellTableRepository.GetListAsync(x => x.AssessmentRespondentTypeId == assessmentRespondentTypeId && x.DistrictCode == districtCode);
                if (existingRecords.Any())
                {
                    _unitOfWork.ShellTableRepository.RemoveRange(existingRecords);
                }
            }
            else
            {
                IEnumerable<ShellTable> existingRecords = await _unitOfWork.ShellTableRepository.GetListAsync(x => x.AssessmentRespondentTypeId == assessmentRespondentTypeId && x.HealthFacilityId == healthFacilityId);
                if (existingRecords.Any())
                {
                    _unitOfWork.ShellTableRepository.RemoveRange(existingRecords);
                }
            }
        }
    }
}
