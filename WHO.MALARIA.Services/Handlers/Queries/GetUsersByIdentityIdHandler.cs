﻿using MediatR;
using System.Threading.Tasks;
using System.Threading;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Database;
using Serilog;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetUsersByIdentityIdHandler : IRequestHandler<GetUsersByIdentityIdQuery, List<UserDto>>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        // private readonly ILogger _logger;

        public GetUsersByIdentityIdHandler(IMediator mediator, IUnitOfWork unitOfWork)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            // _logger = logger;
        }


        public async Task<List<UserDto>> Handle(GetUsersByIdentityIdQuery request, CancellationToken cancellationToken)
        {
            // _logger.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name, new Dictionary<string, dynamic> { { "UserId", request.UserId } });


            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetUsersByIdentityIdAsync(request.IdentityId);

            return users.ToList();
        }
    }
}
