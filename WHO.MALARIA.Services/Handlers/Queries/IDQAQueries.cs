﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to DQA
    /// </summary>
    public interface IDQAQueries
    {
        Task<IEnumerable<DQAVariableDto>> GetServiceLevelVariablesByStrategyAsync(Guid strategyId, bool includeSystemDefined);
        Task<IEnumerable<DQADataSourcesDto>> GetDQADataSourcesAsync();        
        Task<ServiceLevel> GetServiceLevelDataAsync(Guid serviceLevelId);
        Task<ServiceLevelDto> GetServiceLevelForAssessmentAsync(Guid assessmentId);
        Task<FileResponseDto> ExportServiceLevelTemplate(Guid serviceLevelId, Guid currentUserId);
        Task<ServiceLevelSummaryDto> GetServiceLevelSummaryAsync(Guid assessmentId);
        Task<IndicatorReportDto> GetReportForIndicatorAsync(DQAIndicatorReport indicator, Guid assessmentId);
        Task<FileResponseDto> GetDeskLevelTemplate(Guid assessmentId);
        Task<FileResponseDto> GetDeskLevelReport(Guid assessmentId);
        Task<IEnumerable<Domain.Dtos.OutputDtos.DeskLevelDQA.IndicatorDto>> GetDeskLevelIndicatorsAsync();
        Task<IEnumerable<short>> GetDeskLevelNationalSummaryYearsAsync(Guid assessmentId);
        Task<List<NationalLevelSummaryRequestDto>> GetDeskLevelNationalSummaryDataAsync(Guid assessmentId, short year);
        Task<SelectedParametersDto> GetDeskLevelSelectedParametersAsync(Guid assessmentId);
        FileResponseDto GetEliminationTemplate();
        Task<Domain.Dtos.InputDtos.EliminationDQA.NationalLevelSummaryRequestDto> GetEliminationNationalSummaryDataAsync(Guid assessmentId);
        Task<FileResponseDto> GetEliminationReportAsync(Guid assessmentId);
        Task<ServiceLevel> GetServiceLevelByAssessmentAsync(Guid assessemntId);
    }
}
