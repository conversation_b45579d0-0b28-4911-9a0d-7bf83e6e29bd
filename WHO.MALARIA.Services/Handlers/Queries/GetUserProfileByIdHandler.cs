﻿using MediatR;
using System.Threading.Tasks;
using System.Threading;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Database;
using Serilog;
using System.Reflection;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.Dxos;
using System.Linq;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Get user profile by id with country list
    /// </summary>
    public class GetUserProfileByIdHandler : IRequestHandler<GetUserProfileByIdQuery, UserProfileDto>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUserCountryAccessDxos _userCountryAccessDxos;

        public GetUserProfileByIdHandler(IMediator mediator, IUnitOfWork unitOfWork, IUserCountryAccessDxos userCountryAccessDxos)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _userCountryAccessDxos = userCountryAccessDxos;
        }


        public async Task<UserProfileDto> Handle(GetUserProfileByIdQuery request, CancellationToken cancellationToken)
        {
            Log.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name, new Dictionary<string, dynamic> { { "UserId", request.UserId } });

            int accepted = (int)UserCountryAccessRightsEnum.Accepted;
            int inActive = (int)UserCountryAccessRightsEnum.InActive;
            int pending = (int)UserCountryAccessRightsEnum.Pending;
            int rejected = (int)UserCountryAccessRightsEnum.Rejected;

            // Get the countries whose access is requested or accepted for user.
            IEnumerable<UserCountryAccess> userCountryAccess = await _unitOfWork.UserCountryAccessRepository.GetListAsync(uca => uca.UserId == request.UserId && uca.Status != rejected);

            var userProfile = new UserProfileDto
            {
                User = await _mediator.Send(new GetUserByIdQuery(request.UserId)),
                UserCountryAccess = _userCountryAccessDxos.MapToDto(userCountryAccess.Where(x => x.Status == accepted)),
                InActivatedCountryIds = userCountryAccess.Where(x => x.Status == inActive).Select(y => y.CountryId),
                PendingRequestCountryIds = userCountryAccess.Where(x => x.Status == pending).Select(y => y.CountryId)
            };

            userProfile.User.UserType = userProfile.User.IsWhoAdmin ? (int)UserRoleEnum.WHOAdmin :
                                        userCountryAccess != null ?
                                        userCountryAccess.Any(userCountryAccess => userCountryAccess.UserType == (int)UserRoleEnum.SuperManager) ? (int)UserRoleEnum.SuperManager :
                                        userCountryAccess.Any(userCountryAccess => userCountryAccess.UserType == (int)UserRoleEnum.Manager) ? (int)UserRoleEnum.Manager :
                                        userCountryAccess.Any(userCountryAccess => userCountryAccess.UserType == (int)UserRoleEnum.Viewer) ? (int)UserRoleEnum.Viewer : 0
                                        : 0;

            return userProfile;
        }

    }
}
