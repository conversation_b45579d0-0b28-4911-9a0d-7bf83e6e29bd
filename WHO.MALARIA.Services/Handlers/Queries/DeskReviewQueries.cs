﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for desk review of the indicators
    /// </summary>
    public class DeskReviewQueries : IDeskReviewQueries
    {
        private readonly IUnitOfWork _unitOfWork;

        public DeskReviewQueries(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get response documents for the specific indicator and strategy
        /// </summary>
        /// <param name="assessmentIndicatorId">Indicator id that is associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id that is associated with the assessment</param>
        /// <returns>List of documents</returns>
        public Task<IEnumerable<ResponseDocumentDto>> GetResponseDocuments(Guid assessmentIndicatorId, Guid assessmentStrategyId) => _unitOfWork.ResponseDocumentRepository.GetAsync(assessmentIndicatorId, assessmentStrategyId);
    }
}
