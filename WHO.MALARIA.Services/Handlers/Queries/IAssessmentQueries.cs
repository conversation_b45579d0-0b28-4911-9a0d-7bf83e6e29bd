﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// This interface contains methods related to assessment module
    /// </summary>
    public interface IAssessmentQueries
    {
        Task<ActionResult<GetAssessmentOutputDto>> GetAssessmentAsync(Guid assessmentId, Guid currentUserId);
        Task<QueryListResultDto<AssessmentListResultDto>> GetAssessmentsAsync(InputDtoBase inputDto, UserRoleEnum userType);
        Task<IEnumerable<ObjectiveDto>> GetAllObjectivesAsync();
        Task<string> GetAssessmentCountryAsync(Guid assessmentId);
        Task<IEnumerable<SubObjectiveDto>> GetSubObjectivesAsync(Guid objectiveId);
        Task<IEnumerable<IndicatorDto>> GetSubObjectiveIndicatorsAsync(Guid subObjectiveId);
        Task<IEnumerable<StrategyDto>> GetAllStrategiesAsync();
        Task<IEnumerable<IndicatorDto>> GetStrategyIndicatorsAsync(GetIndicatorsInputDto inputDto);
        Task<IEnumerable<AssessmentUserDto>> GetAllAssessmentUsersByCountryAsync(GetAllAssessmentUsersByCountryInputDto inputDto);
        Task<IEnumerable<StrategyDto>> GetAssessmentStrategiesAsync(Guid assessmentId);
        Task<IEnumerable<IndicatorDto>> GetScopeDefinitionAssessmentIndicatorsAsync(Guid assessmentId);
        Task<IEnumerable<IndicatorDto>> GetAssessmentIndicatorsForDeskReviewAsync(GetAssessmentIndicatorsInputDto inputDto);
        bool DoesAssessmentExist(Guid assessmentId);
        Task<DashboardAssessmentStatisticsDTO> GetDashboardAssessmentStatisticsAsync();
        Task<UserAssessmentPermissionDto> GetUserPermissionsOnAssessmentAsync(Guid assessmentId, Guid userId);
        JObject GetDeskReviewResponse(Guid assessmentIndicatorId, Guid assessmentStrategyId);
        Guid GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(Guid assessmentId, Guid indicatorId);
        Task<IEnumerable<DRVariableCheckListDto>> GetRecordedDRVariablesAsync(Guid strategyId);
        Task<IEnumerable<DRVariableCheckListDto>> GetReportedDRVariablesAsync(Guid strategyId);
        Task<IEnumerable<DRIndicatorCheckListDto>> GetDeskReviewIndicatorsChecklistAysnc(Guid strategyId);
        Task<ObjectiveDiagramDto> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId);
        Task<IEnumerable<SubObjectiveDiagramDto>> GetSubObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId);
        Task<DiagramsStatusDto> GetDiagramsStatusAsync(Guid assessmentId, Guid strategyId);
        Task<IEnumerable<QBAssessmentRespondentTypeDto>> GetRespondentTypesAsync(Guid assessmentId);      
    }
}
