﻿using MediatR;
using System.Threading.Tasks;
using System.Threading;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Database;
using Serilog;
using System.Reflection;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetUserByIdHandler : IRequestHandler<GetUserByIdQuery, UserDto>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;


        public GetUserByIdHandler(IMediator mediator, IUnitOfWork unitOfWork)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            // _logger = logger;
        }


        public async Task<UserDto> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
        {
            Log.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name, new Dictionary<string, dynamic> { { "UserId", request.UserId } });

            UserDto user = await _unitOfWork.UserRepository.GetUserByIdAsync(request.UserId);

            return user;
        }
    }
}
