﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for assessment survey questions
    /// </summary>
    public interface IAssessmentQuestionQueries
    {
        Task<IEnumerable<AssessmentQuestionDto>> GetAsync(Guid assessmentId);
        Task<ObjectivesSubObjectivesIndicatorsDto> GetObjectivesSubObjectivesIndicatorsAsync(Guid assessmentId);
        Task<List<FileResponseDto>> GenerateQuestionnairesAsync(Guid currentUserId, Guid assessmentId);
        Task<IEnumerable<HealthFacilityDto>> GetLatestHealthFacilitiesByCountryAsync(Guid countryId);
        Task<FileResponseDto> GetHealthFacilitiesTemplateFileResponseAsync(Guid userId, Guid countryId);
        Task<bool> HasHealthFacilityDataAsync(Guid currentUserId, Guid assessmentId);
        Task<FileResponseDto> GetHealthFacilitiesTemplateFileResponseForAssessmentAsync(Guid userId, Guid assessmentId, Guid countryId);
        string GetLatestHealthFacilitiesUploadedFilenameAsync(Guid countryId);
        Task<ShellTableReportDto> GetUploadedQuestionBankShellTableFileInformationAsync(Guid assessmentId);
    }
}
