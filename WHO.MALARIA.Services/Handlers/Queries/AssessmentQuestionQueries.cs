﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;
using Newtonsoft.Json;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.SeedingMetadata;
using System.IO;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for assessment survey questions
    /// </summary>
    public class AssessmentQuestionQueries : RuleBase, IAssessmentQuestionQueries
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IQuestionBank _questionBankDocumentManager;
        private readonly IShellTable _shellTableDocumentManager;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;

        public AssessmentQuestionQueries(IUnitOfWork unitOfWork, IQuestionBank questionBankDocumentManager, IShellTable shellTableDocumentManager, ITranslationService translationService, IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _questionBankDocumentManager = questionBankDocumentManager;
            _shellTableDocumentManager = shellTableDocumentManager;
            _translationService = translationService;
            _assesmentRuleChecker = assesmentRuleChecker;
        }

        #region Public Method

        /// <summary>
        /// Gets questions selected for the specific assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id for which question bank questions are to be fetched</param>
        /// <returns>List of questions</returns>
        public async Task<IEnumerable<AssessmentQuestionDto>> GetAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            return await _unitOfWork.AssessmentQuestionRepository.GetAsync(assessmentId);
        }

        /// <summary>
        /// Get collection of indicators, sub-objectives and objectives information that are associated with the question bank and assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id for which question bank objectives sub-objectives indicators are to be fetched</param>
        /// <returns>Collection of question bank indicators, sub-objectives and objectives</returns>
        public async Task<ObjectivesSubObjectivesIndicatorsDto> GetObjectivesSubObjectivesIndicatorsAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            ObjectivesSubObjectivesIndicatorsDto objectiveSubObjectiveIndicator = new ObjectivesSubObjectivesIndicatorsDto();

            IEnumerable<AssessmentQuestionObjectiveSubObjectiveIndicatorDto> assessmentQuestionSubObjectiveIndicators = await _unitOfWork.AssessmentQuestionRepository.GetObjectivesSubObjectivesIndicatorsAsync(assessmentId);

            objectiveSubObjectiveIndicator.Objectives = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.ObjectiveId, x.ObjectiveName, x.ObjectiveSequence })
                                                                                       .Select(x => new QuestionBankObjectiveDto
                                                                                       {
                                                                                           RespondentType = x.Key.RespondentType,
                                                                                           Id = x.Key.ObjectiveId,
                                                                                           Name = x.Key.ObjectiveName,
                                                                                           Sequence = x.Key.ObjectiveSequence
                                                                                       }).ToList();

            objectiveSubObjectiveIndicator.SubObjectives = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.SubObjectiveId, x.SubObjectiveName, x.SubObjectiveSequence, x.ObjectiveId })
                                                                                          .Select(x => new QuestionBankSubObjectiveDto
                                                                                          {
                                                                                              RespondentType = x.Key.RespondentType,
                                                                                              Id = x.Key.SubObjectiveId,
                                                                                              Name = x.Key.SubObjectiveName,
                                                                                              Sequence = x.Key.SubObjectiveSequence,
                                                                                              ObjectiveId = x.Key.ObjectiveId
                                                                                          }).ToList();

            objectiveSubObjectiveIndicator.Indicators = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.IndicatorId, x.IndicatorName, x.IndicatorSequence, x.SubObjectiveId })
                                                                                       .Select(x => new QuestionBankIndicatorDto
                                                                                       {
                                                                                           RespondentType = x.Key.RespondentType,
                                                                                           Id = x.Key.IndicatorId,
                                                                                           Name = x.Key.IndicatorName,
                                                                                           Sequence = x.Key.IndicatorSequence,
                                                                                           SubObjectiveId = x.Key.SubObjectiveId
                                                                                       }).ToList();

            return objectiveSubObjectiveIndicator;
        }

        /// <summary>
        /// Generate questionnaires and shell table template
        /// </summary>
        /// <param name="currentUserId">Current user id</param>  
        /// <param name="assessmentId">Assessment id for which question bank questions are to be fetched</param>  
        /// <returns>List of file download response with file byte[] and file name</returns>
        public async Task<List<FileResponseDto>> GenerateQuestionnairesAsync(Guid currentUserId, Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            CheckRule(new CanUserGenerateFile(_translationService, _assesmentRuleChecker, assessmentId, currentUserId, UserAssessmentPermission.CanGenerateQuestionBank, Constants.Exception.NoPermissionToGenerateQuestionBankTemplate));

            Assessment currentUser = await _unitOfWork.AssessmentRepository.Queryable(u => u.Id == assessmentId)
                                                                           .Include(u => u.Country)
                                                                           .SingleAsync();

            List<FileResponseDto> responses = new List<FileResponseDto>();

            IEnumerable<HealthFacilityDto> healthFacilities = await _unitOfWork.HealthFacilityRepository.GetLatestHealthFacilitiesByCountryAsync(currentUser.Country.Id);

            if (healthFacilities.Any())
            {
                IEnumerable<QBQuestionIndicatorSubObjectiveObjectiveDto> assessmentGenerateQuestionDtos = await _unitOfWork.AssessmentQuestionRepository.GetWithIndicatorsSubObjctivesObjectivesAsync(assessmentId);

                string countryName = currentUser.Country?.Name;

                IEnumerable<HealthFacilityDistrictDto> healthFacilitySubNationalData = null;

                DataTable healthFacilityDatatable = new DataTable();

                healthFacilityDatatable = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityDto), healthFacilities, "HealthFacility", null);

                // translate header columns it may be 6 to 7 column
                foreach (DataColumn column in healthFacilityDatatable.Columns)
                {
                    column.ColumnName = _translationService.GetTranslation(column.ColumnName);
                }

                QuestionDocumentInputModel questionDocumentInputModel = new QuestionDocumentInputModel();

                if (assessmentGenerateQuestionDtos.Any())
                {
                    questionDocumentInputModel.HasSelfAssessmentQuestions = assessmentGenerateQuestionDtos.FirstOrDefault().HasSelfAssessmentQuestions;

                    questionDocumentInputModel.RespondentTypes = assessmentGenerateQuestionDtos.GroupBy(x => x.RespondentType).Select(x => Convert.ToInt32(x.Key)).ToArray();

                    questionDocumentInputModel.Questions = assessmentGenerateQuestionDtos.Select(x => new Question
                    {
                        RespondentType = x.RespondentType,
                        ObjectiveId = x.ObjectiveId,
                        ObjectiveName = x.ObjectiveName,
                        ObjectiveSequence = x.ObjectiveSequence,
                        SubObjectiveId = x.SubObjectiveId,
                        SubObjectiveName = x.SubObjectiveName,
                        SubObjectiveSequence = x.SubObjectiveSequence,
                        IndicatorName = x.IndicatorName,
                        IndicatorSequence = x.IndicatorSequence,
                        QuestionName = x.Question,
                        Notes = x.Notes,
                        ResponseOption = GetResponseOption(x.IndicatorSequence, x.Code, x.RespondentType, x.ResponseOption, assessmentGenerateQuestionDtos),
                        ForSelfAssessment = x.ForSelfAssessment,
                        Code = x.Code,
                        ShellTableFileVersion = x.ShellTableFileVersion,
                        QuestionId = x.QuestionId,
                        StaticResponseOption = x.StaticResponseOption
                    });

                    questionDocumentInputModel.HealthFacilities = healthFacilityDatatable;

                    //For genrating question bank template we are using this response
                    FileResponseDto fileResponseDto = new FileResponseDto()
                    {
                        FileData = _questionBankDocumentManager.ProcessExcel(questionDocumentInputModel, _translationService.GetCurrentCulture()),
                        FileName = $"{Constants.DownloadDocument.QuestionBankTemplateFileName}_{countryName}.xlsx"
                    };

                    responses.Add(fileResponseDto);

                    IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings = await _unitOfWork.ShellTableRepository.GetQuestionsBankMappingAsync();

                    shellTableQuestionBankMappings.ToList().ForEach(q =>
                    {
                        q.IndicatorMapSetting = JsonConvert.DeserializeObject<IndicatorMapSetting>(q.ShellTableExcelTemplateSetting);
                    });

                    if (questionDocumentInputModel.RespondentTypes.Any(r => r == (int)QBRespondentType.SubnationalLevel))
                    {
                        var respondentType = ((QBRespondentType)Convert.ToInt32(QBRespondentType.SubnationalLevel)).GetTranslationKey();
                        var translatedRespondentType = _translationService.GetTranslation(respondentType);
                        healthFacilitySubNationalData = await _unitOfWork.HealthFacilityRepository.GetDistrictsByCountryAsync(currentUser.Country.Id, translatedRespondentType);
                    }

                    //Shell table template generate
                    questionDocumentInputModel.RespondentTypes.ToList().ForEach(respondentType =>
                    {
                        IEnumerable<Question> questions = questionDocumentInputModel.Questions.Where(q => q.RespondentType == respondentType);

                        DataTable dt = new DataTable();

                        if (respondentType == (int)QBRespondentType.SubnationalLevel)
                        {
                            dt = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityDistrictDto), healthFacilitySubNationalData, "HealthFacility", null);
                        }
                        else
                        {
                            List<HealthFacilityRespondentTypeDto> healthFacilityCommunityAndNationalData = healthFacilities.Select(x => new HealthFacilityRespondentTypeDto
                            {
                                CountryName = countryName,
                                Region = x.Region,
                                DistrictCode = x.DistrictCode,
                                DistrictName = x.DistrictName,
                                Name = x.Name,
                                Code = x.Code,
                                Type = x.Type,
                                RespondentType = _translationService.GetTranslation(((QBRespondentType)respondentType).GetTranslationKey())
                            }).ToList();

                            dt = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityRespondentTypeDto), healthFacilityCommunityAndNationalData, "HealthFacility", null);
                        }

                        int shellTableFileVersion = questions.FirstOrDefault().ShellTableFileVersion;

                        ShellTableTemplateInputModel shellTableTemplateInputModel = new ShellTableTemplateInputModel()
                        {
                            AssessmentId = assessmentId,
                            RespondentType = respondentType,
                            Questions = questions,
                            ShellTableFileVersion = shellTableFileVersion,
                            HealthFacilities = dt,
                            ShellTableQuestionBankMappings = shellTableQuestionBankMappings
                        };

                        FileResponseDto fileResponse = new FileResponseDto()
                        {
                            FileData = _shellTableDocumentManager.GenerateTemplate(shellTableTemplateInputModel, _translationService.GetCurrentCulture()),
                            FileName = $"{Constants.DownloadDocument.ShellTable}_{countryName}_{_translationService.GetTranslation(((QBRespondentType)respondentType).GetTranslationKey())}.xlsx"
                        };

                        responses.Add(fileResponse);
                    });
                }

            }

            return responses;
        }

        /// <summary>
        /// Check has health facility data
        /// </summary>
        /// <param name="currentUserId">Current user id</param>  
        /// <param name="assessmentId">Assessment id for which health facility data are to be fetched</param> 
        /// <returns>Return true or false</returns>
        public async Task<bool> HasHealthFacilityDataAsync(Guid currentUserId, Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return false;
            }

            Assessment currentUser = await _unitOfWork.AssessmentRepository.Queryable(u => u.Id == assessmentId)
                                                                           .Include(u => u.Country)
                                                                           .SingleAsync();

            bool hasHealthFacilities = _unitOfWork.HealthFacilityRepository.HasLatestHealthFacilitiesByCountryAsync(currentUser.Country.Id);

            return hasHealthFacilities;
        }

        /// <summary>
        /// Get latest health facility data
        /// </summary>
        /// <param name="countryId">Country id for which health facility data are to be fetched</param>
        /// <returns>List of latest health facility data for country</returns>
        public async Task<IEnumerable<HealthFacilityDto>> GetLatestHealthFacilitiesByCountryAsync(Guid countryId)
        {
            IEnumerable<HealthFacilityDto> healthFacilities = await _unitOfWork.HealthFacilityRepository.GetLatestHealthFacilitiesByCountryAsync(countryId);

            return healthFacilities;
        }

        /// <summary>
        /// Get latest health facility uploaded file name data
        /// </summary>
        /// <param name="countryId">Country id for which health facility data are to be fetched</param>
        /// <returns>Latest uploaded health facility file name</returns>
        public string GetLatestHealthFacilitiesUploadedFilenameAsync(Guid countryId)
        {
            return _unitOfWork.HealthFacilityRepository.GetLatestHealthFacilitiesUploadedFilenameAsync(countryId);
        }

        /// <summary>
        /// Get health facilities template file response
        /// </summary>
        /// <param name="userId">User id for which health facility data are to be fetched</param>
        /// <param name="countryId">Country id for which health facility data are to be fetched</param>
        /// <returns>File name and its content</returns>
        public async Task<FileResponseDto> GetHealthFacilitiesTemplateFileResponseAsync(Guid userId, Guid countryId)
        {
            IdAndNameDto superManagerCountry = _unitOfWork.UserCountryAccessRepository.GetCountryOfSuperManager(userId, countryId);

            IEnumerable<HealthFacilityDto> healthFacilities = await GetLatestHealthFacilitiesByCountryAsync(superManagerCountry.Id);

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityDto), healthFacilities, "HealthFacility", null);

            // translate header columns it may be 6 to 7 column
            foreach (DataColumn column in dt.Columns)
            {
                column.ColumnName = _translationService.GetTranslation(column.ColumnName);
            }

            FileResponseDto fileResponseDto = new FileResponseDto()
            {
                FileData = _questionBankDocumentManager.GenerateHealthFacilitiesTemplate(dt),
                FileName = $"{Constants.DownloadDocument.HealthFacilityFileName}"
            };

            return fileResponseDto;
        }

        /// <summary>
        /// Get health facilities template file response for assessment
        /// </summary>
        /// <param name="userId">Login user id</param>
        /// <param name="assessmentId">Assessment id for which associated with the assessment</param>
        /// <returns>File name and its content</returns>
        public async Task<FileResponseDto> GetHealthFacilitiesTemplateFileResponseForAssessmentAsync(Guid userId, Guid assessmentId,Guid countryId)
        {
            int assessmentStaus = await _unitOfWork.AssessmentRepository.GetAssessmentStatus(assessmentId);

            IEnumerable<HealthFacilityDto> healthFacilities = null;

            if (assessmentStaus == (int)Domain.Enum.AssessmentStatus.Published)
            {
                healthFacilities = await _unitOfWork.HealthFacilityRepository.GetLatestHealthFacilitiesByAssessmentAsync(assessmentId);
            }
            else
            {
                IdAndNameDto superManagerCountry = _unitOfWork.UserCountryAccessRepository.GetCountryOfSuperManager(userId, countryId);

                healthFacilities = await GetLatestHealthFacilitiesByCountryAsync(superManagerCountry.Id);
            }

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(HealthFacilityDto), healthFacilities, "HealthFacility", null);

            // translate header columns it may be 6 to 7 column
            foreach (DataColumn column in dt.Columns)
            {
                column.ColumnName = _translationService.GetTranslation(column.ColumnName);
            }

            FileResponseDto fileResponseDto = new FileResponseDto()
            {
                FileData = _questionBankDocumentManager.GenerateHealthFacilitiesTemplate(dt),
                FileName = $"{Constants.DownloadDocument.HealthFacilityFileName}"
            };

            return fileResponseDto;
        }       

        /// <summary>
        /// Get All uploaded shell table reports of Question Bank module
        /// </summary>
        /// <param name="assessmentId">GUID of an Assessment</param>
        /// <returns>List of Instance of ShellTableReport</returns>
        public async Task<ShellTableReportDto> GetUploadedQuestionBankShellTableFileInformationAsync(Guid assessmentId)
        {

            ShellTableReportDto shellDataReport = new ShellTableReportDto()
            {
                ReportFiles = await _unitOfWork.ShellTableReportRepository.GetUploadedQuestionBankShellTableFilesInfoAsync(assessmentId),
                ShowResultsOnGlobalDashboard = false
            };

            return shellDataReport;
        }
        #endregion

        #region Private methods
        /// <summary>
        /// Prepare response option 
        /// </summary>
        /// <param name="indicatorSequence">Sequence of indicator</param>
        /// <param name="code">Question code</param>
        /// <param name="respondentType">Type of respondent</param>
        /// <param name="responseOption">Response option captured for question</param>
        /// <param name="assessmentGenerateQuestionDtos">Assessment question object </param>
        /// <returns>Prepared response option</returns>
        private string GetResponseOption(string indicatorSequence, string code, byte respondentType, string responseOption, IEnumerable<QBQuestionIndicatorSubObjectiveObjectiveDto> assessmentGenerateQuestionDtos)
        {
            return (indicatorSequence == "2.2.2" && code == ShellTableQuestionBankMappingSeedingMetadata.IS_4) ? assessmentGenerateQuestionDtos.Where(x => x.IndicatorSequence == "2.2.1" && x.RespondentType == respondentType).FirstOrDefault().ResponseOption : responseOption;
        }
        #endregion
    }
}
