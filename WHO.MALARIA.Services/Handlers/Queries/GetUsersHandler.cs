﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using MediatR;
using Serilog;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetUsersHandler : IRequestHandler<GetUsersQuery, List<UserDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        // private readonly ILogger _logger;

        public GetUsersHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<UserDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
        {
           // _logger.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name);


            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetAllUsersAsync();

            return users.ToList();
        }
    }
}
