﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// This class contains all methods and business logic pertaining to user module
    /// </summary>
    public class UserQueries : RuleBase, IUserQueries
    {

        #region User Queries :- Private Variable
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly IUserCountryAccessRuleChecker _userCountryAccessRuleChecker;
        private readonly ITranslationService _translationService;
        #endregion

        #region User Queries :-  Constructor

        public UserQueries(IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IUserRuleChecker userRuleChecker,
            IUserCountryAccessRuleChecker userCountryAccessRuleChecker,
            ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _userCountryAccessRuleChecker = userCountryAccessRuleChecker;
            _translationService = translationService;
        }
        #endregion

        #region User Queries :- Public Implemented Methods

        /// <summary>
        /// Get Non-WHO users list 
        /// </summary>
        /// <param name="inputDto">Object of GetNonWHOUsersInputDto </param>
        /// <returns>List of users of type IEnumerable<UserDto></returns>
        public async Task<IEnumerable<UserDto>> GetNonWHOUsersAsync(GetNonWHOUsersInputDto inputDto)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, inputDto.CountryId, "CountryId"));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetNonWHOUsersAsync(inputDto);

            if (!users.Any())
            {
                throw new RecordNotFoundException(inputDto.CountryId, "CountryId");
            }

            return users;
        }

        /// <summary>
        /// Returns object with the list of user activation requests and user country access pending requests
        /// </summary>
        /// <param name="currentUserId">Id of the user calling the api</param>
        /// <returns>Returns PendingRequestsOutputDto object</returns>
        public async Task<PendingRequestsOutputDto> GetPendingRequestsAsync(Guid currentUserId, Guid countryId)
        {
            // Check Business Rules
            CheckRule(new UserShouldBeSuperManagerRule(_translationService, _userRuleChecker, currentUserId));

            PendingRequestsOutputDto pendingRequests = await _unitOfWork.UserRepository.GetPendingRequestsAsync(countryId);

            return pendingRequests;
        }

        /// <summary>
        /// Get user details by its' Id
        /// </summary>
        /// <param name="userId">Guid Parameter of User</param>
        /// <returns>Single Object of type UserDto</returns>
        public async Task<UserDto> GetUserByIdAsync(Guid userId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, userId, "userId"));

            UserDto user = await _unitOfWork.UserRepository.GetUserByIdAsync(userId);

            if (user == null)
            {
                throw new RecordNotFoundException(userId, "User");
            }

            return user;
        }

        /// <summary>
        /// Returns users for WHO requestor
        /// </summary>
        /// <param name="currentUserId">User Id of current user</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetUsersForWHORequesterAsync(Guid currentUserId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, currentUserId, "CurrentUserId"));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, currentUserId));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetUsersForWHORequesterAsync(currentUserId);

            return users;
        }

        /// <summary>
        /// Get newly registered in active viewers
        /// </summary>
        /// <param name="currentUserId">User Id of current user</param>
        /// <returns>List of newly registered users</returns>
        public async Task<IEnumerable<UserDto>> GetNewlyRegisteredInActiveViewersAsync(Guid currentUserId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, currentUserId, "CurrentUserId"));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, currentUserId));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetNewlyRegisteredInActiveViewersAsync(currentUserId);

            return users;
        }

        /// <summary>
        /// Returns users for SuperManager requestor
        /// </summary>
        /// <param name="currentUserId">User Id of current user</param>
        /// <param name="countryId">Country Id of current user</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetUsersForSuperManagerRequesterAsync(Guid currentUserId, Guid countryId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, currentUserId, "CurrentUserId"));
            CheckRule(new UserShouldBeSuperManagerRule(_translationService, _userRuleChecker, currentUserId));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetUsersForSuperManagerRequesterAsync(countryId);

            return users;
        }

        /// <summary>
        /// Returns all users of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetAllUsersOfCountryAsync(Guid countryId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, countryId, "Country"));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetAllUsersOfCountryAsync(countryId);

            return users;
        }

        /// <summary>
        /// Returns all managers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetCountryManagersAsync(Guid countryId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, countryId, "Country"));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetCountryManagersAsync(countryId);

            return users;
        }

        /// <summary>
        /// Returns all viewers of a country
        /// </summary>
        /// <param name="countryId">Id of country</param>
        /// <returns>Enumerable of UserDto objects</returns>
        public async Task<IEnumerable<UserDto>> GetCountryViewersAsync(Guid countryId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, countryId, "Country"));

            IEnumerable<UserDto> users = await _unitOfWork.UserRepository.GetCountryViewersAsync(countryId);

            return users;
        }

        /// <summary>
        /// Returns countries for login user
        /// </summary>
        /// <param name="currentUserIdentity">Identity object of current user</param>
        /// <returns>Enumerable of RetrieveMultipleRecordsDto objects</returns>
        public async Task<IEnumerable<IdAndNameDto>> GetUserCountriesAsync(WHOIdentity currentUserIdentity)
        {
            IEnumerable<IdAndNameDto> countries = await _unitOfWork.UserRepository.GetUserCountriesAsync(currentUserIdentity);

            return countries;
        }

        /// <summary>
        /// Returns countries which do not have any active super manager
        /// </summary>
        /// <returns>Enumerable of RetrieveMultipleRecordsDto objects</returns>
        public async Task<IEnumerable<IdAndNameDto>> GetCountriesWithoutSuperManager()
        {
            IEnumerable<IdAndNameDto> countries = await _unitOfWork.UserRepository.GetCountriesWithoutSuperManager();

            return countries;
        }

        /// <summary>
        /// Get assigned countries of the user
        /// </summary>
        /// <param name="userId">User id </param>
        /// <returns>List of active countries for the given user</returns>
        public async Task<IEnumerable<UserCountryDto>> GetAssignedCountriesOfUserAsync(Guid userId)
        {
            IEnumerable<UserCountryDto> userCountries = await _unitOfWork.UserCountryAccessRepository.GetAssignedCountriesOfUserAsync(userId);

            return userCountries;
        }

        /// <summary>
        /// Get user profile and countries details by its' user id
        /// </summary>
        /// <param name="userId">User id</param>
        /// <returns>User profile and countries detail</returns>
        public async Task<UserProfileCountryDetailDto> GetUserProfileAndCountryDetailsAsync(Guid userId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, userId, "userId"));

            UserProfileCountryDetailDto userProfileCountryDetail = new UserProfileCountryDetailDto();

            userProfileCountryDetail.UserProfileDetail = await _unitOfWork.UserRepository.GetUserByIdAsync(userId);

            userProfileCountryDetail.Countries = await _unitOfWork.UserRepository.GetUnassignedCountries(userId);

            return userProfileCountryDetail;
        }
        #endregion
    }
}
