﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for score card
    /// </summary>
    public interface IScoreCardQueries
    {
        Task<ObjectivesSubObjectivesIndicatorsDetailsDto> GetScoreCardDetailsAsync(Guid assessmentId);
        Task<FileResponseDto> GetScoreCardExcelFileResponse(Guid assessmentId,Guid currentUserId);
        Task<bool> CanScoreCardBeGeneratedAsync(Guid assessmentId);
        Task<bool> HasScoreCardDataAsync(Guid assessmentId);
    }
}