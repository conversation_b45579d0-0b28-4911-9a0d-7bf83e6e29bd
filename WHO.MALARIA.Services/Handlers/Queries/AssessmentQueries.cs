﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Services.BusinessRuleValidations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// This class contains methods related to assessment module
    /// </summary>
    public class AssessmentQueries : RuleBase, IAssessmentQueries
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly ITranslationService _translationService;

        public AssessmentQueries(IUnitOfWork unitOfWork, ICommonRuleChecker commonRuleChecker, ITranslationService translationService)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _translationService = translationService;
        }

        /// <summary>
        /// Get assessment using assessment id
        /// </summary>
        /// <param name="assessmentId"> Id of assessment </param>
        /// <param name="currentUserId">User id of current user</param>
        /// <returns> Assessment object </returns>
        public async Task<ActionResult<GetAssessmentOutputDto>> GetAssessmentAsync(Guid assessmentId, Guid currentUserId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, assessmentId, "AssessmentId"));
            GetAssessmentOutputDto record = await _unitOfWork.AssessmentRepository.GetAssessment(assessmentId, currentUserId);

            return record;
        }

        /// <summary>
        /// Fetch country name associated with assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id for which associated with the assessment</param>       
        /// <returns>Country name</returns>
        public async Task<string> GetAssessmentCountryAsync(Guid assessmentId)
        {
            return await _unitOfWork.AssessmentRepository.GetAssessmentCountryAsync(assessmentId);            
        }

        /// <summary>
        /// Get paginated list of assessments according to filter/sort criterias
        /// </summary>
        /// <param name="inputDto">Input base object for list returning</param> 
        /// <param name="userType">User type</param>
        /// /// <returns> List of assessments </returns>
        public async Task<QueryListResultDto<AssessmentListResultDto>> GetAssessmentsAsync(InputDtoBase inputDto, UserRoleEnum userType)
        {
            CheckRule(new ValidFilterCriteriaRule(inputDto.FilterCriterias));
            QueryListResultDto<AssessmentListResultDto> records = await _unitOfWork.AssessmentRepository.GetAssessments(inputDto, userType);

            return records;
        }

        /// <summary>
        /// Get indicators based on the selected strategies
        /// </summary>
        /// <param name="inputDto">Object of GetIndicatorsInputDto</param>
        /// <returns>List of indicators</returns>
        public async Task<IEnumerable<IndicatorDto>> GetStrategyIndicatorsAsync(GetIndicatorsInputDto inputDto)
        {
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, inputDto.CaseStrategyIds, "CaseStrategyIds"));

            IEnumerable<IndicatorDto> records = await _unitOfWork.AssessmentRepository.GetStrategyIndicatorsAsync(inputDto);


            // This block will be executed only in case of elimination and other strategy selected
            if (inputDto.CaseStrategyIds != null && inputDto.CaseStrategyIds.All(x => x == StrategySeedingMetadata.ELIMINATION_ID) && inputDto.MalariaControlStrategyIds != null && inputDto.MalariaControlStrategyIds.Any())
            {
                IndicatorDto indicator_3_2_1_record = records.Where(x => x.Id == IndicatorSeedingMetadata.IND_3_2_1).SingleOrDefault();
                if (indicator_3_2_1_record != null)
                {
                    indicator_3_2_1_record.IndicatorPriority = 1;
                }

                IndicatorDto indicator_3_2_2_record = records.Where(x => x.Id == IndicatorSeedingMetadata.IND_3_2_2).SingleOrDefault();
                if (indicator_3_2_2_record != null)
                {
                    indicator_3_2_2_record.IndicatorPriority = 1;
                }
            }

            return records;
        }
        /// <summary>
        /// Responsible to serve list of all users belonging to the assessment 
        /// </summary>
        /// <param name="inputDto">An object of GetAllAssessmentUsersByCountryInputDto</param>
        /// <returns>List of users of type IEnumerable<AssessmentUserDto> </returns>
        public async Task<IEnumerable<AssessmentUserDto>> GetAllAssessmentUsersByCountryAsync(GetAllAssessmentUsersByCountryInputDto inputDto)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, inputDto.UserId, "UserId"));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, inputDto.CountryId, "CountryId"));

            IEnumerable<AssessmentUserDto> records = await _unitOfWork.AssessmentRepository.GetAllAssessmentUsersByCountryAsync(inputDto);

            return records;
        }

        /// <summary>
        /// Get all objectives
        /// </summary>
        /// <returns>List of objectives</returns>
        public async Task<IEnumerable<ObjectiveDto>> GetAllObjectivesAsync()
        {
            return await _unitOfWork.AssessmentRepository.GetAllObjectivesAsync();
        }

        /// <summary>
        /// Get sub-objectives for an objective
        /// </summary>
        /// <param name="objectiveId">Objective Id for which sub-objectives have to be loaded</param>
        /// <returns>List of sub-objectives</returns>
        public async Task<IEnumerable<SubObjectiveDto>> GetSubObjectivesAsync(Guid objectiveId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, objectiveId, "ObjectiveId"));

            return await _unitOfWork.AssessmentRepository.GetSubObjectivesAsync(objectiveId);
        }

        /// <summary>
        /// Get indicators for a sub-objectives
        /// </summary>
        /// <param name="subObjectiveId">SubObjective Id for which indicators have to be loaded</param>
        /// <returns>List of indicators</returns>
        public async Task<IEnumerable<IndicatorDto>> GetSubObjectiveIndicatorsAsync(Guid subObjectiveId)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, subObjectiveId, "SubObjectiveId"));

            return await _unitOfWork.AssessmentRepository.GetSubObjectiveIndicatorsAsync(subObjectiveId);
        }

        /// <summary>
        /// Check if assessment exist or not for assessment id
        /// </summary>
        /// <param name="assessmentId"> Id of assessment </param>
        /// <returns> True if assessment exist else false </returns>
        public bool DoesAssessmentExist(Guid assessmentId)
        {
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, assessmentId, "AssessmentId"));
            return _unitOfWork.AssessmentRepository.Queryable(x => x.Id == assessmentId).Any();
        }

        /// <summary>
        /// Get all strategies
        /// </summary>
        /// <returns>List of strategies</returns>
        public async Task<IEnumerable<StrategyDto>> GetAllStrategiesAsync()
        {
            return await _unitOfWork.AssessmentRepository.GetAllStrategiesAsync();
        }

        /// <summary>
        /// Get selected strategies of an assessment
        /// </summary>
        /// <param name="assessmentId">Assessment Id for which indicators have to be loaded</param>
        /// <returns>List of strategies</returns>
        public async Task<IEnumerable<StrategyDto>> GetAssessmentStrategiesAsync(Guid assessmentId)
        {
            return await _unitOfWork.AssessmentRepository.GetAssessmentStrategiesAsync(assessmentId);
        }

        /// <summary>
        /// Get selected indicators for an assessment for scope definition
        /// </summary>
        /// <param name="inputDto">GetAssessmentIndicatorsInputDto object with input values</param>
        /// <returns>List of indicators</returns>
        public async Task<IEnumerable<IndicatorDto>> GetScopeDefinitionAssessmentIndicatorsAsync(Guid assessmentId)
        {
            return await _unitOfWork.AssessmentRepository.GetScopeDefinitionAssessmentIndicatorsAsync(assessmentId);
        }

        /// <summary>
        /// Get selected indicators for an assessment for data collection
        /// </summary>
        /// <param name="inputDto">GetAssessmentIndicatorsInputDto object with input values</param>
        /// <returns>List of indicators</returns>
        public async Task<IEnumerable<IndicatorDto>> GetAssessmentIndicatorsForDeskReviewAsync(GetAssessmentIndicatorsInputDto inputDto)
        {
            return await _unitOfWork.AssessmentRepository.GetAssessmentIndicatorsForDeskReviewAsync(inputDto);
        }

        /// <summary>
        /// Returns the different properties with assessment details to show on dashboard 
        /// </summary>
        /// <returns>DashboardAssessmentPropertiesDTO object.</returns>
        public async Task<DashboardAssessmentStatisticsDTO> GetDashboardAssessmentStatisticsAsync()
        {
            return await _unitOfWork.AssessmentRepository.GetDashboardAssessmentStatisticsAsync();
        }

        /// <summary>
        /// Returns user permissions on assessment
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <param name="userid">User Id whose permissions have to be fetched</param>
        /// <returns>UserAssessmentPermissionDto object</returns>
        public async Task<UserAssessmentPermissionDto> GetUserPermissionsOnAssessmentAsync(Guid assessmentId, Guid userId)
        {
            return await _unitOfWork.AssessmentRepository.GetUserPermissionsOnAssessmentAsync(assessmentId, userId);
        }

        /// <summary>
        /// Get response json for the indicator and strategy
        /// </summary>
        /// <param name="assessmentIndicatorId">Indicator id associated with the assessment</param>
        /// <param name="assessmentStrategyId">Strategy id associated with the assessment</param>
        /// <returns>JSON object</returns>
        public JObject GetDeskReviewResponse(Guid assessmentIndicatorId, Guid assessmentStrategyId)
        {
            return _unitOfWork.AssessmentDRResponseRepository.GetResponse(assessmentIndicatorId, assessmentStrategyId);
        }

        /// <summary>
        /// Get assessment indicator id based on assessmentId and indicatorId
        /// </summary>
        /// <param name="assessmentId">current assessmentId</param>
        /// <param name="indicatorId">indicatorId for which we want assessmentIndicatorId</param>
        /// <returns>Returns assessmentIndicatorId based on assessmentId and indicatorId</returns>
        public Guid GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(Guid assessmentId, Guid indicatorId)
        {
            return _unitOfWork.AssessmentDRResponseRepository.GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(assessmentId, indicatorId);
        }

        /// <summary>
        /// Returns desk review variables for particular strategy where recorded is true
        /// </summary>
        /// <param name="strategyId">Strategy id for which variables are to be fetched </param>
        /// <returns> List of DRVariableCheckListDto object</returns>
        public async Task<IEnumerable<DRVariableCheckListDto>> GetRecordedDRVariablesAsync(Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetRecordedDRVariablesAsync(strategyId);
        }

        /// <summary>
        /// Returns desk review variables for particular strategy where reported is true
        /// </summary>
        /// <param name="strategyId">Strategy id for which variables are to be fetched </param>
        /// <returns> List of DRVariableCheckListDto object</returns>
        public async Task<IEnumerable<DRVariableCheckListDto>> GetReportedDRVariablesAsync(Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetReportedDRVariablesAsync(strategyId);
        }


        /// <summary>
        /// Get desk review indicators checklist for particular strategy
        /// </summary>
        /// <param name="strategyId">Strategy id for which indicators are to be fetched</param>
        /// <returns>List of indicators checklist</returns>
        public async Task<IEnumerable<DRIndicatorCheckListDto>> GetDeskReviewIndicatorsChecklistAysnc(Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetDeskReviewIndicatorsChecklistAsync(strategyId);
        }

        /// <summary>
        /// Fetch objective diagrams based on the assessmentId and strategyId
        /// </summary>
        /// <param name="assessmentId">Required to fetch association of assessment with the strategy</param>
        /// <param name="strategyId">Required to fetch the association of strategy with the assessment</param>
        /// <returns>Objective diagrams and other details</returns>
        public async Task<ObjectiveDiagramDto> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetObjectiveDiagramsAsync(assessmentId, strategyId);
        }

        /// <summary>
        /// Fetch sub objective diagrams based on the assessmentId and strategyId
        /// </summary>
        /// <param name="assessmentId">Required to fetch association of assessment with the strategy</param>
        /// <param name="strategyId">Required to fetch the association of strategy with the assessment</param>
        /// <returns>Sub objective diagrams</returns>
        public async Task<IEnumerable<SubObjectiveDiagramDto>> GetSubObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetSubObjectiveDiagramsAsync(assessmentId, strategyId);
        }

        /// <summary>
        /// Fetch diagrams status based on the assessmentId and strategyId
        /// </summary>
        /// <param name="assessmentId">Required to fetch association of assessment with the strategy</param>
        /// <param name="strategyId">Required to fetch the association of strategy with the assessment</param>
        /// <returns>Object of diagrams status</returns>
        public async Task<DiagramsStatusDto> GetDiagramsStatusAsync(Guid assessmentId, Guid strategyId)
        {
            return await _unitOfWork.AssessmentRepository.GetDiagramsStatusAsync(assessmentId, strategyId);
        }

        /// <summary>
        /// Gets respondent types selected for the specific assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id for which Question Bank respondent type are to be fetched</param>
        /// <returns>List of respondent types</returns>
        public async Task<IEnumerable<QBAssessmentRespondentTypeDto>> GetRespondentTypesAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            return await _unitOfWork.AssessmentRespondentTypeRepository.Get(assessmentId);
        }
    }
}
