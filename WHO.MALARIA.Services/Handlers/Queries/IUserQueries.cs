﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to user module
    /// </summary>
    public interface IUserQueries
    {            
        Task<PendingRequestsOutputDto> GetPendingRequestsAsync(Guid currentUserId, Guid countryId);
        Task<IEnumerable<UserDto>> GetNonWHOUsersAsync(GetNonWHOUsersInputDto inputDto);
        Task<UserDto> GetUserByIdAsync(Guid id);      
        Task<IEnumerable<UserDto>> GetUsersForWHORequesterAsync(Guid currentUserId);
        Task<IEnumerable<UserDto>> GetUsersForSuperManagerRequesterAsync(Guid userId, Guid countryId);
        Task<IEnumerable<UserDto>> GetAllUsersOfCountryAsync(Guid countryId);
        Task<IEnumerable<UserDto>> GetCountryManagersAsync(Guid countryId);
        Task<IEnumerable<UserDto>> GetCountryViewersAsync(Guid countryId);
        Task<IEnumerable<IdAndNameDto>> GetUserCountriesAsync(WHOIdentity currentUserIdentity);
        Task<IEnumerable<IdAndNameDto>> GetCountriesWithoutSuperManager();
        Task<IEnumerable<UserCountryDto>> GetAssignedCountriesOfUserAsync(Guid userId);
        Task<UserProfileCountryDetailDto> GetUserProfileAndCountryDetailsAsync(Guid userId);
        Task<IEnumerable<UserDto>> GetNewlyRegisteredInActiveViewersAsync(Guid currentUserId);
    }
}
