﻿using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using WHO.MALARIA.Database;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Services.BusinessRuleValidations;
using Serilog;
using System.Reflection;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetIdentitiesHandler : RuleBase, IRequestHandler<GetIdentityQuery, List<IdentityDto>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetIdentitiesHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get users based on filter
        /// </summary>
        /// <param name="request">Object of GetIdentityQuery</param>
        /// <param name="cancellationToken">Object of CancellationToken</param>
        /// <returns>Returns users based on filter criteria</returns>
        public async Task<List<IdentityDto>> Handle(GetIdentityQuery request, CancellationToken cancellationToken)
        {
            Log.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name,
               new Dictionary<string, dynamic> { { "FilterCriterias", request.FilterCriterias } });

            CheckRule(new ValidFilterCriteriaRule(request.FilterCriterias));

            IEnumerable<IdentityDto> identities = await _unitOfWork.IdentityRepository.GetAllIdentitiesWithFilterCriteria(request.FilterCriterias);

            return identities.ToList();
        }

    }
}
