﻿using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Database.Repositories;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Services.Dxos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetEntityMultipleRecordsHandler : IRequestHandler<GetEntityMultipleRecordsQuery, IEnumerable<IdAndNameDto>>
    {

        private readonly ICountryRepository _countryRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserRepository _userRepository;
             
        private readonly IRetrieveMultipleRecordsDxos<IEnumerable<Country>, IEnumerable<IdAndNameDto>> _retrieveMultipleRecordsDxos;

        public GetEntityMultipleRecordsHandler(ICountryRepository countryRepository, IServiceProvider serviceProvider,
            IRetrieveMultipleRecordsDxos<IEnumerable<Country>, IEnumerable<IdAndNameDto>> retrieveMultipleRecordsDxos,
            IUserRepository userRepository)
        {
            _countryRepository = countryRepository;
            _serviceProvider = serviceProvider;
            _retrieveMultipleRecordsDxos = retrieveMultipleRecordsDxos;
            _userRepository = userRepository;
        }

        public async Task<IEnumerable<IdAndNameDto>> Handle(GetEntityMultipleRecordsQuery request, CancellationToken cancellationToken)
        {
            IEnumerable<IdAndNameDto> records = Enumerable.Empty<IdAndNameDto>();
            switch (request.Entity.ToLower())
            {
                case "country":
                    IEnumerable<Country> countries = await _countryRepository.GetListAsync(country => country.IsActive);
                    records = countries.Select((country) => new IdAndNameDto { Id = country.Id, Name = country.Name }).OrderBy(c => c.Name);

                    return records;

                case "user":
                    IEnumerable<UserDto> users = await _userRepository.GetAllUsersAsync(true);
                    records = users.Select((user) => new IdAndNameDto { Id = user.Id, Name = user.Name }).OrderBy(c => c.Name);

                    return records;
            }

            return Enumerable.Empty<IdAndNameDto>();
        }



    }
}
