﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to Data Analysis Reports
    /// </summary>
    public class DataAnalysisReportQueries : RuleBase, IDataAnalysisReportQueries
    {
        private readonly IUnitOfWork _unitOfWork;

        public DataAnalysisReportQueries(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get All uploaded reports
        /// </summary>
        /// <param name="assessmentId">GUID of an Assessment</param>
        /// <returns>List of Instance of DataAnalysisReport</returns>
        public async Task<DataAnalysisReportDto> GetReportsAsync(Guid assessmentId)
        {
            Assessment assessment = await _unitOfWork.AssessmentRepository.GetAsync(x => x.Id == assessmentId);

            bool isAssessmentPublished = await _unitOfWork.AssessmentStatusRepository.HasStatusAsync(assessmentId, (int)Domain.Enum.AssessmentStatus.Published);

            IEnumerable<DataAnalysisReport> reports = await _unitOfWork.DataAnalysisReportRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            List<DataAnalysisReportFile> analysisReports = reports.Select(x => new DataAnalysisReportFile { Id = x.Id, AssessmentId = x.AssessmentId, Name = x.Name, Size = x.Size, UpdatedAt=x.UpdatedAt }).OrderByDescending(x=> x.UpdatedAt).ToList();

            DataAnalysisReportDto dataAnalysisReport = new DataAnalysisReportDto()
            {
                ReportFiles = analysisReports,
                IsAssessmentPublished = isAssessmentPublished,
                ShowResultsOnGlobalDashboard = assessment.ShowResultsOnGlobalDashboard
            };

            return dataAnalysisReport;
        }

        /// <summary>
        /// Get Data Analysis Report
        /// </summary>
        /// <param name="assessmentId">GUID of an Assessment</param>
        /// <returns>Instance of DataAnalysisReport</returns>
        public async Task<DataAnalysisReport> GetReportDocumentAsync(Guid reportId)
        {
            DataAnalysisReport report = await _unitOfWork.DataAnalysisReportRepository.GetAsync(x => x.Id == reportId);

            return report;
        }
    }
}
