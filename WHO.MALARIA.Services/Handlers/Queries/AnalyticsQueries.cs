﻿using Google.Apis.AnalyticsReporting.v4;
using Google.Apis.AnalyticsReporting.v4.Data;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// This class contains all methods and business logic pertaining to analytics
    /// </summary>
    public class AnalyticsQueries : RuleBase, IAnalyticsQueries
    {
        #region Analytics Queries :- Private Variable
        private readonly AppSettings _appSettings;
        private readonly ITranslationService _translationService;
        #endregion

        #region Analytics Queries :-  Constructor
        public AnalyticsQueries(AppSettings appSettings, ITranslationService translationService)
        {
            _appSettings = appSettings;
            _translationService = translationService;
        }
        #endregion

        /// <summary>
        /// Returns list of google analytics data
        /// </summary>
        /// <param name="command">command contains startdate and enddate </param>
        /// <returns>Returns list of google analytics data</returns>
        public async Task<List<GetGoogleAnalyticsData>> GetGoogleAnalyticsResponseData(GetUserAnalyticsDataCommand command)
        {
            CheckRule(new EndDateShouldBeGreaterThanStartDateRule(_translationService, command.StartDate, command.EndDate));

            List<GetGoogleAnalyticsData> googleAnalyticsData = new List<GetGoogleAnalyticsData>();

            string privateKey= Regex.Replace(_appSettings.AnalyticsFields.PrivateKey,@"\\n","");

            Log.Information("Private key - " + privateKey);

            if (privateKey != null)
            {
                var xCred = new ServiceAccountCredential(new ServiceAccountCredential.Initializer(_appSettings.AnalyticsFields.ClientEmail)
                {
                    Scopes = new[] { AnalyticsReportingService.Scope.Analytics }
                }.FromPrivateKey(privateKey));

                using (var svc = new AnalyticsReportingService(new BaseClientService.Initializer { HttpClientInitializer = xCred, ApplicationName = "Malaria Surveillance Assessment Toolkit" }))
                {
                    // Create the DateRange object.
                    DateRange dateRange = new DateRange()
                    {
                        StartDate = command.StartDate.Date.ToString("yyyy-MM-dd"),
                        EndDate = command.EndDate.ToString("yyyy-MM-dd")
                    };

                    // Create the Metrics object.
                    Metric sessions = new Metric { Expression = "ga:users" };

                    //Create the Dimensions object.
                    Dimension eventCategory = new Dimension { Name = "ga:EventCategory" };
                    Dimension eventAction = new Dimension { Name = "ga:EventAction" };
                    Dimension pageTitle = new Dimension { Name = "ga:pageTitle" };
                    Dimension userType = new Dimension { Name = "ga:userType" };
                    Dimension city = new Dimension { Name = "ga:city" };
                    Dimension date = new Dimension { Name = "ga:Date" };

                    // Create the ReportRequest object.
                    ReportRequest reportRequest = new ReportRequest
                    {
                        ViewId = _appSettings.AnalyticsFields.WebsiteCode,
                        DateRanges = new List<DateRange>() { dateRange },
                        Dimensions = new List<Dimension>() { eventCategory, eventAction, pageTitle, userType, city, date },
                        Metrics = new List<Metric>() { sessions }
                    };

                    List<ReportRequest> requests = new List<ReportRequest>();

                    requests.Add(reportRequest);

                    GetReportsRequest getReport = new GetReportsRequest() { ReportRequests = requests };

                    GetReportsResponse response = svc.Reports.BatchGet(getReport).Execute();

                    if (response != null && response.Reports.First().Data.Rows != null)
                    {
                        response.Reports.First().Data.Rows.ToList().ForEach(row =>
                        {
                            GetGoogleAnalyticsData googleAnalytics = new GetGoogleAnalyticsData()
                            {
                                EventCategory = row.Dimensions[0],
                                EventAction = row.Dimensions[1],
                                PageTitle = row.Dimensions[2],
                                UserType = row.Dimensions[3],
                                City = row.Dimensions[4]
                            };

                            googleAnalytics.ActiveUsersCount = Convert.ToInt32(row.Metrics[0].Values[0]);

                            googleAnalytics.Date = DateTime.ParseExact(row.Dimensions[5], "yyyyMMdd", CultureInfo.InvariantCulture);

                            googleAnalyticsData.Add(googleAnalytics);
                        });
                    }
                }
            }
            return googleAnalyticsData;
        }
    }
}
