﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to country dashboard
    /// </summary>
    public class DashboardQueries : RuleBase, IDashboardQueries
    {
        #region DashboardQueries Queries :- Private Variable        
        private readonly IUnitOfWork _unitOfWork;
        private readonly IScoreCardQueries _scoreCardQueries;
        private readonly ICacheDataService _cacheService;
        #endregion

        public DashboardQueries(
            IUnitOfWork unitOfWork,
            IScoreCardQueries scoreCardQueries,
             ICacheDataService cacheService)
        {
            _unitOfWork = unitOfWork;
            _scoreCardQueries = scoreCardQueries;
            _cacheService = cacheService;
        }

        #region DashboardQueries Queries :- Public methods        

        /// <summary>
        /// Get all eligible records for dashboard
        /// </summary>
        /// <param name="userId">User Id of logged in user</param>
        /// <param name="countryId">Country Id of logged in user</param>
        /// <param name="year">Year of an Assessment</param>
        /// <returns>List of Instance of DashboardResultDto</returns>
        public async Task<DashboardResultDto> GetDashboardRecordsAsync(Guid userId, Guid countryId, int year, bool checkForUserAceess = true)
        {
            DashboardResultDto result = new DashboardResultDto();

            UserCountryAccess userCountryAccess = await _unitOfWork.UserCountryAccessRepository.GetAsync(x => x.UserId == userId && x.CountryId == countryId);

            if (!checkForUserAceess || userCountryAccess != null && userCountryAccess.Status == (int)UserCountryAccessRightsEnum.Accepted)
            {
                Assessment assessment = await _unitOfWork.AssessmentRepository.GetAsync(x => x.CountryId == countryId && x.StartDate.Year == year);

                if (assessment != null)
                {
                    ObjectivesSubObjectivesIndicatorsDetailsDto scoreData = await _scoreCardQueries.GetScoreCardDetailsAsync(assessment.Id);

                    result.Year = year;

                    result.Objectives = scoreData.Objectives.Select(o => new ObjectiveResultDto
                    {
                        Id = o.Id,
                        Name = o.Name,
                        Percentage = o.NationalScorePercentage,
                        SubObjective = scoreData.SubObjectives.Where(s => s.ObjectiveId == o.Id).Select(x => new SubObjectiveResultDto
                        {
                            Id = x.Id,
                            ObjectiveId = x.ObjectiveId,
                            Name = x.Name.RemoveText("("),
                            Percentage = x.IsNotAssessed ? 100 : x.NationalScorePercentage,
                            IsNotAssessed = x.IsNotAssessed
                        }).ToList()

                    }).ToList();
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the assessment years
        /// </summary>
        /// <param name="userId">User ID of logged in user</param>
        /// <param name="countryId">Country ID of logged in user</param>
        /// <returns>List of year</returns>
        public async Task<IEnumerable<int>> GetYearsAsync(Guid userId, Guid countryId)
        {
            UserCountryAccess userCountryAccess = await _unitOfWork.UserCountryAccessRepository.GetAsync(x => x.UserId == userId && x.CountryId == countryId);

            if (userCountryAccess != null && userCountryAccess.Status == (int)UserCountryAccessRightsEnum.Accepted)
            {
                IEnumerable<int> years = await _unitOfWork.AssessmentRepository.GetYearOfPublishedAssessments(countryId);

                years = years.GroupBy(x => x).Select(x => x.Key).ToList();

                return years;
            }

            return Enumerable.Empty<int>();
        }

        /// <summary>
        /// Get regional summary details for global dashboard
        /// </summary>
        /// <returns>Regional summary details for global dashboard</returns>
        public async Task<GlobalDashboardRegionalSummaryDto> GetRegionalSummaryDetailsAsync(string language)
        {
            GlobalDashboardRegionalSummaryDto globalDashboardRegionalSummary = new GlobalDashboardRegionalSummaryDto();

            GlobalDashboardRegionalSummaryDto globalDashboardRegionalSummaryCache = (GlobalDashboardRegionalSummaryDto)_cacheService.GetDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardRegionalSummaryResponseJson + language);

            if (globalDashboardRegionalSummaryCache != null)
            {
                //Commented for resolving cache issue
                // return globalDashboardRegionalSummaryCache;
            }

            globalDashboardRegionalSummary.Type = (int)GlobalDashboardGraphType.WhoRegionalSummary;

            List<RegionalSummaryDto> listOfRegionalSummary = new List<RegionalSummaryDto>();

            IEnumerable<RegionDto> regions = await _unitOfWork.RegionRepository.GetAllAsync();

            IEnumerable<Country> countries = await _unitOfWork.CountryRepository.GetListAsync(c => c.IsActive == true);

            IEnumerable<PriorityIndicatorDetailsDto> priorityIndicatorDetails = await _unitOfWork.IndicatorRepository.GetPriorityIndicatorsDetailsAsync();

            IEnumerable<PublishAssessmentsDto> publishedAssessments = await _unitOfWork.AssessmentRepository.GetPublishedAssessmentDetailsAsync();

            IEnumerable<IndicatorSubObjectiveObjectiveDto> deskReviewindicatorDetails = await _unitOfWork.AssessmentRepository.GetPriorityPublishedIndicatorsAsync();

            IEnumerable<IndicatorSubObjectiveObjectiveDto> scoreCardIndicatorDetails = await _unitOfWork.ScoreCardRepository.GetScoreCardIndicatorsDetailsAsync();

            IEnumerable<DQAEliminationSummaryDto> eliminationSummaryDetails = await _unitOfWork.DQARepository.GetEliminationSummaryForPublishedAssessmentsAsync();

            IEnumerable<DQADeskLevelSummaryDto> deskLevelSummaryDetails = await _unitOfWork.DQARepository.GetAllDeskLevelSummariesAsync();

            IEnumerable<DQAServiceLevelSummaryDto> serviceLevelSummaryDetails = await _unitOfWork.ServiceLevelRepository.GetServiceLevelVariableSummaryForPublishedAssessmentAsync();

            IEnumerable<PriorityIndicatorDetailsDto> assessmentPriorityIndicatorDetails = await _unitOfWork.IndicatorRepository.GetAssessmentPriorityIndicatorsDetailsAsync();

            IEnumerable<IndicatorSubObjectiveObjectiveDto> dqaPriorityIndicators = await _unitOfWork.DQARepository.GetPriorityIndicatorsAsync(DQAType.DeskLevel);

            IEnumerable<IndicatorSubObjectiveObjectiveDto> dqaServiceDeliveryPriorityIndicators = await _unitOfWork.DQARepository.GetPriorityIndicatorsAsync(DQAType.ServiceDeliveryLevel);

            IEnumerable<AssessmentStrategyDto> assessmentStrategies = await _unitOfWork.AssessmentRepository.GetPublishedAssessmentStrategiesAsync();

            int regionCount = 0;

            regions.ToList().ForEach(region =>
            {
                regionCount = regionCount + 1;

                RegionalSummaryDto regionalSummary = new RegionalSummaryDto();

                List<RegionalSummaryMetNotMetStatuses> regionalSummaryMetNotMetIndicatorStatuses = new List<RegionalSummaryMetNotMetStatuses>();

                List<MetNotMetStatuses> indicatorsMetNotMetStatuses = new List<MetNotMetStatuses>();

                RegionalSummaryMetNotMetStatuses regionalSummaryMetNotMetStatuses = new RegionalSummaryMetNotMetStatuses();

                List<double> metIndicatorsCounts = new List<double>();
                List<double> notMetIndicatorsCounts = new List<double>();
                List<double> notAssessedIndicatorsCounts = new List<double>();
                List<double> partiallyMetIndictaorsCounts = new List<double>();

                List<SubObjectiveResultDto> metIndicatorsForSubObjective = new List<SubObjectiveResultDto>();
                List<SubObjectiveResultDto> notMetIndicatorsForSubObjective = new List<SubObjectiveResultDto>();
                List<SubObjectiveResultDto> partiallyMetIndicatorsForSubObjective = new List<SubObjectiveResultDto>();
                List<SubObjectiveResultDto> notAssessedIndicatorsForSubObjective = new List<SubObjectiveResultDto>();

                regionalSummary.TabId = regionCount;
                regionalSummary.TabName = region.Code;

                List<Guid> regionalCountryIds = countries.Where(c => c.RegionId == region.Id).Select(c => c.Id).ToList();

                List<RegionalSummarySubObjectiveDetailsDto> categories = priorityIndicatorDetails.GroupBy(x => new
                {
                    x.SubObjectiveId,
                    x.SubObjectiveName,
                    x.SubObjectiveSequence,
                    x.ObjectiveId

                }).Select(x => new RegionalSummarySubObjectiveDetailsDto
                {
                    Id = x.Key.SubObjectiveId,
                    Name = x.Key.SubObjectiveName.RemoveText("("),
                    Sequence = x.Key.SubObjectiveSequence,
                    ObjectiveId = x.Key.ObjectiveId
                }).ToList();

                regionalSummaryMetNotMetStatuses.Categories = categories.Select(d => $"{d.Sequence} {d.Name}").ToList();

                regionalSummaryMetNotMetStatuses.Title = language == "en" ? "Regional Summary graph" : "Graphique récapitulatif régional";
                regionalSummaryMetNotMetStatuses.XAxis = language == "en" ? "Proportion of " + region.Code + " countries (%)." : "Proportion de pays du  " + region.Code + " (%).";
                regionalSummaryMetNotMetStatuses.YAxis = language == "en" ? "Sub-Objectives" : "Sous-objectifs";

                int countryCount = 0;

                regionalCountryIds.ForEach(country =>
                {
                    var assessments = publishedAssessments.Where(r => r.CountryId == country).OrderByDescending(d => d.UpdatedAt)
                                     .GroupBy(x => x.AssessmentId).Select(x => x.First()).ToList();
                    if (assessments.Count > 0)
                    {                        
                        var latestYearOfAssessment = assessments.Max(x => x.UpdatedAt);
                        assessments = assessments.Where(w => w.UpdatedAt >= latestYearOfAssessment).ToList();
                    }
                    assessments.ForEach(assessmentDetails =>
                    {
                        if (assessmentDetails != null)
                        {
                            AssessmentStrategyDto assessmentStrategy = assessmentStrategies.FirstOrDefault(a => a.AssessmentId == assessmentDetails.AssessmentId);

                            IEnumerable<DQAServiceLevelSummaryDto> servicLevelVariablesSummary = serviceLevelSummaryDetails.Where(d => d.AssessmentId == assessmentDetails.AssessmentId);

                            //For dqa burden reduction strategy
                            if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == assessmentStrategy.StrategyId || StrategySeedingMetadata.Both_ID == assessmentStrategy.StrategyId)
                            {
                                DQADeskLevelSummaryDto dqaDeskLevelSummary = deskLevelSummaryDetails.FirstOrDefault(d => d.AssessmentId == assessmentDetails.AssessmentId);

                                if (dqaDeskLevelSummary != null)
                                {
                                    dqaPriorityIndicators.ToList().ForEach(indicator =>
                                    {
                                        int metNotMetStatus = GetBurdenReductionIndicatorMetNotMetStatus(indicator.IndicatorSequence, dqaDeskLevelSummary);

                                        indicator.MetNotMetStatus = (byte)metNotMetStatus;

                                        indicator.AssessmentId = assessmentDetails.AssessmentId;
                                    });
                                }

                                //For dqa service level variable burden reduction strategy
                                if (servicLevelVariablesSummary.Any())
                                {
                                    dqaServiceDeliveryPriorityIndicators.ToList().ForEach(indicator =>
                                    {
                                        int metNotMetStatus = GetServiceLevelIndicatorMetNotMetStatus(indicator.IndicatorSequence, serviceLevelSummaryDetails);

                                        indicator.MetNotMetStatus = (byte)metNotMetStatus;

                                        indicator.AssessmentId = assessmentDetails.AssessmentId;
                                    });
                                }
                            }
                            //For dqa elimination strategy
                            if (StrategySeedingMetadata.ELIMINATION_ID == assessmentStrategy.StrategyId || StrategySeedingMetadata.Both_ID == assessmentStrategy.StrategyId)
                            {
                                DQAEliminationSummaryDto dqaEliminationSummary = eliminationSummaryDetails.FirstOrDefault(a => a.AssessmentId == assessmentDetails.AssessmentId);

                                if (dqaEliminationSummary != null)
                                {
                                    dqaPriorityIndicators.ToList().ForEach(indicator =>
                                    {
                                        int metNotMetStatus = GetEliminationIndicatorMetNotMetStatus(indicator.IndicatorSequence, dqaEliminationSummary);

                                        indicator.MetNotMetStatus = (byte)metNotMetStatus;

                                        indicator.AssessmentId = assessmentDetails.AssessmentId;
                                    });
                                }
                                
                            }

                            if (assessmentDetails.AssessmentId != Guid.Empty)
                            {
                                countryCount = countryCount + 1;

                                categories.ForEach(subObj =>
                                {                                    
                                    List<IndicatorSubObjectiveObjectiveDto> deskReviewIndicators = deskReviewindicatorDetails.Where(d => d.AssessmentId == assessmentDetails.AssessmentId && d.SubObjectiveId == subObj.Id).ToList();

                                    int metCountOfDeskReviewIndicators = deskReviewIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.Met);

                                    List<IndicatorSubObjectiveObjectiveDto> scoreCardIndicators = scoreCardIndicatorDetails.Where(d => d.AssessmentId == assessmentDetails.AssessmentId && d.SubObjectiveId == subObj.Id).ToList();

                                    int metCountOfScoreCardIndicators = scoreCardIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.Met);

                                    List<IndicatorSubObjectiveObjectiveDto> dqaIndicators = dqaPriorityIndicators.Where(d => d.AssessmentId == assessmentDetails.AssessmentId && d.SubObjectiveId == subObj.Id).ToList();

                                    int metCountOfDQAIndicators = dqaIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.Met);

                                    List<IndicatorSubObjectiveObjectiveDto> dqaServiceDeliveryIndicators = dqaServiceDeliveryPriorityIndicators.Where(d => d.AssessmentId == assessmentDetails.AssessmentId && d.SubObjectiveId == subObj.Id).ToList();

                                    int metCountOfServiceDeliveryIndicators = dqaServiceDeliveryIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.Met);

                                    int metCountOfAssessmentIndicators = metCountOfDeskReviewIndicators + metCountOfScoreCardIndicators + metCountOfDQAIndicators + metCountOfServiceDeliveryIndicators;

                                    int totalCountIndicators = deskReviewIndicators.Count + scoreCardIndicators.Count + dqaIndicators.Count + dqaServiceDeliveryIndicators.Count;

                                    if (totalCountIndicators > 0)
                                    {
                                        double metPercentage = ((double)metCountOfAssessmentIndicators / (double)totalCountIndicators) * 100;

                                        metIndicatorsForSubObjective.Add(new SubObjectiveResultDto { Id = subObj.Id, Percentage = metPercentage });

                                        int notMetCountOfDeskReviewIndicators = deskReviewIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotMet);

                                        int notMetCountOfScoreCardIndicators = scoreCardIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotMet);

                                        int notMetCountOfDQAIndicators = dqaIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotMet);
                                        
                                        int notMetCountOfServiceDeliveryIndicators = dqaServiceDeliveryIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotMet);

                                        int notMetCountOfAssessmentIndicators = notMetCountOfDeskReviewIndicators + notMetCountOfScoreCardIndicators + notMetCountOfDQAIndicators + notMetCountOfServiceDeliveryIndicators;

                                        double notMetPercentage = ((double)notMetCountOfAssessmentIndicators / (double)totalCountIndicators) * 100;

                                        notMetIndicatorsForSubObjective.Add(new SubObjectiveResultDto { Id = subObj.Id, Percentage = notMetPercentage });

                                        int partiallyMetCountOfDeskReviewIndicators = deskReviewIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.PartiallyMet);

                                        int partiallyMetCountOfScoreCardIndicators = scoreCardIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.PartiallyMet);

                                        int partiallyMetCountOfDQAIndicators = dqaIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.PartiallyMet);

                                        int partiallyMetCountOfServiceDeliveryIndicators = dqaServiceDeliveryIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.PartiallyMet);

                                        int partiallyMetCountOfAssessmentIndicators = partiallyMetCountOfDeskReviewIndicators + partiallyMetCountOfScoreCardIndicators + partiallyMetCountOfDQAIndicators + partiallyMetCountOfServiceDeliveryIndicators;
                               
                                        double partiallyMetPercentage = ((double)partiallyMetCountOfAssessmentIndicators / (double)totalCountIndicators) * 100;

                                        partiallyMetIndicatorsForSubObjective.Add(new SubObjectiveResultDto { Id = subObj.Id, Percentage = partiallyMetPercentage });

                                        int notAssessedCountOfDeskReviewIndicators = deskReviewIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotAssessed);

                                        int notAssessedCountOfScoreCardIndicators = scoreCardIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotAssessed);

                                        int notAssessedCountOfDQAIndicators = dqaIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotAssessed);

                                        int notAssessedCountOfServiceDeliveryIndicators = dqaServiceDeliveryIndicators.Count(d => d.MetNotMetStatus == (int)MetNotMetStatus.NotAssessed);

                                        int notAssessedCountOfAssessmentIndicators = notAssessedCountOfDeskReviewIndicators + notAssessedCountOfScoreCardIndicators + notAssessedCountOfDQAIndicators + notAssessedCountOfServiceDeliveryIndicators;

                                        double notAssessedPercentage = ((double)notAssessedCountOfAssessmentIndicators / (double)totalCountIndicators) * 100;

                                        double totalPercentage = metPercentage + notMetPercentage + partiallyMetPercentage + notAssessedPercentage;

                                        if (totalPercentage != 100)
                                        {
                                            notAssessedPercentage = (100 - totalPercentage) + notAssessedPercentage;
                                        }

                                        notAssessedIndicatorsForSubObjective.Add(new SubObjectiveResultDto { Id = subObj.Id, Percentage = notAssessedPercentage });
                                    }
                                    else
                                    {
                                        notAssessedIndicatorsForSubObjective.Add(new SubObjectiveResultDto { Id = subObj.Id, Percentage = 100 });
                                    }
                                });
                            }
                        }
                    });
                });

                if (metIndicatorsForSubObjective.Any() || notMetIndicatorsForSubObjective.Any() ||
                 partiallyMetIndicatorsForSubObjective.Any() || notAssessedIndicatorsForSubObjective.Any())
                {
                    categories.ForEach(subObj =>
                    {
                        if (countryCount > 0)
                        {
                            double metAll = Math.Round(metIndicatorsForSubObjective.Where(d => d.Id == subObj.Id).Sum(d => d.Percentage ?? 0),2);
                            metIndicatorsCounts.Add(Math.Round(metAll / countryCount,2));

                            double notMetAll = Math.Round(notMetIndicatorsForSubObjective.Where(d => d.Id == subObj.Id).Sum(d => d.Percentage ?? 0),2);
                            notMetIndicatorsCounts.Add(Math.Round(notMetAll / countryCount,2));

                            double partiallyMetAll = Math.Round(partiallyMetIndicatorsForSubObjective.Where(d => d.Id == subObj.Id).Sum(d => d.Percentage ?? 0),2);
                            partiallyMetIndictaorsCounts.Add(Math.Round(partiallyMetAll / countryCount,2));

                            double notAssessedAll = Math.Round(notAssessedIndicatorsForSubObjective.Where(d => d.Id == subObj.Id).Sum(d => d.Percentage ?? 0),2);
                            notAssessedIndicatorsCounts.Add(Math.Round(notAssessedAll / countryCount,2));
                        }
                    });

                    if (metIndicatorsCounts.Any())
                    {
                        MetNotMetStatuses met = new MetNotMetStatuses()
                        {
                            Key = "Met",
                            Value = metIndicatorsCounts
                        };
                        indicatorsMetNotMetStatuses.Add(met);
                    }

                    if (notMetIndicatorsCounts.Any())
                    {
                        MetNotMetStatuses notmet = new MetNotMetStatuses()
                        {
                            Key = "Not Met",
                            Value = notMetIndicatorsCounts
                        };
                        indicatorsMetNotMetStatuses.Add(notmet);
                    }

                    if (partiallyMetIndictaorsCounts.Any())
                    {
                        MetNotMetStatuses partiallymet = new MetNotMetStatuses()
                        {
                            Key = "PartiallyMet",
                            Value = partiallyMetIndictaorsCounts
                        };
                        indicatorsMetNotMetStatuses.Add(partiallymet);
                    }

                    if (notAssessedIndicatorsCounts.Any())
                    {
                        MetNotMetStatuses notassesdmet = new MetNotMetStatuses()
                        {
                            Key = "Not Assessed",
                            Value = notAssessedIndicatorsCounts
                        };
                        indicatorsMetNotMetStatuses.Add(notassesdmet);
                    }
                }

                regionalSummaryMetNotMetStatuses.Values = indicatorsMetNotMetStatuses;

                regionalSummaryMetNotMetIndicatorStatuses.Add(regionalSummaryMetNotMetStatuses);

                regionalSummary.Charts = regionalSummaryMetNotMetIndicatorStatuses;

                listOfRegionalSummary.Add(regionalSummary);
            });

            globalDashboardRegionalSummary.Response = listOfRegionalSummary;

            _cacheService.SetDataIntoCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardRegionalSummaryResponseJson + language, globalDashboardRegionalSummary);

            return globalDashboardRegionalSummary;
        }

        /// <summary>
        /// Get indicator summary details for global dashboard
        /// </summary>
        /// <returns>Indicator summary details for global dashboard</returns>
        public async Task<GlobalDashboardIndicatorSummaryDto> GetIndicatorSummaryDetailsAsync(string language)
        {
            GlobalDashboardIndicatorSummaryDto globalDashboardIndicatorSummary = new GlobalDashboardIndicatorSummaryDto();

            GlobalDashboardIndicatorSummaryDto globalDashboardIndicatorSummaryCache = (GlobalDashboardIndicatorSummaryDto)_cacheService.GetDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardIndicatorSummaryResponsesJson + language.ToLower());
            
            if (globalDashboardIndicatorSummaryCache != null)
            {
               //Commented for resolving cache issue
               // return globalDashboardIndicatorSummaryCache;
            }

            globalDashboardIndicatorSummary.GraphType = (int)GlobalDashboardGraphType.IndicatorDashboard;

            IEnumerable<PublishAssessmentsDto> publishedAssessments = await _unitOfWork.AssessmentRepository.GetPublishedAssessmentDetailsAsync();

            if (!publishedAssessments.Any())
            {
                return globalDashboardIndicatorSummary;
            }

            IEnumerable<PriorityIndicatorDetailsDto> priorityIndicatorDetails = await _unitOfWork.IndicatorRepository.GetPriorityIndicatorsDetailsAsync();

            List<DashboardObjectiveSubObjectiveIndicatorDto> objectives = priorityIndicatorDetails.GroupBy(x => new { x.ObjectiveId, x.ObjectiveName, x.ObjectiveSequence })
                                                                                                .Select(x => new DashboardObjectiveSubObjectiveIndicatorDto
                                                                                                {
                                                                                                    Id = x.Key.ObjectiveId,
                                                                                                    Name = x.Key.ObjectiveName,
                                                                                                    Sequence = x.Key.ObjectiveSequence
                                                                                                }).ToList();

            List<DashboardObjectiveSubObjectiveIndicatorDto> subObjectives = priorityIndicatorDetails.GroupBy(x => new { x.SubObjectiveId, x.SubObjectiveName, x.SubObjectiveSequence, x.ObjectiveId })
                                                                                                   .Select(x => new DashboardObjectiveSubObjectiveIndicatorDto
                                                                                                   {
                                                                                                       Id = x.Key.SubObjectiveId,
                                                                                                       Name = x.Key.SubObjectiveName.RemoveText("("),
                                                                                                       Sequence = x.Key.SubObjectiveSequence,
                                                                                                       ObjectiveId = x.Key.ObjectiveId
                                                                                                   }).ToList();

            List<DashboardObjectiveSubObjectiveIndicatorDto> indicators = priorityIndicatorDetails.GroupBy(x => new { x.IndicatorId, x.IndicatorName, x.IndicatorSequence, x.SubObjectiveId, x.ObjectiveId })
                                                                                                .Select(x => new DashboardObjectiveSubObjectiveIndicatorDto
                                                                                                {
                                                                                                    Id = x.Key.IndicatorId,
                                                                                                    Name = x.Key.IndicatorName,
                                                                                                    Sequence = x.Key.IndicatorSequence,
                                                                                                    SubObjectiveId = x.Key.SubObjectiveId,
                                                                                                    ObjectiveId = x.Key.ObjectiveId
                                                                                                }).ToList();


            List<Region> regions = publishedAssessments.ToList().GroupBy(x => new { x.RegionId, x.RegionName })
                                                               .Select(x => new Region
                                                               {
                                                                   Id = x.Key.RegionId,
                                                                   Name = x.Key.RegionName
                                                               }).ToList();

            List<Country> countries = publishedAssessments.ToList().GroupBy(x => new { x.CountryId, x.CountryName, x.RegionId })
                                                            .Select(x => new Country
                                                            {
                                                                Id = x.Key.CountryId,
                                                                Name = x.Key.CountryName,
                                                                RegionId = x.Key.RegionId
                                                            }).ToList();

            IndicatorSummary indicatorSummary = new IndicatorSummary();

            indicatorSummary.Objectives = objectives.Select(o => new KeyValuePair<string, int>
                                      (
                                        o.Name,
                                        indicators.Count(i => i.ObjectiveId == o.Id)
                                      )).ToList();

            indicatorSummary.SubObjectives = subObjectives.Select(s => new KeyValuePair<string, int>
                                     (
                                       s.Name,
                                       indicators.Count(i => i.SubObjectiveId == s.Id)
                                     )).ToList();

            indicatorSummary.Indicators = new IndicatorTabularModel();

            List<ColumnModel> Columns = new List<ColumnModel>();

            Columns.Add(new ColumnModel { Key = "Region", Label = language == "en" ? "Region" : "Région", Width = "100" });
            Columns.Add(new ColumnModel { Key = "Country", Label = language == "en" ? "Country" : "Pays", Width = "100" });
            Columns.Add(new ColumnModel { Key = "YearOfAssessment", Label = language == "en" ? "Year Of Assessment" : "Année d'évaluation", Width = "100" });

            indicators.ForEach(indicator =>
            {
                Columns.Add(new ColumnModel { Key = $"Indicator_{indicator.Sequence.Replace('.', '_')}", Label = indicator.Sequence, Width = "100" });
            });

            indicatorSummary.Indicators.Columns = Columns;

            JArray rows = new JArray() as dynamic;

            IEnumerable<IndicatorSubObjectiveObjectiveDto> deskReviewindicatorDetails = await _unitOfWork.AssessmentRepository.GetPriorityPublishedIndicatorsAsync();

            IEnumerable<IndicatorSubObjectiveObjectiveDto> scoreCardindicatorDetails = await _unitOfWork.ScoreCardRepository.GetScoreCardIndicatorsDetailsAsync();

            IEnumerable<DQAEliminationSummaryDto> eliminationSummaryDetails = await _unitOfWork.DQARepository.GetEliminationSummaryForPublishedAssessmentsAsync();

            IEnumerable<DQADeskLevelSummaryDto> deskLevelSummeryDetails = await _unitOfWork.DQARepository.GetAllDeskLevelSummariesAsync();

            IEnumerable<AssessmentStrategyDto> assessmentStrategies = await _unitOfWork.AssessmentRepository.GetPublishedAssessmentStrategiesAsync();

            IEnumerable<DQAServiceLevelSummaryDto> serviceLevelSummaryDetails = await _unitOfWork.ServiceLevelRepository.GetServiceLevelVariableSummaryForPublishedAssessmentAsync();

            regions.ToList().ForEach(region =>
            {
                List<Country> regionalCountries = countries.Where(c => c.RegionId == region.Id).ToList();

                regionalCountries.ForEach(country =>
                {
                    var assessments = publishedAssessments.Where(r => r.CountryId == country.Id).OrderByDescending(d => d.UpdatedAt)
                                     .GroupBy(x => x.AssessmentId).Select(x => x.First()).ToList();

                    if (assessments.Count > 0)
                    {
                        //var latestYearOfAssessment = assessments.FirstOrDefault().StartDate.Year;
                        //assessments = assessments.Where(w => w.StartDate.Year >= latestYearOfAssessment).ToList();
                        var latestYearOfAssessment = assessments.Max(x => x.UpdatedAt);
                        assessments = assessments.Where(w => w.UpdatedAt >= latestYearOfAssessment).ToList();
                    }

                    assessments.ForEach(assessment =>
                    {
                        if (assessment != null)
                        {
                            dynamic row = new JObject();

                            row["Region"] = region.Name;
                            row["Country"] = country.Name;
                            row["YearOfAssessment"] = assessment.StartDate.Year;
                            IEnumerable<IndicatorSubObjectiveObjectiveDto> deskReviewIndicators = deskReviewindicatorDetails.Where(i => i.AssessmentId == assessment.AssessmentId);
                            indicators.ToList().ForEach((indicator =>
                            {
                                dynamic indicatorDetail = new JObject();

                                bool isDeskReviewIndicator = false;

                                //Desk review indicator                                
                                    IndicatorSubObjectiveObjectiveDto deskReviewIndicator = deskReviewIndicators.FirstOrDefault(i => i.IndicatorId == indicator.Id);

                                    if (deskReviewIndicator != null)
                                    {
                                    if (deskReviewIndicator.MetNotMetStatus != null)
                                        {
                                            if (deskReviewIndicator.MetNotMetStatus == (int)MetNotMetStatus.NotAssessed)
                                            {
                                                indicatorDetail["DeskReview"] = (int)MetNotMetStatus.NotAssessed;
                                            }
                                            else
                                            {
                                                indicatorDetail["DeskReview"] = deskReviewIndicator.MetNotMetStatus;
                                            }
                                        }
                                        else
                                        {
                                            indicatorDetail["DeskReview"] = (int)MetNotMetStatus.NotAssessed;
                                        }
                                        isDeskReviewIndicator = true;
                                    }

                                //Question bank score card indicator
                                IndicatorSubObjectiveObjectiveDto scoreCardindicator = scoreCardindicatorDetails.FirstOrDefault(i => i.AssessmentId == assessment.AssessmentId && i.IndicatorId == indicator.Id);
                             
                                    if (scoreCardindicator != null)
                                {
                                    indicatorDetail["IsSurvey"] = false;
                                    if (scoreCardindicator.MetNotMetStatus != null)
                                    {
                                        indicatorDetail["SurveyValue"] = scoreCardindicator.MetNotMetStatus;
                                    }
                                    else
                                    {
                                        indicatorDetail["SurveyValue"] = (int)MetNotMetStatus.NotAssessed;
                                    }
                                }
                                else
                                {
                                    indicatorDetail["IsSurvey"] = false;
                                    indicatorDetail["SurveyValue"] = (int)MetNotMetStatus.NotAssessed;
                                }

                                // DQA
                                AssessmentStrategyDto assessmentStrategy = assessmentStrategies.FirstOrDefault(a => a.AssessmentId == assessment.AssessmentId);

                                IEnumerable<DQAServiceLevelSummaryDto> servicLevelVariablesSummary = serviceLevelSummaryDetails.Where(d => d.AssessmentId == assessment.AssessmentId);

                                if (assessmentStrategy != null)
                                {
                                    //For dqa burden reduction strategy
                                    if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == assessmentStrategy.StrategyId || StrategySeedingMetadata.Both_ID == assessmentStrategy.StrategyId)
                                    {
                                        DQADeskLevelSummaryDto dqaDeskLevelSummary = deskLevelSummeryDetails.FirstOrDefault(d => d.AssessmentId == assessment.AssessmentId);
                                        if (dqaDeskLevelSummary != null)
                                        {

                                            List<string> burdenReductionIndicatorSequecne = new List<string> { "1.2.1", "1.2.3", "1.2.7", "1.2.8", "1.2.10", "1.2.9" };

                                            if (burdenReductionIndicatorSequecne.IndexOf(indicator.Sequence) != -1)
                                            {
                                                indicatorDetail["DeskReview"] = GetBurdenReductionIndicatorMetNotMetStatus(indicator.Sequence, dqaDeskLevelSummary);

                                                isDeskReviewIndicator = true;
                                            }
                                        }

                                        //For dqa service level variable burden reduction strategy
                                        if (servicLevelVariablesSummary.Any())
                                        {
                                            List<string> slBurdenReductionIndicatorSequence = new List<string> { "1.2.11", "1.2.12", "1.2.13" };

                                            if (slBurdenReductionIndicatorSequence.IndexOf(indicator.Sequence) != -1)
                                            {
                                                indicatorDetail["DeskReview"] = GetServiceLevelIndicatorMetNotMetStatus(indicator.Sequence, servicLevelVariablesSummary);
                                                isDeskReviewIndicator = true;
                                            }
                                        }
                                    }
                                    //For dqa elimination strategy
                                    if (StrategySeedingMetadata.ELIMINATION_ID == assessmentStrategy.StrategyId || StrategySeedingMetadata.Both_ID == assessmentStrategy.StrategyId)
                                    {
                                        DQAEliminationSummaryDto dqaEliminationSummary = eliminationSummaryDetails.FirstOrDefault(a => a.AssessmentId == assessment.AssessmentId);

                                        if (dqaEliminationSummary != null)
                                        {
                                            List<string> eliminationIndicatorSequence = new List<string> { "1.2.1", "1.2.2", "1.2.3", "1.2.4", "1.2.5", "1.2.6", "1.2.7", "1.2.8", "1.2.9", "1.2.10", "1.2.11", "1.2.12", "1.2.13" };

                                            if (eliminationIndicatorSequence.IndexOf(indicator.Sequence) != -1)
                                            {
                                                indicatorDetail["DeskReview"] = GetEliminationIndicatorMetNotMetStatus(indicator.Sequence, dqaEliminationSummary);
                                                isDeskReviewIndicator = true;
                                            }
                                        }

                                        //For dqa service level variable elimination strategy
                                        //if (servicLevelVariablesSummary.Any())
                                        //{
                                        //    List<string> slEliminationIndicatorSequence = new List<string> {"1.2.11", "1.2.12"};

                                        //    if (slEliminationIndicatorSequence.IndexOf(indicator.Sequence) != -1)
                                        //    {
                                        //        indicatorDetail["DeskReview"] = GetServiceLevelIndicatorMetNotMetStatus(indicator.Sequence, servicLevelVariablesSummary);
                                        //        isDeskReviewIndicator = true;
                                        //    }
                                        //}
                                    }
                                }

                                if (isDeskReviewIndicator == false)
                                {
                                    indicatorDetail["DeskReview"] = (int)MetNotMetStatus.NotAssessed;
                                }

                                row[$"Indicator_{indicator.Sequence.Replace(".", "_")}"] = indicatorDetail;
                            }));
                            rows.Add(row);
                        }
                    });
                });
            });

            indicatorSummary.Indicators.Rows = rows;

            globalDashboardIndicatorSummary.Response = indicatorSummary;

            _cacheService.SetDataIntoCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardIndicatorSummaryResponsesJson + language.ToLower(), globalDashboardIndicatorSummary);

            return globalDashboardIndicatorSummary;
        }

        /// <summary>
        /// Get objective map summary details for global dashboard
        /// </summary>
        /// <returns>Objective map summary details for global dashboard</returns>
        public async Task<GlobalDashboardObjectiveMapSummaryDto> GetObjectiveMapSummaryDetailsAsync(string language, Guid userId)
        {
            GlobalDashboardObjectiveMapSummaryDto globalDashboardObjectiveMapSummary = new GlobalDashboardObjectiveMapSummaryDto();

            GlobalDashboardObjectiveMapSummaryDto globalDashboardObjectiveMapSummaryCache = (GlobalDashboardObjectiveMapSummaryDto)_cacheService.GetDataFromCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardObjectiveMapSummaryResponseJson + language);

            if (globalDashboardObjectiveMapSummaryCache != null)
            {
                //Commented for resolving cache issue
                // return globalDashboardObjectiveMapSummaryCache;
            }

            globalDashboardObjectiveMapSummary.Type = (int)GlobalDashboardGraphType.ObjectiveMap;

            IEnumerable<PriorityIndicatorDetailsDto> priorityIndicatorDetails = await _unitOfWork.IndicatorRepository.GetPriorityIndicatorsDetailsAsync();

            IEnumerable<PublishAssessmentsDto> publishedAssessments = await _unitOfWork.AssessmentRepository.GetPublishedAssessmentDetailsAsync();

            List<Country> countries = publishedAssessments.GroupBy(x => new { x.CountryId, x.CountryName, x.RegionId, x.ISO })
                                                     .Select(x => new Country
                                                     {
                                                         Id = x.Key.CountryId,
                                                         Name = x.Key.CountryName,
                                                         RegionId = x.Key.RegionId,
                                                         ISO = x.Key.ISO
                                                     }).ToList();

            List<GlobalDashboardObjectiveDetailsDto> categories = priorityIndicatorDetails.GroupBy(x => new
            {
                x.ObjectiveId,
                x.ObjectiveName,
                x.ObjectiveSequence

            }).Select(x => new GlobalDashboardObjectiveDetailsDto
            {
                Id = x.Key.ObjectiveId,
                Name = x.Key.ObjectiveName,
                Sequence = x.Key.ObjectiveSequence
            }).ToList();

            List<GlobalDashboardStrategyDto> strategies = new List<GlobalDashboardStrategyDto>();

            strategies.Add(new GlobalDashboardStrategyDto() { Id = StrategySeedingMetadata.BURDEN_REDUCTION_ID, Name = language == "en" ? "Burden Reduction" : "Réduction de la charge" });
            strategies.Add(new GlobalDashboardStrategyDto() { Id = StrategySeedingMetadata.ELIMINATION_ID, Name = language == "en" ? "Elimination" : "Élimination" });
            strategies.Add(new GlobalDashboardStrategyDto() { Id = StrategySeedingMetadata.Both_ID, Name = language == "en" ? "Both" : "Les deux" });

            int strategyCount = 0;
            List<GlobalDashboardObjectivesStrategyDto> globalDashboardStrategies = new List<GlobalDashboardObjectivesStrategyDto>();

            for (int s = 0; s < strategies.Count; s++)
            {
                strategyCount = strategyCount + 1;

                List<ObjectiveMapSummaryDetails> objectiveMapSummaryDetails = new List<ObjectiveMapSummaryDetails>();

                GlobalDashboardObjectivesStrategyDto globalDashboardObjectivesInStrategy = new GlobalDashboardObjectivesStrategyDto();
                globalDashboardObjectivesInStrategy.TabId = strategyCount;
                globalDashboardObjectivesInStrategy.TabName = strategies[s].Name;
                globalDashboardObjectivesInStrategy.StrategyId = strategies[s].Id;

                categories.ForEach(obj =>
                {
                    List<CountryWiseAssessmentMetNotMetStatus> listOfCountryWiseAssessmentMetNotMetstatus = new List<CountryWiseAssessmentMetNotMetStatus>();

                    ObjectiveMapSummaryDetails objectiveMapSummary = new ObjectiveMapSummaryDetails();

                    objectiveMapSummary.TabId = Convert.ToInt32(obj.Sequence);

                    objectiveMapSummary.TabName = obj.Name;

                    objectiveMapSummary.ObjectiveId = obj.Id;

                    objectiveMapSummary.Countries = listOfCountryWiseAssessmentMetNotMetstatus;

                    objectiveMapSummaryDetails.Add(objectiveMapSummary);
                });


                List<Country> countryList = countries.ToList();

                for (int i = 0; i < countryList.Count; i++)
                {
                    PublishAssessmentsDto assessment = publishedAssessments.Where(r => r.CountryId == countryList[i].Id && r.StrategyId == strategies[s].Id).OrderByDescending(d => d.StartDate).FirstOrDefault();

                    if (assessment != null)
                    {
                        DashboardResultDto result = await GetDashboardRecordsAsync(userId, countryList[i].Id, assessment.StartDate.Year, false);

                        if (result.Objectives != null)
                        {
                            foreach (ObjectiveResultDto Objective in result.Objectives)
                            {
                                ObjectiveMapSummaryDetails objectiveMapSummaryDetail = objectiveMapSummaryDetails.FirstOrDefault(obj => obj.ObjectiveId == Objective.Id);
                                if (objectiveMapSummaryDetail != null)
                                {
                                    CountryWiseAssessmentMetNotMetStatus countryWiseAssessmentMetNotMetstatus = new CountryWiseAssessmentMetNotMetStatus();

                                    countryWiseAssessmentMetNotMetstatus.ISO = countryList[i].ISO;

                                    countryWiseAssessmentMetNotMetstatus.Status = GetDeskLevelBurdenReductionStrategyMetNotMetStatus((float)Objective.Percentage);

                                    objectiveMapSummaryDetail.Countries.Add(countryWiseAssessmentMetNotMetstatus);
                                }

                            }
                        }

                    }
                }
                globalDashboardObjectivesInStrategy.Response = objectiveMapSummaryDetails;

                globalDashboardStrategies.Add(globalDashboardObjectivesInStrategy);
            }

            globalDashboardObjectiveMapSummary.Response = globalDashboardStrategies;

            _cacheService.SetDataIntoCache(GlobalDashboardSummaryCacheKeys.GlobalDashboardObjectiveMapSummaryResponseJson + language, globalDashboardObjectiveMapSummary);

            return globalDashboardObjectiveMapSummary;
        }


        /// <summary>
        /// Get country wise assessent status and approach details for dashboard 
        /// </summary>
        /// <returns>Dashboard assessment status object</returns>
        public async Task<DashboardAssessmentStatusApproachDTO> GetDashboardAssessmentStatusesAndApproachesAsync()
        {
            DashboardAssessmentStatusApproachDTO dashboardAssessmentStatuses = new DashboardAssessmentStatusApproachDTO();

            dashboardAssessmentStatuses.InProgressSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.CompletedSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.MultipleCompletedSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.RapidSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.TailoredSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.ComprehensiveSurveillanceAssessments = new List<AssessementCountryStatusDTO>();

            IEnumerable<AssessmentStatusDTO> assessmentStatuses = await _unitOfWork.AssessmentRepository.GetAssessmentStatusesAndApproachesAsync();

            if (assessmentStatuses.Any())
            {
                List<Country> countries = assessmentStatuses.ToList().GroupBy(c => new { c.CountryId, c.CountryISO })
                                         .Select(x => new Country
                                         {
                                             ISO = x.Key.CountryISO,
                                             Id = x.Key.CountryId
                                         }).ToList();

                countries.ForEach(country =>
                {
                    List<AssessmentStatusDTO> assesments = assessmentStatuses.Where(a => a.CountryId == country.Id).OrderByDescending(d => d.StartDate).ToList();

                    AssessmentStatusDTO inProgressAssessment = assesments.FirstOrDefault(a => a.Status != (int)Domain.Enum.AssessmentStatus.Published && a.Status <= (int)Domain.Enum.AssessmentStatus.InProgress);

                    if (inProgressAssessment != null)
                    {
                        dashboardAssessmentStatuses.InProgressSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)Domain.Enum.AssessmentStatus.InProgress });
                    }
                    else
                    {
                        List<AssessmentStatusDTO> completedAssessments = assesments.Where(a => a.Status == (int)Domain.Enum.AssessmentStatus.Published).ToList();

                        if (completedAssessments.Any())
                        {
                            if (completedAssessments.Count > 1)
                            {
                                //If more than one assessment is published, the map's status is publish+1=7. 
                                dashboardAssessmentStatuses.MultipleCompletedSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)Domain.Enum.AssessmentStatus.Published + 1 });
                            }
                            else
                            {
                                dashboardAssessmentStatuses.CompletedSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)Domain.Enum.AssessmentStatus.Published });
                            }
                        }
                    }

                    AssessmentStatusDTO surveillanceAssessment = assesments.FirstOrDefault();

                    if (surveillanceAssessment != null)
                    {
                        switch (surveillanceAssessment.Approach)
                        {
                            case (int)AssessmentApproach.Rapid:
                                dashboardAssessmentStatuses.RapidSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)AssessmentApproach.Rapid });
                                break;

                            case (int)AssessmentApproach.Tailored:
                                dashboardAssessmentStatuses.TailoredSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)AssessmentApproach.Tailored });
                                break;

                            case (int)AssessmentApproach.Comprehensive:
                                dashboardAssessmentStatuses.ComprehensiveSurveillanceAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)AssessmentApproach.Comprehensive });
                                break;
                        }
                    }
                });
            }

            return dashboardAssessmentStatuses;
        }


        /// <summary>
        /// Get country wise assessent status details for dashboard 
        /// </summary>
        /// <param name="assessmentYear">Year of an assessment</param>
        /// <returns>Dashboard assessment status object</returns>
        public async Task<DashboardAssessmentStatusDTO> GetDashboardAssessmentStatusesAsync(int assessmentYear)
        {
            DashboardAssessmentStatusDTO dashboardAssessmentStatuses = new DashboardAssessmentStatusDTO();

            dashboardAssessmentStatuses.InProgressAssessments = new List<AssessementCountryStatusDTO>();

            dashboardAssessmentStatuses.CompletedAssessments = new List<AssessementCountryStatusDTO>();

            IEnumerable<AssessmentStatusDTO> assessmentStatuses = await _unitOfWork.AssessmentRepository.GetAssessmentStatusesByYearAsync(assessmentYear);

            if (assessmentStatuses.Any())
            {
                List<Country> countries = assessmentStatuses.ToList().GroupBy(c => new { c.CountryId, c.CountryISO })
                                         .Select(x => new Country
                                         {
                                             ISO = x.Key.CountryISO,
                                             Id = x.Key.CountryId
                                         }).ToList();

                countries.ForEach(country =>
                {
                    List<AssessmentStatusDTO> assesments = assessmentStatuses.Where(a => a.CountryId == country.Id).OrderByDescending(d => d.StartDate).ToList();

                    AssessmentStatusDTO inProgressAssessment = assesments.FirstOrDefault(a => a.Status != (int)Domain.Enum.AssessmentStatus.Published && a.Status <= (int)Domain.Enum.AssessmentStatus.InProgress);

                    if (inProgressAssessment != null)
                    {
                        dashboardAssessmentStatuses.InProgressAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)Domain.Enum.AssessmentStatus.InProgress });
                    }
                    else
                    {
                        List<AssessmentStatusDTO> completedAssessments = assesments.Where(a => a.Status == (int)Domain.Enum.AssessmentStatus.Published).ToList();

                        if (completedAssessments.Any())
                        {
                            dashboardAssessmentStatuses.CompletedAssessments.Add(new AssessementCountryStatusDTO { ISO = country.ISO, Status = (int)Domain.Enum.AssessmentStatus.Published });
                        }
                    }
                });
            }

            return dashboardAssessmentStatuses;
        }

        #endregion

        #region Private Method
        /// <summary>
        /// Get burden reduction indicator met not met status
        /// </summary>
        /// <param name="indicatorSequence">Indicator sequence</param>
        /// <param name="dqaDeskLevelSummary">DQA desk level burden reduction summary</param>
        /// <returns>Met not met status</returns>
        private int GetBurdenReductionIndicatorMetNotMetStatus(string indicatorSequence, DQADeskLevelSummaryDto dqaDeskLevelSummary)
        {
            switch (indicatorSequence)
            {
                case "1.2.1":
                    return dqaDeskLevelSummary.ReportCompleteness.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                case "1.2.3":
                    return dqaDeskLevelSummary.ReportTimeliness.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                case "1.2.7":
                    return dqaDeskLevelSummary.VariableCompleteness.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                case "1.2.8":
                    return dqaDeskLevelSummary.VariableConsistency.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                case "1.2.10":
                    return dqaDeskLevelSummary.VariableConcordance.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                case "1.2.9":
                    List<bool> consistencyOvertimeCoreIndicatorValues = new List<bool>();

                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaOutpatientProportion);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaInPatientProportion);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaInPatientDeathProportion);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.TestPositivityRate);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.SlidePositivityRate);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.RDTPositivityRate);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.SuspectedTestProportion);

                    float consistencyTimeCoreIndicatorsStatus = ((float)consistencyOvertimeCoreIndicatorValues.Count(d => d == true) / (float)consistencyOvertimeCoreIndicatorValues.Count()) * 100;

                    return consistencyTimeCoreIndicatorsStatus.GetDeskLevelBurdenReductionStrategyMetNotMetStatus();

                default:
                    return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Get elimination indicator met not met status
        /// </summary>
        /// <param name="indicatorSequence">Indicator sequence</param>
        /// <param name="dqaEliminationSummary">DQA desk level elimination summary</param>
        /// <returns>Met not met status</returns>
        private int GetEliminationIndicatorMetNotMetStatus(string indicatorSequence, DQAEliminationSummaryDto dqaEliminationSummary)
        {

            switch (indicatorSequence)
            {
                case "1.2.1":
                    if (dqaEliminationSummary.ReportCompleteness)
                    {
                        return (int)Domain.Enum.MetNotMetStatus.Met;
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotMet;
                    }
                case "1.2.3":
                    return dqaEliminationSummary.ReportTimeliness.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.2":
                    return dqaEliminationSummary.CaseInvestigationReportsCompleteness.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.4":
                    return dqaEliminationSummary.CaseNotificationReportsTimeliness.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.5":
                    return dqaEliminationSummary.CaseInvestigationReportsTimeliness.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.6":
                    return dqaEliminationSummary.FociInvestigationReportsTimeliness.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.7":
                    return dqaEliminationSummary.CoreVariableCompletenessWithinReport.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.8":
                    return dqaEliminationSummary.ConsistencyBetweenCoreVariables.GetDeskLevelEliminationStrategyMetNotMetStatus();

                case "1.2.10":
                    if (dqaEliminationSummary.KeyVariableConcordanceBtwTwoReportingSystem)
                    {
                        return (int)Domain.Enum.MetNotMetStatus.Met;
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotMet;
                    }
                case "1.2.9":
                    List<bool> consistencyOvertimeCoreIndicatorValues = new List<bool>();

                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesNotified);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesInvestigated);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassified);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsLocal);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsIndigenous);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsIntroduced);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsImported);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPF);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPK);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPM);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPO);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPV);

                    float consistencyTimeCoreIndicatorsStatus = (consistencyOvertimeCoreIndicatorValues.Count(d => d == true) / consistencyOvertimeCoreIndicatorValues.Count()) * 100;

                    return consistencyTimeCoreIndicatorsStatus.GetDeskLevelEliminationStrategyMetNotMetStatus();
                case "1.2.11":
                    return dqaEliminationSummary.CoreVariableCompletenessWithinRegister.GetDeskLevelEliminationStrategyMetNotMetStatus();
                case "1.2.12":
                    return dqaEliminationSummary.CoreVariableConcordanceBtwRegister.GetDeskLevelEliminationStrategyMetNotMetStatus();               

                default:
                    return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Get dqa service level indicator met not met status
        /// </summary>
        /// <param name="indicatorSequence">Indicator sequence</param>
        /// <param name="dqaServiceLevelVariablesSummary">List of dqa service level variables summary</param>
        /// <returns>Met not met status</returns>
        private int GetServiceLevelIndicatorMetNotMetStatus(string indicatorSequence, IEnumerable<DQAServiceLevelSummaryDto> dqaServiceLevelVariablesSummary)
        {
            DQAServiceLevelSummaryDto dqaServicLevelSummary = new DQAServiceLevelSummaryDto();
            switch (indicatorSequence)
            {
                case "1.2.11":
                    dqaServicLevelSummary = dqaServiceLevelVariablesSummary.FirstOrDefault();
                    if (dqaServicLevelSummary != null && dqaServicLevelSummary.Total.HasValue)
                    {
                        return ((float)dqaServicLevelSummary.Total.Value * 100).GetDeskLevelBurdenReductionStrategyMetNotMetStatus();
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                case "1.2.12":
                    dqaServicLevelSummary = dqaServiceLevelVariablesSummary.FirstOrDefault(d => d.DQAVariableId == DQAVariableSeedingMetadata.OverallConcordance_ID);
                    if (dqaServicLevelSummary != null && dqaServicLevelSummary.MonthConcordance.HasValue)
                    {
                        return ((float)dqaServicLevelSummary.MonthConcordance.Value * 100).GetDeskLevelBurdenReductionStrategyMetNotMetStatus();
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                case "1.2.13":
                    IEnumerable<DQAServiceLevelSummaryDto> dqaSLErrorInDatasourcesVariables = dqaServiceLevelVariablesSummary.Where(d => d.DQAVariableId != DQAVariableSeedingMetadata.OverallConcordance_ID);
                    if (dqaSLErrorInDatasourcesVariables.Any())
                    {
                        int errorInDataSourceCountVariable = dqaSLErrorInDatasourcesVariables.Count(d => d.ErrorInDataSources.Value == 0);
                        if (errorInDataSourceCountVariable == dqaSLErrorInDatasourcesVariables.Count())
                        {
                            return (int)Domain.Enum.MetNotMetStatus.Met;
                        }
                        else
                        {
                            return (int)Domain.Enum.MetNotMetStatus.NotMet;
                        }
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                default:
                    return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Get desk level burden reduction strategy met not met status
        /// </summary>
        /// <param name="percentage">Percentage</param>
        /// <returns>Met not met status</returns>
        private int GetDeskLevelBurdenReductionStrategyMetNotMetStatus(float percentage)
        {
            if (percentage >= 80)
            {
                return (int)Domain.Enum.MetNotMetStatus.Met;
            }
            else if (percentage >= 50 && percentage <= 79)
            {
                return (int)Domain.Enum.MetNotMetStatus.PartiallyMet;
            }
            else if (percentage < 50)
            {
                return (int)Domain.Enum.MetNotMetStatus.NotMet;
            }
            else
            {
                return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }
        #endregion
    }
}
