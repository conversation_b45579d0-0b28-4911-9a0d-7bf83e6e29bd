﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for assessment analytical output
    /// </summary>
    public interface IAnalyticalOutputQueries
    {
        Task<AnalyticalOutputDetailDto> GetAnalyticalOutputDetailsAsync(Guid assessmentId);
        Task<IEnumerable<FileDto>> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId);
        Task<dynamic> GetAnalyticalOutputIndicatorResponseAsync(AnalyticalOutputIndicatorResponseInputDto request);
        Task<FileResponseDto> CreateAnalyticalOutputReportTemplateAsync(AnalyticalOutputReportRequestModel command);       
    }
}
