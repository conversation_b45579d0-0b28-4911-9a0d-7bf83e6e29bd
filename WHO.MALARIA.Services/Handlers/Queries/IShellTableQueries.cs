﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for shell table
    /// </summary>
    public interface IShellTableQueries
    {
        Task<ObjectivesSubObjectivesIndicatorsDto> GetObjectivesSubObjectivesIndicatorsAsync(Guid assessmentId);

        Task<RespondentAndHealthFacilityTypeDto> GetRespondentTypesAndHealthFacilityTypesAsync(Guid assessmentId);

        Task<IEnumerable<ShellTableQuestionsDto>> GetQuestionsAsync(Guid assessmentId, QBRespondentType respondentType, GeoGraphicLevels geoGraphicLevel, HealthFacilityType healthFacilityType);

        Task<FileResponseDto> GetShellTableQuestionsFileResponseAsync(Guid currentUserId,Guid assessmentId, QBRespondentType respondentType, HealthFacilityType healthFacilityType);
    }
}
