﻿using MediatR;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Queries;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Handles the incoming request to check whether the user is active for his assigned countries or not.
    /// </summary>
    public class HasUserActiveForAssignedCountriesCommandHandler : IRequestHandler<HasUserActiveForAssignedCountriesCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;

        public HasUserActiveForAssignedCountriesCommandHandler(IMediator mediator, IUnitOfWork unitOfWork)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Check whether the user is active for his assigned countries or not.
        /// </summary>
        /// <param name="request">Contains user data</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>TRUE if a user is active for his assigned countries else FALSE</returns>
        public async Task<bool> Handle(HasUserActiveForAssignedCountriesCommand request, CancellationToken cancellationToken)
        {
            bool hasUserCountryAccess = _unitOfWork.UserCountryAccessRepository.HasUserActiveForAssignedCountries(request.UserId);

            return hasUserCountryAccess;
        }
    }
}