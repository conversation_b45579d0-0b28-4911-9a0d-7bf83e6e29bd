﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Serilog;
using WHO.MALARIA.Database;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    public class GetIdentityByIdHandler : IRequestHandler<GetIdentityByIdQuery, IdentityDto>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        // private readonly ILogger _logger;

        public GetIdentityByIdHandler(IMediator mediator, IUnitOfWork unitOfWork)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            // _logger = logger;
        }

        public async Task<IdentityDto> Handle(GetIdentityByIdQuery request, CancellationToken cancellationToken)
        {
            // _logger.Information(this.GetType().Name, MethodBase.GetCurrentMethod().Name, new Dictionary<string, dynamic> { { "IdentityId", request.IdentityId } });

            IdentityDto identity = await _unitOfWork.IdentityRepository.GetIdentityById(request.IdentityId);

            return identity;
        }
    }
}
