﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA.District;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA.HealthFacility;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA.National;
using WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA.Province;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DQA.DeskLevel;
using WHO.MALARIA.Domain.Models.DQA.ServiceLevel;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;
using DomainDto = WHO.MALARIA.Domain.Dtos;



namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// This class contains all methods related to  DQA 
    /// </summary>
    public class DQAQueries : RuleBase, IDQAQueries
    {
        private readonly ITranslationService _translationService;
        private readonly IDQADocumentManager _dqaDocumentManager;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;
        private readonly IDQAReportGeneration _reportGeneration;
        private readonly DQAExcelSetting _dqaExcelSettings;
        private readonly ICacheDataService _cacheService;
        private readonly IAssessmentQueries _assessmentQueries;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private string _language => _httpContextAccessor?.HttpContext?.Request?.Cookies[Common.Constants.Common.I18next] ?? Common.Constants.Common.DefaultLanguage;

        private readonly string IPTp = "IPTp1-4";
        private readonly string ANC = "ANC1-4";
        private readonly string PriorityVariable = "Priority Variable";
        private readonly string OptionalVariable = "Optional Variable";
        private const string YearProperty = "Year";
        private const string TypeProperty = "Type";
        private const string VariableProperty = "Variables";
        private const string NumberOfReportsProperty = "NumberOfReports";
        private const string NumberOfReportReceivedProperty = "NumberOfReportReceived";
        private const string PercentageProperty = "DataQualityAnalysis.Percentage";
        private const string ProvinceProperty = "Province";
        private const string HealthFacilityProperty = "HealthFacilityType";
        private const string DistrictProperty = "District";
        private const string HealthFacilityNameProperty = "HealthFacilityName";
        private const string IsConsistencyOverTimeProperty = "IsConsistencyOverTimeProperty";
        private const string ConsistencyOverTimeTrend = "Assessment.DataCollection.DataQualityAssessment.ConsistentTrend";
        string reportTitleKey = string.Empty;
        private const string PercentageColumn = "Percentage";

        public DQAQueries(ICommonRuleChecker commonRuleChecker,
            ITranslationService translationService,
            IDQADocumentManager dqaDocumentManager,
            IUnitOfWork unitOfWork,
            IAssessmentRuleChecker assesmentRuleChecker,
            IDQAReportGeneration reportGeneration,
            IOptions<DQAExcelSetting> dqaExcelSettings,
            ICacheDataService cacheService,
            IAssessmentQueries assessmentQueries,
            IHttpContextAccessor httpContextAccessor)
        {
            _translationService = translationService;
            _dqaDocumentManager = dqaDocumentManager;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assesmentRuleChecker = assesmentRuleChecker;
            _reportGeneration = reportGeneration;
            _dqaExcelSettings = dqaExcelSettings.Value;
            _cacheService = cacheService;
            _assessmentQueries = assessmentQueries;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets DQA variables for particular strategy
        /// </summary>
        /// <param name="strategyId">Strategy id for which DQA variables are to be fetched</param>
        /// <param name="includeSystemDefined">Boolean value if IsSystemDefined variables to be included in result, true for including IsSystemDeffined variables else false</param>
        /// <returns>List of DQA variables</returns>
        public async Task<IEnumerable<DQAVariableDto>> GetServiceLevelVariablesByStrategyAsync(Guid strategyId, bool includeSystemDefined)
        {
            return await _unitOfWork.DQARepository.GetVariablesAsync(strategyId, includeSystemDefined);
        }

        /// <summary>
        /// Gets data sources for DQAs
        /// </summary>
        /// <returns>List of data sources</returns>
        public async Task<IEnumerable<DQADataSourcesDto>> GetDQADataSourcesAsync()
        {
            return await _unitOfWork.DQARepository.GetDQADataSourcesAsync();
        }

        /// <summary>
        /// Get SL Variable data 
        /// </summary>
        /// <param name="serviceLevelId">serviceLevelId</param>
        /// <returns>service level related data</returns>
        public async Task<ServiceLevel> GetServiceLevelDataAsync(Guid serviceLevelId)
        {
            ServiceLevel serviceLevelData = await _unitOfWork.ServiceLevelRepository
                                                   .Queryable(x => x.Id == serviceLevelId)
                                                   .Include(vd => vd.ServiceLevelVariables).ThenInclude(slvd => slvd.ServiceLevelVariableData)
                                                   .Include(vc => vc.ServiceLevelVariableCompletnesses)
                                                   .Include(slv => slv.ServiceLevelVariables)
                                                   .ThenInclude(v => v.DQAVariable)
                                                   .AsNoTracking()
                                                   .SingleOrDefaultAsync();

            return serviceLevelData;
        }

        /// <summary>
        /// Get SL data by assementId 
        /// </summary>
        /// <param name="assessmentId">assessmentId</param>
        /// <returns>service level related data</returns>
        public async Task<ServiceLevel> GetServiceLevelByAssessmentAsync(Guid assessmentId)
        {
            ServiceLevel serviceLevelData = await _unitOfWork.ServiceLevelRepository
                                                   .Queryable(x => x.AssessmentId == assessmentId)
                                                   .AsNoTracking()
                                                   .SingleOrDefaultAsync();

            return serviceLevelData;
        }

        /// <summary>
        /// Gets service level data
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>Object of Service levels for an assessment along with registers and data system sources</returns>
        public async Task<ServiceLevelDto> GetServiceLevelForAssessmentAsync(Guid assessmentId)
        {
            ServiceLevel serviceLevel = await _unitOfWork.ServiceLevelRepository
                                                      .Queryable(a => a.AssessmentId == assessmentId)
                                                      ?.Include(r => r.ServiceLevelRegisters)
                                                      ?.Include(v => v.ServiceLevelVariables)
                                                      ?.Include(r => r.ServiceLevelVariableCompletnesses).SingleOrDefaultAsync();

            if (serviceLevel == null)
            {
                return null;
            }

            return new ServiceLevelDto
            {
                ServiceLevelId = serviceLevel.Id,
                From = serviceLevel.From,
                To = serviceLevel.To,
                Year = serviceLevel.Year,
                ValidationPeriodStartDate = serviceLevel.ValidationPeriodStartDate,
                IsFinalized = serviceLevel.IsFinalized,
                RegisterTypes = serviceLevel.ServiceLevelRegisters.Select(r => r.Type),
                VariableIds = serviceLevel.ServiceLevelVariables.Where(s => s.DQAVariableId != DQAVariableSeedingMetadata.OverallConcordance_ID)//Guid of IsSystemDefined variable
                                                           .Select(r => r.DQAVariableId).ToArray(), //Excluding entry of IsSystemDefined variable.
                HasSummaryData = serviceLevel.ServiceLevelVariableCompletnesses.Any(),
                FileName = serviceLevel.FileName

            };
        }

        /// <summary>
        /// Export Service Level DQA Template
        /// </summary>
        /// <param name="serviceLevelId">The id is required to fetch template detail </param>
        /// <param name="currentUserId">Id of current logged in user</param>
        /// <returns>file download response with file byte[] and file name</returns>
        public async Task<FileResponseDto> ExportServiceLevelTemplate(Guid serviceLevelId, Guid currentUserId)
        {
            FileResponseDto fileResponseDto = new FileResponseDto();

            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, serviceLevelId, "ServiceLevelId"));

            ServiceLevel serviceLevelData = await _unitOfWork.ServiceLevelRepository
                                                  .Queryable(x => x.Id == serviceLevelId)
                                                  .Include(x => x.ServiceLevelRegisters)
                                                  .Include(x => x.ServiceLevelVariables)
                                                  .Include(x => x.Assessment)
                                                  .ThenInclude(y => y.Country).SingleOrDefaultAsync();
            if (serviceLevelData != null)
            {
                CheckRule(new UserShouldHaveUploadFilePermissionRule(_translationService, _assesmentRuleChecker, serviceLevelData.AssessmentId, currentUserId, UserAssessmentPermission.CanUploadFile, Constants.Exception.NoPermissionToCreateTemplate));

                CreateTemplateInputModel createTemplateInputModel = new CreateTemplateInputModel
                {
                    StartDate = serviceLevelData.ValidationPeriodStartDate,
                    From = serviceLevelData.From,
                    To = serviceLevelData.To,
                    Year = serviceLevelData.Year,
                    ServiceLevelId = serviceLevelId,
                    Version = serviceLevelData.Version,
                    RegisterTypes = serviceLevelData.ServiceLevelRegisters.Select(r => (int)r.Type).ToList(),
                    VariableIds = serviceLevelData.ServiceLevelVariables.Select(r => r.DQAVariableId).ToList()
                };

                fileResponseDto.FileData = _dqaDocumentManager.ProcessExcel(createTemplateInputModel, _language);
                fileResponseDto.FileName = $"{Constants.DownloadDocument.SLDQATemplateFile}_{serviceLevelData.Assessment.Country.Name}_{Constants.Common.Months[serviceLevelData.From.Month - 1]}_{Constants.Common.Months[serviceLevelData.To.Month - 1]}_{serviceLevelData.To.Year}.xlsx";
            }

            return fileResponseDto;
        }

        /// <summary>
        /// Gets service level summary for an assessment.
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which service level is to be fetched</param>
        /// <returns>Object of service level summary including variable completeness and variable data</returns>
        public async Task<ServiceLevelSummaryDto> GetServiceLevelSummaryAsync(Guid assessmentId)
        {
            ServiceLevel serviceLevel = await _unitOfWork.ServiceLevelRepository
                                                       .Queryable(a => a.AssessmentId == assessmentId)
                                                       ?.Include(sv => sv.ServiceLevelVariables).ThenInclude(v => v.DQAVariable)
                                                       ?.Include(vd => vd.ServiceLevelVariables).ThenInclude(m => m.ServiceLevelVariableData)
                                                       ?.Include(cv => cv.ServiceLevelVariableCompletnesses)
                                                       .SingleOrDefaultAsync();

            if (serviceLevel == null)
            {
                return null;
            }

            return new ServiceLevelSummaryDto
            {
                Year = serviceLevel.Year,

                ServiceLevelId = serviceLevel.Id,

                VariableData = serviceLevel.ServiceLevelVariables.Where(v => v.ServiceLevelVariableData != null)
                                                                 .SelectMany(slvd => slvd.ServiceLevelVariableData)
                                                                 .Select(vd => new VariableData
                                                                 {
                                                                     ErrorInDataSources = vd.ErrorInDataSources,
                                                                     MonthConcordance = vd.MonthConcordance,
                                                                     ServiceLevelVariableName = _language == Common.Constants.Common.DefaultLanguage ? vd.ServiceLevelVariable?.DQAVariable?.Name : vd.ServiceLevelVariable?.DQAVariable?.Name_FR,
                                                                     Order = vd.ServiceLevelVariable?.DQAVariable?.Order
                                                                 }).OrderBy(vd => vd.Order).ToList(),

                VariableCompletenesses = serviceLevel.ServiceLevelVariableCompletnesses
                                                     .Select(vc => new VariableCompleteness
                                                     {
                                                         Sex = vc.Sex,
                                                         Age = vc.Age,
                                                         Diagnosis = vc.Diagnosis,
                                                         ObservedDataQualityResultReason = vc.ObservedDataQualityResultReason,
                                                         Total = vc.Total
                                                     }).SingleOrDefault()
            };
        }

        /// <summary>
        /// Gets report based on DQA indicators and report types
        /// </summary>
        /// <param name="indicatorReportType">indicator value as integer - refers DQAIndicatorReport enum</param>
        /// <param name="assessmentId">The id of the assessment for which desk level reports are getting fetched</param>
        /// <returns>list of report data </returns>
        public async Task<IndicatorReportDto> GetReportForIndicatorAsync(DQAIndicatorReport indicatorReportType, Guid assessmentId)
        {
            IndicatorReportDto indicatorReport = new IndicatorReportDto();

            List<DeskLevelDataSystem1> dataSystem1HealthFacilities = new List<DeskLevelDataSystem1>();
            List<DeskLevelDataSystem2> dataSystem2HealthFacilities = new List<DeskLevelDataSystem2>();

            dataSystem1HealthFacilities = (List<DeskLevelDataSystem1>)_cacheService.GetDataFromCache(DQAConstants.DeskLevelDataSystem1 + "_" + assessmentId.ToString());
            dataSystem2HealthFacilities = (List<DeskLevelDataSystem2>)_cacheService.GetDataFromCache(DQAConstants.DeskLevelDataSystem2 + "_" + assessmentId.ToString());

            if (dataSystem1HealthFacilities == null && dataSystem2HealthFacilities == null)
            {
                IEnumerable<DeskLevelDataSystem1> healthFacilitiesDataSystem1 = await _unitOfWork.DeskLevelDataSystem1Repository.GetListAsync(x => x.AssessmentId == assessmentId);
                IEnumerable<DeskLevelDataSystem2> healthFacilitiesDataSystem2 = await _unitOfWork.DeskLevelDataSystem2Repository.GetListAsync(x => x.AssessmentId == assessmentId);

                dataSystem1HealthFacilities = healthFacilitiesDataSystem1.ToList();
                dataSystem2HealthFacilities = healthFacilitiesDataSystem2.ToList();
            }

            List<TemplateDataSourceCombinedVariablesDto> dataSystem1 = UtilityHelper.GetDataInModelFromEntity<TemplateDataSourceCombinedVariablesDto, DeskLevelDataSystem1>(dataSystem1HealthFacilities);
            List<TemplateDataSourceCombinedVariablesDto> dataSystem2 = UtilityHelper.GetDataInModelFromEntity<TemplateDataSourceCombinedVariablesDto, DeskLevelDataSystem2>(dataSystem2HealthFacilities);

            if (!dataSystem1.Any() || !dataSystem2.Any())
            {
                return indicatorReport;
            }

            Dictionary<string, int> reportTypes = UtilityHelper.GetEnumerableData<DQAReportType>();

            List<TableResponse> reportResponseType1 = new List<TableResponse>();
            List<MultipleTabResponse> reportResponseType2 = new List<MultipleTabResponse>();

            DataAnalysisReportRequest dataAnalysisRequest = new DataAnalysisReportRequest();

            //  Get Selected variable dto
            SelectedVariableDto selectedVariables = await _unitOfWork.DQARepository.GetDeskLevelSelectedVariablesForCalculation(assessmentId);

            // Tab name trasltion 
            string nationalTabName = _translationService.GetTranslation(DQAConstants.National);
            string provinceTabName = _translationService.GetTranslation(DQAConstants.Province);
            string districtTabName = _translationService.GetTranslation(DQAConstants.District);
            string heathfacilityTabName = _translationService.GetTranslation(DQAConstants.HealthFacility);

            switch (indicatorReportType)
            {
                case DQAIndicatorReport.ReportingCompleteness:

                case DQAIndicatorReport.ReportingTimeliness:

                    //variables to handle FE response of type 1
                    List<ReportTable> nationalTables = new List<ReportTable>();
                    List<ReportTable> provinceTables = new List<ReportTable>();
                    List<ReportTable> districtTables = new List<ReportTable>();
                    List<ReportTable> healthFacilityTables = new List<ReportTable>();

                    List<DeskLevelChart> nationalCharts = new List<DeskLevelChart>();
                    List<DeskLevelChart> provinceCharts = new List<DeskLevelChart>();
                    List<DeskLevelChart> districtCharts = new List<DeskLevelChart>();
                    List<DeskLevelChart> healthFacilityCharts = new List<DeskLevelChart>();

                    TableResponse nationalResponse = new TableResponse();
                    TableResponse provinceResponse = new TableResponse();
                    TableResponse districtResponse = new TableResponse();
                    TableResponse healthFacilityResponse = new TableResponse();

                    foreach (KeyValuePair<string, int> reportType in reportTypes)
                    {
                        switch (reportType.Value)
                        {
                            case (int)DQAReportType.NationalReport:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable nationalReport = GetFENationalReportResponse(dataAnalysisRequest, out DeskLevelChart nationalReportChart);

                                nationalTables.Add(nationalReport);
                                nationalCharts.Add(nationalReportChart);

                                break;
                            case (int)DQAReportType.NationalReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable nationalReport1 = GetFENationalReportType1Response(dataAnalysisRequest, out DeskLevelChart nationalReport1Chart);

                                nationalTables.Add(nationalReport1);
                                nationalCharts.Add(nationalReport1Chart);

                                break;
                            case (int)DQAReportType.ProvinceReport:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable provinceReport = GetFEProvinceReportResponse(dataAnalysisRequest, out DeskLevelChart provinceReportChart);

                                provinceTables.Add(provinceReport);
                                provinceCharts.Add(provinceReportChart);

                                break;

                            case (int)DQAReportType.ProvinceReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable provinceReport1 = GetFEProvinceReportType1Response(dataAnalysisRequest, out DeskLevelChart provinceReport1Chart);

                                provinceTables.Add(provinceReport1);
                                provinceCharts.Add(provinceReport1Chart);

                                break;

                            case (int)DQAReportType.DistrictReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable districtReport1 = GetFEDistrictReportType1Response(dataAnalysisRequest, out DeskLevelChart districtReport1Chart);

                                districtTables.Add(districtReport1);
                                districtCharts.Add(districtReport1Chart);

                                break;

                            case (int)DQAReportType.HealthFacilityReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                ReportTable hfReport1 = GetFEHealthFacilityReportType1Response(dataAnalysisRequest, out DeskLevelChart healthFacilityReport1Chart);

                                healthFacilityTables.Add(hfReport1);

                                break;
                        }
                    }

                    nationalResponse = new TableResponse
                    {
                        TabId = 0,
                        TabName = nationalTabName,
                        Tables = nationalTables,
                        Charts = nationalCharts
                    };

                    provinceResponse = new TableResponse
                    {
                        TabId = 1,
                        TabName = provinceTabName,
                        Tables = provinceTables,
                        Charts = provinceCharts
                    };

                    districtResponse = new TableResponse
                    {
                        TabId = 2,
                        TabName = districtTabName,
                        Tables = districtTables,
                        Charts = districtCharts
                    };

                    healthFacilityResponse = new TableResponse
                    {
                        TabId = 3,
                        TabName = heathfacilityTabName,
                        Tables = healthFacilityTables,
                        Charts = healthFacilityCharts
                    };

                    reportResponseType1.Add(nationalResponse);
                    reportResponseType1.Add(provinceResponse);
                    reportResponseType1.Add(districtResponse);
                    reportResponseType1.Add(healthFacilityResponse);

                    indicatorReport = new IndicatorReportDto()
                    {
                        IndicatorSequence = (int)indicatorReportType,
                        Type = (int)DQAChartType.TabWithTableChart,
                        Response = reportResponseType1
                    };

                    break;

                case DQAIndicatorReport.ReportingVariableCompleteness:

                case DQAIndicatorReport.ReportingConsistencyBtwVariables:

                case DQAIndicatorReport.ReportingConcordance:

                    //variables to handle FE response of type 2
                    List<MultipleTabTableResponse> nationalLevelTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> provinceLevelTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> districtLevelTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> healthFacilityLevelTables = new List<MultipleTabTableResponse>();

                    MultipleTabResponse nationalLevelResponse = new MultipleTabResponse();
                    MultipleTabResponse provinceLevelResponse = new MultipleTabResponse();
                    MultipleTabResponse districtLevelResponse = new MultipleTabResponse();
                    MultipleTabResponse healthFacilityLevelResponse = new MultipleTabResponse();

                    foreach (KeyValuePair<string, int> reportType in reportTypes)
                    {
                        switch (reportType.Value)
                        {
                            case (int)DQAReportType.NationalReport2:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> nationalReport2 = GetFENationalReportType2Response(dataAnalysisRequest);

                                nationalLevelTables.AddRange(nationalReport2);

                                break;

                            case (int)DQAReportType.NationalReport3:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                MultipleTabTableResponse nationalReport3 = GetFENationalReportType3Response(dataAnalysisRequest, (int)DQAReportType.NationalReport3);

                                nationalLevelTables.Add(nationalReport3);

                                break;

                            case (int)DQAReportType.ProvinceReport2:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> provinceReport2 = GetFEProvinceReportType2Response(dataAnalysisRequest);

                                provinceLevelTables.AddRange(provinceReport2);

                                break;

                            case (int)DQAReportType.ProvinceReport3:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                MultipleTabTableResponse provinceReport3 = GetFEProvinceReportType3Response(dataAnalysisRequest, (int)DQAReportType.ProvinceReport3);

                                provinceLevelTables.Add(provinceReport3);

                                break;

                            case (int)DQAReportType.DistrictReport2:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> districtReport2 = GetFEDistrictReportType2Response(dataAnalysisRequest);

                                districtLevelTables.AddRange(districtReport2);

                                break;

                            case (int)DQAReportType.DistrictReport3:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                MultipleTabTableResponse districtReport3 = GetFEDistrictReportType3Response(dataAnalysisRequest, (int)DQAReportType.DistrictReport3);

                                districtLevelTables.Add(districtReport3);

                                break;

                            case (int)DQAReportType.HealthFacilityReport2:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> healthFacilityReport2 = GetFEHealthFacilityReportType2Response(dataAnalysisRequest);

                                healthFacilityLevelTables.AddRange(healthFacilityReport2);

                                break;

                            case (int)DQAReportType.HealthFacilityReport3:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                MultipleTabTableResponse healthFacilityReport3 = GetFEHealthFacilityReportType3Response(dataAnalysisRequest, (int)DQAReportType.HealthFacilityReport3);

                                healthFacilityLevelTables.Add(healthFacilityReport3);

                                break;
                        }

                    }

                    nationalLevelResponse = new MultipleTabResponse
                    {
                        TabId = 0,
                        TabName = nationalTabName,
                        Tabs = nationalLevelTables
                    };

                    provinceLevelResponse = new MultipleTabResponse
                    {
                        TabId = 1,
                        TabName = provinceTabName,
                        Tabs = provinceLevelTables
                    };

                    districtLevelResponse = new MultipleTabResponse
                    {
                        TabId = 2,
                        TabName = districtTabName,
                        Tabs = districtLevelTables,
                    };

                    healthFacilityLevelResponse = new MultipleTabResponse
                    {
                        TabId = 3,
                        TabName = heathfacilityTabName,
                        Tabs = healthFacilityLevelTables
                    };

                    reportResponseType2.Add(nationalLevelResponse);
                    reportResponseType2.Add(provinceLevelResponse);
                    reportResponseType2.Add(districtLevelResponse);
                    reportResponseType2.Add(healthFacilityLevelResponse);

                    indicatorReport = new IndicatorReportDto()
                    {
                        IndicatorSequence = (int)indicatorReportType,
                        Type = (int)DQAChartType.MultipleTabsWithTableChart,
                        Response = reportResponseType2
                    };

                    break;

                case DQAIndicatorReport.ReportingConsistencyOverTime:

                    //variables to handle FE response of type 2
                    List<MultipleTabTableResponse> nationalConsistencyOverTimeTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> provinceConsistencyOverTimeTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> districtConsistencyOverTimeTables = new List<MultipleTabTableResponse>();
                    List<MultipleTabTableResponse> healthFacilityConsistencyOverTimeTables = new List<MultipleTabTableResponse>();

                    MultipleTabResponse nationalLevelConsistencyOverTimeResponse = new MultipleTabResponse();
                    MultipleTabResponse provinceLevelConsistencyOverTimeResponse = new MultipleTabResponse();
                    MultipleTabResponse districtLevelConsistencyOverTimeResponse = new MultipleTabResponse();
                    MultipleTabResponse healthFacilityLevelConsistencyOverTimeResponse = new MultipleTabResponse();

                    foreach (KeyValuePair<string, int> reportType in reportTypes)
                    {
                        switch (reportType.Value)
                        {
                            case (int)DQAReportType.NationalReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> nationalReportTables = GetConsistencyOverTimeFENationalReportType1Response(dataAnalysisRequest);

                                nationalConsistencyOverTimeTables = nationalReportTables;

                                break;

                            case (int)DQAReportType.ProvinceReport:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> provinceReportTables = GetConsistencyOverTimeFEProvinceReportResponse(dataAnalysisRequest);

                                provinceConsistencyOverTimeTables = provinceReportTables;

                                break;

                            case (int)DQAReportType.DistrictReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> districtReportTables = GetConsistencyOverTimeFEDistrictReportType1Response(dataAnalysisRequest);

                                districtConsistencyOverTimeTables = districtReportTables;

                                break;

                            case (int)DQAReportType.HealthFacilityReport1:

                                dataAnalysisRequest = new DataAnalysisReportRequest
                                {
                                    AssessmentId = assessmentId,
                                    IndicatorSequence = (int)indicatorReportType,
                                    ReportType = reportType.Value,
                                    DataSystem1 = dataSystem1,
                                    DataSystem2 = dataSystem2,
                                    SelectedVariable = selectedVariables
                                };

                                List<MultipleTabTableResponse> healthFacilityReportTables = GetConsistencyOverTimeFEHealthFacilityReportType1Response(dataAnalysisRequest);

                                healthFacilityConsistencyOverTimeTables = healthFacilityReportTables;

                                break;
                        }
                    }

                    nationalLevelConsistencyOverTimeResponse = new MultipleTabResponse
                    {
                        TabId = 0,
                        TabName = nationalTabName,
                        Tabs = nationalConsistencyOverTimeTables
                    };

                    provinceLevelConsistencyOverTimeResponse = new MultipleTabResponse
                    {
                        TabId = 1,
                        TabName = provinceTabName,
                        Tabs = provinceConsistencyOverTimeTables
                    };

                    districtLevelConsistencyOverTimeResponse = new MultipleTabResponse
                    {
                        TabId = 2,
                        TabName = districtTabName,
                        Tabs = districtConsistencyOverTimeTables,
                    };

                    healthFacilityLevelConsistencyOverTimeResponse = new MultipleTabResponse
                    {
                        TabId = 3,
                        TabName = heathfacilityTabName,
                        Tabs = healthFacilityConsistencyOverTimeTables
                    };

                    reportResponseType2.Add(nationalLevelConsistencyOverTimeResponse);
                    reportResponseType2.Add(provinceLevelConsistencyOverTimeResponse);
                    reportResponseType2.Add(districtLevelConsistencyOverTimeResponse);
                    reportResponseType2.Add(healthFacilityLevelConsistencyOverTimeResponse);

                    indicatorReport = new IndicatorReportDto()
                    {
                        IndicatorSequence = (int)indicatorReportType,
                        Type = (int)DQAChartType.SidebarTabsWithTableGraph,
                        Response = reportResponseType2
                    };

                    break;
            }

            return indicatorReport;
        }

        /// <summary>
        /// Get desk Level DQA report
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which desk level variable list is getting fetched</param>
        /// <returns>Instance of FileResponseDto</returns>
        public async Task<FileResponseDto> GetDeskLevelReport(Guid assessmentId)
        {
            FileResponseDto fileResponseDto = new FileResponseDto();

            List<DeskLevelExcelTemplateSheetDetails> reportData = await GetDataForExcelReport(assessmentId);

            List<DQAVariableDto> dqaVariables = await _unitOfWork.DQARepository.GetDQAVariablesAsync();

            fileResponseDto.FileData = _dqaDocumentManager.GenerateDeskLevelReport(reportData, dqaVariables, _language);
            fileResponseDto.FileName = $"{_dqaExcelSettings.DeskLevel.ReportFileName}.xlsx";

            _cacheService.RemoveDataFromCache(DQAConstants.DeskLevelDataSystem1);
            _cacheService.RemoveDataFromCache(DQAConstants.DeskLevelDataSystem2);
            _cacheService.RemoveDataFromCache(DQAConstants.DiscrepancyForConsistancyBetweenVariables);
            _cacheService.RemoveDataFromCache(DQAConstants.DiscrepancyForSameVariablesWithConcordance);

            return fileResponseDto;
        }

        /// <summary>
        /// Get desk Level DQA Template
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which desk level variable list is getting fetched</param>
        /// <returns>Instance of FileResponseDto</returns>
        public async Task<FileResponseDto> GetDeskLevelTemplate(Guid assessmentId)
        {
            List<string> mainSheetVariables = new List<string>();

            List<string> concordanceSheetVariables = new List<string>();

            IEnumerable<VariableDto> deskLevelVariables = await _unitOfWork.DQARepository.GetDeskLevelSelectedParametersAsync(assessmentId);

            IEnumerable<DeskLevelDataSourceDto> deskLevelDataSource = await _unitOfWork.DQARepository.GetDeskLevelDataSources(assessmentId);

            List<string> variables = deskLevelVariables.Select(x => x.Name).ToList();
            int priorityVariableCount = deskLevelVariables.Count(x => x.Priority == (short)Priority.Priority);
            int optionalVariableCount = deskLevelVariables.Count(x => x.Priority == (short)Priority.Optional);

            List<string> concordanceVariables = deskLevelVariables.Where(x => x.Concordance == true).Select(x => x.Name).ToList();
            int concordancePriorityVariableCount = deskLevelVariables.Count(x => x.Priority == (short)Priority.Priority && x.Concordance == true);
            int concordanceOptionalVariableCount = deskLevelVariables.Count(x => x.Priority == (short)Priority.Optional && x.Concordance == true);

            FileResponseDto fileResponseDto = new FileResponseDto();

            List<string> disaggregationParams = new List<string>();
            string sheetName1 = "";
            string sheetName2 = "";
            string sheetTitle1 = "";
            string sheetTitle2 = "";

            if (_language == Common.Constants.Common.DefaultLanguage)
            {
                //english
                disaggregationParams = DQAConstants.BasicColumns.Select(x => x.Name).ToList();
                sheetName1 = _dqaExcelSettings.DeskLevel.Sheet1Name;
                sheetTitle1 = _dqaExcelSettings.DeskLevel.Sheet1Title;
                sheetName2 = _dqaExcelSettings.DeskLevel.Sheet2Name;
                sheetTitle2 = _dqaExcelSettings.DeskLevel.Sheet2Title;
            }
            else
            {
                // french
                disaggregationParams = DQAConstants.BasicColumns.Select(x => x.Name_FR).ToList();
                sheetName1 = _dqaExcelSettings.DeskLevel.Sheet1Name_FR;
                sheetTitle1 = _dqaExcelSettings.DeskLevel.Sheet1Title_FR;
                sheetName2 = _dqaExcelSettings.DeskLevel.Sheet2Name_FR;
                sheetTitle2 = _dqaExcelSettings.DeskLevel.Sheet2Title_FR;
            }

            mainSheetVariables.AddRange(disaggregationParams);
            mainSheetVariables.AddRange(variables);

            concordanceSheetVariables.AddRange(disaggregationParams);
            concordanceSheetVariables.AddRange(concordanceVariables);

            DeskLevelDataSourceDto DataSystem1DataSource = deskLevelDataSource.Single(x => x.DataSystem == (short)DQADataSystem.System1);

            DeskLevelDataSourceDto DataSystem2DataSource = deskLevelDataSource.FirstOrDefault(x => x.DataSystem == (short)DQADataSystem.System2);

            string dataSystem1Sources = string.Empty;
            string dataSystem2Sources = string.Empty;

            if (!string.IsNullOrEmpty(DataSystem1DataSource.Name) && !string.IsNullOrEmpty(DataSystem1DataSource.OtherSystemName))
            {
                dataSystem1Sources = DataSystem1DataSource.OtherSystemName;
            }
            else if (!string.IsNullOrEmpty(DataSystem1DataSource.Name) && string.IsNullOrEmpty(DataSystem1DataSource.OtherSystemName))
            {
                dataSystem1Sources = DataSystem1DataSource.Name;
            }

            List<DeskLevelExcelTemplateSheetDetails> sheets = new List<DeskLevelExcelTemplateSheetDetails>();

            DeskLevelExcelTemplateSheetDetails sheet1 = new DeskLevelExcelTemplateSheetDetails()
            {
                Name = sheetName1,
                Title = sheetTitle1 + dataSystem1Sources,
                Variables = mainSheetVariables,
                DisaggregationVariableCount = disaggregationParams.Count,
                PriorityVariableCount = priorityVariableCount,
                OptionalVariableCount = optionalVariableCount
            };

            sheets.Add(sheet1);

            if (DataSystem2DataSource != null)
            {
                if (!string.IsNullOrEmpty(DataSystem2DataSource.Name) && !string.IsNullOrEmpty(DataSystem2DataSource.OtherSystemName))
                {
                    dataSystem2Sources = DataSystem2DataSource.OtherSystemName;
                }
                else if (!string.IsNullOrEmpty(DataSystem2DataSource.Name) && string.IsNullOrEmpty(DataSystem2DataSource.OtherSystemName))
                {
                    dataSystem2Sources = DataSystem2DataSource.Name;
                }

                DeskLevelExcelTemplateSheetDetails sheet2 = new DeskLevelExcelTemplateSheetDetails()
                {
                    Name = sheetName2,
                    Title = sheetTitle2 + dataSystem2Sources,
                    Variables = concordanceSheetVariables,
                    DisaggregationVariableCount = disaggregationParams.Count,
                    PriorityVariableCount = concordancePriorityVariableCount,
                    OptionalVariableCount = concordanceOptionalVariableCount
                };

                sheets.Add(sheet2);
            }

            string countryName = await _assessmentQueries.GetAssessmentCountryAsync(assessmentId);

            if (disaggregationParams != null)
            {
                fileResponseDto.FileData = _dqaDocumentManager.GenerateDeskLevelTemplate(sheets, _language);
                fileResponseDto.FileName = $"{_dqaExcelSettings.DeskLevel.TemplateFileName}{countryName}.xlsx";
            }

            return fileResponseDto;
        }

        /// <summary>
        /// Get Desk level indicator list
        /// </summary>
        /// <returns>Instance of IndicatorListDto</returns>
        public async Task<IEnumerable<DomainDto.OutputDtos.DeskLevelDQA.IndicatorDto>> GetDeskLevelIndicatorsAsync()
        {
            IEnumerable<DomainDto.OutputDtos.DeskLevelDQA.IndicatorDto> deskLevelData = await _unitOfWork.DQARepository.GetDeskLevelIndicatorsAsync();

            IEnumerable<DomainDto.OutputDtos.DeskLevelDQA.IndicatorDto> modifieddeskLevelData = deskLevelData.Where(x => x.Sequence != "1.2.14").Select(x => x);

            return modifieddeskLevelData;
        }

        /// <summary>
        /// Get the desk level national summary distinct year list for assessment
        /// </summary>
        /// <param name="assessmentId">The id of the assessment </param>
        /// <returns>List of integers</returns>
        public async Task<IEnumerable<short>> GetDeskLevelNationalSummaryYearsAsync(Guid assessmentId)
        {
            IEnumerable<Summary> deskLevelSummaries = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            IEnumerable<short> years = deskLevelSummaries.Select(x => x.Year).OrderByDescending(yr => yr).Distinct();

            return years;
        }

        /// <summary>
        /// Get the desk level national summary data for assessment
        /// </summary>
        /// <param name="assessmentId">The id of the assessment </param>
        /// <param name="year">Year</param>
        /// <returns>List of instance of NationalLevelSummaryRequestDto</returns>
        public async Task<List<NationalLevelSummaryRequestDto>> GetDeskLevelNationalSummaryDataAsync(Guid assessmentId, short year)
        {
            List<NationalLevelSummaryRequestDto> summaryReportData = await GetFENationalLevelReportSummaryData(assessmentId, year);

            return summaryReportData;
        }

        /// <summary>
        /// Get elimination national summary data for assessment
        /// </summary>
        /// <param name="assessmentId">The id of the assessment </param>
        /// <param name="year">Year</param>
        /// <returns>instance of DQADeskLevelNationalSummaryData</returns>
        public async Task<Domain.Dtos.InputDtos.EliminationDQA.NationalLevelSummaryRequestDto> GetEliminationNationalSummaryDataAsync(Guid assessmentId)
        {
            WHO.MALARIA.Domain.Models.DQA.Elimination.Summary summary = await _unitOfWork.EliminationSummaryRepository.GetAsync(x => x.AssessmentId == assessmentId);

            WHO.MALARIA.Domain.Models.DQA.Elimination.SummaryDataQualityResultReason dataQualityReason = await _unitOfWork.EliminationSummaryDataQualityReasonRepository.GetAsync(x => x.AssessmentId == assessmentId);

            if (summary == null)
            {
                return new DomainDto.InputDtos.EliminationDQA.NationalLevelSummaryRequestDto();
            }

            Domain.Dtos.InputDtos.EliminationDQA.NationalLevelSummaryRequestDto summaryReport = new Domain.Dtos.InputDtos.EliminationDQA.NationalLevelSummaryRequestDto()
            {
                AssessmentId = assessmentId,
                ReportCompleteness = summary.ReportCompleteness,
                ReportTimeliness = summary.ReportTimeliness,
                CaseInvestigationReportsCompleteness = summary.CaseInvestigationReportsCompleteness,
                CaseNotificationReportsTimeliness = summary.CaseNotificationReportsTimeliness,
                CaseInvestigationReportsTimeliness = summary.CaseInvestigationReportsTimeliness,
                FociInvestigationReportsTimeliness = summary.FociInvestigationReportsTimeliness,
                CoreVariableCompletenessWithinReport = summary.CoreVariableCompletenessWithinReport,
                ConsistencyBetweenCoreVariables = summary.ConsistencyBetweenCoreVariables,
                ConsistencyOverTimeCoreIndicators = summary.ConsistencyOverTimeCoreIndicators,
                ConfirmMalariaCasesNotified = summary.ConfirmMalariaCasesNotified,
                ConfirmMalariaCasesInvestigated = summary.ConfirmMalariaCasesInvestigated,
                ConfirmMalariaCasesClassified = summary.ConfirmMalariaCasesClassified,
                ConfirmMalariaCasesClassifiedAsLocal = summary.ConfirmMalariaCasesClassifiedAsLocal,
                ConfirmMalariaCasesClassifiedAsIndigenous = summary.ConfirmMalariaCasesClassifiedAsIndigenous,
                ConfirmMalariaCasesClassifiedAsIntroduced = summary.ConfirmMalariaCasesClassifiedAsIntroduced,
                ConfirmMalariaCasesClassifiedAsImported = summary.ConfirmMalariaCasesClassifiedAsImported,
                MalariaCasesDueToPF = summary.MalariaCasesDueToPF,
                MalariaCasesDueToPK = summary.MalariaCasesDueToPK,
                MalariaCasesDueToPM = summary.MalariaCasesDueToPM,
                MalariaCasesDueToPO = summary.MalariaCasesDueToPO,
                MalariaCasesDueToPV = summary.MalariaCasesDueToPV,
                KeyVariableConcordanceBtwTwoReportingSystem = summary.KeyVariableConcordanceBtwTwoReportingSystem,
                CoreVariableCompletenessWithinRegister = summary.CoreVariableCompletenessWithinRegister,
                CoreVariableConcordanceBtwRegister = summary.CoreVariableConcordanceBtwRegister,
                DataQualityResultReason = dataQualityReason.Reason,
                Year = summary.Year,
                IsFinalized = summary.IsFinalized
            };

            return summaryReport;
        }

        /// <summary>
        /// Get the desk level selected parameters such as data systems and variables for the assessment
        /// </summary>
        /// <param name="assessmentId">The id of the assessment</param>
        /// <returns>List of instance of GetVariableOutputDto</returns>
        public async Task<SelectedParametersDto> GetDeskLevelSelectedParametersAsync(Guid assessmentId)
        {
            IEnumerable<VariableDto> variables = await _unitOfWork.DQARepository.GetDeskLevelSelectedParametersAsync(assessmentId);

            if (!variables.Any())
            {
                return null;
            }

            DeskLevel deskLevelData = await _unitOfWork.DeskLevelRepository.GetAsync(x => x.AssessmentId == assessmentId);

            IEnumerable<DataSource> deskLevelDataSource = await _unitOfWork.DataSourceRepository.GetListAsync(x => x.DeskLevelId == deskLevelData.Id);

            IEnumerable<DQADataSource> dQADataSources = await _unitOfWork.DQADataSourceRepository.GetListAsync(x => deskLevelDataSource.Select(p => p.DataSourceId).Contains(x.Id));

            Guid? dataSystem1Id = dQADataSources.FirstOrDefault(x => x.DataSystem == (byte)DQADataSystem.System1)?.Id;

            string otherDataSource1Name = string.Empty;

            if (dataSystem1Id.HasValue)
            {
                otherDataSource1Name = deskLevelDataSource.FirstOrDefault(x => x.DataSourceId == dataSystem1Id).OtherSystemName;
            }

            Guid? dataSystem2Id = dQADataSources.FirstOrDefault(x => x.DataSystem == (byte)DQADataSystem.System2)?.Id;

            string otherDataSource2Name = string.Empty;

            if (dataSystem2Id.HasValue)
            {
                otherDataSource2Name = deskLevelDataSource.FirstOrDefault(x => x.DataSourceId == dataSystem2Id).OtherSystemName;
            }

            SelectedParametersDto deskLevelVariables = new SelectedParametersDto()
            {
                AssessmentId = assessmentId,
                DataSystemsOneId = dataSystem1Id,
                OtherDataSystem1SourceName = otherDataSource1Name,
                IsOtherDataSystem1Selected = !string.IsNullOrEmpty(otherDataSource1Name) ? true : false,
                DataSystemsTwoId = dataSystem2Id,
                OtherDataSystem2SourceName = otherDataSource2Name,
                IsOtherDataSystem2Selected = !string.IsNullOrEmpty(otherDataSource2Name) ? true : false,
                IsFinalized = deskLevelData.IsFinalized,
                PriorityVariableIds = variables.Where(x => x.Priority == (short)Priority.Priority).Select(x => x.Id).ToList(),
                OptionalVariableIds = variables.Where(x => x.Priority == (short)Priority.Optional).Select(x => x.Id).ToList(),
                ConcordanceVariables = variables.Where(x => x.Concordance == true).Select(x => x.Id).ToList(),
                FileName = deskLevelData.FileName
            };

            return deskLevelVariables;
        }

        /// <summary>
        /// Get the elimination template
        /// </summary>
        /// <returns>Instance of FileResponseDto</returns>
        public FileResponseDto GetEliminationTemplate()
        {
            FileResponseDto fileResponseDto = new FileResponseDto()
            {
                FileData = _dqaDocumentManager.GetEliminationTemplate(),
                FileName = $"{_dqaExcelSettings.Elimination.TemplateFileName}.xlsx"
            };

            return fileResponseDto;
        }

        /// <summary>
        /// Get elimination DQA report
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which desk level variable list is getting fetched</param>
        /// <returns>Instance of FileResponseDto</returns>
        public async Task<FileResponseDto> GetEliminationReportAsync(Guid assessmentId)
        {
            FileResponseDto fileResponseDto = new FileResponseDto();

            List<DeskLevelExcelTemplateSheetDetails> reportData = await GetEliminationDataForExcelReportAsync(assessmentId);

            fileResponseDto.FileData = _dqaDocumentManager.GenerateEliminationReport(reportData);
            fileResponseDto.FileName = $"{_dqaExcelSettings.Elimination.ReportFileName}.xlsx";

            return fileResponseDto;
        }

        #region Core FE Report Methods

        #region National Level FE Response

        /// <summary>
        /// Gets expected Front end response format for national level report : Contains year and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="nationalReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFENationalReportResponse(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart nationalReportChart)
        {
            List<NationalLevelDto> reports = new List<NationalLevelDto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(NationalLevelReportDto),
                GetReport = GetNationalLevelReport,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets base level report for national level
            reports = (List<NationalLevelDto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);


            //yearwise result table
            yearWiseData = reports
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.Variables,
                                    items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

            yearWiseData.Columns.Remove(VariableProperty);

            yearWiseData.TableName = "NationalReport";

            ReportTable nationalReport = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.NationalLevelPercentage),
                Columns = tableColumnforJsons,
                Rows = yearWiseData
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in yearWiseData.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = "National",
                    Values = row.ItemArray.Select(c => !String.IsNullOrWhiteSpace(c.ToString()) ? c.ToString() : null).ToList()
                };

                chartValues.Add(facilityTypeYearWiseData);
            }

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            nationalReportChart = ReportChart;

            return nationalReport;
        }

        /// <summary>
        /// Gets expected Front end response format for national level report Type 1 : Contains year, health faciltity type and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="nationalReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFENationalReportType1Response(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart nationalReportChart)
        {
            List<NationalLevelType1Dto> reports = new List<NationalLevelType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(NationalLevelReportType1Dto),
                GetReport = GetNationalLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets national level report for type 1
            reports = (List<NationalLevelType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

            //yearwise result table
            yearWiseData = reports
                            .OrderByDescending(x => x.HealthFacilityType)
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.HealthFacilityType,
                                    items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

            yearWiseData.TableName = "NationalReport1";

            ReportTable nationalReport = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.NationalLevelPercentage),
                Columns = tableColumnforJsons,
                Rows = yearWiseData
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in yearWiseData.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = row[0].ToString(),
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()

                };

                chartValues.Add(facilityTypeYearWiseData);
            }

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            nationalReportChart = ReportChart;

            return nationalReport;
        }

        /// <summary>
        /// Gets expected Front end response format for national level report Type 2 : Contains year, priority-optional variables and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>List of Instance of TableResponse</returns>
        private List<MultipleTabTableResponse> GetFENationalReportType2Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<NationalLevelType2Dto> reports = new List<NationalLevelType2Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWisePriorityVariableData = new DataTable();
            DataTable yearWiseOptionalVariableData = new DataTable();

            List<TableColumnforJson> priorityVariableColumnforJsons = new List<TableColumnforJson>();
            List<TableColumnforJson> optionalVariableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(NationalLevelReportType2Dto),
                GetReport = GetNationalLevelReportType2,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets national level report type 2
            reports = (List<NationalLevelType2Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            List<NationalLevelType2Dto> priorityVariableNationalReport2 = reports.Where(x => x.VariableType == PriorityVariable).ToList();

            List<NationalLevelType2Dto> optionalVariableNationalReport2 = reports.Where(x => x.VariableType == OptionalVariable).ToList();

            string nationalLevelTabName = _translationService.GetTranslation(DQAConstants.NationalLevel);

            string nationalLevelTableName = _translationService.GetTranslation(DQAConstants.NationalLevelPercentage);

            if (priorityVariableNationalReport2.Count == 0 && optionalVariableNationalReport2.Count == 0)
            {
                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns, null, dataAnalysisRequest.IndicatorSequence);

                //yearwise result table
                DataTable yearWiseData = reports
                                .ToPivotTable(
                                        item => item.Variables,
                                        item => item.Year,
                                        items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                yearWiseData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWiseData.Columns[0].ColumnName) ?
                   _translationService.GetDQATranslation(yearWiseData.Columns[0].ColumnName) : yearWiseData.Columns[0].ColumnName;

                yearWiseData.TableName = "NationalReport2";

                ReportTable nationalReport2 = new ReportTable
                {
                    Title = nationalLevelTableName,
                    Columns = tableColumnforJsons,
                    Rows = yearWiseData
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse nationalReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = nationalLevelTabName,
                    Tables = new List<ReportTable> { nationalReport2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { nationalReport };
            }
            else
            {
                //yearwise priority variable result tables
                yearWisePriorityVariableData = priorityVariableNationalReport2
                                .OrderBy(x => x.Year)
                                .ToPivotTable(
                                        item => item.Variables,
                                        item => item.Year,
                                        items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);


                yearWisePriorityVariableData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWisePriorityVariableData.Columns[0].ColumnName) ?
                    _translationService.GetDQATranslation(yearWisePriorityVariableData.Columns[0].ColumnName) : yearWisePriorityVariableData.Columns[0].ColumnName;

                //yearwise optional variable result tables
                yearWiseOptionalVariableData = optionalVariableNationalReport2
                                .OrderBy(x => x.Year)
                                .ToPivotTable(
                                        item => item.Variables,
                                        item => item.Year,
                                        items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                yearWiseOptionalVariableData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWiseOptionalVariableData.Columns[0].ColumnName) ?
                    _translationService.GetDQATranslation(yearWiseOptionalVariableData.Columns[0].ColumnName) : yearWiseOptionalVariableData.Columns[0].ColumnName;

                bool yearColumnExist = modifiedHeaderColumns.Select(x => x.Headers.FirstOrDefault(p => p == YearProperty)).Any();

                TableColumn yearColumn = null;

                if (yearColumnExist)
                {
                    string columnWidth = modifiedHeaderColumns.FirstOrDefault().Width;
                    yearColumn = new TableColumn(new List<string> { YearProperty }, columnWidth);
                }

                //Gets column list according to expected format for front end
                priorityVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, priorityVariableColumns }, null, dataAnalysisRequest.IndicatorSequence);
                optionalVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, optionalVariableColumns }, null, dataAnalysisRequest.IndicatorSequence);

                ReportTable nationalReport2Table1 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.PriorityVariablePercentage),
                    Columns = priorityVariableColumnforJsons,
                    Rows = yearWisePriorityVariableData
                };

                ReportTable nationalReport2Table2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.OptionalVariablePercentage),
                    Columns = optionalVariableColumnforJsons,
                    Rows = yearWiseOptionalVariableData
                };

                List<ChartDataRange> priorityVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWisePriorityVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(1).ToList()
                    };

                    priorityVariableChartValues.Add(facilityTypeYearWiseData);
                }

                List<ChartDataRange> optionalVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseOptionalVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(1).ToList()
                    };

                    optionalVariableChartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart priorityVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = priorityVariableChartValues
                };

                MultipleTabTableResponse priorityVariableNationalReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.PriorityVariable),
                    Tables = new List<ReportTable> { nationalReport2Table1 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                DeskLevelChart optionalVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = optionalVariableChartValues
                };

                MultipleTabTableResponse optionalVariableNationalReport = new MultipleTabTableResponse()
                {
                    TabId = 1,
                    TabName = _translationService.GetTranslation(DQAConstants.OptionalVariable),
                    Tables = new List<ReportTable> { nationalReport2Table2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { priorityVariableNationalReport, optionalVariableNationalReport };
            }
        }

        /// <summary>
        /// Gets expected Front end response format for national level report Type 3 : Contains year, count and percentage data 
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of TableResponse</returns>
        private MultipleTabTableResponse GetFENationalReportType3Response(DataAnalysisReportRequest dataAnalysisRequest, int reportType)
        {
            List<NationalLevelReportType3Dto> reports = new List<NationalLevelReportType3Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseSummaryData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(NationalLevelReportType3Dto),
                GetReport = GetNationalLevelReportType3,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets national level report type 3
            reports = (List<NationalLevelReportType3Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns, reportType, dataAnalysisRequest.IndicatorSequence);

            //yearwise summary result table
            yearWiseSummaryData = reports.OrderBy(x => x.Year).ToList().ToDataTable();

            yearWiseSummaryData.TableName = "NationalReport3";

            ReportTable nationalReport3 = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.NationalLevelSummary),
                Columns = tableColumnforJsons,
                Rows = GetNewSummaryTable(dataAnalysisRequest.IndicatorSequence, yearWiseSummaryData)
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
            {
                Key = "National",
                Values = reports.Select(x => Convert.ToString(x.Percentage)).ToList()
            };

            chartValues.Add(facilityTypeYearWiseData);

            string titlekey = string.Empty;
            string title = GetReportCategoryTitle(dataAnalysisRequest.IndicatorSequence, reportType, null, out titlekey);

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = title,
                Values = chartValues
            };

            MultipleTabTableResponse nationalReport = new MultipleTabTableResponse()
            {
                TabId = 2,
                TabName = _translationService.GetTranslation(DQAConstants.NationalLevelSummary),
                Tables = new List<ReportTable> { nationalReport3 },
                Charts = new List<DeskLevelChart> { ReportChart }
            };

            return nationalReport;
        }

        /// <summary>
        /// Gets expected Front end response format for Consistency over time national level report Type 1 : Contains year, health faciltity type and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of List of TableResponse</returns>
        private List<MultipleTabTableResponse> GetConsistencyOverTimeFENationalReportType1Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<MultipleTabTableResponse> tableResponses = new List<MultipleTabTableResponse>();

            List<NationalLevelType1Dto> reports = new List<NationalLevelType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(NationalLevelReportType1Dto),
                GetReport = GetNationalLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets national level report for type 1
            reports = (List<NationalLevelType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            int reportCount = 0;

            foreach (string key in keyIndicators)
            {
                DataTable keyIndicatorWiseData = new DataTable();

                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                IEnumerable<NationalLevelType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                keyIndicatorWiseData = keyIndicatorReports
                                                   .ToPivotTable(
                                                           item => item.Year,
                                                           item => item.HealthFacilityType,
                                                           items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                keyIndicatorWiseData.TableName = key;

                ReportTable nationalReportTable = new ReportTable
                {
                    Title = $"{key}, %",
                    Columns = tableColumnforJsons,
                    Rows = keyIndicatorWiseData
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in keyIndicatorWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(1).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }


                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse nationalReportTableResponse = new MultipleTabTableResponse()
                {
                    TabId = reportCount,
                    TabName = key,
                    Tables = new List<ReportTable> { nationalReportTable },
                    Charts = new List<DeskLevelChart> { ReportChart }
                };

                tableResponses.Add(nationalReportTableResponse);

                reportCount++;
            }

            return tableResponses;
        }

        #endregion

        #region Province Level FE Response

        /// <summary>
        /// Gets expected Front end response format for province level report : Contains year, province and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="provinceReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFEProvinceReportResponse(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart provinceReportChart)
        {
            List<ProvinceLevelDto> reports = new List<ProvinceLevelDto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(ProvinceLevelReportDto),
                GetReport = GetProvinceLevelReport,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets base level province report
            reports = (List<ProvinceLevelDto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

            //yearwise result table
            yearWiseData = reports
                            .OrderBy(x => x.Province)
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.Province,
                                    items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

            yearWiseData.TableName = "ProvinceReport";

            ReportTable provinceReport = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.ProvinceLevelPercentage),
                Columns = tableColumnforJsons,
                Rows = yearWiseData
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in yearWiseData.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = row[0].ToString(),
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()
                };

                chartValues.Add(facilityTypeYearWiseData);
            }


            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            provinceReportChart = ReportChart;

            return provinceReport;
        }

        /// <summary>
        /// Gets expected Front end response format for province level report type 1: Contains year, province, health facility type and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="provinceReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFEProvinceReportType1Response(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart provinceReportChart)
        {
            List<ProvinceLevelType1Dto> reports = new List<ProvinceLevelType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            DataTable staticReportData = new DataTable();
            DataTable resultTable = new DataTable();

            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                AssessmentId = dataAnalysisRequest.AssessmentId,
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(ProvinceLevelReportType1Dto),
                GetReport = GetProvinceLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable
            };

            //Gets province level report type 1
            reports = (List<ProvinceLevelType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

            IOrderedEnumerable<ProvinceLevelType1Dto> province1reports = reports.OrderBy(x => x.Province);

            staticReportData = GetStaticReportData(province1reports, new string[] { ProvinceProperty, HealthFacilityProperty }, modifiedHeaderColumns);

            //yearwise result table
            resultTable = GetProvinceLevelType1DataTable(province1reports, staticReportData, modifiedHeaderColumns);

            resultTable.TableName = "ProvinceReport1";

            ReportTable provinceReport = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.ProvinceLevelPercentage),
                Columns = tableColumnforJsons,
                Rows = resultTable
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in resultTable.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = $"{row[0]}-{row[1]}",
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(3).ToList()
                };

                chartValues.Add(facilityTypeYearWiseData);
            }

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            provinceReportChart = ReportChart;

            return provinceReport;
        }

        /// <summary>
        /// Gets expected Front end response format for province level report Type 2: Contains year, province, priority-optional variable and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>        
        /// <returns>Instance of TableResponse</returns>
        private List<MultipleTabTableResponse> GetFEProvinceReportType2Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<ProvinceLevelType2Dto> reports = new List<ProvinceLevelType2Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWisePriorityVariableData = new DataTable();
            DataTable yearWiseOptionalVariableData = new DataTable();
            DataTable staticReportData = new DataTable();

            List<TableColumnforJson> priorityVariableColumnforJsons = new List<TableColumnforJson>();
            List<TableColumnforJson> optionalVariableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(ProvinceLevelReportType2Dto),
                GetReport = GetProvinceLevelReportType2,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets province level report type 2
            reports = (List<ProvinceLevelType2Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            TableColumn yearColumn = null;
            bool isYearColumnExist = modifiedHeaderColumns.Any(x => x.Headers.Any(x => x == YearProperty));
            string columnWidth = modifiedHeaderColumns.FirstOrDefault().Width;

            if (isYearColumnExist)
            {
                yearColumn = new TableColumn(new List<string> { YearProperty }, columnWidth);
            }

            TableColumn provinceColumn = new TableColumn(new List<string> { ProvinceProperty }, columnWidth);

            //Gets column list according to expected format for front end
            priorityVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, priorityVariableColumns });
            optionalVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, optionalVariableColumns });

            IOrderedEnumerable<ProvinceLevelType2Dto> province2report = reports.OrderBy(x => x.Province).ThenBy(x => x.Year);

            staticReportData = GetStaticReportData(province2report, new string[] { ProvinceProperty, YearProperty }, modifiedHeaderColumns);

            if (priorityVariableColumns.Headers.Count == 0 && optionalVariableColumns.Headers.Count == 0)
            {
                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                //yearwise result table
                DataTable resultTable = GetProvinceLevelType2DataTable(province2report, staticReportData, modifiedHeaderColumns);

                resultTable.TableName = "ProvinceReport2";

                ReportTable provinceReport2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.ProvinceLevelPercentage),
                    Columns = tableColumnforJsons,
                    Rows = resultTable
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in resultTable.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(2).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse provinceReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.ProvinceLevel),
                    Tables = new List<ReportTable> { provinceReport2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { provinceReport };
            }
            else
            {
                //yearwise priority variable result tables
                yearWisePriorityVariableData = GetProvinceLevelType2DataTable(province2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                                    new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                                    new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                                    priorityVariableColumns
                                                });

                //yearwise optional variable result tables
                yearWiseOptionalVariableData = GetProvinceLevelType2DataTable(province2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                                    new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                                    new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                                    optionalVariableColumns
                                                });

                ReportTable provinceReport2Table1 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.PriorityVariablePercentage),
                    Columns = priorityVariableColumnforJsons,
                    Rows = yearWisePriorityVariableData
                };

                ReportTable provinceReport2Table2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.OptionalVariablePercentage),
                    Columns = optionalVariableColumnforJsons,
                    Rows = yearWiseOptionalVariableData
                };

                List<ChartDataRange> priorityVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWisePriorityVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(2).ToList()
                    };

                    priorityVariableChartValues.Add(facilityTypeYearWiseData);
                }

                List<ChartDataRange> optionalVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseOptionalVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(2).ToList()
                    };

                    optionalVariableChartValues.Add(facilityTypeYearWiseData);
                }


                DeskLevelChart priorityVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = priorityVariableChartValues
                };

                MultipleTabTableResponse priorityVariableProvinceReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.PriorityVariable),
                    Tables = new List<ReportTable> { provinceReport2Table1 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                DeskLevelChart optionalVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = optionalVariableChartValues
                };

                MultipleTabTableResponse optionalVariableProvinceReport = new MultipleTabTableResponse()
                {
                    TabId = 1,
                    TabName = _translationService.GetTranslation(DQAConstants.OptionalVariable),
                    Tables = new List<ReportTable> { provinceReport2Table2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { priorityVariableProvinceReport, optionalVariableProvinceReport };
            }
        }

        /// <summary>
        /// Gets expected Front end response format for province level report Type 3 : Contains year, count and percentage data 
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>        
        /// <returns>Instance of TableResponse</returns>
        private MultipleTabTableResponse GetFEProvinceReportType3Response(DataAnalysisReportRequest dataAnalysisRequest, int reportType)
        {
            List<ProvinceLevelReportType3Dto> reports = new List<ProvinceLevelReportType3Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseSummaryData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(ProvinceLevelReportType3Dto),
                GetReport = GetProvinceLevelReportType3,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets province level report type 3
            reports = (List<ProvinceLevelReportType3Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns, reportType, dataAnalysisRequest.IndicatorSequence);

            //yearwise summary result table
            yearWiseSummaryData = reports.OrderBy(x => x.Province).ThenBy(x => x.Year).ToList().ToDataTable();

            yearWiseSummaryData.TableName = "ProvinceReport3";

            ReportTable provinceReport3 = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.ProvinceLevelSummary),
                Columns = tableColumnforJsons,
                Rows = GetNewSummaryTable(dataAnalysisRequest.IndicatorSequence, yearWiseSummaryData)
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            DataTable graphData = reports
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.Province,
                                    items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

            foreach (DataRow row in graphData.Rows)
            {
                ChartDataRange provinceTypeYearWiseData = new ChartDataRange()
                {
                    Key = $"{row[0]}",
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()
                };

                chartValues.Add(provinceTypeYearWiseData);
            }

            string tiltleKey;

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportCategoryTitle(dataAnalysisRequest.IndicatorSequence, reportType, null, out tiltleKey),
                Values = chartValues
            };

            MultipleTabTableResponse provinceReport = new MultipleTabTableResponse()
            {
                TabId = 2,
                TabName = _translationService.GetTranslation(DQAConstants.ProvinceSummary),
                Tables = new List<ReportTable> { provinceReport3 },
                Charts = new List<DeskLevelChart> { ReportChart }
            };

            return provinceReport;
        }

        /// <summary>
        /// Gets expected Front end response format for Consistency over time province level report : Contains year, province and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of List of TableResponse</returns>
        private List<MultipleTabTableResponse> GetConsistencyOverTimeFEProvinceReportResponse(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<MultipleTabTableResponse> tableResponses = new List<MultipleTabTableResponse>();

            List<ProvinceLevelDto> reports = new List<ProvinceLevelDto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(ProvinceLevelReportDto),
                GetReport = GetProvinceLevelReport,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets national level report for type 1
            reports = (List<ProvinceLevelDto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            int reportCount = 0;

            foreach (string key in keyIndicators)
            {
                DataTable keyIndicatorWiseData = new DataTable();

                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                IEnumerable<ProvinceLevelDto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                keyIndicatorWiseData = keyIndicatorReports
                                        .OrderBy(x => x.Province)
                                        .ToPivotTable(
                                                item => item.Year,
                                                item => item.Province,
                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                keyIndicatorWiseData.TableName = key;

                ReportTable provinceReportTable = new ReportTable
                {
                    Title = $"{key}, %",
                    Columns = tableColumnforJsons,
                    Rows = keyIndicatorWiseData
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in keyIndicatorWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(1).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse provinceReportTableResponse = new MultipleTabTableResponse()
                {
                    TabId = reportCount,
                    TabName = key,
                    Tables = new List<ReportTable> { provinceReportTable },
                    Charts = new List<DeskLevelChart> { ReportChart }
                };

                tableResponses.Add(provinceReportTableResponse);

                reportCount++;
            }

            return tableResponses;
        }
        #endregion

        #region District Level FE Response

        /// <summary>
        /// Gets expected Front end response format for district level report : Contains year, province, district and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="districtReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFEDistrictReportType1Response(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart districtReportChart)
        {
            List<DistrictLevelType1Dto> reports = new List<DistrictLevelType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            DataTable staticReportData = new DataTable();
            DataTable resultTable = new DataTable();

            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(DistrictLevelReportType1Dto),
                GetReport = GetDistrictLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets district level report type 1
            reports = (List<DistrictLevelType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

            yearWiseData = reports
                            .OrderBy(x => x.Province).ThenBy(x => x.District)
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.District,
                                    items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

            IOrderedEnumerable<DistrictLevelType1Dto> district1Reports = reports.OrderBy(x => x.District);

            staticReportData = GetStaticReportData(district1Reports, new string[] { ProvinceProperty, DistrictProperty }, modifiedHeaderColumns);

            //yearwise result table
            resultTable = JoinDataTable(staticReportData, yearWiseData);

            resultTable.TableName = "DistrictReport1";

            ReportTable districtReport = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.DistrictLevelPercentage),
                Columns = tableColumnforJsons,
                Rows = resultTable
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in resultTable.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = $"{row[0]}-{row[1]}",
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(2).ToList()
                };

                chartValues.Add(facilityTypeYearWiseData);
            }

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            districtReportChart = ReportChart;

            return districtReport;
        }

        /// <summary>
        /// Gets expected Front end response format for district level report Type 2 : Contains year, province, district, priority-optional variables and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="districtReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of TableResponse</returns>
        private List<MultipleTabTableResponse> GetFEDistrictReportType2Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<DistrictLevelType2Dto> reports = new List<DistrictLevelType2Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWisePriorityVariableData = new DataTable();
            DataTable yearWiseOptionalVariableData = new DataTable();
            DataTable staticReportData = new DataTable();

            List<TableColumnforJson> priorityVariableColumnforJsons = new List<TableColumnforJson>();
            List<TableColumnforJson> optionalVariableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(DistrictLevelReportType2Dto),
                GetReport = GetDistrictLevelReportType2,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets district level report type 2
            reports = (List<DistrictLevelType2Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            TableColumn yearColumn = null;
            bool isYearColumnExist = modifiedHeaderColumns.Any(x => x.Headers.Any(x => x == YearProperty));
            string columnWidth = modifiedHeaderColumns.FirstOrDefault().Width;

            if (isYearColumnExist)
            {
                yearColumn = new TableColumn(new List<string> { YearProperty }, columnWidth);
            }

            TableColumn provinceColumn = new TableColumn(new List<string> { ProvinceProperty }, columnWidth);
            TableColumn districtColumn = new TableColumn(new List<string> { DistrictProperty }, columnWidth);

            //Gets column list according to expected format for front end
            priorityVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, districtColumn, priorityVariableColumns });
            optionalVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, districtColumn, optionalVariableColumns });

            IOrderedEnumerable<DistrictLevelType2Dto> district2report = reports.OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year);

            staticReportData = GetStaticReportData(district2report, new string[] { ProvinceProperty, DistrictProperty, YearProperty }, modifiedHeaderColumns);

            if (priorityVariableColumns.Headers.Count == 0 && optionalVariableColumns.Headers.Count == 0)
            {
                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                //yearwise result table
                DataTable resultTable = GetDistrictLevelType2DataTable(district2report, staticReportData, modifiedHeaderColumns);

                resultTable.TableName = "DistrictReport2";

                ReportTable districtReport2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.DistrictLevelPercentage),
                    Columns = tableColumnforJsons,
                    Rows = resultTable
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in resultTable.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(3).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }


                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse districtReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.DistrictLevel),
                    Tables = new List<ReportTable> { districtReport2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { districtReport };
            }
            else
            {
                //yearwise priority variable result tables
                yearWisePriorityVariableData = GetDistrictLevelType2DataTable(district2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                new TableColumn(new List<string> { DistrictProperty }, columnWidth),
                                                new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                priorityVariableColumns
                                                });

                //yearwise optional variable result tables
                yearWiseOptionalVariableData = GetDistrictLevelType2DataTable(district2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                new TableColumn(new List<string> { DistrictProperty }, columnWidth),
                                                new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                optionalVariableColumns
                                                });

                ReportTable districtReport2Table1 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.PriorityVariablePercentage),
                    Columns = priorityVariableColumnforJsons,
                    Rows = yearWisePriorityVariableData
                };

                ReportTable districtReport2Table2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.OptionalVariablePercentage),
                    Columns = optionalVariableColumnforJsons,
                    Rows = yearWiseOptionalVariableData
                };

                List<ChartDataRange> priorityVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWisePriorityVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(3).ToList()
                    };

                    priorityVariableChartValues.Add(facilityTypeYearWiseData);
                }

                List<ChartDataRange> optionalVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseOptionalVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}",
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(3).ToList()
                    };

                    optionalVariableChartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart priorityVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = priorityVariableChartValues
                };

                DeskLevelChart optionalVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = optionalVariableChartValues
                };

                MultipleTabTableResponse priorityVariableDistrictReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.PriorityVariable),
                    Tables = new List<ReportTable> { districtReport2Table1 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                MultipleTabTableResponse optionalVariableDistrictReport = new MultipleTabTableResponse()
                {
                    TabId = 1,
                    TabName = _translationService.GetTranslation(DQAConstants.OptionalVariable),
                    Tables = new List<ReportTable> { districtReport2Table2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { priorityVariableDistrictReport, optionalVariableDistrictReport }; ;
            }
        }

        /// <summary>
        /// Gets expected Front end response format for district level report Type 3: Contains year, province, district, health facility, count and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>        
        /// <returns>Instance of TableResponse</returns>
        private MultipleTabTableResponse GetFEDistrictReportType3Response(DataAnalysisReportRequest dataAnalysisRequest, int reportType)
        {
            List<DistrictLevelReportType3Dto> reports = new List<DistrictLevelReportType3Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseSummaryData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(DistrictLevelReportType3Dto),
                GetReport = GetDistrictLevelReportType3,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets district level report type 3
            reports = (List<DistrictLevelReportType3Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns, reportType, dataAnalysisRequest.IndicatorSequence);

            //yearwise summary result tables
            yearWiseSummaryData = reports.OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year).ToList().ToDataTable();

            ReportTable districtReport3Table1 = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.DistrictSummary),
                Columns = tableColumnforJsons,
                Rows = GetNewSummaryTable(dataAnalysisRequest.IndicatorSequence, yearWiseSummaryData)
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            DataTable graphData = reports
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.District,
                                    items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

            foreach (DataRow row in graphData.Rows)
            {
                ChartDataRange districtTypeYearWiseData = new ChartDataRange()
                {
                    Key = $"{row[0]}",
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()
                };

                chartValues.Add(districtTypeYearWiseData);
            }

            string tiltleKey;

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportCategoryTitle(dataAnalysisRequest.IndicatorSequence, reportType, null, out tiltleKey),
                Values = chartValues
            };

            MultipleTabTableResponse districtReport = new MultipleTabTableResponse()
            {
                TabId = 2,
                TabName = _translationService.GetTranslation(DQAConstants.DistrictSummary),
                Tables = new List<ReportTable> { districtReport3Table1 },
                Charts = new List<DeskLevelChart> { ReportChart }
            };

            return districtReport;
        }

        /// <summary>
        /// Gets expected Front end response format for Consistency over time district level report : Contains year, province, district and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of List of TableResponse</returns>
        private List<MultipleTabTableResponse> GetConsistencyOverTimeFEDistrictReportType1Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<MultipleTabTableResponse> tableResponses = new List<MultipleTabTableResponse>();

            List<DistrictLevelType1Dto> reports = new List<DistrictLevelType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            DataTable staticReportData = new DataTable();
            DataTable resultTable = new DataTable();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(DistrictLevelReportType1Dto),
                GetReport = GetDistrictLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets district level report type 1
            reports = (List<DistrictLevelType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            int reportCount = 0;

            foreach (string key in keyIndicators)
            {
                DataTable keyIndicatorWiseData = new DataTable();

                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                IEnumerable<DistrictLevelType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                keyIndicatorWiseData = keyIndicatorReports
                                       .OrderBy(x => x.Province).ThenBy(x => x.District)
                                        .ToPivotTable(
                                    item => item.Year,
                                    item => item.District,
                                    items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                IOrderedEnumerable<DistrictLevelType1Dto> district1Reports = reports.OrderBy(x => x.District);

                staticReportData = GetStaticReportData(district1Reports, new string[] { ProvinceProperty, DistrictProperty }, modifiedHeaderColumns);

                //yearwise result table
                resultTable = JoinDataTable(staticReportData, keyIndicatorWiseData);

                resultTable.TableName = key;

                ReportTable districtReportTable = new ReportTable
                {
                    Title = $"{key}, %",
                    Columns = tableColumnforJsons,
                    Rows = resultTable
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in keyIndicatorWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(1).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse districtReportTableResponse = new MultipleTabTableResponse()
                {
                    TabId = reportCount,
                    TabName = key,
                    Tables = new List<ReportTable> { districtReportTable },
                    Charts = new List<DeskLevelChart> { ReportChart }
                };

                tableResponses.Add(districtReportTableResponse);

                reportCount++;
            }

            return tableResponses;
        }
        #endregion

        #region Health Facility Level FE Response

        /// <summary>
        /// Gets expected Front end response format for health facility level report : Contains year, province, district, health facility and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="healthFacilityReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of ReportTable</returns>
        private ReportTable GetFEHealthFacilityReportType1Response(DataAnalysisReportRequest dataAnalysisRequest, out DeskLevelChart healthFacilityReportChart)
        {
            List<HealthFacilityType1Dto> reports = new List<HealthFacilityType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseData = new DataTable();
            DataTable staticReportData = new DataTable();
            DataTable resultTable = new DataTable();

            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(HealthFacilityReportType1Dto),
                GetReport = GetHealthFacilityLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets health facility level report type 1
            reports = (List<HealthFacilityType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

            yearWiseData = reports
                            .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName)
                            .ToPivotTable(
                                    item => item.Year,
                                    item => item.HealthFacilityName,
                                    items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

            IOrderedEnumerable<HealthFacilityType1Dto> healthfacility1report = reports.OrderBy(x => x.HealthFacilityName);

            staticReportData = GetStaticReportData(healthfacility1report, new string[] { ProvinceProperty, DistrictProperty, HealthFacilityNameProperty }, modifiedHeaderColumns);

            //yearwise result table
            resultTable = JoinDataTable(staticReportData, yearWiseData);

            resultTable.TableName = "HealthFacilityReport1";

            ReportTable hfReport = new ReportTable
            {
                Title = $"{_translationService.GetTranslation(DQAConstants.HealthFacilityLevel)}, %",
                Columns = tableColumnforJsons,
                Rows = resultTable
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            foreach (DataRow row in resultTable.Rows)
            {
                ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                {
                    Key = $"{row[0]}-{row[1]}-{row[2]}",
                    Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(3).ToList()
                };

                chartValues.Add(facilityTypeYearWiseData);
            }


            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                Values = chartValues
            };

            healthFacilityReportChart = ReportChart;

            return hfReport;
        }

        /// <summary>
        /// Gets expected Front end response format for health facility level report Type 2: Contains year, province, district, health facility, priority-optional variables and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <param name="healthFacilityReportChart">Output parameter instance of DeskLevelChart</param>
        /// <returns>Instance of TableResponse</returns>
        private List<MultipleTabTableResponse> GetFEHealthFacilityReportType2Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<HealthFacilityType2Dto> reports = new List<HealthFacilityType2Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWisePriorityVariableData = new DataTable();
            DataTable yearWiseOptionalVariableData = new DataTable();
            DataTable staticReportData = new DataTable();

            List<TableColumnforJson> priorityVariableColumnforJsons = new List<TableColumnforJson>();
            List<TableColumnforJson> optionalVariableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(HealthFacilityReportType2Dto),
                GetReport = GetHealthFacilityLevelReportType2,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets health facility level report type 2
            reports = (List<HealthFacilityType2Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            TableColumn yearColumn = null;
            bool isYearColumnExist = modifiedHeaderColumns.Any(x => x.Headers.Any(x => x == YearProperty));
            string columnWidth = modifiedHeaderColumns.FirstOrDefault().Width;

            if (isYearColumnExist)
            {
                yearColumn = new TableColumn(new List<string> { YearProperty }, columnWidth);
            }

            TableColumn provinceColumn = new TableColumn(new List<string> { ProvinceProperty }, columnWidth);
            TableColumn districtColumn = new TableColumn(new List<string> { DistrictProperty }, columnWidth);
            TableColumn healthFacilityNameColumn = new TableColumn(new List<string> { HealthFacilityNameProperty }, columnWidth);

            //Gets column list according to expected format for front end
            priorityVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, districtColumn, healthFacilityNameColumn, priorityVariableColumns });
            optionalVariableColumnforJsons = GetColumnsForFETable(new List<TableColumn> { yearColumn, provinceColumn, districtColumn, healthFacilityNameColumn, optionalVariableColumns });

            IOrderedEnumerable<HealthFacilityType2Dto> healthfacility2report = reports.OrderBy(x => x.Province)
                                                                                    .ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName).ThenBy(x => x.Year);

            staticReportData = GetStaticReportData(healthfacility2report, new string[] { ProvinceProperty, DistrictProperty, HealthFacilityNameProperty, YearProperty }, modifiedHeaderColumns);

            if (priorityVariableColumns.Headers.Count == 0 && optionalVariableColumns.Headers.Count == 0)
            {
                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                //yearwise result table
                DataTable yearWiseData = GetHealthFacilityLevelType2DataTable(healthfacility2report, staticReportData, modifiedHeaderColumns);

                yearWiseData.TableName = "HealthFacilityReport2";

                ReportTable healthFacilityReport2 = new ReportTable
                {
                    Title = $"{_translationService.GetTranslation(DQAConstants.HealthFacilityLevel)}, %",
                    Columns = tableColumnforJsons,
                    Rows = yearWiseData
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}-{row[3]}",
                        Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(4).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }

                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse healthFacilityReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.HealthFacilityLevel),
                    Tables = new List<ReportTable> { healthFacilityReport2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { healthFacilityReport };
            }
            else
            {
                //yearwise priority variable result tables
                yearWisePriorityVariableData = GetHealthFacilityLevelType2DataTable(healthfacility2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                new TableColumn(new List<string> { DistrictProperty }, columnWidth),
                                                new TableColumn(new List<string> { HealthFacilityNameProperty }, columnWidth),
                                                new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                priorityVariableColumns
                                                });
                //yearwise optional variable result tables
                yearWiseOptionalVariableData = GetHealthFacilityLevelType2DataTable(healthfacility2report, staticReportData,
                                                new List<TableColumn>
                                                {
                                                new TableColumn(new List<string> { ProvinceProperty }, columnWidth),
                                                new TableColumn(new List<string> { DistrictProperty }, columnWidth),
                                                new TableColumn(new List<string> { HealthFacilityNameProperty }, columnWidth),
                                                new TableColumn(new List<string> { YearProperty }, columnWidth),
                                                optionalVariableColumns
                                                });

                ReportTable healthFacilityReport2Table1 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.PriorityVariablePercentage),
                    Columns = priorityVariableColumnforJsons,
                    Rows = yearWisePriorityVariableData
                };

                ReportTable healthFacilityReport2Table2 = new ReportTable
                {
                    Title = _translationService.GetTranslation(DQAConstants.OptionalVariablePercentage),
                    Columns = optionalVariableColumnforJsons,
                    Rows = yearWiseOptionalVariableData
                };

                List<ChartDataRange> priorityVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWisePriorityVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}-{row[3]}",
                        Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(4).ToList()
                    };

                    priorityVariableChartValues.Add(facilityTypeYearWiseData);
                }

                List<ChartDataRange> optionalVariableChartValues = new List<ChartDataRange>();

                foreach (DataRow row in yearWiseOptionalVariableData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = $"{row[0]}-{row[1]}-{row[2]}-{row[3]}",
                        Values = row.ItemArray.Select(c => c.ReplaceToNullIfExist()).Skip(4).ToList()
                    };

                    optionalVariableChartValues.Add(facilityTypeYearWiseData);
                }


                DeskLevelChart priorityVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = priorityVariableChartValues
                };

                MultipleTabTableResponse priorityVariableHealthFacilityReport = new MultipleTabTableResponse()
                {
                    TabId = 0,
                    TabName = _translationService.GetTranslation(DQAConstants.PriorityVariable),
                    Tables = new List<ReportTable> { healthFacilityReport2Table1 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                DeskLevelChart optionalVariableChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = optionalVariableChartValues
                };

                MultipleTabTableResponse optionalVariableHealthFacilityReport = new MultipleTabTableResponse()
                {
                    TabId = 1,
                    TabName = _translationService.GetTranslation(DQAConstants.OptionalVariable),
                    Tables = new List<ReportTable> { healthFacilityReport2Table2 },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                return new List<MultipleTabTableResponse> { priorityVariableHealthFacilityReport, optionalVariableHealthFacilityReport };
            }
        }

        /// <summary>
        /// Gets expected Front end response format for health facility level report Type 3 : Contains year, province, district, health facility, count and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of TableResponse</returns>
        private MultipleTabTableResponse GetFEHealthFacilityReportType3Response(DataAnalysisReportRequest dataAnalysisRequest, int reportType)
        {
            List<HealthFacilityReportType3Dto> reports = new List<HealthFacilityReportType3Dto>();

            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

            DataTable yearWiseSummaryData = new DataTable();
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(HealthFacilityReportType3Dto),
                GetReport = GetHealthFacilityLevelReportType3,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets health facility level report type 3
            reports = (List<HealthFacilityReportType3Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            //Gets column list according to expected format for front end
            tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns, reportType, dataAnalysisRequest.IndicatorSequence);

            //yearwise summary result table
            yearWiseSummaryData = reports.OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName).ThenBy(x => x.Year).ToList().ToDataTable();

            yearWiseSummaryData.TableName = "HealthFacilityReport3";

            ReportTable healthFacilityReport3Table1 = new ReportTable
            {
                Title = _translationService.GetTranslation(DQAConstants.HealthFacilitySummary),
                Columns = tableColumnforJsons,
                Rows = GetNewSummaryTable(dataAnalysisRequest.IndicatorSequence, yearWiseSummaryData)
            };

            List<ChartDataRange> chartValues = new List<ChartDataRange>();

            ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
            {
                Key = "National",
                Values = reports.Select(x => Convert.ToString(x.Percentage)).ToList()
            };

            chartValues.Add(facilityTypeYearWiseData);

            string titlekey = string.Empty;

            DeskLevelChart ReportChart = new DeskLevelChart()
            {
                Categories = reports.Select(x => x.Year).Distinct().ToList(),
                XAxis = "Year",
                YAxis = "Value",
                Title = GetReportCategoryTitle(dataAnalysisRequest.IndicatorSequence, reportType, null, out titlekey),
                Values = chartValues
            };

            MultipleTabTableResponse healthFacilityReport = new MultipleTabTableResponse()
            {
                TabId = 2,
                TabName = _translationService.GetTranslation(DQAConstants.HealthFacilitySummary),
                Tables = new List<ReportTable> { healthFacilityReport3Table1 },
                //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                Charts = new List<DeskLevelChart>()
            };

            return healthFacilityReport;
        }

        /// <summary>
        /// Gets expected Front end response format for Consistency over time health facility level report : Contains year, province, district, health facility and percentage data
        /// </summary>
        /// <param name="dataAnalysisRequest">Instance of DataAnalysisReportRequest</param>
        /// <returns>Instance of List of TableResponse</returns>
        private List<MultipleTabTableResponse> GetConsistencyOverTimeFEHealthFacilityReportType1Response(DataAnalysisReportRequest dataAnalysisRequest)
        {
            List<MultipleTabTableResponse> tableResponses = new List<MultipleTabTableResponse>();

            List<HealthFacilityType1Dto> reports = new List<HealthFacilityType1Dto>();
            List<TableColumn> headerColumns = new List<TableColumn>();
            List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();

            DataTable staticReportData = new DataTable();
            DataTable resultTable = new DataTable();

            ReportGenerationRequest request = new ReportGenerationRequest()
            {
                IndicatorSequence = dataAnalysisRequest.IndicatorSequence,
                ReportType = dataAnalysisRequest.ReportType,
                HeaderRowType = typeof(HealthFacilityReportType1Dto),
                GetReport = GetHealthFacilityLevelReportType1,
                DataSystem1 = dataAnalysisRequest.DataSystem1,
                DataSystem2 = dataAnalysisRequest.DataSystem2,
                SelectedVariable = dataAnalysisRequest.SelectedVariable,
                AssessmentId = dataAnalysisRequest.AssessmentId
            };

            //Gets district level report type 1
            reports = (List<HealthFacilityType1Dto>)GetReportData(request, out modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns);

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            int reportCount = 0;

            foreach (string key in keyIndicators)
            {
                DataTable keyIndicatorWiseData = new DataTable();

                //Gets column list according to expected format for front end
                List<TableColumnforJson> tableColumnforJsons = GetColumnsForFETable(modifiedHeaderColumns);

                IEnumerable<HealthFacilityType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                keyIndicatorWiseData = keyIndicatorReports
                                            .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName)
                                            .ToPivotTable(
                                            item => item.Year,
                                            item => item.HealthFacilityName,
                                            items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                IOrderedEnumerable<HealthFacilityType1Dto> healthfacility1report = reports.OrderBy(x => x.HealthFacilityName);

                staticReportData = GetStaticReportData(healthfacility1report, new string[] { ProvinceProperty, DistrictProperty, HealthFacilityNameProperty }, modifiedHeaderColumns);

                //yearwise result table
                resultTable = JoinDataTable(staticReportData, keyIndicatorWiseData);

                resultTable.TableName = key;

                ReportTable healthfacilityReportTable = new ReportTable
                {
                    Title = $"{key}, %",
                    Columns = tableColumnforJsons,
                    Rows = resultTable
                };

                List<ChartDataRange> chartValues = new List<ChartDataRange>();

                foreach (DataRow row in keyIndicatorWiseData.Rows)
                {
                    ChartDataRange facilityTypeYearWiseData = new ChartDataRange()
                    {
                        Key = row[0].ToString(),
                        Values = row.ItemArray.Select(c => c.ToString()).Skip(1).ToList()
                    };

                    chartValues.Add(facilityTypeYearWiseData);
                }


                DeskLevelChart ReportChart = new DeskLevelChart()
                {
                    Categories = reports.Select(x => x.Year).Distinct().ToList(),
                    XAxis = "Year",
                    YAxis = "Value",
                    Title = GetReportTitle(dataAnalysisRequest.IndicatorSequence, out reportTitleKey),
                    Values = chartValues
                };

                MultipleTabTableResponse healthfacilityReportTableResponse = new MultipleTabTableResponse()
                {
                    TabId = reportCount,
                    TabName = key,
                    Tables = new List<ReportTable> { healthfacilityReportTable },
                    //FE needs empty array for charts wherever chart is not required, So we are sending that as below
                    Charts = new List<DeskLevelChart>()
                };

                tableResponses.Add(healthfacilityReportTableResponse);

                reportCount++;
            }

            return tableResponses;
        }

        #endregion

        #endregion

        #region Private Methods
        /// <summary>
        /// Get National level result data for data quality indicators
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <param name="year">Summary data year</param>
        /// <returns>List of Instance of NationalLevelSummaryRequestDto</returns>
        private async Task<List<NationalLevelSummaryRequestDto>> GetFENationalLevelReportSummaryData(Guid assessmentId, short year)
        {
            List<NationalLevelSummaryRequestDto> nationalLevelSummaries = new List<NationalLevelSummaryRequestDto>();

            IEnumerable<Summary> summeries = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == assessmentId && x.Year == year);

            IEnumerable<SummaryDataQualityResultReason> dataQualityReasons = await _unitOfWork.SummaryDataQualityResultReasonRepository.GetListAsync(x => x.AssessmentId == assessmentId && x.Year == year);

            if (!summeries.Any())
            {
                return new List<NationalLevelSummaryRequestDto>();
            }

            foreach (Summary summary in summeries)
            {
                NationalLevelSummaryRequestDto nationalLevelSummary = new NationalLevelSummaryRequestDto
                {
                    AssessmentId = assessmentId,
                    ReportCompleteness = summary.ReportCompleteness,
                    ReportTimeliness = summary.ReportTimeliness,
                    VariableCompleteness = summary.VariableCompleteness,
                    VariableConsistency = summary.VariableConsistency,
                    VariableConcordance = summary.VariableConcordance,
                    MalariaOutpatientProportion = summary.MalariaOutpatientProportion,
                    MalariaInPatientProportion = summary.MalariaInPatientProportion,
                    MalariaInPatientDeathProportion = summary.MalariaInPatientDeathProportion,
                    TestPositivityRate = summary.TestPositivityRate,
                    SlidePositivityRate = summary.SlidePositivityRate,
                    RDTPositivityRate = summary.RDTPositivityRate,
                    SuspectedTestProportion = summary.SuspectedTestProportion,
                    Type = summary.Type,
                    Year = summary.Year,
                    IsFinalized = summary.IsFinalized,
                    DataQualityResultReason = dataQualityReasons.OrderByDescending(d => d.UpdatedAt).FirstOrDefault()?.Reason
                };

                nationalLevelSummaries.Add(nationalLevelSummary);
            }

            return nationalLevelSummaries;
        }

        /// <summary>
        /// Get National level result data for data quality indicators
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <returns>List of instance of TableReportDetails</returns>
        private async Task<List<TableReportDetails>> GetNationalLevelReportSummaryDataForExcel(Guid assessmentId)
        {
            IEnumerable<Summary> summeries = await _unitOfWork.SummaryRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            IEnumerable<SummaryDataQualityResultReason> dataQualityReasons = await _unitOfWork.SummaryDataQualityResultReasonRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            // TO show only latest year data
            List<short> years = new List<short> {
               summeries
                .Select(x => (short)x.Year)
                .Distinct().OrderByDescending(x => x)
                .FirstOrDefault()
            };

            List<TableReportDetails> reportItems = new List<TableReportDetails>();

            years.ForEach((short year) =>
            {
                var summary = summeries.Where(x => x.Year == year)
                                        .Select(x => new
                                        {
                                            ReportingCompleteness = x.ReportCompleteness,
                                            ReportingTimeliness = x.ReportTimeliness,
                                            ReportingVariableCompleteness = x.VariableCompleteness,
                                            ReportingConsistencyBtwVariables = x.VariableConsistency,
                                            ReportingConcordance = x.VariableConcordance,
                                            ReportingConsistencyOverTime = "",
                                            ProportionOfMalariaOutpatients = x.MalariaOutpatientProportion,
                                            ProportionOfMalariaInpatients = x.MalariaInPatientProportion,
                                            ProportionOfMalariaInpatientDeaths = x.MalariaInPatientDeathProportion,
                                            TestPositivityRate = x.TestPositivityRate,
                                            SlidePositivityRate = x.SlidePositivityRate,
                                            RDTPositivityRate = x.RDTPositivityRate,
                                            ProportionOfSuspectsTested = x.SuspectedTestProportion,
                                            Type = x.Type
                                        }).ToList();

                string dataQualityReason = dataQualityReasons.FirstOrDefault(x => x.Year == year)?.Reason;

                DataTable summaryTable = new DataTable();

                summaryTable = summary.ToDataTable();

                List<TableColumnAttribute> headerColumns = DeskLevelDQAHelper.GetCustomDescriptionForColumn(typeof(NationalLevelSummaryReportItemsDto));

                DataTable modifiedSummaryTable = GetYearWiseNationalLevelReportSummaryData(summaryTable, headerColumns, dataQualityReason);

                modifiedSummaryTable.TableName = "National Summary Report";

                TableReportDetails nationalLevelSummaryReport = new TableReportDetails
                {
                    Title = $"{_translationService.GetDQATranslation(DQAConstants.SummaryReportTitle)} - {year.ToString()}",
                    TitleTranslationKey = $"{DQAConstants.SummaryReportTitle} - {year.ToString()}",
                    ReportTable = modifiedSummaryTable
                };

                reportItems.Add(nationalLevelSummaryReport);

            });

            return reportItems;
        }

        /// <summary>
        /// Get Elimination National level result data for data quality indicators
        /// </summary>
        /// <param name="assessmentId">Assessment Id</param>
        /// <returns>List of instance of TableReportDetails</returns>
        private async Task<List<TableReportDetails>> GetEliminationNationalLevelReportSummaryDataForExcelAsync(Guid assessmentId)
        {
            IEnumerable<Domain.Models.DQA.Elimination.Summary> summeries = await _unitOfWork.EliminationSummaryRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            IEnumerable<Domain.Models.DQA.Elimination.SummaryDataQualityResultReason> dataQualityReasons = await _unitOfWork.EliminationSummaryDataQualityReasonRepository.GetListAsync(x => x.AssessmentId == assessmentId);

            List<short> years = summeries.Select(x => (short)x.Year).Distinct().OrderByDescending(x => x).ToList();

            List<TableReportDetails> reportItems = new List<TableReportDetails>();

            List<Dictionary<string, string>> reportData = new List<Dictionary<string, string>>();
            Dictionary<string, string> report = new Dictionary<string, string>();
           
            string consistencyOverTimeTrend = _translationService.GetTranslation(ConsistencyOverTimeTrend);
            string dqaReportCompleteness =$"1.2.1 {_translationService.GetTranslation(DQAConstants.DQAReportCompleteness)}";
            string dqaCaseInvestigationReportsCompleteness =$"1.2.2 {_translationService.GetTranslation(DQAConstants.DQACaseInvestigationReportsCompleteness)}";
            string dqaReportTimeliness = $"1.2.3 {_translationService.GetTranslation(DQAConstants.DQAReportTimeliness)}";
            string dqaCaseNotificationReportsTimeliness = $"1.2.4 {_translationService.GetTranslation(DQAConstants.DQACaseNotificationReportsTimeliness)}";
            string dqaCaseInvestigationReportsTimeliness = $"1.2.5 {_translationService.GetTranslation(DQAConstants.DQACaseInvestigationReportsTimeliness)}";
            string dqaFociInvestigationReportsTimeliness = $"1.2.6 {_translationService.GetTranslation(DQAConstants.DQAFociInvestigationReportsTimeliness)}";
            string dqaCoreVariableCompletenessWithinReport = $"1.2.7 {_translationService.GetTranslation(DQAConstants.DQACoreVariableCompletenessWithinReport)}";
            string dqaConsistencyBetweenCoreVariables = $"1.2.8 {_translationService.GetTranslation(DQAConstants.DQAConsistencyBetweenCoreVariables)}";
            string dqaConsistencyOverTimeCoreIndicators = $"1.2.9 {_translationService.GetTranslation(DQAConstants.DQAConsistencyOverTimeCoreIndicators)}";
            string dqaConfirmMalariaCasesNotified = $"1. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesNotified)}";
            string dqaConfirmMalariaCasesInvestigated = $"2. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesInvestigated)}";
            string dqaConfirmMalariaCasesClassified = $"3. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesClassified)}";
            string dqaConfirmMalariaCasesClassifiedAsLocal = $"4. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesClassifiedAsLocal)}";
            string dqaConfirmMalariaCasesClassifiedAsIndigenous = $"5. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesClassifiedAsIndigenous)}";
            string dqaConfirmMalariaCasesClassifiedAsIntroduced = $"6. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesClassifiedAsIntroduced)}";
            string dqaConfirmMalariaCasesClassifiedAsImported = $"7. {_translationService.GetTranslation(DQAConstants.DQAConfirmMalariaCasesClassifiedAsImported)}";
            string dqaMalariaCasesDueToPF = $"8. {_translationService.GetTranslation(DQAConstants.DQAMalariaCasesDueToPF)}";
            string dqaMalariaCasesDueToPK = $"9. {_translationService.GetTranslation(DQAConstants.DQAMalariaCasesDueToPK)}";
            string dqaMalariaCasesDueToPM = $"10. {_translationService.GetTranslation(DQAConstants.DQAMalariaCasesDueToPM)}";
            string dqaMalariaCasesDueToPO = $"11. {_translationService.GetTranslation(DQAConstants.DQAMalariaCasesDueToPO)}";
            string dqaMalariaCasesDueToPV = $"12. {_translationService.GetTranslation(DQAConstants.DQAMalariaCasesDueToPV)}";
            string dqaKeyVariableConcordanceBtwTwoReportingSystem = $"1.2.10 {_translationService.GetTranslation(DQAConstants.DQAKeyVariableConcordanceBtwTwoReportingSystem)}";
            string dqaCoreVariableCompletenessWithinRegister = $"1.2.11 {_translationService.GetTranslation(DQAConstants.DQACoreVariableCompletenessWithinRegister)}";
            string dqaCoreVariableConcordanceBtwRegister = $"1.2.12 {_translationService.GetTranslation(DQAConstants.DQACoreVariableConcordanceBtwRegister)}";
            string dqaSummaryNationalDataQualityEstimates = _translationService.GetTranslation(DQAConstants.DQASummaryNationalDataQualityEstimates);
            string dqaQualityResultReason = _translationService.GetTranslation(DQAConstants.DataQualityResultReason);
            string dqaSummaryPercentage = _translationService.GetTranslation(DQAConstants.DQASummaryPercentage);


            years.ForEach((short year) =>
            {
                Domain.Models.DQA.Elimination.Summary summary = summeries.Where(x => x.Year == year).Single();
                Domain.Models.DQA.Elimination.SummaryDataQualityResultReason dataQualityReason = dataQualityReasons.SingleOrDefault(x => x.Year == year);

                report.Add(dqaReportCompleteness, summary.ReportCompleteness?.ToYesNo());
                report.Add(dqaCaseInvestigationReportsCompleteness, summary.CaseInvestigationReportsCompleteness.ToString());
                //commented this code as client requested to remve 1.2.3 DQAReportTimeliness
                //report.Add(dqaReportTimeliness, summary.ReportTimeliness.ToString());
                report.Add(dqaCaseNotificationReportsTimeliness, summary.CaseNotificationReportsTimeliness.ToString());
                report.Add(dqaCaseInvestigationReportsTimeliness, summary.CaseInvestigationReportsTimeliness.ToString());
                report.Add(dqaFociInvestigationReportsTimeliness, summary.FociInvestigationReportsTimeliness.ToString());
                report.Add(dqaCoreVariableCompletenessWithinReport, summary.CoreVariableCompletenessWithinReport.ToString());
                report.Add(dqaConsistencyBetweenCoreVariables, summary.ConsistencyBetweenCoreVariables.ToString());
                report.Add(dqaConsistencyOverTimeCoreIndicators, summary.ConsistencyOverTimeCoreIndicators?.ToYesNo());
                report.Add("", consistencyOverTimeTrend);
                report.Add(dqaConfirmMalariaCasesNotified, summary.ConfirmMalariaCasesNotified?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesInvestigated, summary.ConfirmMalariaCasesInvestigated?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesClassified, summary.ConfirmMalariaCasesClassified?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesClassifiedAsLocal, summary.ConfirmMalariaCasesClassifiedAsLocal?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesClassifiedAsIndigenous, summary.ConfirmMalariaCasesClassifiedAsIndigenous?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesClassifiedAsIntroduced, summary.ConfirmMalariaCasesClassifiedAsIntroduced?.ToYesNo());
                report.Add(dqaConfirmMalariaCasesClassifiedAsImported, summary.ConfirmMalariaCasesClassifiedAsImported?.ToYesNo());
                report.Add(dqaMalariaCasesDueToPF, summary.MalariaCasesDueToPF?.ToYesNo());
                report.Add(dqaMalariaCasesDueToPK, summary.MalariaCasesDueToPK?.ToYesNo());
                report.Add(dqaMalariaCasesDueToPM, summary.MalariaCasesDueToPM?.ToYesNo());
                report.Add(dqaMalariaCasesDueToPO, summary.MalariaCasesDueToPO?.ToYesNo());
                report.Add(dqaMalariaCasesDueToPV, summary.MalariaCasesDueToPV?.ToYesNo());
                report.Add(dqaKeyVariableConcordanceBtwTwoReportingSystem, summary.KeyVariableConcordanceBtwTwoReportingSystem?.ToYesNo());
                report.Add(dqaCoreVariableCompletenessWithinRegister, summary.CoreVariableCompletenessWithinRegister.ToString());
                report.Add(dqaCoreVariableConcordanceBtwRegister, summary.CoreVariableConcordanceBtwRegister.ToString());
                report.Add(dqaQualityResultReason, dataQualityReason?.Reason);

                DataTable summaryTable = new DataTable();
                summaryTable.Columns.Add(dqaSummaryNationalDataQualityEstimates);
                summaryTable.Columns.Add(dqaSummaryPercentage);

                foreach (KeyValuePair<string, string> reportItem in report)
                {
                    DataRow dataRow = summaryTable.NewRow();
                    dataRow[0] = reportItem.Key;
                    dataRow[1] = reportItem.Value;
                    summaryTable.Rows.Add(dataRow);
                }

                summaryTable.TableName = "Elimination National Summary Report";

                TableReportDetails nationalLevelSummaryReport = new TableReportDetails
                {
                    Title = $"{_translationService.GetDQATranslation(DQAConstants.SummaryReportTitle)} - {year.ToString()}",
                    TitleTranslationKey = $"{DQAConstants.SummaryReportTitle} - {year.ToString()}",
                    ReportTable = summaryTable,
                };

                reportItems.Add(nationalLevelSummaryReport);
            });

            return reportItems;
        }

        /// <summary>
        /// Get yearwise national level summary result
        /// </summary>
        /// <param name="summaryTable">Original summary result table</param>
        /// <param name="headerColumns">Modified header of table</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetYearWiseNationalLevelReportSummaryData(DataTable summaryTable, List<TableColumnAttribute> headerColumns, string dataQualityReason)
        {
            List<NationalLevelSummaryReportItemsDto> summeries = new List<NationalLevelSummaryReportItemsDto>();

            Dictionary<string, string> reportIndicators = UtilityHelper.GetEnumItemsTranslationKey(typeof(DQAIndicatorReport));
            Dictionary<string, string> consistencyOverTimeChecks = UtilityHelper.GetEnumItemsTranslationKey(typeof(ConsistencyOverTimeKeyIndicator));
            consistencyOverTimeChecks.Add("ReportingConsistencyOverTime", "ReportingConsistencyOverTime");

            DataColumnCollection columns = summaryTable.Columns;

            DataRow[] nationalLevelResult = summaryTable.Select($"Type = {(int)DQADLSummaryResultType.NationalLevelResults}");
            DataRow[] nationalLeveltarget = summaryTable.Select($"Type = {(int)DQADLSummaryResultType.NationalLevelTarget}");

            int nationalLeveltargetRowCount = nationalLeveltarget.Count();

            int consistencyOverTimePropTrueCount = 0;

            foreach (DataColumn column in columns)
            {
                if (column.ColumnName.ToLower() != TypeProperty.ToLower())
                {
                    bool consistencyOverTimeProperty = consistencyOverTimeChecks.Any(x => x.Key == column.ColumnName);
                    string nationalResult = string.Empty;
                    Type columnType = (nationalLevelResult[0][column.ColumnName]).GetType();
                    if (consistencyOverTimeProperty && columnType == typeof(bool) && nationalLeveltargetRowCount > 0)
                    {

                        nationalResult = Convert.ToBoolean(nationalLeveltarget[0][column.ColumnName]).ToYesNo();
                        if (Convert.ToBoolean(nationalLeveltarget[0][column.ColumnName]))
                        {
                            consistencyOverTimePropTrueCount++;
                        }
                    }
                    else
                    {
                        if (columnType != typeof(bool) && nationalLeveltargetRowCount <= 0)
                        {
                            nationalResult = Convert.ToString(nationalLevelResult[0][column.ColumnName]);
                        }
                        else if (columnType != typeof(bool) && nationalLeveltargetRowCount > 0)
                        {
                            nationalResult = Convert.ToString(nationalLevelResult[0][column.ColumnName]);
                        }
                    }

                    NationalLevelSummaryReportItemsDto summary = new NationalLevelSummaryReportItemsDto
                    {
                        DataQualityIndicator = _translationService.GetTranslation(reportIndicators.FirstOrDefault(x => x.Key == column.ColumnName).Value ?? consistencyOverTimeChecks.FirstOrDefault(x => x.Key == column.ColumnName).Value),
                        NationalLevelResult = nationalResult,
                        NationalLevelTarget = !consistencyOverTimeProperty && nationalLeveltargetRowCount > 0 ? Convert.ToString(nationalLeveltarget[0][column.ColumnName]) : "",
                        IsConsistencyOverTimeProperty = consistencyOverTimeProperty
                    };
                    summeries.Add(summary);

                    if (column.ColumnName == Enum.GetName(typeof(DQAIndicatorReport), DQAIndicatorReport.ReportingConsistencyOverTime))
                    {
                        summeries.Add(new NationalLevelSummaryReportItemsDto
                        {
                            DataQualityIndicator = "",
                            NationalLevelResult = _translationService.GetTranslation(ConsistencyOverTimeTrend),
                            NationalLevelTarget = "",
                            IsConsistencyOverTimeProperty = false
                        });
                    }
                }
            }

            NationalLevelSummaryReportItemsDto rowReportingConsistencyOverTime = summeries.FirstOrDefault(x => x.DataQualityIndicator == _translationService.GetTranslation(DQAIndicatorReport.ReportingConsistencyOverTime.GetTranslationKey()));
            rowReportingConsistencyOverTime.NationalLevelResult = consistencyOverTimePropTrueCount == 7 ? _translationService.GetTranslation(MetNotMetStatus.Met.GetTranslationKey()) : _translationService.GetTranslation(MetNotMetStatus.NotMet.GetTranslationKey());

            NationalLevelSummaryReportItemsDto qualityReason = new NationalLevelSummaryReportItemsDto
            {
                DataQualityIndicator = _translationService.GetTranslation(DQAConstants.DataQualityResultReason),
                NationalLevelResult = dataQualityReason,
                NationalLevelTarget = "",
                IsConsistencyOverTimeProperty = false
            };
            summeries.Add(qualityReason);

            DataTable modifiedSummaryTable = GetNewNationalLevelSummaryTable(headerColumns, summeries.ToDataTable());

            return modifiedSummaryTable;
        }

        /// <summary>
        /// Get yearwise national level summary result for Front end
        /// </summary>
        /// <param name="summaryTable">Original summary result table</param>
        /// <param name="headerColumns">Modified header of table</param>
        /// <returns>Instance of NationalLevelSummaryRequestDto</returns>
        private DQADeskLevelNationalSummaryData GetYearWiseFENationalLevelReportSummaryData(DataTable summaryTable, List<TableColumnAttribute> headerColumns)
        {
            DataTable qualitySummary = new DataTable();
            DataTable consistencyOverTimeSummary = new DataTable();

            List<NationalLevelSummaryReportItemsDto> summeries = new List<NationalLevelSummaryReportItemsDto>();

            Dictionary<string, string> reportIndicators = UtilityHelper.GetEnumItemsTranslationKey(typeof(DQAIndicatorReport));
            Dictionary<string, string> consistencyOverTimeChecks = UtilityHelper.GetEnumItemsTranslationKey(typeof(ConsistencyOverTimeKeyIndicator));
            consistencyOverTimeChecks.Add("ReportingConsistencyOverTime", "ReportingConsistencyOverTime");

            DataColumnCollection columns = summaryTable.Columns;

            DataRow[] nationalLevelResult = summaryTable.Select($"Type = {(int)DQADLSummaryResultType.NationalLevelResults}");
            DataRow[] nationalLeveltarget = summaryTable.Select($"Type = {(int)DQADLSummaryResultType.NationalLevelTarget}");

            int nationalLeveltargetRowCount = nationalLeveltarget.Count();

            foreach (DataColumn column in columns)
            {
                if (column.ColumnName.ToLower() != TypeProperty.ToLower())
                {
                    bool consistencyOverTimeProperty = consistencyOverTimeChecks.Any(x => x.Key == column.ColumnName);
                    string nationalResult = string.Empty;
                    Type columnType = (nationalLevelResult[0][column.ColumnName]).GetType();
                    if (consistencyOverTimeProperty && columnType == typeof(bool) && nationalLeveltargetRowCount > 0)
                    {
                        nationalResult = Convert.ToString(nationalLeveltarget[0][column.ColumnName]);
                    }
                    else
                    {
                        if (columnType != typeof(bool) && nationalLeveltargetRowCount <= 0)
                        {
                            nationalResult = Convert.ToString(nationalLevelResult[0][column.ColumnName]);
                        }
                        else if (columnType != typeof(bool) && nationalLeveltargetRowCount > 0)
                        {
                            nationalResult = Convert.ToString(nationalLevelResult[0][column.ColumnName]);
                        }
                    }
                    NationalLevelSummaryReportItemsDto summary = new NationalLevelSummaryReportItemsDto
                    {
                        DataQualityIndicator = reportIndicators.FirstOrDefault(x => x.Key == column.ColumnName).Value ?? consistencyOverTimeChecks.FirstOrDefault(x => x.Key == column.ColumnName).Value,
                        NationalLevelResult = nationalResult,
                        NationalLevelTarget = !consistencyOverTimeProperty && nationalLeveltargetRowCount > 0 ? Convert.ToString(nationalLeveltarget[0][column.ColumnName]) : "",
                        IsConsistencyOverTimeProperty = consistencyOverTimeProperty
                    };
                    summeries.Add(summary);
                }
            }

            qualitySummary = summeries.Where(x => x.IsConsistencyOverTimeProperty == false).Select(x => new { x.DataQualityIndicator, x.NationalLevelResult, x.NationalLevelTarget }).ToList().ToDataTable();
            qualitySummary.TableName = "DataQualityIndicators";

            consistencyOverTimeSummary = summeries.Where(x => x.IsConsistencyOverTimeProperty == true).Select(x => new { x.DataQualityIndicator, x.NationalLevelResult }).ToList().ToDataTable();
            consistencyOverTimeSummary.TableName = "ConsistencyOverTimeData";

            return new DQADeskLevelNationalSummaryData
            {
                DataQualityIndicators = qualitySummary,
                ConsistencyOverTimeData = consistencyOverTimeSummary,
            };
        }


        /// <summary>
        /// Gets column list to show response on FE
        /// </summary>
        /// <param name="modifiedHeaderColumns">List of instance of TableColumn</param>
        /// <returns>List of instance of TableColumnforJson</returns>
        private List<TableColumnforJson> GetColumnsForFETable(List<TableColumn> modifiedHeaderColumns, int? reportType = null, int? indicatorSequence = null)
        {
            List<TableColumnforJson> tableColumnforJsons = new List<TableColumnforJson>();

            List<string> dynamicColumns = new List<string>();

            modifiedHeaderColumns.ForEach((TableColumn column) =>
            {
                //if column is generated from provided data then if loop executes, for other static columns else loop executes
                if (column.Headers.GetType().IsGenericType && column.Headers.Count > 1)
                {
                    dynamicColumns = column.Headers.Select(x => x.ToString()).ToList();

                    foreach (string columnItem in dynamicColumns)
                    {
                        //generate column according to expected FE format
                        TableColumnforJson tableColumn = new TableColumnforJson();

                        if (columnItem.ToLower() == IPTp.ToLower() || columnItem.ToLower() == ANC.ToLower())
                        {
                            tableColumn.Key = columnItem.ToLowerFirstChar(2);

                        }
                        else
                        {
                            tableColumn.Key = columnItem.ToLowerFirstChar(3);
                        }

                        tableColumn.Label = columnItem;
                        tableColumn.Width = column.Width;

                        tableColumnforJsons.Add(tableColumn);
                    }
                }
                else
                {
                    string staticColumn = column.Headers.Select(x => x.ToString()).FirstOrDefault();

                    if (staticColumn != null)
                    {
                        string columnName = _translationService.GetDQATranslation(staticColumn);
                        //generate column according to expected FE format
                        TableColumnforJson tableColumn = new TableColumnforJson()
                        {
                            Key = columnName.ToLowerFirstChar(1),
                            Label = !string.IsNullOrEmpty(columnName) ? columnName : staticColumn,
                            Width = column.Width
                        };

                        tableColumnforJsons.Add(tableColumn);
                    }
                }
            });

            //Remove the unwanted column from list
            string percentageProperty = _translationService.GetTranslation(PercentageProperty).ToLower();

            if (reportType != (int)DQAReportType.NationalReport3 && reportType != (int)DQAReportType.ProvinceReport3
                && reportType != (int)DQAReportType.DistrictReport3 && reportType != (int)DQAReportType.HealthFacilityReport3 && tableColumnforJsons.Any(x => x.Key == percentageProperty))
            {
                TableColumnforJson itemToRemove = tableColumnforJsons.Where(x => x.Key == percentageProperty).FirstOrDefault();
                tableColumnforJsons.Remove(itemToRemove);
            }
            else
            {
                TableColumnforJson tableColumnforJson = tableColumnforJsons.FirstOrDefault(x => x.Key == percentageProperty);
                if (tableColumnforJson != null)
                {
                    tableColumnforJson.Label = GetPercentageColumnTranslation(indicatorSequence ?? 0);
                }
            }

            if (reportType == null)
            {
                if (tableColumnforJsons.Any(x => x.Key == DQAConstants.CompletenessReportResultHeader.ToLower()))
                {
                    TableColumnforJson itemToRemove = tableColumnforJsons.Where(x => x.Key == DQAConstants.CompletenessReportResultHeader.ToLower()).FirstOrDefault();
                    tableColumnforJsons.Remove(itemToRemove);
                }

                if (tableColumnforJsons.Any(x => x.Key == DQAConstants.ConsistencyReportResultHeader.ToLower()))
                {
                    TableColumnforJson itemToRemove = tableColumnforJsons.Where(x => x.Key == DQAConstants.ConsistencyReportResultHeader.ToLower()).FirstOrDefault();
                    tableColumnforJsons.Remove(itemToRemove);
                }

                if (tableColumnforJsons.Any(x => x.Key == DQAConstants.ConcordanceReportResultHeader.ToLower()))
                {
                    TableColumnforJson itemToRemove = tableColumnforJsons.Where(x => x.Key == DQAConstants.ConcordanceReportResultHeader.ToLower()).FirstOrDefault();
                    tableColumnforJsons.Remove(itemToRemove);
                }
            }

            return tableColumnforJsons;
        }

        /// <summary>
        /// Get all indicator report data for excel
        /// </summary>
        /// <param name="assessmentId">The id of the assessment</param>
        /// <returns>List of instance of DeskLevelExcelTemplateSheetDetails</returns>
        private async Task<List<DeskLevelExcelTemplateSheetDetails>> GetDataForExcelReport(Guid assessmentId)
        {
            List<DeskLevelExcelTemplateSheetDetails> reportTables = new List<DeskLevelExcelTemplateSheetDetails>();

            List<DeskLevelDataSystem1> dataSystem1HealthFacilities = new List<DeskLevelDataSystem1>();
            List<DeskLevelDataSystem2> dataSystem2HealthFacilities = new List<DeskLevelDataSystem2>();

            dataSystem1HealthFacilities = (List<DeskLevelDataSystem1>)_cacheService.GetDataFromCache(DQAConstants.DeskLevelDataSystem1);
            dataSystem2HealthFacilities = (List<DeskLevelDataSystem2>)_cacheService.GetDataFromCache(DQAConstants.DeskLevelDataSystem2);

            if (dataSystem1HealthFacilities == null && dataSystem2HealthFacilities == null)
            {
                IEnumerable<DeskLevelDataSystem1> healthFacilityData = await _unitOfWork.DeskLevelDataSystem1Repository.GetListAsync(x => x.AssessmentId == assessmentId);
                IEnumerable<DeskLevelDataSystem2> healthFacilityConcordanceData = await _unitOfWork.DeskLevelDataSystem2Repository.GetListAsync(x => x.AssessmentId == assessmentId);

                dataSystem1HealthFacilities = healthFacilityData.ToList();
                dataSystem2HealthFacilities = healthFacilityConcordanceData.ToList();
            }

            List<TemplateDataSourceCombinedVariablesDto> dataSystem1 = UtilityHelper.GetDataInModelFromEntity<TemplateDataSourceCombinedVariablesDto, DeskLevelDataSystem1>(dataSystem1HealthFacilities);
            List<TemplateDataSourceCombinedVariablesDto> dataSystem2 = UtilityHelper.GetDataInModelFromEntity<TemplateDataSourceCombinedVariablesDto, DeskLevelDataSystem2>(dataSystem2HealthFacilities);

            //  Get Selected variable dto
            SelectedVariableDto selectedVariablesDto = await _unitOfWork.DQARepository.GetDeskLevelSelectedVariablesForCalculation(assessmentId);

            if (dataSystem1.Any() && dataSystem2.Any())
            {
                Dictionary<string, int> indicators = UtilityHelper.GetEnumerableData<DQAIndicatorReport>();
                Dictionary<string, int> reportTypes = UtilityHelper.GetEnumerableData<DQAReportType>();

                List<TableReportDetails> summaryReportData = await GetNationalLevelReportSummaryDataForExcel(assessmentId);

                DeskLevelExcelTemplateSheetDetails summaryReportTable = new DeskLevelExcelTemplateSheetDetails
                {
                    Name = $"{_translationService.GetDQATranslation(DQAConstants.SummaryReportName)}",
                    Title = $"{ _translationService.GetDQATranslation(DQAConstants.SummaryReportName)}",
                    ReportDetails = summaryReportData,
                };

                reportTables.Add(summaryReportTable);

                TableColumn priorityVariableColumns = null;
                TableColumn optionalVariableColumns = null;

                foreach (KeyValuePair<string, int> indicator in indicators)
                {
                    DeskLevelExcelTemplateSheetDetails reportTable = new DeskLevelExcelTemplateSheetDetails();
                    List<TableReportDetails> tableReports = new List<TableReportDetails>();

                    reportTable.Name = GetReportExcelSheetTitle(indicator.Value, out reportTitleKey);

                    reportTable.Title = GetReportTitle(indicator.Value, out reportTitleKey);

                    reportTable.NameTranslationKey = reportTitleKey;

                    switch (indicator.Value)
                    {
                        case (int)DQAIndicatorReport.ReportingCompleteness:

                        case (int)DQAIndicatorReport.ReportingTimeliness:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                object reports = new object();
                                DataTable yearWiseData = new DataTable();
                                DataTable staticReportData = new DataTable();
                                DataTable resultTable = new DataTable();
                                List<TableColumn> headerColumns = new List<TableColumn>();
                                List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();
                                List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetNationalLevelReport(calculatedReports);

                                        yearWiseData = ((List<NationalLevelDto>)reports)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.Variables,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        yearWiseData.Columns.Remove(VariableProperty);

                                        yearWiseData.TableName = "NationalReport";
                                        string titleKey;
                                        string title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails nationalReport = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type,
                                            ReportTable = yearWiseData,
                                            GraphReport = yearWiseData
                                        };

                                        tableReports.Add(nationalReport);

                                        break;

                                    case (int)DQAReportType.NationalReport1:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId);

                                        reports = GetNationalLevelReportType1(calculatedReports);

                                        yearWiseData = ((List<NationalLevelType1Dto>)reports)
                                                        .OrderByDescending(x => x.HealthFacilityType)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.HealthFacilityType,
                                                                items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                                        yearWiseData.TableName = "NationalReport1";

                                        yearWiseData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWiseData.Columns[0].ColumnName) ? _translationService.GetDQATranslation(yearWiseData.Columns[0].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails nationalReport1 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type1,
                                            ReportTable = yearWiseData,
                                            GraphReport = yearWiseData
                                        };


                                        tableReports.Add(nationalReport1);

                                        break;

                                    case (int)DQAReportType.ProvinceReport:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetProvinceLevelReport(calculatedReports);

                                        yearWiseData = ((List<ProvinceLevelDto>)reports)
                                                        .OrderBy(x => x.Province)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.Province,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        yearWiseData.TableName = "ProvinceReport";
                                        yearWiseData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWiseData.Columns[0].ColumnName) ? _translationService.GetDQATranslation(yearWiseData.Columns[0].ColumnName) : string.Empty;
                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails provinceReport = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type,
                                            ReportTable = yearWiseData,
                                            GraphReport = yearWiseData
                                        };


                                        tableReports.Add(provinceReport);

                                        break;

                                    case (int)DQAReportType.ProvinceReport1:

                                        ReportGenerationRequest request = new ReportGenerationRequest()
                                        {
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(ProvinceLevelReportType1Dto),
                                            GetReport = GetProvinceLevelReportType1,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto,
                                            AssessmentId = assessmentId
                                        };

                                        reports = GetReportData(request, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<ProvinceLevelType1Dto> province1reports = ((List<ProvinceLevelType1Dto>)reports).OrderBy(x => x.Province);

                                        staticReportData = GetStaticReportData(province1reports, new string[] { ProvinceProperty, HealthFacilityProperty }, modifiedHeaderColumns);

                                        resultTable = GetProvinceLevelType1DataTable(province1reports, staticReportData, modifiedHeaderColumns);

                                        resultTable.TableName = "ProvinceReport1";


                                        resultTable.Columns[0].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[0].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[0].ColumnName) : string.Empty;
                                        resultTable.Columns[1].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[1].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[1].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails provinceReport1 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = resultTable,
                                        };

                                        tableReports.Add(provinceReport1);

                                        break;

                                    case (int)DQAReportType.DistrictReport1:

                                        ReportGenerationRequest districtRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(DistrictLevelReportType1Dto),
                                            GetReport = GetDistrictLevelReportType1,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(districtRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        yearWiseData = ((List<DistrictLevelType1Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.District)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.District,
                                                                items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                                        IOrderedEnumerable<DistrictLevelType1Dto> district1Reports = ((List<DistrictLevelType1Dto>)reports).OrderBy(x => x.District);

                                        staticReportData = GetStaticReportData(district1Reports, new string[] { ProvinceProperty, DistrictProperty }, modifiedHeaderColumns);

                                        resultTable = JoinDataTable(staticReportData, yearWiseData);

                                        resultTable.TableName = "DistrictReport1";

                                        resultTable.Columns[0].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[0].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[0].ColumnName) : string.Empty;
                                        resultTable.Columns[1].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[1].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[1].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails districtReport1 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type,
                                            ReportTable = resultTable,
                                            GraphReport = yearWiseData
                                        };

                                        tableReports.Add(districtReport1);

                                        break;

                                    case (int)DQAReportType.HealthFacilityReport1:

                                        ReportGenerationRequest hfRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(HealthFacilityReportType1Dto),
                                            GetReport = GetHealthFacilityLevelReportType1,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(hfRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        yearWiseData = ((List<HealthFacilityType1Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.HealthFacilityName,
                                                                items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                                        IOrderedEnumerable<HealthFacilityType1Dto> healthfacility1report = ((List<HealthFacilityType1Dto>)reports).OrderBy(x => x.HealthFacilityName);

                                        staticReportData = GetStaticReportData(healthfacility1report, new string[] { ProvinceProperty, DistrictProperty, HealthFacilityNameProperty }, modifiedHeaderColumns);

                                        resultTable = JoinDataTable(staticReportData, yearWiseData);

                                        resultTable.TableName = "HealthFacilityReport1";

                                        resultTable.Columns[0].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[0].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[0].ColumnName) : string.Empty;
                                        resultTable.Columns[1].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[1].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[1].ColumnName) : string.Empty;
                                        resultTable.Columns[2].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[2].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[2].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails healthFacilityReport1 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = resultTable,
                                        };

                                        tableReports.Add(healthFacilityReport1);

                                        break;
                                }
                            }

                            reportTable.ReportDetails = tableReports;

                            break;

                        case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                        case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                        case (int)DQAIndicatorReport.ReportingConcordance:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                object reports = new object();
                                DataTable yearWiseData = new DataTable();
                                DataTable summaryTable = new DataTable();
                                DataTable staticReportData = new DataTable();
                                DataTable resultTable = new DataTable();
                                List<TableColumn> headerColumns = new List<TableColumn>();
                                List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();
                                List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport2:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetNationalLevelReportType2(calculatedReports);

                                        yearWiseData = ((List<NationalLevelType2Dto>)reports)
                                                        .OrderBy(x => x.Year)
                                                        .ToPivotTable(
                                                                item => item.Variables,
                                                                item => item.Year,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        yearWiseData.TableName = "NationalReport2";


                                        yearWiseData.Columns[0].ColumnName = !string.IsNullOrEmpty(yearWiseData.Columns[0].ColumnName) ? _translationService.GetDQATranslation(yearWiseData.Columns[0].ColumnName) : string.Empty;

                                        string titleKey;
                                        string title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails nationalReport2 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = yearWiseData,
                                        };

                                        tableReports.Add(nationalReport2);

                                        break;

                                    case (int)DQAReportType.NationalReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetNationalLevelReportType3(calculatedReports);

                                        summaryTable = ((List<NationalLevelReportType3Dto>)reports).OrderBy(x => x.Year).ToList().ToDataTable();

                                        summaryTable.Columns.Remove("VariableType");

                                        summaryTable.TableName = "NationalReport3";

                                        yearWiseData = ((List<NationalLevelReportType3Dto>)reports)
                                                        .OrderBy(x => x.Year)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.VariableType,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        yearWiseData.Columns.Remove("VariableType");

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        summaryTable.Columns[PercentageColumn].ColumnName = GetPercentageColumnTranslation(indicator.Value);

                                        TableReportDetails nationalReport3 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type3,
                                            ReportTable = GetNewSummaryTable(indicator.Value, summaryTable),
                                            GraphReport = yearWiseData
                                        };

                                        tableReports.Add(nationalReport3);

                                        break;

                                    case (int)DQAReportType.ProvinceReport2:

                                        ReportGenerationRequest provinceRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(ProvinceLevelReportType2Dto),
                                            GetReport = GetProvinceLevelReportType2,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(provinceRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<ProvinceLevelType2Dto> province2report = ((List<ProvinceLevelType2Dto>)reports).OrderBy(x => x.Province).ThenBy(x => x.Year);

                                        staticReportData = GetStaticReportData(province2report, new string[] { "Province", "Year" }, modifiedHeaderColumns);

                                        resultTable = GetProvinceLevelType2DataTable(province2report, staticReportData, modifiedHeaderColumns);

                                        resultTable.TableName = "ProvinceReport1";

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails provinceReport2 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = resultTable,
                                        };

                                        tableReports.Add(provinceReport2);

                                        break;

                                    case (int)DQAReportType.ProvinceReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetProvinceLevelReportType3(calculatedReports);

                                        summaryTable = ((List<ProvinceLevelReportType3Dto>)reports).OrderBy(x => x.Province).ThenBy(x => x.Year).ToList().ToDataTable();

                                        summaryTable.TableName = "ProvinceReport3";

                                        yearWiseData = ((List<ProvinceLevelReportType3Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.Year)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.Province,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        summaryTable.Columns[PercentageColumn].ColumnName = GetPercentageColumnTranslation(indicator.Value);

                                        TableReportDetails provinceReport3 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type3,
                                            ReportTable = GetNewSummaryTable(indicator.Value, summaryTable),
                                            GraphReport = yearWiseData
                                        };

                                        tableReports.Add(provinceReport3);

                                        break;

                                    case (int)DQAReportType.DistrictReport2:

                                        ReportGenerationRequest districtRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(DistrictLevelReportType2Dto),
                                            GetReport = GetDistrictLevelReportType2,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(districtRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<DistrictLevelType2Dto> district2report = ((List<DistrictLevelType2Dto>)reports).OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year);

                                        staticReportData = GetStaticReportData(district2report, new string[] { "Province", "District", "Year" }, modifiedHeaderColumns);

                                        resultTable = GetDistrictLevelType2DataTable(district2report, staticReportData, modifiedHeaderColumns);

                                        resultTable.TableName = "DistrictReport2";

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails districtReport2 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = resultTable,
                                        };

                                        tableReports.Add(districtReport2);

                                        break;

                                    case (int)DQAReportType.DistrictReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetDistrictLevelReportType3(calculatedReports);

                                        summaryTable = ((List<DistrictLevelReportType3Dto>)reports).OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year).ToList().ToDataTable();

                                        summaryTable.TableName = "DistrictReport3";

                                        yearWiseData = ((List<DistrictLevelReportType3Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year)
                                                        .ToPivotTable(
                                                                item => item.Year,
                                                                item => item.District,
                                                                items => items.Any() ? items.Average(x => x.Percentage).ToString() : null);

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        summaryTable.Columns[PercentageColumn].ColumnName = GetPercentageColumnTranslation(indicator.Value);

                                        TableReportDetails districtReport3 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type3,
                                            ReportTable = GetNewSummaryTable(indicator.Value, summaryTable),
                                            GraphReport = yearWiseData
                                        };

                                        tableReports.Add(districtReport3);

                                        break;

                                    case (int)DQAReportType.HealthFacilityReport2:

                                        ReportGenerationRequest hfRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(HealthFacilityReportType2Dto),
                                            GetReport = GetHealthFacilityLevelReportType2,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(hfRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<HealthFacilityType2Dto> healthfacility2report = ((List<HealthFacilityType2Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName).ThenBy(x => x.Year);

                                        staticReportData = GetStaticReportData(healthfacility2report, new string[] { "Province", "District", "HealthFacilityName", "Year" }, modifiedHeaderColumns);

                                        resultTable = GetHealthFacilityLevelType2DataTable(healthfacility2report, staticReportData, modifiedHeaderColumns);

                                        resultTable.TableName = "HealthFacilityReport2";

                                        resultTable.Columns[2].ColumnName = !string.IsNullOrEmpty(resultTable.Columns[2].ColumnName) ? _translationService.GetDQATranslation(resultTable.Columns[2].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        TableReportDetails healthFacilityReport2 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            ReportTable = resultTable,
                                        };

                                        tableReports.Add(healthFacilityReport2);

                                        break;

                                    case (int)DQAReportType.HealthFacilityReport3:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetHealthFacilityLevelReportType3(calculatedReports);

                                        summaryTable = ((List<HealthFacilityReportType3Dto>)reports)
                                            .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName).ThenBy(x => x.Year).ToList().ToDataTable();

                                        summaryTable.TableName = "HealthFacilityReport3";

                                        summaryTable.Columns[0].ColumnName = !string.IsNullOrEmpty(summaryTable.Columns[0].ColumnName) ? _translationService.GetDQATranslation(summaryTable.Columns[0].ColumnName) : string.Empty;
                                        summaryTable.Columns[3].ColumnName = !string.IsNullOrEmpty(summaryTable.Columns[3].ColumnName) ? _translationService.GetDQATranslation(summaryTable.Columns[3].ColumnName) : string.Empty;

                                        title = GetReportCategoryTitle(indicator.Value, reportType.Value, null, out titleKey);

                                        summaryTable.Columns[PercentageColumn].ColumnName = GetPercentageColumnTranslation(indicator.Value);

                                        TableReportDetails healthFacilityReport3 = new TableReportDetails
                                        {
                                            Title = title,
                                            TitleTranslationKey = titleKey,
                                            Type = (int)DeskLevelGraphTypes.Type3,
                                            ReportTable = GetNewSummaryTable(indicator.Value, summaryTable),
                                            GraphReport = GetNewSummaryTable(indicator.Value, summaryTable)
                                        };

                                        tableReports.Add(healthFacilityReport3);

                                        break;
                                }

                            }

                            reportTable.ReportDetails = tableReports;

                            break;

                        case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                            foreach (KeyValuePair<string, int> reportType in reportTypes)
                            {
                                object reports = new object();
                                DataTable staticReportData = new DataTable();
                                List<TableReportDetails> resultReports = new List<TableReportDetails>();
                                List<TableColumn> headerColumns = new List<TableColumn>();
                                List<TableColumn> modifiedHeaderColumns = new List<TableColumn>();
                                List<DQAReportDto> calculatedReports = new List<DQAReportDto>();

                                switch (reportType.Value)
                                {
                                    case (int)DQAReportType.NationalReport1:

                                        calculatedReports = _reportGeneration.GenerateReport(indicator.Value, reportType.Value, dataSystem1, dataSystem2, assessmentId, selectedVariablesDto);

                                        reports = GetNationalLevelReportType1(calculatedReports);

                                        resultReports = GetConsistencyOverTimeNationalLevelData((List<NationalLevelType1Dto>)reports, indicator.Value, reportType.Value);

                                        tableReports.AddRange(resultReports);

                                        break;

                                    case (int)DQAReportType.ProvinceReport:

                                        ReportGenerationRequest request = new ReportGenerationRequest()
                                        {
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(ProvinceLevelReportDto),
                                            GetReport = GetProvinceLevelReport,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto,
                                            AssessmentId = assessmentId
                                        };

                                        reports = GetReportData(request, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<ProvinceLevelDto> provinceReports = ((List<ProvinceLevelDto>)reports).OrderBy(x => x.Province);

                                        staticReportData = GetStaticReportData(provinceReports, new string[] { ProvinceProperty }, modifiedHeaderColumns);

                                        resultReports = GetConsistencyOverTimeProvinceLevelData(provinceReports, staticReportData, modifiedHeaderColumns, indicator.Value, reportType.Value);

                                        tableReports.AddRange(resultReports);

                                        break;

                                    case (int)DQAReportType.DistrictReport1:

                                        ReportGenerationRequest districtRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(DistrictLevelReportType1Dto),
                                            GetReport = GetDistrictLevelReportType1,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(districtRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<DistrictLevelType1Dto> district1report = ((List<DistrictLevelType1Dto>)reports).OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.Year);

                                        staticReportData = GetStaticReportData(district1report, new string[] { ProvinceProperty, DistrictProperty }, modifiedHeaderColumns);

                                        resultReports = GetConsistencyOverTimeDistrictLevelData(district1report, staticReportData, modifiedHeaderColumns, indicator.Value, reportType.Value);

                                        tableReports.AddRange(resultReports);

                                        break;

                                    case (int)DQAReportType.HealthFacilityReport1:

                                        ReportGenerationRequest hfRequest = new ReportGenerationRequest()
                                        {
                                            AssessmentId = assessmentId,
                                            IndicatorSequence = indicator.Value,
                                            ReportType = reportType.Value,
                                            HeaderRowType = typeof(HealthFacilityReportType1Dto),
                                            GetReport = GetHealthFacilityLevelReportType1,
                                            DataSystem1 = dataSystem1,
                                            DataSystem2 = dataSystem2,
                                            SelectedVariable = selectedVariablesDto
                                        };

                                        reports = GetReportData(hfRequest, out modifiedHeaderColumns, out priorityVariableColumns, out optionalVariableColumns);

                                        IOrderedEnumerable<HealthFacilityType1Dto> healthfacility1report = ((List<HealthFacilityType1Dto>)reports)
                                                        .OrderBy(x => x.Province).ThenBy(x => x.District).ThenBy(x => x.HealthFacilityName).ThenBy(x => x.Year);

                                        staticReportData = GetStaticReportData(healthfacility1report, new string[] { ProvinceProperty, DistrictProperty, HealthFacilityNameProperty }, modifiedHeaderColumns);

                                        resultReports = GetConsistencyOverTimeHealthFacilityLevelData(healthfacility1report, staticReportData, modifiedHeaderColumns, indicator.Value, reportType.Value);

                                        tableReports.AddRange(resultReports);

                                        break;
                                }
                            }

                            reportTable.ReportDetails = tableReports;

                            break;

                    }

                    reportTables.Add(reportTable);
                }

            }

            return reportTables;
        }

        /// <summary>
        /// Get Elimination report data for excel
        /// </summary>
        /// <param name="assessmentId">The id of the assessment</param>
        /// <returns>List of instance of DeskLevelExcelTemplateSheetDetails</returns>
        private async Task<List<DeskLevelExcelTemplateSheetDetails>> GetEliminationDataForExcelReportAsync(Guid assessmentId)
        {
            List<DeskLevelExcelTemplateSheetDetails> reportTables = new List<DeskLevelExcelTemplateSheetDetails>();

            List<TableReportDetails> summaryReportData = await GetEliminationNationalLevelReportSummaryDataForExcelAsync(assessmentId);
            DeskLevelExcelTemplateSheetDetails summaryReportTable = new DeskLevelExcelTemplateSheetDetails
            {
                Name = $"{_translationService.GetDQATranslation(DQAConstants.SummaryReportName)}",
                Title = $"{_translationService.GetDQATranslation(DQAConstants.SummaryReportName)}",
                ReportDetails = summaryReportData
            };

            reportTables.Add(summaryReportTable);

            return reportTables;
        }

        /// <summary>
        /// Gets new data table with modification of some headers - only for summary reports
        /// </summary>
        /// <param name="indicator">indicator value as integer - refers DQAIndicatorReport enum</param>
        /// <param name="summaryTable">Instance of DataTable</param>
        /// <returns>Instance of Datatable</returns>
        private DataTable GetNewSummaryTable(int indicator, DataTable summaryTable)
        {
            DataColumnCollection columns = summaryTable.Columns;

            switch (indicator)
            {
                case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                    foreach (DataColumn column in columns)
                    {
                        switch (column.ColumnName)
                        {
                            case NumberOfReportsProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.CompletenessReportNumeratorHeader);

                                break;

                            case NumberOfReportReceivedProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.CompletenessReportDenomeratorHeader);

                                break;

                            case YearProperty:

                                column.ColumnName = _translationService.GetDQATranslation(YearProperty);

                                break;
                            case ProvinceProperty:

                                column.ColumnName = _translationService.GetDQATranslation(ProvinceProperty);
                                break;

                            case DistrictProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DistrictProperty);
                                break;

                            case HealthFacilityNameProperty:

                                column.ColumnName = HealthFacilityNameProperty;
                                break;
                        }
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                    foreach (DataColumn column in columns)
                    {
                        switch (column.ColumnName)
                        {
                            case NumberOfReportsProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.ConsistencyReportNumeratorHeader);

                                break;

                            case NumberOfReportReceivedProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.ConsistencyReportDenomeratorHeader);

                                break;

                            case YearProperty:

                                column.ColumnName = _translationService.GetDQATranslation(YearProperty);

                                break;
                            case ProvinceProperty:

                                column.ColumnName = _translationService.GetDQATranslation(ProvinceProperty);
                                break;

                            case DistrictProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DistrictProperty);
                                break;

                            case HealthFacilityNameProperty:

                                column.ColumnName = HealthFacilityNameProperty;
                                break;

                        }
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:

                    foreach (DataColumn column in columns)
                    {
                        switch (column.ColumnName)
                        {
                            case NumberOfReportsProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.ConcordanceReportNumeratorHeader);

                                break;

                            case NumberOfReportReceivedProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DQAConstants.ConcordanceReportDenomeratorHeader);

                                break;

                            case YearProperty:

                                column.ColumnName = _translationService.GetDQATranslation(YearProperty);

                                break;
                            case ProvinceProperty:

                                column.ColumnName = _translationService.GetDQATranslation(ProvinceProperty);
                                break;

                            case DistrictProperty:

                                column.ColumnName = _translationService.GetDQATranslation(DistrictProperty);
                                break;

                            case HealthFacilityNameProperty:

                                column.ColumnName = HealthFacilityNameProperty;
                                break;
                        }

                    }

                    break;
            }

            return summaryTable;
        }

        /// <summary>
        /// To get report data
        /// </summary>
        /// <param name="indicator">Report indicator id</param>
        /// <param name="reportType">Type of report</param>
        /// <param name="headerRowType">Instance of type</param>
        /// <param name="getReport">Reference method to get report</param>
        /// <param name="dataSystem1">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="dataSystem2">List of instance of TemplateDataSourceCombinedVariablesDto</param>
        /// <param name="modifiedHeaderColumns">List of instance of Tablecolumn</param>
        /// <returns>Instance of object</returns>
        private object GetReportData(ReportGenerationRequest request, out List<TableColumn> modifiedHeaderColumns, out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns)
        {
            object reports = new object();

            List<DQAReportDto> calculatedReports = _reportGeneration.GenerateReport(request.IndicatorSequence, request.ReportType, request.DataSystem1, request.DataSystem2, request.AssessmentId, request.SelectedVariable);

            List<TableColumn> headerColumns = DeskLevelDQAHelper.GetReportTableColumnResponse(request.HeaderRowType);

            modifiedHeaderColumns = ModifyHeaderColumnKeyValuePair(request.IndicatorSequence, request.HeaderRowType, headerColumns, calculatedReports, out priorityVariableColumns, out optionalVariableColumns);

            if (calculatedReports.Count > 0)
            {
                reports = request.GetReport(calculatedReports);
            }

            return reports;
        }

        /// <summary>
        /// Generate table with column and empty rows
        /// </summary>
        /// <param name="modifiedHeaderColumns">List of instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetEmptyDataTableWithColumns(List<TableColumn> modifiedHeaderColumns)
        {
            DataTable tableWithEmptyRows = new DataTable();

            List<string> dynamicColumns = new List<string>();

            modifiedHeaderColumns.ForEach((TableColumn column) =>
            {
                if (column.Headers.GetType().IsGenericType && column.Headers.Count > 1)
                {
                    dynamicColumns = column.Headers.Select(x => x.ToString()).ToList();
                    foreach (string columnItem in dynamicColumns)
                    {
                        DataColumn col = new DataColumn();
                        col.ColumnName = columnItem;
                        col.DataType = typeof(Int32);
                        if (!tableWithEmptyRows.Columns.Contains(columnItem))
                        {
                            tableWithEmptyRows.Columns.Add(col);
                        }
                    }
                }
                else
                {
                    string staticColumn = column.Headers.Select(x => x.ToString()).FirstOrDefault();
                    tableWithEmptyRows.Columns.Add(staticColumn);
                    if (!string.IsNullOrWhiteSpace(staticColumn) && !tableWithEmptyRows.Columns.Contains(staticColumn))
                    {
                        tableWithEmptyRows.Columns.Add(staticColumn);
                    }
                }
            });

            if (tableWithEmptyRows.Columns.IndexOf(PercentageProperty) > 0)
            {
                tableWithEmptyRows.Columns.Remove(PercentageProperty);
            }

            if (tableWithEmptyRows.Columns.IndexOf(DQAConstants.CompletenessReportResultHeader) > 0)
            {
                tableWithEmptyRows.Columns.Remove(DQAConstants.CompletenessReportResultHeader);
            }

            if (tableWithEmptyRows.Columns.IndexOf(DQAConstants.ConsistencyReportResultHeader) > 0)
            {
                tableWithEmptyRows.Columns.Remove(DQAConstants.ConsistencyReportResultHeader);
            }

            if (tableWithEmptyRows.Columns.IndexOf(DQAConstants.ConcordanceReportResultHeader) > 0)
            {
                tableWithEmptyRows.Columns.Remove(DQAConstants.ConcordanceReportResultHeader);
            }

            return tableWithEmptyRows;
        }

        /// <summary>
        /// Generate table with moified column name
        /// </summary>
        /// <param name="modifiedHeaderColumns">List of instance of TableColumnAttribute</param>
        /// <param name="summaryTable">Instance of DataTable</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetNewNationalLevelSummaryTable(List<TableColumnAttribute> modifiedHeaderColumns, DataTable summaryTable)
        {
            DataColumnCollection columns = summaryTable.Columns;

            foreach (DataColumn column in columns)
            {
                if (modifiedHeaderColumns.Any(x => x.Name == column.ColumnName))
                {
                    column.ColumnName = _translationService.GetTranslation(modifiedHeaderColumns.FirstOrDefault(x => x.Name == column.ColumnName).TranslationKey);
                }
            }

            if (summaryTable.Columns.IndexOf(IsConsistencyOverTimeProperty) > 0)
            {
                summaryTable.Columns.Remove(IsConsistencyOverTimeProperty);
            }

            return summaryTable;
        }

        /// <summary>
        /// Gets Province type 1 data 
        /// </summary>
        /// <param name="reports">Instance of Ordered List of ProvinceLevelType1Dto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetProvinceLevelType1DataTable(IOrderedEnumerable<ProvinceLevelType1Dto> reports, DataTable tableWithValues, List<TableColumn> modifiedHeaderColumns)
        {
            DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

            var data = tableWithValues.AsEnumerable().Select(x => new
            {
                Province = x.Field<string>(ProvinceProperty),
                HealthFacility = x.Field<string>(HealthFacilityProperty)
            });

            foreach (ProvinceLevelType1Dto row in reports)
            {
                string rowYear = Convert.ToString(row.Year);

                DataRow dataRow = tableWithEmptyRows.NewRow();

                var rowData = data.Where(x => x.Province == row.Province && x.HealthFacility == row.HealthFacilityType).Select(x => new { x.Province, x.HealthFacility });

                string province = rowData.Select(x => x.Province).FirstOrDefault();

                string healthFacility = rowData.Select(x => x.HealthFacility).FirstOrDefault();

                foreach (DataColumn column in tableWithEmptyRows.Columns)
                {
                    if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province && x.Field<string>(HealthFacilityProperty) == row.HealthFacilityType) > 0)
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (rowYear == column.Caption)
                            {
                                tableWithEmptyRows.AsEnumerable()
                                        .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                && x.Field<string>(HealthFacilityProperty) == row.HealthFacilityType)
                                        .ToList<DataRow>()
                                        .ForEach(r => { r[column.Caption] = row.Percentage; });
                            }
                        }
                        else
                        {
                            if (row.Province == province && row.HealthFacilityType == healthFacility)
                            {
                                if (rowYear == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (rowYear == column.Caption)
                            {
                                dataRow[column.ColumnName] = row.Percentage;
                            }
                        }
                        else
                        {
                            dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                        }
                    }

                }
                tableWithEmptyRows.Rows.Add(dataRow);
            }

            DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                    .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                    .CopyToDataTable();

            return filteredRows;
        }

        /// <summary>
        /// Gets national level data of consistency over time indicator
        /// </summary>
        /// <param name="reports">Instance of List of NationalLevelType1Dto</param>
        /// <returns>Instance of Dataset</returns>
        private List<TableReportDetails> GetConsistencyOverTimeNationalLevelData(List<NationalLevelType1Dto> reports, int indicator, int reportType)
        {
            List<TableReportDetails> nationalDataReports = new List<TableReportDetails>();

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            foreach (string key in keyIndicators)
            {
                DataTable keyIndicatorWiseData = new DataTable();

                IEnumerable<NationalLevelType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                keyIndicatorWiseData = keyIndicatorReports
                                                   .ToPivotTable(
                                                           item => item.Year,
                                                           item => item.HealthFacilityType,
                                                           items => items.Any() ? items.Sum(x => x.Percentage).ToString() : null);

                keyIndicatorWiseData.TableName = key;

                keyIndicatorWiseData.Columns[0].ColumnName = !string.IsNullOrEmpty(keyIndicatorWiseData.Columns[0].ColumnName) ?
                    _translationService.GetDQATranslation(keyIndicatorWiseData.Columns[0].ColumnName)
                    : string.Empty;

                string reportTypekey = reports.FirstOrDefault(x => x.VariableType == key)?.VariableTypeTraslationKey;

                string titleKey;
                string title = GetReportCategoryTitle(indicator, reportType, keyIndicatorWiseData.TableName, out titleKey, reportTypekey);

                TableReportDetails nationalReport1 = new TableReportDetails
                {
                    Title = title,
                    TitleTranslationKey = titleKey,
                    Type = (int)DeskLevelGraphTypes.Type,
                    ReportTable = keyIndicatorWiseData,
                    GraphReport = keyIndicatorWiseData
                };

                nationalDataReports.Add(nationalReport1);

            }

            return nationalDataReports;
        }

        /// <summary>
        /// Gets Province level data of consistency over time indicator
        /// </summary>
        /// <param name="reports">Instance of Ordered List of ProvinceLevelDto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataSet</returns>
        private List<TableReportDetails> GetConsistencyOverTimeProvinceLevelData(IOrderedEnumerable<ProvinceLevelDto> reports, DataTable tableWithValues,
                                                                                 List<TableColumn> modifiedHeaderColumns, int indicator, int reportType)
        {
            List<TableReportDetails> provinceDataReports = new List<TableReportDetails>();

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            foreach (string key in keyIndicators)
            {
                DataTable tableWithEmptyRows = new DataTable();

                tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

                var data = tableWithValues.AsEnumerable().Select(x => new
                {
                    Province = x.Field<string>(ProvinceProperty)
                });

                IEnumerable<ProvinceLevelDto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                foreach (ProvinceLevelDto row in keyIndicatorReports)
                {
                    string rowYear = Convert.ToString(row.Year);

                    DataRow dataRow = tableWithEmptyRows.NewRow();

                    var rowData = data.Where(x => x.Province == row.Province).Select(x => new { x.Province });

                    string province = rowData.Select(x => x.Province).FirstOrDefault();

                    foreach (DataColumn column in tableWithEmptyRows.Columns)
                    {
                        if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province) > 0)
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    tableWithEmptyRows.AsEnumerable()
                                            .Where(x => x.Field<string>(ProvinceProperty) == row.Province)
                                            .ToList<DataRow>()
                                            .ForEach(r => { r[column.Caption] = row.Percentage; });
                                }
                            }
                            else
                            {
                                if (row.Province == province)
                                {
                                    if (rowYear == column.Caption)
                                    {
                                        dataRow[column.ColumnName] = row.Percentage;
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                            else
                            {
                                dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                            }
                        }

                    }
                    tableWithEmptyRows.Rows.Add(dataRow);
                }

                DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                        .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                        .CopyToDataTable();

                filteredRows.TableName = key;

                string variableTypeKey = reports.FirstOrDefault(x => x.VariableType == key)?.VariableTypeTraslationKey;

                string titleKey;
                string title = GetReportCategoryTitle(indicator, reportType, filteredRows.TableName, out titleKey, variableTypeKey);

                TableReportDetails provinceReport = new TableReportDetails
                {
                    Title = title,
                    TitleTranslationKey = titleKey,
                    Type = (int)DeskLevelGraphTypes.Type,
                    ReportTable = filteredRows,
                    GraphReport = filteredRows
                };

                provinceDataReports.Add(provinceReport);
            }

            return provinceDataReports;
        }

        /// <summary>
        /// Gets district level data of consistency over time indicator
        /// </summary>
        /// <param name="reports">Instance of Ordered List of ProvinceLevelDto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataSet</returns>
        private List<TableReportDetails> GetConsistencyOverTimeDistrictLevelData(IOrderedEnumerable<DistrictLevelType1Dto> reports, DataTable tableWithValues,
                                                                List<TableColumn> modifiedHeaderColumns, int indicator, int reportType)
        {
            List<TableReportDetails> districtDataReports = new List<TableReportDetails>();

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            foreach (string key in keyIndicators)
            {
                DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

                var data = tableWithValues.AsEnumerable().Select(x => new
                {
                    Province = x.Field<string>(ProvinceProperty),
                    District = x.Field<string>(DistrictProperty)
                });

                IEnumerable<DistrictLevelType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                foreach (DistrictLevelType1Dto row in keyIndicatorReports)
                {
                    string rowYear = Convert.ToString(row.Year);

                    DataRow dataRow = tableWithEmptyRows.NewRow();

                    var rowData = data.Where(x => x.Province == row.Province && x.District == row.District).Select(x => new { x.Province, x.District });

                    string province = rowData.Select(x => x.Province).FirstOrDefault();

                    string district = rowData.Select(x => x.District).FirstOrDefault();

                    foreach (DataColumn column in tableWithEmptyRows.Columns)
                    {
                        //TODO : See if we can use dictionary just to check for value count
                        if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province
                                && x.Field<string>(DistrictProperty) == row.District) > 0)
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    tableWithEmptyRows.AsEnumerable()
                                            .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                    && x.Field<string>(DistrictProperty) == row.District)
                                            .ToList<DataRow>()
                                            .ForEach(r => { r[column.Caption] = row.Percentage; });
                                }
                            }
                            else
                            {
                                if (row.Province == province && row.District == district)
                                {
                                    if (rowYear == column.Caption)
                                    {
                                        dataRow[column.ColumnName] = row.Percentage;
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                            else
                            {
                                dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                            }
                        }

                    }
                    tableWithEmptyRows.Rows.Add(dataRow);
                }

                DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                        .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                        .CopyToDataTable();

                filteredRows.TableName = key;

                DataTable graphTable = filteredRows;
                graphTable.Columns.Remove(ProvinceProperty);

                string reportParameterTypeKey = reports.FirstOrDefault(x => x.VariableType == key)?.VariableTypeTraslationKey;

                string titleKey;
                string title = GetReportCategoryTitle(indicator, reportType, filteredRows.TableName, out titleKey, reportParameterTypeKey);

                TableReportDetails districtReport = new TableReportDetails
                {
                    Title = title,
                    TitleTranslationKey = titleKey,
                    Type = (int)DeskLevelGraphTypes.Type,
                    ReportTable = filteredRows,
                    GraphReport = graphTable
                };

                districtDataReports.Add(districtReport);
            }

            return districtDataReports;
        }

        /// <summary>
        /// Gets health facility level data of consistency over time indicator
        /// </summary>
        /// <param name="reports">Instance of Ordered List of ProvinceLevelDto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataSet</returns>
        private List<TableReportDetails> GetConsistencyOverTimeHealthFacilityLevelData(IOrderedEnumerable<HealthFacilityType1Dto> reports, DataTable tableWithValues,
                                                                      List<TableColumn> modifiedHeaderColumns, int indicator, int reportType)
        {
            List<TableReportDetails> healthFacilityDataReports = new List<TableReportDetails>();

            List<string> keyIndicators = reports.Select(x => x.VariableType).Distinct().ToList();

            foreach (string key in keyIndicators)
            {
                DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

                var data = tableWithValues.AsEnumerable().Select(x => new
                {
                    Province = x.Field<string>(ProvinceProperty),
                    District = x.Field<string>(DistrictProperty),
                    HealthFacilityName = x.Field<string>(HealthFacilityNameProperty)
                });

                IEnumerable<HealthFacilityType1Dto> keyIndicatorReports = reports.Where(x => x.VariableType == key).Select(x => x);

                foreach (HealthFacilityType1Dto row in keyIndicatorReports)
                {
                    string rowYear = Convert.ToString(row.Year);

                    DataRow dataRow = tableWithEmptyRows.NewRow();

                    var rowData = data.Where(x => x.Province == row.Province && x.District == row.District && x.HealthFacilityName == row.HealthFacilityName)
                                        .Select(x => new { x.Province, x.District, x.HealthFacilityName });

                    string province = rowData.Select(x => x.Province).FirstOrDefault();

                    string district = rowData.Select(x => x.District).FirstOrDefault();

                    string healthFacilityName = rowData.Select(x => x.HealthFacilityName).FirstOrDefault();

                    foreach (DataColumn column in tableWithEmptyRows.Columns)
                    {
                        if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province
                                && x.Field<string>(DistrictProperty) == row.District && x.Field<string>(HealthFacilityNameProperty) == row.HealthFacilityName) > 0)
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    tableWithEmptyRows.AsEnumerable()
                                            .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                    && x.Field<string>(DistrictProperty) == row.District
                                                    && x.Field<string>(HealthFacilityNameProperty) == row.HealthFacilityName)
                                            .ToList<DataRow>()
                                            .ForEach(r => { r[column.Caption] = row.Percentage; });
                                }
                            }
                            else
                            {
                                if (row.Province == province && row.District == district && row.HealthFacilityName == healthFacilityName)
                                {
                                    if (rowYear == column.Caption)
                                    {
                                        dataRow[column.ColumnName] = row.Percentage;
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                            {
                                if (rowYear == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                            else
                            {
                                dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                            }
                        }

                    }
                    tableWithEmptyRows.Rows.Add(dataRow);
                }

                DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                        .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                        .CopyToDataTable();

                filteredRows.TableName = key;

                string reportParameterTypeKey = reports.FirstOrDefault(x => x.VariableType == key)?.VariableTypeTraslationKey;

                string titleKey;
                string title = GetReportCategoryTitle(indicator, reportType, filteredRows.TableName, out titleKey, reportParameterTypeKey);

                filteredRows.Columns[2].ColumnName = !string.IsNullOrEmpty(filteredRows.Columns[2].ColumnName) ? _translationService.GetDQATranslation(filteredRows.Columns[2].ColumnName)
                    : string.Empty;

                TableReportDetails healthFacilityReport = new TableReportDetails
                {
                    Title = title,
                    TitleTranslationKey = titleKey,
                    ReportTable = filteredRows
                };

                healthFacilityDataReports.Add(healthFacilityReport);
            }

            return healthFacilityDataReports;
        }

        /// <summary>
        /// Gets Province type 2 data
        /// </summary>
        /// <param name="reports">Instance of Ordered List of ProvinceLevelType2Dto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetProvinceLevelType2DataTable(IOrderedEnumerable<ProvinceLevelType2Dto> reports, DataTable tableWithValues, List<TableColumn> modifiedHeaderColumns)
        {
            DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

            var data = tableWithValues.AsEnumerable().Select(x => new
            {
                Province = x.Field<string>(ProvinceProperty),
                Year = x.Field<string>(YearProperty)
            });

            foreach (ProvinceLevelType2Dto row in reports)
            {
                DataRow dataRow = tableWithEmptyRows.NewRow();

                var rowData = data.Where(x => x.Province == row.Province && x.Year == row.Year.ToString()).Select(x => new { x.Province, x.Year });

                string provinceData = rowData.Select(x => x.Province).FirstOrDefault();

                string rowYear = rowData.Select(x => x.Year).FirstOrDefault();

                string variable = Convert.ToString(row.Variables);

                foreach (DataColumn column in tableWithEmptyRows.Columns)
                {
                    if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province && x.Field<string>(YearProperty) == row.Year.ToString()) > 0)
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                tableWithEmptyRows.AsEnumerable()
                                        .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                && x.Field<string>(YearProperty) == row.Year.ToString())
                                        .ToList<DataRow>()
                                        .ForEach(r => { r[column.Caption] = row.Percentage; });
                            }
                        }
                        else
                        {
                            if (row.Province == provinceData && row.Year.ToString() == rowYear)
                            {
                                if (variable == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                dataRow[column.ColumnName] = row.Percentage;
                            }
                        }
                        else
                        {
                            dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                        }
                    }

                }
                tableWithEmptyRows.Rows.Add(dataRow);
            }

            DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                    .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                    .CopyToDataTable();

            filteredRows.Columns[1].ColumnName = !string.IsNullOrEmpty(filteredRows.Columns[1].ColumnName) ?
                   _translationService.GetDQATranslation(filteredRows.Columns[1].ColumnName) : filteredRows.Columns[1].ColumnName;

            return filteredRows;
        }

        /// <summary>
        /// Gets District type 2 data
        /// </summary>
        /// <param name="reports">Instance of Ordered List of DistrictLevelType2Dto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetDistrictLevelType2DataTable(IOrderedEnumerable<DistrictLevelType2Dto> reports, DataTable tableWithValues, List<TableColumn> modifiedHeaderColumns)
        {
            DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

            var data = tableWithValues.AsEnumerable().Select(x => new
            {
                Province = x.Field<string>(ProvinceProperty),
                District = x.Field<string>(DistrictProperty),
                Year = x.Field<string>(YearProperty)
            });

            foreach (DistrictLevelType2Dto row in reports)
            {
                DataRow dataRow = tableWithEmptyRows.NewRow();

                var rowData = data.Where(x => x.Province == row.Province && x.District == row.District && x.Year == row.Year.ToString()).Select(x => new { x.Province, x.District, x.Year });

                string province = rowData.Select(x => x.Province).FirstOrDefault();

                string district = rowData.Select(x => x.District).FirstOrDefault();

                string rowYear = rowData.Select(x => x.Year).FirstOrDefault();

                string variable = Convert.ToString(row.Variables);

                foreach (DataColumn column in tableWithEmptyRows.Columns)
                {
                    if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province
                            && x.Field<string>(DistrictProperty) == row.District && x.Field<string>(YearProperty) == row.Year.ToString()) > 0)
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                tableWithEmptyRows.AsEnumerable()
                                        .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                && x.Field<string>(DistrictProperty) == row.District
                                                && x.Field<string>(YearProperty) == row.Year.ToString())
                                        .ToList<DataRow>()
                                        .ForEach(r => { r[column.Caption] = row.Percentage; });
                            }
                        }
                        else
                        {
                            if (row.Province == province && row.District == district && row.Year.ToString() == rowYear)
                            {
                                if (variable == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                dataRow[column.ColumnName] = row.Percentage;
                            }
                        }
                        else
                        {
                            dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                        }
                    }

                }
                tableWithEmptyRows.Rows.Add(dataRow);
            }

            DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                    .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                    .CopyToDataTable();
            filteredRows.Columns[2].ColumnName = !string.IsNullOrEmpty(filteredRows.Columns[2].ColumnName) ?
                _translationService.GetDQATranslation(filteredRows.Columns[2].ColumnName) : filteredRows.Columns[2].ColumnName;

            return filteredRows;
        }

        /// <summary>
        /// Gets health facility type 2 data
        /// </summary>
        /// <param name="reports">Instance of Ordered List of HealthFacilityType2Dto</param>
        /// <param name="tableWithValues">Instance of DataTable</param>
        /// <param name="modifiedHeaderColumns">List of Instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetHealthFacilityLevelType2DataTable(IOrderedEnumerable<HealthFacilityType2Dto> reports, DataTable tableWithValues, List<TableColumn> modifiedHeaderColumns)
        {
            DataTable tableWithEmptyRows = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

            var data = tableWithValues.AsEnumerable().Select(x => new
            {
                Province = x.Field<string>(ProvinceProperty),
                District = x.Field<string>(DistrictProperty),
                HealthFacilityName = x.Field<string>(HealthFacilityNameProperty),
                Year = x.Field<string>(YearProperty)
            });

            foreach (HealthFacilityType2Dto row in reports)
            {
                DataRow dataRow = tableWithEmptyRows.NewRow();

                var rowData = data.Where(x => x.Province == row.Province && x.District == row.District
                                        && x.HealthFacilityName == row.HealthFacilityName && x.Year == row.Year.ToString())
                                .Select(x => new { x.Province, x.District, x.HealthFacilityName, x.Year });

                string province = rowData.Select(x => x.Province).FirstOrDefault();

                string district = rowData.Select(x => x.District).FirstOrDefault();

                string healthFacilityName = rowData.Select(x => x.HealthFacilityName).FirstOrDefault();

                string rowYear = rowData.Select(x => x.Year).FirstOrDefault();

                string variable = Convert.ToString(row.Variables);

                foreach (DataColumn column in tableWithEmptyRows.Columns)
                {
                    if (tableWithEmptyRows.AsEnumerable().Count(x => x.Field<string>(ProvinceProperty) == row.Province && x.Field<string>(DistrictProperty) == row.District
                            && x.Field<string>(HealthFacilityNameProperty) == row.HealthFacilityName && x.Field<string>(YearProperty) == row.Year.ToString()) > 0)
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                tableWithEmptyRows.AsEnumerable()
                                        .Where(x => x.Field<string>(ProvinceProperty) == row.Province
                                                && x.Field<string>(DistrictProperty) == row.District
                                                && x.Field<string>(HealthFacilityNameProperty) == row.HealthFacilityName
                                                && x.Field<string>(YearProperty) == row.Year.ToString())
                                        .ToList<DataRow>()
                                        .ForEach(r => { r[column.Caption] = row.Percentage; });
                            }
                        }
                        else
                        {
                            if (row.Province == province && row.District == district && row.HealthFacilityName == healthFacilityName && row.Year.ToString() == rowYear)
                            {
                                if (variable == column.Caption)
                                {
                                    dataRow[column.ColumnName] = row.Percentage;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (row.GetType().GetRuntimeProperties().FirstOrDefault(x => x.Name == column.ColumnName) == null)
                        {
                            if (variable == column.Caption)
                            {
                                dataRow[column.ColumnName] = row.Percentage;
                            }
                        }
                        else
                        {
                            dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                        }
                    }

                }
                tableWithEmptyRows.Rows.Add(dataRow);
            }

            DataTable filteredRows = tableWithEmptyRows.Rows.Cast<DataRow>()
                                    .Where(row => !row.ItemArray.All(field => field is System.DBNull))
                                    .CopyToDataTable();

            filteredRows.Columns[3].ColumnName = !string.IsNullOrEmpty(filteredRows.Columns[3].ColumnName) ?
                _translationService.GetDQATranslation(filteredRows.Columns[3].ColumnName) : filteredRows.Columns[3].ColumnName;

            return filteredRows;
        }

        /// <summary>
        /// Get static data from report
        /// </summary>
        /// <param name="reports">Dynamic object</param>
        /// <param name="columnsToPopulate">Array of string</param>
        /// <param name="modifiedHeaderColumns">List of instance of TableColumn</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable GetStaticReportData(dynamic reports, string[] columnsToPopulate, List<TableColumn> modifiedHeaderColumns)
        {
            DataTable parameterTable = GetEmptyDataTableWithColumns(modifiedHeaderColumns);

            foreach (object row in reports)
            {
                DataRow dataRow = parameterTable.NewRow();

                foreach (DataColumn column in parameterTable.Columns)
                {
                    if (row.GetType().GetProperty(column.ColumnName) != null)
                    {
                        if (row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null) != null)
                        {
                            dataRow[column.ColumnName] = row.GetType().GetRuntimeProperty(column.ColumnName).GetValue(row, null);
                        }
                    }
                }

                parameterTable.Rows.Add(dataRow);
            }

            DataTable distinctTable = parameterTable.DefaultView.ToTable(true, columnsToPopulate);

            return distinctTable;
        }

        /// <summary>
        /// Joins 2 instance of DataTable
        /// </summary>
        /// <param name="innerTable">Instance of DataTable</param>
        /// <param name="outerTable">Instance of DataTable</param>
        /// <returns>Instance of DataTable</returns>
        private DataTable JoinDataTable(DataTable innerTable, DataTable outerTable)
        {
            DataTable resultTable = new DataTable();
            var innerTableColumns = new List<string>();
            foreach (DataColumn column in innerTable.Columns)
            {
                innerTableColumns.Add(column.ColumnName);
                resultTable.Columns.Add(column.ColumnName);
            }

            var outerTableColumns = new List<string>();
            foreach (DataColumn column in outerTable.Columns)
            {
                if (!innerTableColumns.Contains(column.ColumnName))
                {
                    outerTableColumns.Add(column.ColumnName);
                    resultTable.Columns.Add(new DataColumn { ColumnName = column.ColumnName, DataType = typeof(Int32) });
                }
            }

            for (int i = 0; i < innerTable.Rows.Count; i++)
            {
                var row = resultTable.NewRow();
                innerTableColumns.ForEach(x =>
                {
                    row[x] = innerTable.Rows[i][x];
                });
                outerTableColumns.ForEach(x =>
                {
                    if (i <= (outerTable.Rows.Count - 1))
                    {
                        row[x] = outerTable.Rows[i][x];
                    }
                });
                resultTable.Rows.Add(row);
            }
            return resultTable;
        }

        /// <summary>
        /// Method to get report title from constants
        /// </summary>
        /// <param name="indicator">An DQA indicator id</param>
        /// <returns>Name of indicator</returns>
        private string GetReportTitle(int indicator, out string reportTitleKey)
        {
            string reportTitle = string.Empty;
            reportTitleKey = string.Empty;

            switch (indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                    reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.CompletenessOfReporting)}, %";
                    reportTitleKey = DQAConstants.CompletenessOfReporting;
                    break;

                case (int)DQAIndicatorReport.ReportingTimeliness:

                    reportTitle = $"{ _translationService.GetDQATranslation(DQAConstants.TimelinessOfReporting)}, %";
                    reportTitleKey = DQAConstants.TimelinessOfReporting;
                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                    reportTitle = $"{ _translationService.GetDQATranslation(DQAConstants.VariableCompleteness)}, %";
                    reportTitleKey = DQAConstants.VariableCompleteness;
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                    reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.ConsistencyBetweenVariable)}, %";
                    reportTitleKey = DQAConstants.ConsistencyBetweenVariable;
                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                    reportTitle = $"{ _translationService.GetDQATranslation(DQAConstants.ConsistencyOverTime)}, %";
                    reportTitleKey = DQAConstants.ConsistencyOverTime;
                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:

                    reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.Concordance)}, %";
                    reportTitleKey = DQAConstants.Concordance;
                    break;
            }

            return reportTitle;
        }

		/// <summary>
		/// Method to get report title from constants to use for excel sheets
		/// </summary>
		/// <param name="indicator">An DQA indicator id</param>
		/// <returns>Name of indicator</returns>
		private string GetReportExcelSheetTitle(int indicator, out string reportTitleKey)
		{
			string reportTitle = string.Empty;
			reportTitleKey = string.Empty;

			switch (indicator)
			{
				case (int)DQAIndicatorReport.ReportingCompleteness:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.CompletenessOfReporting)}";
					reportTitleKey = DQAConstants.CompletenessOfReporting;
					break;

				case (int)DQAIndicatorReport.ReportingTimeliness:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.TimelinessOfReporting)}";
					reportTitleKey = DQAConstants.TimelinessOfReporting;
					break;

				case (int)DQAIndicatorReport.ReportingVariableCompleteness:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.VariableCompleteness)}";
					reportTitleKey = DQAConstants.VariableCompleteness;
					break;

				case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.ConsistencyBetweenVariable)}";
					reportTitleKey = DQAConstants.ConsistencyBetweenVariable;
					break;

				case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.ConsistencyOverTime)}";
					reportTitleKey = DQAConstants.ConsistencyOverTime;
					break;

				case (int)DQAIndicatorReport.ReportingConcordance:

					reportTitle = $"{_translationService.GetDQATranslation(DQAConstants.Concordance)}";
					reportTitleKey = DQAConstants.Concordance;
					break;
			}

			return reportTitle;
		}

		private string GetReportCategoryTitle(int indicator, int reportType, string ReportParameterType, out string reportCategoryTitleKey, string ReportParameterTypeKey = null)
        {
            string reportCategoryTitle = string.Empty;
            reportCategoryTitleKey = string.Empty;

            switch (indicator)
            {
                case (int)DQAIndicatorReport.ReportingCompleteness:

                case (int)DQAIndicatorReport.ReportingTimeliness:

                    switch (reportType)
                    {
                        case (int)DQAReportType.NationalReport:

                        case (int)DQAReportType.NationalReport1:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.NationalReport);
                            reportCategoryTitleKey = DQAConstants.NationalReport;

                            break;

                        case (int)DQAReportType.ProvinceReport:

                        case (int)DQAReportType.ProvinceReport1:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.ProvinceReport);
                            reportCategoryTitleKey = DQAConstants.ProvinceReport;

                            break;

                        case (int)DQAReportType.DistrictReport1:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.DistrictReport);
                            reportCategoryTitleKey = DQAConstants.DistrictReport;

                            break;

                        case (int)DQAReportType.HealthFacilityReport1:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.HealthFacilityReport);
                            reportCategoryTitleKey = DQAConstants.HealthFacilityReport;

                            break;
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                    switch (reportType)
                    {
                        case (int)DQAReportType.NationalReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.NationalReport);
                            reportCategoryTitleKey = DQAConstants.NationalReport;

                            break;

                        case (int)DQAReportType.NationalReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.NationalReport)} - { _translationService.GetDQATranslation(DQAConstants.CompletenessReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.NationalReport} - { DQAConstants.CompletenessReportResultHeader}";

                            break;

                        case (int)DQAReportType.ProvinceReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.ProvinceReport);
                            reportCategoryTitleKey = DQAConstants.ProvinceReport;

                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.ProvinceReport)} - {_translationService.GetDQATranslation(DQAConstants.CompletenessReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.ProvinceReport} - {DQAConstants.CompletenessReportResultHeader}";

                            break;

                        case (int)DQAReportType.DistrictReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.DistrictReport);
                            reportCategoryTitleKey = DQAConstants.DistrictReport;

                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.DistrictReport)} - {_translationService.GetDQATranslation(DQAConstants.CompletenessReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.DistrictReport} - {DQAConstants.CompletenessReportResultHeader}";


                            break;

                        case (int)DQAReportType.HealthFacilityReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.HealthFacilityReport);
                            reportCategoryTitleKey = DQAConstants.HealthFacilityReport;

                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.HealthFacilityReport)} - {_translationService.GetDQATranslation(DQAConstants.CompletenessReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.HealthFacilityReport} - {DQAConstants.CompletenessReportResultHeader}";

                            break;
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                    switch (reportType)
                    {
                        case (int)DQAReportType.NationalReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.NationalReport);
                            reportCategoryTitleKey = DQAConstants.NationalReport;


                            break;

                        case (int)DQAReportType.NationalReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.NationalReport)} - { _translationService.GetDQATranslation(DQAConstants.ConsistencyReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.NationalReport} - {DQAConstants.ConsistencyReportResultHeader}";



                            break;

                        case (int)DQAReportType.ProvinceReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.ProvinceReport);
                            reportCategoryTitleKey = DQAConstants.ProvinceReport;

                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.ProvinceReport)} - {_translationService.GetDQATranslation(DQAConstants.ConsistencyReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.ProvinceReport} - {DQAConstants.ConsistencyReportResultHeader}";

                            break;

                        case (int)DQAReportType.DistrictReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.DistrictReport);
                            reportCategoryTitleKey = DQAConstants.DistrictReport;

                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.DistrictReport)} - {_translationService.GetDQATranslation(DQAConstants.ConsistencyReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.DistrictReport} - {DQAConstants.ConsistencyReportResultHeader}";

                            break;

                        case (int)DQAReportType.HealthFacilityReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.HealthFacilityReport);
                            reportCategoryTitleKey = DQAConstants.HealthFacilityReport;

                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.HealthFacilityReport)} - {_translationService.GetDQATranslation(DQAConstants.ConsistencyReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.HealthFacilityReport} - {DQAConstants.ConsistencyReportResultHeader}";

                            break;
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingConsistencyOverTime:

                    switch (reportType)
                    {
                        case (int)DQAReportType.NationalReport:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.NationalReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.NationalReport} - {ReportParameterTypeKey}";

                            break;

                        case (int)DQAReportType.NationalReport1:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.NationalReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.NationalReport} - {ReportParameterTypeKey}";

                            break;

                        case (int)DQAReportType.ProvinceReport:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.ProvinceReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.ProvinceReport} - {ReportParameterTypeKey}";

                            break;

                        case (int)DQAReportType.ProvinceReport1:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.ProvinceReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.ProvinceReport} - {ReportParameterTypeKey}";

                            break;

                        case (int)DQAReportType.DistrictReport1:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.DistrictReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.DistrictReport} - {ReportParameterTypeKey}";

                            break;

                        case (int)DQAReportType.HealthFacilityReport1:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.HealthFacilityReport)} - {ReportParameterType}";
                            reportCategoryTitleKey = $"{DQAConstants.HealthFacilityReport} - {ReportParameterTypeKey}";

                            break;
                    }

                    break;

                case (int)DQAIndicatorReport.ReportingConcordance:

                    switch (reportType)
                    {
                        case (int)DQAReportType.NationalReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.NationalReport);
                            reportCategoryTitleKey = DQAConstants.NationalReport;

                            break;

                        case (int)DQAReportType.NationalReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.NationalReport)} - {_translationService.GetDQATranslation(DQAConstants.ConcordanceReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.NationalReport} - {DQAConstants.ConcordanceReportResultHeader}";

                            break;

                        case (int)DQAReportType.ProvinceReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.ProvinceReport);
                            reportCategoryTitleKey = DQAConstants.ProvinceReport;

                            break;

                        case (int)DQAReportType.ProvinceReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.ProvinceReport)} - {_translationService.GetDQATranslation(DQAConstants.ConcordanceReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.ProvinceReport} - {DQAConstants.ConcordanceReportResultHeader}";

                            break;

                        case (int)DQAReportType.DistrictReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.DistrictReport);
                            reportCategoryTitleKey = DQAConstants.DistrictReport;

                            break;

                        case (int)DQAReportType.DistrictReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.DistrictReport)} - {_translationService.GetDQATranslation(DQAConstants.ConcordanceReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.DistrictReport} - {DQAConstants.ConcordanceReportResultHeader}";
                            break;

                        case (int)DQAReportType.HealthFacilityReport2:

                            reportCategoryTitle = _translationService.GetDQATranslation(DQAConstants.HealthFacilityReport);
                            reportCategoryTitleKey = DQAConstants.HealthFacilityReport;

                            break;

                        case (int)DQAReportType.HealthFacilityReport3:

                            reportCategoryTitle = $"{_translationService.GetDQATranslation(DQAConstants.HealthFacilityReport)} - {_translationService.GetDQATranslation(DQAConstants.ConcordanceReportResultHeader)}";
                            reportCategoryTitleKey = $"{DQAConstants.HealthFacilityReport} - {DQAConstants.ConcordanceReportResultHeader}";

                            break;
                    }

                    break;
            }

            return reportCategoryTitle;
        }

        /// <summary>
        /// Method to modify original header columns of the report
        /// </summary>
        /// <param name="indicator">Report Indicator id</param>
        /// <param name="headerRowType">Type of model</param>
        /// <param name="headerColumns">List of instance of TableColumn</param>
        /// <param name="calculatedReports">List of instance of DQAReportDto</param>
        /// <returns>List of TableColumn instance</returns>
        private List<TableColumn> ModifyHeaderColumnKeyValuePair(int indicator, Type headerRowType, List<TableColumn> headerColumns, List<DQAReportDto> calculatedReports,
                                                                    out TableColumn priorityVariableColumns, out TableColumn optionalVariableColumns)
        {
            TableColumn priorityVariableColumn = null;
            TableColumn optionalVariableColumn = null;

            PropertyInfo[] requestObjectProperties = headerRowType.GetProperties();

            foreach (PropertyInfo requestObjectProperty in requestObjectProperties)
            {
                foreach (TableColumn column in headerColumns)
                {
                    int keyValue = column.Headers.Count(x => x == requestObjectProperty.Name);
                    Type keyType = column.Headers.GetType();

                    if (keyValue > 0 && requestObjectProperty.Name == YearProperty && requestObjectProperty.PropertyType.IsGenericType)
                    {
                        List<string> years = calculatedReports.Select(x => Convert.ToString(x.Year)).Distinct().ToList();
                        column.Headers = years;
                    }
                    else if (keyValue > 0 && requestObjectProperty.Name == VariableProperty && requestObjectProperty.PropertyType.IsGenericType)
                    {
                        List<string> variables = new List<string>();
                        List<string> priorityVariables = new List<string>();
                        List<string> optionalVariables = new List<string>();

                        calculatedReports.ForEach((calculatedReport) =>
                        {
                            string translateVariblename = _translationService.GetDQATranslation(calculatedReport.ReportParameterType);

                            if (!variables.Contains(translateVariblename))
                                variables.Add(translateVariblename);

                            if (calculatedReport.VariableType == PriorityVariable)
                            {
                                if (!priorityVariables.Contains(translateVariblename))
                                    priorityVariables.Add(translateVariblename);
                            }

                            if (calculatedReport.VariableType == OptionalVariable)
                            {
                                if (!optionalVariables.Contains(translateVariblename))
                                    optionalVariables.Add(translateVariblename);
                            }
                        });

                        column.Headers = variables;

                        priorityVariableColumn = new TableColumn(priorityVariables, column.Width);

                        optionalVariableColumn = new TableColumn(optionalVariables, column.Width);
                    }
                    else if (keyValue > 0 && requestObjectProperty.Name == NumberOfReportsProperty)
                    {
                        switch (indicator)
                        {
                            case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                                column.Headers = new List<string> { DQAConstants.CompletenessReportNumeratorHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                                column.Headers = new List<string> { DQAConstants.ConsistencyReportNumeratorHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConcordance:

                                column.Headers = new List<string> { DQAConstants.ConcordanceReportNumeratorHeader };
                                break;
                        }
                    }
                    else if (keyValue > 0 && requestObjectProperty.Name == NumberOfReportReceivedProperty)
                    {
                        switch (indicator)
                        {
                            case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                                column.Headers = new List<string> { DQAConstants.CompletenessReportDenomeratorHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                                column.Headers = new List<string> { DQAConstants.ConsistencyReportDenomeratorHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConcordance:

                                column.Headers = new List<string> { DQAConstants.ConcordanceReportDenomeratorHeader };
                                break;
                        }
                    }
                    else if (keyValue > 0 && requestObjectProperty.Name == PercentageProperty)
                    {
                        switch (indicator)
                        {
                            case (int)DQAIndicatorReport.ReportingVariableCompleteness:

                                column.Headers = new List<string> { DQAConstants.CompletenessReportResultHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:

                                column.Headers = new List<string> { DQAConstants.ConsistencyReportResultHeader };
                                break;

                            case (int)DQAIndicatorReport.ReportingConcordance:

                                column.Headers = new List<string> { DQAConstants.ConcordanceReportResultHeader };
                                break;
                        }
                    }
                }
            }

            priorityVariableColumns = priorityVariableColumn;

            optionalVariableColumns = optionalVariableColumn;

            return headerColumns;
        }

        /// <summary>
        /// Method returns Translation for percentage column according to indicator sequence
        /// </summary>
        /// <param name="indicatorSequence">Sequence id </param>
        /// <returns> translation string</returns>
        private string GetPercentageColumnTranslation(int indicatorSequence)
        {
            string percentageTranslation = string.Empty;

            switch (indicatorSequence)
            {
                case (int)DQAIndicatorReport.ReportingVariableCompleteness:
                    percentageTranslation = _translationService.GetDQATranslation(DQAConstants.CompletenessReportResultHeader);
                    break;
                case (int)DQAIndicatorReport.ReportingConsistencyBtwVariables:
                    percentageTranslation = _translationService.GetDQATranslation(DQAConstants.ConsistencyReportResultHeader);
                    break;
                case (int)DQAIndicatorReport.ReportingConcordance:
                    percentageTranslation = _translationService.GetDQATranslation(DQAConstants.ConcordanceReportResultHeader);
                    break;
            }

            return percentageTranslation;
        }

        #endregion

        #region Show Reports

        #region National Level Report
        /// <summary>
        /// Method to generate national report for DQA indicator 1.2.1 and 1.2.3
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetNationalLevelReport(List<DQAReportDto> calculatedReports)
        {
            List<NationalLevelDto> reports = new List<NationalLevelDto>();
            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    NationalLevelDto report = new NationalLevelDto
                    {
                        Percentage = requestData.PercentageValue,
                        Year = requestData.Year
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate national report type 1 for DQA indicator 1.2.1, 1.2.3, 1.2.9
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetNationalLevelReportType1(List<DQAReportDto> calculatedReports)
        {
            List<NationalLevelType1Dto> reports = new List<NationalLevelType1Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    NationalLevelType1Dto report = new NationalLevelType1Dto
                    {
                        HealthFacilityType = requestData.HealthFacilityType,
                        Percentage = requestData.PercentageValue,
                        VariableType = !string.IsNullOrEmpty(requestData.ReportParameterType) ?
                        _translationService.GetDQATranslation(requestData.ReportParameterType)
                        : string.Empty,
                        VariableTypeTraslationKey = requestData.ReportParameterType,
                        Year = requestData.Year
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate national report type 2 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetNationalLevelReportType2(List<DQAReportDto> calculatedReports)
        {
            List<NationalLevelType2Dto> reports = new List<NationalLevelType2Dto>();


            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    NationalLevelType2Dto report = new NationalLevelType2Dto
                    {
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        VariableType = requestData.VariableType,
                        Variables = _translationService.GetDQATranslation(requestData.ReportParameterType),
                        VariableTypeTraslationKey = requestData.ReportParameterType
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate national summary report type 3 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetNationalLevelReportType3(List<DQAReportDto> calculatedReports)
        {
            List<NationalLevelReportType3Dto> reports = new List<NationalLevelReportType3Dto>();
            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    NationalLevelReportType3Dto report = new NationalLevelReportType3Dto
                    {
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        NumberOfReports = requestData.NumberOfReport,
                        NumberOfReportReceived = requestData.NumberOfReportReceived,
                        VariableType = requestData.VariableType,
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }


        #endregion

        #region Province Level Report

        /// <summary>
        /// Method to generate province report for DQA indicator 1.2.1 and 1.2.3
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetProvinceLevelReport(List<DQAReportDto> calculatedReports)
        {
            List<ProvinceLevelDto> reports = new List<ProvinceLevelDto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    ProvinceLevelDto report = new ProvinceLevelDto
                    {
                        Province = requestData.Province,
                        Percentage = requestData.PercentageValue,
                        VariableType = !string.IsNullOrEmpty(requestData.ReportParameterType) ? _translationService.GetDQATranslation(requestData.ReportParameterType) : string.Empty,
                        VariableTypeTraslationKey = requestData.ReportParameterType,
                        Year = requestData.Year
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate province report type 1 for DQA indicator 1.2.1, 1.2.3, 1.2.9
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetProvinceLevelReportType1(List<DQAReportDto> calculatedReports)
        {
            List<ProvinceLevelType1Dto> reports = new List<ProvinceLevelType1Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    ProvinceLevelType1Dto report = new ProvinceLevelType1Dto
                    {
                        Province = requestData.Province,
                        HealthFacilityType = requestData.HealthFacilityType,
                        Percentage = requestData.PercentageValue,
                        Year = requestData.Year
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate province report type 2 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetProvinceLevelReportType2(List<DQAReportDto> calculatedReports)
        {
            List<ProvinceLevelType2Dto> reports = new List<ProvinceLevelType2Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    ProvinceLevelType2Dto report = new ProvinceLevelType2Dto
                    {
                        Province = requestData.Province,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        VariableTypeTraslationKey = requestData.VariableType,
                        Variables = _translationService.GetDQATranslation(requestData.ReportParameterType)
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate province summary report type 3 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetProvinceLevelReportType3(List<DQAReportDto> calculatedReports)
        {
            List<ProvinceLevelReportType3Dto> reports = new List<ProvinceLevelReportType3Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    ProvinceLevelReportType3Dto report = new ProvinceLevelReportType3Dto
                    {
                        Province = requestData.Province,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        NumberOfReports = requestData.NumberOfReport,
                        NumberOfReportReceived = requestData.NumberOfReportReceived
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        #endregion

        #region District Level Report

        /// <summary>
        /// Method to generate district level report for DQA indicator 1.2.1, 1.2.3, 1.2.9
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetDistrictLevelReportType1(List<DQAReportDto> calculatedReports)
        {
            List<DistrictLevelType1Dto> reports = new List<DistrictLevelType1Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    DistrictLevelType1Dto report = new DistrictLevelType1Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        Percentage = requestData.PercentageValue,
                        Year = requestData.Year,
                        VariableType = !string.IsNullOrEmpty(requestData.ReportParameterType) ? _translationService.GetDQATranslation(requestData.ReportParameterType) : string.Empty,
                        VariableTypeTraslationKey = requestData.ReportParameterType
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate district report type 2 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetDistrictLevelReportType2(List<DQAReportDto> calculatedReports)
        {
            List<DistrictLevelType2Dto> reports = new List<DistrictLevelType2Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    DistrictLevelType2Dto report = new DistrictLevelType2Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        VariableType = requestData.VariableType,
                        Variables = _translationService.GetDQATranslation(requestData.ReportParameterType),
                        VariableTypeTraslationKey = requestData.ReportParameterType,

                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate district summary report type 3 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetDistrictLevelReportType3(List<DQAReportDto> calculatedReports)
        {
            List<DistrictLevelReportType3Dto> reports = new List<DistrictLevelReportType3Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    DistrictLevelReportType3Dto report = new DistrictLevelReportType3Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        NumberOfReports = requestData.NumberOfReport,
                        NumberOfReportReceived = requestData.NumberOfReportReceived
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        #endregion

        #region Health Facility Level Report

        /// <summary>
        /// Method to generate health facility level report for DQA indicator 1.2.1, 1.2.3, 1.2.9
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetHealthFacilityLevelReportType1(List<DQAReportDto> calculatedReports)
        {
            List<HealthFacilityType1Dto> reports = new List<HealthFacilityType1Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    HealthFacilityType1Dto report = new HealthFacilityType1Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        HealthFacilityName = requestData.HealthFacilityName,
                        Percentage = requestData.PercentageValue,
                        Year = requestData.Year,
                        VariableType = !string.IsNullOrEmpty(requestData.ReportParameterType) ? _translationService.GetDQATranslation(requestData.ReportParameterType) : string.Empty,
                        VariableTypeTraslationKey = requestData.ReportParameterType
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate health facility level report type 2 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetHealthFacilityLevelReportType2(List<DQAReportDto> calculatedReports)
        {
            List<HealthFacilityType2Dto> reports = new List<HealthFacilityType2Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    HealthFacilityType2Dto report = new HealthFacilityType2Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        HealthFacilityName = requestData.HealthFacilityName,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        VariableType = requestData.VariableType,
                        Variables = _translationService.GetDQATranslation(requestData.ReportParameterType),
                        VariableTypeTraslationKey = requestData.ReportParameterType
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }

        /// <summary>
        /// Method to generate health facility level summary report type 3 for DQA indicator 1.2.7, 1.2.8 and 1.2.10
        /// </summary>
        /// <param name="calculatedReports">List of Instance of DQAReportDto</param>
        /// <returns>Instance of object</returns>
        private object GetHealthFacilityLevelReportType3(List<DQAReportDto> calculatedReports)
        {
            List<HealthFacilityReportType3Dto> reports = new List<HealthFacilityReportType3Dto>();

            if (calculatedReports.Count > 0)
            {
                calculatedReports.ForEach((DQAReportDto requestData) =>
                {
                    HealthFacilityReportType3Dto report = new HealthFacilityReportType3Dto
                    {
                        Province = requestData.Province,
                        District = requestData.District,
                        HealthFacilityName = requestData.HealthFacilityName,
                        Year = requestData.Year,
                        Percentage = requestData.PercentageValue,
                        NumberOfReports = requestData.NumberOfReport,
                        NumberOfReportReceived = requestData.NumberOfReportReceived
                    };

                    reports.Add(report);
                });
            }

            return reports;
        }


        #endregion

        #endregion
    }
}
