﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for desk review of the indicators
    /// </summary>
    public interface IDeskReviewQueries
    {
        Task<IEnumerable<ResponseDocumentDto>> GetResponseDocuments(Guid assessmentIndicatorId, Guid assessmentStrategyId);
    }
}
