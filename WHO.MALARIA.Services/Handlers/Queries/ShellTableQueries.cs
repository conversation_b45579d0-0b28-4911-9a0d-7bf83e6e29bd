﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for shell table
    /// </summary>
    public class ShellTableQueries : RuleBase, IShellTableQueries
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IShellTable _shellTableDocumentManager;
        private readonly IAssessmentRuleChecker _assesmentRuleChecker;

        public ShellTableQueries(IUnitOfWork unitOfWork, ITranslationService translationService, ICommonRuleChecker commonRuleChecker, IShellTable shellTableDocumentManager, IAssessmentRuleChecker assesmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
            _shellTableDocumentManager = shellTableDocumentManager;
            _assesmentRuleChecker = assesmentRuleChecker;
        }

        #region Public Method             

        /// <summary>
        /// Get shell table respondent types and health facility types for the given assessment id
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table data are to be fetched</param>
        /// <returns>List of shell table respondent types and health facility types</returns>
        public async Task<RespondentAndHealthFacilityTypeDto> GetRespondentTypesAndHealthFacilityTypesAsync(Guid assessmentId)
        {
            RespondentAndHealthFacilityTypeDto response = new RespondentAndHealthFacilityTypeDto();

            IEnumerable<int> respondentTypes = await _unitOfWork.ShellTableRepository.GetRespondentTypesAsync(assessmentId);

            response.RespondentTypes = respondentTypes
                                      .Select(value => new KeyValuePair<int, string>
                                      (
                                        value,
                                       _translationService.GetTranslation(((QBRespondentType)value).GetTranslationKey())
                                      )).ToList();

            IEnumerable<HealthFacilityTypeAndRespondentTypeDto> healthFacilityTypes = await _unitOfWork.ShellTableRepository.GetHealthFacilityTypesAsync(assessmentId);

            response.HealthFacilityTypes = healthFacilityTypes.GroupBy(x => new { x.RespondentType, x.HealthFacilityType })
                                                              .Select(value => new HealthFacilityRepondentType
                                                              {
                                                                  RespondentType = value.Key.RespondentType,
                                                                  HealthFacilityName = _translationService.GetTranslation(((HealthFacilityType)value.Key.HealthFacilityType).GetTranslationKey()),
                                                                  HealthFacilityType = value.Key.HealthFacilityType
                                                              }).ToList();

            return response;
        }

        /// <summary>
        /// Get collection of indicators, sub-objectives and objectives information that are associated with the shell table question
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table question objectives sub-objectives indicators are to be fetched</param>
        /// <returns>Collection of shell table question indicators, sub-objectives and objectives</returns>
        public async Task<ObjectivesSubObjectivesIndicatorsDto> GetObjectivesSubObjectivesIndicatorsAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            ObjectivesSubObjectivesIndicatorsDto objectiveSubObjectiveIndicator = new ObjectivesSubObjectivesIndicatorsDto();

            IEnumerable<AssessmentQuestionObjectiveSubObjectiveIndicatorDto> assessmentQuestionSubObjectiveIndicators = await _unitOfWork.ShellTableRepository.GetObjectivesSubObjectivesIndicatorsAsync(assessmentId);

            // Get distinct objectives list based on the question selected for the shell table
            objectiveSubObjectiveIndicator.Objectives = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.ObjectiveId, x.ObjectiveName, x.ObjectiveSequence })
                                                                                                .Select(x => new QuestionBankObjectiveDto
                                                                                                {
                                                                                                    RespondentType = x.Key.RespondentType,
                                                                                                    Id = x.Key.ObjectiveId,
                                                                                                    Name = x.Key.ObjectiveName,
                                                                                                    Sequence = x.Key.ObjectiveSequence
                                                                                                }).ToList();

            // Get distinct subObjectives list based on the question selected for the shell table
            objectiveSubObjectiveIndicator.SubObjectives = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.SubObjectiveId, x.SubObjectiveName, x.SubObjectiveSequence, x.ObjectiveId })
                                                                                                   .Select(x => new QuestionBankSubObjectiveDto
                                                                                                   {
                                                                                                       RespondentType = x.Key.RespondentType,
                                                                                                       Id = x.Key.SubObjectiveId,
                                                                                                       Name = x.Key.SubObjectiveName,
                                                                                                       Sequence = x.Key.SubObjectiveSequence,
                                                                                                       ObjectiveId = x.Key.ObjectiveId
                                                                                                   }).ToList();

            // Get distinct indicators list based on the question selected for the shell table
            objectiveSubObjectiveIndicator.Indicators = assessmentQuestionSubObjectiveIndicators.GroupBy(x => new { x.RespondentType, x.IndicatorId, x.IndicatorName, x.IndicatorSequence, x.SubObjectiveId })
                                                                                                .Select(x => new QuestionBankIndicatorDto
                                                                                                {
                                                                                                    RespondentType = x.Key.RespondentType,
                                                                                                    Id = x.Key.IndicatorId,
                                                                                                    Name = x.Key.IndicatorName,
                                                                                                    Sequence = x.Key.IndicatorSequence,
                                                                                                    SubObjectiveId = x.Key.SubObjectiveId
                                                                                                }).ToList();

            return objectiveSubObjectiveIndicator;
        }

        /// <summary>
        /// Get shell table questions by respondent type and health facility type
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table data are to be fetched</param>
        /// <param name="respondentType">Respondent type for which shell table data are to be fetched</param>
        /// <param name="geoGraphicLevel">Geographic level for which shell table data are to be fetched</param>
        /// <param name="healthFacilityType">Health facility type for which shell table data are to be fetched</param>
        /// <returns>Shell table analysis output</returns>
        public async Task<IEnumerable<ShellTableQuestionsDto>> GetQuestionsAsync(Guid assessmentId, QBRespondentType respondentType, GeoGraphicLevels geoGraphicLevel, HealthFacilityType healthFacilityType)
        {
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, assessmentId, "AssessmentId"));

            List<ShellTableQuestionsDto> shellTableQuestionsDtos = new List<ShellTableQuestionsDto>();

            IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions = await _unitOfWork.ShellTableRepository.GetQuestionsAsync(assessmentId, respondentType, healthFacilityType);

            List<string> shellTableQuestionCodes = shellTableQuestions.Select(x => x.ShellTableQuestionCode).Distinct().ToList();

            List<Tuple<string, string, string>> districtsWithCodes = shellTableQuestions.Select(x => new Tuple<string, string, string>(x.DistrictCode, x.District, x.Region)).Distinct().ToList();

            List<string> districts = districtsWithCodes.Select(x => x.Item2).OrderBy(x => x).ToList();

            List<string> districtCodes = districtsWithCodes.Select(x => x.Item1).OrderBy(x => x).ToList();

            List<string> regionsOfDistricts = districtsWithCodes.Select(x => x.Item3).OrderBy(x => x).ToList();

            List<string> regions = shellTableQuestions.Select(x => x.Region).Distinct().OrderBy(x => x).ToList();

            string countryName = await _unitOfWork.AssessmentRepository.GetAssessmentCountryAsync(assessmentId);

            List<string> countries = new List<string>() { countryName };

            // Get questions wise dynamic row data
            shellTableQuestionCodes.ForEach(shellTableQuestionCode =>
            {
                ShellTableQuestionsDto shellTableQuestionsDto = new ShellTableQuestionsDto();

                List<ShellTableQuestionResponseDto> questionData = shellTableQuestions.Where(q => q.ShellTableQuestionCode == shellTableQuestionCode).ToList();

                shellTableQuestionsDto.IndicatorId = questionData.First().IndicatorId;

                string question = questionData.First().Question;

                ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultType = (ShellTableQuestionCalculationResultTypes)questionData.First().ShellTableQuestionCalculationResultType;

                // get dynamic header and sub header for question
                shellTableQuestionsDto.Headers = GetTableHeaders(geoGraphicLevel, question, countries, regions, districts, regionsOfDistricts);

                shellTableQuestionsDto.ShellTableQuestionCalculationResultType = shellTableQuestionCalculationResultType;

                int helalthFacilityCount = GetHealthFacilityCount(geoGraphicLevel, shellTableQuestions, questionData);

                shellTableQuestionsDto.Totals = GetQuestionTotals(geoGraphicLevel, helalthFacilityCount, countries, regions, districtCodes, shellTableQuestions);

                // Get options and their values based on the geo-graphic level.           
                List<List<string>> optionsValues = new List<List<string>>();

                switch (geoGraphicLevel)
                {
                    case GeoGraphicLevels.Regional:
                        optionsValues = GetQuestionOptions(geoGraphicLevel, regions, shellTableQuestionCode, questionData, helalthFacilityCount, shellTableQuestionCalculationResultType, shellTableQuestions);
                        break;

                    case GeoGraphicLevels.District:
                        optionsValues = GetQuestionOptions(geoGraphicLevel, districtCodes, shellTableQuestionCode, questionData, helalthFacilityCount, shellTableQuestionCalculationResultType, shellTableQuestions);
                        break;

                    default:
                        optionsValues = GetQuestionOptions(geoGraphicLevel, countries, shellTableQuestionCode, questionData, helalthFacilityCount, shellTableQuestionCalculationResultType, shellTableQuestions);
                        break;
                }

                shellTableQuestionsDto.Options = optionsValues;

                shellTableQuestionsDtos.Add(shellTableQuestionsDto);
            });

            return shellTableQuestionsDtos;
        }


        /// <summary>
        /// Get shell table questions by respondent type and health facility type
        /// </summary>
        /// <param name="assessmentId">Assessment id for which shell table data are to be fetched</param>
        /// <param name="respondentType">Respondent type for which shell table data are to be fetched</param>     
        /// <param name="healthFacilityType">Health facility type for which shell table data are to be fetched</param>
        /// <returns>Questions and their option values</returns>
        public async Task<FileResponseDto> GetShellTableQuestionsFileResponseAsync(Guid currentUserId, Guid assessmentId, QBRespondentType respondentType, HealthFacilityType healthFacilityType)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            CheckRule(new CanUserGenerateFile(_translationService, _assesmentRuleChecker, assessmentId, currentUserId, UserAssessmentPermission.CanExportShellTable, Constants.Exception.NoPermissionToExportShellTableTemplate));

            IEnumerable<ShellTableQuestionObjectiveSubObjectiveIndicatorDto> shellTableQuestions = await _unitOfWork.ShellTableRepository.GetQuestionObjectivesSubObjectivesIndicatorsAsync(assessmentId, respondentType, healthFacilityType);

            Assessment assessment = await _unitOfWork.AssessmentRepository.Queryable(u => u.Id == assessmentId)
                                                     .Include(u => u.Country)
                                                     .SingleAsync();

            List<string> columns = new List<string>() { "Objective No.", "Objective", "Sub Objective No.", "Sub Objective", "Indicator No.", "Indicator", "Question", "Question Code", "Option" };

            string countryName = assessment.Country?.Name;

            int assessmentYear = assessment.StartDate.Year;

            List<string> healthFacilities = new List<string>();
            List<string> districts = new List<string>();

            if (respondentType != QBRespondentType.SubnationalLevel)
                healthFacilities = shellTableQuestions.GroupBy(q => q.HealthFacilityName).OrderBy(t => t.Key).Select(x => x.Key).ToList();

            if (respondentType == QBRespondentType.SubnationalLevel)
                districts = shellTableQuestions.GroupBy(q => q.DistrictName).OrderBy(t => t.Key).Select(x => x.Key).ToList();

            List<QuestionOptionOrder> questionOptionOrders = shellTableQuestions.GroupBy(q => new { q.QuestionId, q.OptionOrder }).Select(q => new QuestionOptionOrder { QuestionId = q.Key.QuestionId, OptionOrder = q.Key.OptionOrder }).ToList();

            DataTable dt = new DataTable();

            columns.ForEach(column =>
            {
                dt.Columns.Add(column, typeof(string));
            });

            if (respondentType != QBRespondentType.SubnationalLevel)
            {
                healthFacilities.ForEach(hf =>
                {
                    dt.Columns.Add(hf, typeof(string));
                });
            }

            if (respondentType == QBRespondentType.SubnationalLevel)
            {
                districts.ForEach(d =>
                {
                    dt.Columns.Add(d, typeof(string));
                });
            }
            DataRow row;

            // Add questions detail in datable
            questionOptionOrders.ForEach(questionOrder =>
            {
                row = dt.NewRow();

                List<ShellTableQuestionObjectiveSubObjectiveIndicatorDto> shellTableQuestion = shellTableQuestions.Where(q => q.QuestionId == questionOrder.QuestionId && q.OptionOrder == questionOrder.OptionOrder).ToList();

                row[columns[0]] = shellTableQuestion.FirstOrDefault().ObjectiveSequence;
                row[columns[1]] = shellTableQuestion.FirstOrDefault().ObjectiveName;
                row[columns[2]] = shellTableQuestion.FirstOrDefault().SubObjectiveSequence;
                row[columns[3]] = shellTableQuestion.FirstOrDefault().SubObjectiveName;
                row[columns[4]] = shellTableQuestion.FirstOrDefault().IndicatorSequence;
                row[columns[5]] = shellTableQuestion.FirstOrDefault().IndicatorName;
                row[columns[6]] = shellTableQuestion.FirstOrDefault().Question;
                row[columns[7]] = shellTableQuestion.FirstOrDefault().ShellTableQuestionCode;
                row[columns[8]] = shellTableQuestion.FirstOrDefault().Option;


                // Add question option value health facility code wise
                if (respondentType != QBRespondentType.SubnationalLevel)
                {
                    healthFacilities.ForEach(hf =>
                    {
                        row[hf] = shellTableQuestion.FirstOrDefault(q => q.HealthFacilityName == hf)?.Value;
                    });
                }

                // Add question option value district code wise
                if (respondentType == QBRespondentType.SubnationalLevel)
                {
                    districts.ForEach(d =>
                    {
                        row[d] = shellTableQuestion.FirstOrDefault(q => q.DistrictName == d)?.Value;
                    });
                }

                dt.Rows.Add(row);
            });

            string responenTypeName = _translationService.GetTranslation(((QBRespondentType)respondentType).GetTranslationKey());

            string name = $"{responenTypeName}_Survey";

            string fileName = string.Empty;

            if (respondentType == QBRespondentType.SubnationalLevel)
            {
                fileName = $"{countryName}_{Constants.DownloadDocument.SurveryData}_{responenTypeName}_{assessmentYear}.xlsx";
            }
            else
            {
                string healthFacilityName = _translationService.GetTranslation(((HealthFacilityType)healthFacilityType).GetTranslationKey());

                fileName = $"{countryName}_{Constants.DownloadDocument.SurveryData}_{responenTypeName}_{healthFacilityName}_{assessmentYear}.xlsx";
            }

            FileResponseDto fileResponse = new FileResponseDto()
            {
                FileData = _shellTableDocumentManager.GenerateShellTableFlatFile(dt, name),
                FileName = fileName
            };

            return fileResponse;
        }

        #endregion

        #region  Private Method

        /// <summary>
        /// Get questions option details base on shell table question code 
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questionData">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>
        /// <param name="shellTableQuestions">Shell table questions</param>
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetQuestionOptions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, string shellTableQuestionCode, IEnumerable<ShellTableQuestionResponseDto> questionData, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes, IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions)
        {
            List<List<string>> questionOptionData = new List<List<string>>();

            switch (shellTableQuestionCode)
            {
                case ShellTableQuestionBankMappingSeedingMetadata.CASEMGMT_4:
                case ShellTableQuestionBankMappingSeedingMetadata.CASEMGMT_5:

                    questionOptionData = GetOptionValuesOfCaseManagementPercentageQuestions(geoGraphicLevel, geoGraphicData, questionData, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;

                case ShellTableQuestionBankMappingSeedingMetadata.DATAUSE_5_DATAUSE_6:
                case ShellTableQuestionBankMappingSeedingMetadata.DATAUSE_7_DATAUSE_8_DATAUSE_9_DATAUSE_10:
                case ShellTableQuestionBankMappingSeedingMetadata.STAFF_8_STAFF_9:

                    questionOptionData = GetOptionValuesOfDataUseQuestions(geoGraphicLevel, geoGraphicData, questionData, healthFacilityCount, shellTableQuestionCalculationResultTypes, shellTableQuestionCode);
                    break;        

                case ShellTableQuestionBankMappingSeedingMetadata.QA_6:
                case ShellTableQuestionBankMappingSeedingMetadata.ACCESS_6_ACCESS_7_ACCESS_8:
                case ShellTableQuestionBankMappingSeedingMetadata.INFO_CULT_1_INFO_CULT_2_INFO_CULT_3:
                case ShellTableQuestionBankMappingSeedingMetadata.INFO_CULT_4_INFO_CULT_5_INFO_CULT_6_INFO_CULT_7_INFO_CULT_8:
                case ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8a:
                case ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8b:
                case ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_9_SUPERVISION_10_SUPERVISION_11_SUPERVISION_12:
                case ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_13:
                case ShellTableQuestionBankMappingSeedingMetadata.RECORDING_11_RECORDING_12_RECORDING_13_RECORDING_14:
                case ShellTableQuestionBankMappingSeedingMetadata.REPORING_13_REPORING_14_REPORING_15_REPORING_16:
                case ShellTableQuestionBankMappingSeedingMetadata.ANALYSIS_8_ANALYSIS_9_ANALYSIS_10_ANALYSIS_11:

                    questionOptionData = GetOptionValuesOfSingleAverageQuestions(geoGraphicLevel, geoGraphicData, questionData, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;

                default:

                    questionOptionData = GetOptionValuesOfPercentageQuestions(geoGraphicLevel, geoGraphicData, questionData, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
            }

            return questionOptionData;
        }
        
        /// <summary>
        /// Get option value from single average questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of QA questions option and value</returns>
        private List<List<string>> GetOptionValuesOfSingleAverageQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.District:
                    optionValues = GetDistrictLevelOptionValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                case GeoGraphicLevels.Regional:
                    optionValues = GetRegionalLevelSingleAverageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                default:
                    optionValues = GetNationalLevelAverageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
            }
            return optionValues;
        }

        /// <summary>
        /// Get option value from percentage questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of QA questions option and value</returns>
        private List<List<string>> GetOptionValuesOfPercentageQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.District:
                    optionValues = GetDistrictLevelOptionValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                case GeoGraphicLevels.Regional:
                    optionValues = GetRegionalLevelPercentageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                default:
                    optionValues = GetNationalLevelPercentageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
            }
            return optionValues;
        }


        /// <summary>
        /// Get option value from case management percentage questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of QA questions option and value</returns>
        private List<List<string>> GetOptionValuesOfCaseManagementPercentageQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.District:
                    optionValues = GetOptionValuesOfCaseManangementQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                case GeoGraphicLevels.Regional:
                    optionValues = GetRegionalLevelCaseManagementPercentageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
                default:
                    optionValues = GetNationalLevelCaseManagementPercentageValuesOfQuestions(geoGraphicLevel, geoGraphicData, questions, healthFacilityCount, shellTableQuestionCalculationResultTypes);
                    break;
            }
            return optionValues;
        }

        /// <summary>
        /// Get questions option details based on the shell table questions codes like casemgmt_4, casemgmt_5
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>   
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetOptionValuesOfCaseManangementQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            int caseManagmentOptionOrder = 1;

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questioOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questioOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                foreach (string geoGraphic in geoGraphicData)
                {
                    if (caseManagmentOptionOrder != optionOrder)
                    {
                        List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                        questioOptions.Add(Convert.ToString(geoGraphicWiseQuestions.Sum(q => q.Value)));

                        questioOptions.Add(CalculateOptionValue(geoGraphicWiseQuestions, healthFacilityCount, shellTableQuestionCalculationResultTypes));

                    }
                    else
                    {
                        questioOptions.Add("");
                        questioOptions.Add("");
                        caseManagmentOptionOrder = caseManagmentOptionOrder + 3;
                        break;
                    }
                }

                optionValues.Add(questioOptions);
            });

            return optionValues;

        }


        /// <summary>
        /// Get regional level case management percentage option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of regional level average questions option and value</returns>
        private List<List<string>> GetRegionalLevelCaseManagementPercentageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            int caseManagmentOptionOrder = 1;

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                foreach (string geoGraphic in geoGraphicData)
                {
                    if (caseManagmentOptionOrder != optionOrder)
                    {
                        List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                        int? optionValue = geoGraphicWiseQuestions.Sum(q => q.Value);

                        int numberOfDistrict = geoGraphicWiseQuestions.Count();

                        double percentage = (double)optionValue.Value.CalculatePercentage(numberOfDistrict);

                        string percentageValue = Convert.ToString(Math.Round(percentage, 0, MidpointRounding.AwayFromZero));

                        questionOptions.Add(Convert.ToString(optionValue));

                        questionOptions.Add(percentageValue);
                    }
                    else
                    {
                        questionOptions.Add("");
                        questionOptions.Add("");
                        caseManagmentOptionOrder = caseManagmentOptionOrder + 3;
                        break;
                    }
                }

                optionValues.Add(questionOptions);
            });

            return optionValues;
        }

        /// <summary>
        /// Get national level percentage option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of national level questions option and value</returns>
        private List<List<string>> GetNationalLevelCaseManagementPercentageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            int caseManagmentOptionOrder = 1;

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                foreach (string geoGraphic in geoGraphicData)
                {
                    if (caseManagmentOptionOrder != optionOrder)
                    {

                        List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                        double optionValueAverage = 0;

                        double average = 0;

                        int? optionValue = 0;

                        List<string> geoGraphicRegions = GetQuestionsRegionsByGeographicLevel(geoGraphicWiseQuestions, geoGraphicLevel);

                        foreach (string region in geoGraphicRegions)
                        {
                            List<ShellTableQuestionResponseDto> regionWiseQuestions = geoGraphicWiseQuestions.Where(q => q.Region == region).ToList();

                            int? regionOptionValue = regionWiseQuestions.Sum(q => q.Value);

                            optionValueAverage += Math.Round((double)regionOptionValue.Value.CalculatePercentage(regionWiseQuestions.Count()), 0, MidpointRounding.AwayFromZero);

                            optionValue += regionOptionValue;
                        }

                        int numberOfRegion = geoGraphicRegions.Count();

                        average = (double)(optionValueAverage / numberOfRegion);

                        string averageValue = Convert.ToString(Math.Round(average, 0, MidpointRounding.AwayFromZero));

                        questionOptions.Add(Convert.ToString(optionValue));

                        questionOptions.Add(averageValue);
                    }
                    else
                    {
                        questionOptions.Add("");
                        questionOptions.Add("");
                        caseManagmentOptionOrder = caseManagmentOptionOrder + 3;
                        break;
                    }
                }

                optionValues.Add(questionOptions);
            });

            return optionValues;
        }

        /// <summary>
        /// Get regional level percentage option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of regional level average questions option and value</returns>
        private List<List<string>> GetRegionalLevelPercentageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    int? optionValue = geoGraphicWiseQuestions.Sum(q => q.Value);

                    int numberOfDistrict = geoGraphicWiseQuestions.Count();

                    double percentage = (double)optionValue.Value.CalculatePercentage(numberOfDistrict);

                    string percentageValue = Convert.ToString(Math.Round(percentage, 0, MidpointRounding.AwayFromZero));

                    questionOptions.Add(Convert.ToString(optionValue));

                    questionOptions.Add(percentageValue);
                });

                optionValues.Add(questionOptions);
            });

            return optionValues;

        }


        /// <summary>
        /// Get national level percentage option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of national level questions option and value</returns>
        private List<List<string>> GetNationalLevelPercentageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    double optionValueAverage = 0;

                    double average = 0;

                    int? optionValue = 0;

                    List<string> geoGraphicRegions = GetQuestionsRegionsByGeographicLevel(geoGraphicWiseQuestions, geoGraphicLevel);

                    foreach (string region in geoGraphicRegions)
                    {
                        List<ShellTableQuestionResponseDto> regionWiseQuestions = geoGraphicWiseQuestions.Where(q => q.Region == region).ToList();

                        int? regionOptionValue = regionWiseQuestions.Sum(q => q.Value);

                        optionValueAverage += Math.Round((double)regionOptionValue.Value.CalculatePercentage(regionWiseQuestions.Count()), 0, MidpointRounding.AwayFromZero);

                        optionValue += regionOptionValue;
                    }

                    int numberOfRegion = geoGraphicRegions.Count();

                    average = (double)(optionValueAverage / numberOfRegion);

                    string averageValue = Convert.ToString(Math.Round(average, 0, MidpointRounding.AwayFromZero));

                    questionOptions.Add(Convert.ToString(optionValue));

                    questionOptions.Add(averageValue);
                });

                optionValues.Add(questionOptions);
            });

            return optionValues;

        }

        /// <summary>
        /// Get regional level single average option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of regional level questions option and value</returns>
        private List<List<string>> GetRegionalLevelSingleAverageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    int? optionValue = geoGraphicWiseQuestions.Sum(q => q.Value);

                    double average = (double)optionValue.Value / geoGraphicWiseQuestions.Count();

                    string averageValue = Convert.ToString(Math.Round(average, 0, MidpointRounding.AwayFromZero));

                    questionOptions.Add(averageValue);

                    questionOptions.Add("");
                });

                optionValues.Add(questionOptions);
            });

            return optionValues;

        }
            

        /// <summary>
        /// Get national level average option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of national level questions option and value</returns>
        private List<List<string>> GetNationalLevelAverageValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questionOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questionOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    double optionValueAverage = 0;

                    double average = 0;

                    List<string> geoGraphicRegions = GetQuestionsRegionsByGeographicLevel(geoGraphicWiseQuestions, geoGraphicLevel);

                    foreach (string region in geoGraphicRegions)
                    {
                        List<ShellTableQuestionResponseDto> regionWiseQuestions = geoGraphicWiseQuestions.Where(q => q.Region == region).ToList();
                       
                        double optionValue = Math.Round((double)regionWiseQuestions.Sum(q => q.Value) / regionWiseQuestions.Count(), 0, MidpointRounding.AwayFromZero);

                        optionValueAverage += optionValue;
                    }

                    average = (double)optionValueAverage / geoGraphicRegions.Count();

                    string averageValue = Convert.ToString(Math.Round(average, 0, MidpointRounding.AwayFromZero));

                    questionOptions.Add(averageValue);

                    questionOptions.Add("");
                });

                optionValues.Add(questionOptions);
            });

            return optionValues;

        }

        /// <summary>
        /// Get district level option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetDistrictLevelOptionValuesOfQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questioOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questioOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    questioOptions.Add(Convert.ToString(geoGraphicWiseQuestions.Sum(q => q.Value)));

                    healthFacilityCount = (int)geoGraphicWiseQuestions.Sum(q => q.Value);

                    questioOptions.Add(CalculateOptionValue(geoGraphicWiseQuestions, healthFacilityCount, shellTableQuestionCalculationResultTypes));

                });

                optionValues.Add(questioOptions);
            });

            return optionValues;

        }

        /// <summary>
        /// Get option value from questions based on the geo-graphic level
        /// </summary>      
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>      
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetOptionValuesOfDataUseQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes, string shellTableQuestionCode)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questioOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questioOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    questioOptions.Add(Convert.ToString(geoGraphicWiseQuestions.Sum(q => q.Value)));
                    switch (shellTableQuestionCode)
                    {
                        case ShellTableQuestionBankMappingSeedingMetadata.DATAUSE_5_DATAUSE_6:
                        case ShellTableQuestionBankMappingSeedingMetadata.STAFF_8_STAFF_9:
                            if (optionOrder == (int)OptionOrders.OptionOrder_1)
                            {
                                var expected = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_1).ToList();
                                var actual = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_2).ToList();
                                questioOptions.Add(CalculateOptionValue(expected, actual, healthFacilityCount, geoGraphicLevel, geoGraphic));
                            }
                            break;

                        case ShellTableQuestionBankMappingSeedingMetadata.DATAUSE_7_DATAUSE_8_DATAUSE_9_DATAUSE_10:
                            if (optionOrder == (int)OptionOrders.OptionOrder_1)
                            {
                                var expected = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_1).ToList();
                                var actual = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_2).ToList();
                                questioOptions.Add(CalculateOptionValue(expected, actual, healthFacilityCount, geoGraphicLevel, geoGraphic));
                            }
                            else if (optionOrder == (int)OptionOrders.OptionOrder_3)
                            {
                                var expected = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_3).ToList();
                                var actual = questions.Where(t => t.OptionOrder == (int)OptionOrders.OptionOrder_4).ToList();
                                questioOptions.Add(CalculateOptionValue(expected, actual, healthFacilityCount, geoGraphicLevel, geoGraphic));
                            }
                            break;
                    }
                });

                optionValues.Add(questioOptions);
            });

            return optionValues;

        }

        public string CalculateOptionValue(List<ShellTableQuestionResponseDto> questions_Expected, List<ShellTableQuestionResponseDto> questions_Actual, int healthFacilityCount, GeoGraphicLevels geoGraphicLevel, string geoGraphic)
        {
            decimal averageVal = 0;
            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    questions_Expected = questions_Expected.Where(t => t.Region == geoGraphic).ToList();
                    questions_Actual = questions_Actual.Where(t => t.Region == geoGraphic).ToList();
                    break;

                case GeoGraphicLevels.District:
                    questions_Expected = questions_Expected.Where(t => t.DistrictCode == geoGraphic).ToList();
                    questions_Actual = questions_Actual.Where(t => t.DistrictCode == geoGraphic).ToList();
                    break;
            }

            int? question_actual_value = questions_Actual?.Sum(x => x.Value);

            int? question_expected_value = questions_Expected?.Sum(x => x.Value);

            if ((question_actual_value ?? 0) != 0 && (question_expected_value ?? 0) != 0)
            {
                averageVal += (decimal)question_actual_value / (decimal)question_expected_value;
            }
            else
            {
                averageVal += 0;
            }

            return String.Format("{0:0.00}", (averageVal) * 100);
        }


        /// <summary>
        /// Get questions option details based on the shell table questions codes like equip_2, equip_3 
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="shellTableQuestionCode">Shell table question code</param>
        /// <param name="questions">Shell table questions</param>
        /// <param name="shellTableQuestionCalculationResultTypes">Shell table question calculation result type</param>
        /// <param name="shellTableQuestions">Shell table questions</param>
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetOptionValuesOfEquipmentQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, string shellTableQuestionCode, IEnumerable<ShellTableQuestionResponseDto> questions, ShellTableQuestionCalculationResultTypes shellTableQuestionCalculationResultTypes, IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions)
        {
            List<List<string>> optionValues = new List<List<string>>();

            List<byte> optionOrders = questions.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            int previousQuestionOrderNo = 1;

            // If the shell table question code is EQUIP_3, begin reading from the third option of the question option of shell table question code EQUIP_1. Otherwise, begin reading from option 1.
            if (shellTableQuestionCode == ShellTableQuestionBankMappingSeedingMetadata.EQUIP_3)
            {
                previousQuestionOrderNo = previousQuestionOrderNo + 2;
            }

            optionOrders.ForEach(optionOrder =>
            {
                List<string> questioOptions = new List<string>();

                List<ShellTableQuestionResponseDto> orderWiseQuestions = questions.Where(q => q.OptionOrder == optionOrder).ToList();

                questioOptions.Add(orderWiseQuestions.FirstOrDefault().Option);

                geoGraphicData.ForEach(geoGraphic =>
                {
                    List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(orderWiseQuestions, geoGraphicLevel, geoGraphic);

                    List<ShellTableQuestionResponseDto> geoGraphicWiseEquipmentQuestions = GetQuestionsByGeographicLevel(shellTableQuestions, geoGraphicLevel, geoGraphic);

                    List<ShellTableQuestionResponseDto> equipment1Questions = geoGraphicWiseEquipmentQuestions.Where(q => q.ShellTableQuestionCode == ShellTableQuestionBankMappingSeedingMetadata.EQUIP_1 && q.QuestionBankQuestionCode == ShellTableQuestionBankMappingSeedingMetadata.EQUIP_1 && q.OptionOrder == previousQuestionOrderNo).ToList();

                    int totalValue = equipment1Questions.Sum(q => q.Value).Value;

                    questioOptions.Add(Convert.ToString(geoGraphicWiseQuestions.Sum(q => q.Value)));

                    questioOptions.Add(CalculateOptionValue(geoGraphicWiseQuestions, totalValue, shellTableQuestionCalculationResultTypes));

                });

                previousQuestionOrderNo++;

                optionValues.Add(questioOptions);
            });

            return optionValues;

        }

        /// <summary>
        /// Get questions option details based on the shell table questions codes like "supervision_9, supervision_10, supervision_11, supervision_12"
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicData">Geo graphic data contains a list of districts, regions</param>
        /// <param name="questionData">Shell table questions</param>
        /// <param name="shellTableQuestions">Shell table questions</param>
        /// <returns>List of questions option and value</returns>
        private List<List<string>> GetOptionValuesOfSupervisionQuestions(GeoGraphicLevels geoGraphicLevel, List<string> geoGraphicData, string shellTableQuestionCode, IEnumerable<ShellTableQuestionResponseDto> questionData, IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions)
        {
            List<List<string>> optionValues = new List<List<string>>();

            int optionOrderNo = 1;

            List<byte> optionOrders = questionData.GroupBy(q => q.OptionOrder).Select(q => q.Key).ToList();

            List<string> questioOptions = new List<string>();

            List<ShellTableQuestionResponseDto> questions = questionData.Where(q => q.OptionOrder == optionOrderNo).ToList();

            questioOptions.Add(questions.FirstOrDefault().Option);

            geoGraphicData.ForEach(geoGraphic =>
            {
                List<ShellTableQuestionResponseDto> geoGraphicWiseQuestions = GetQuestionsByGeographicLevel(shellTableQuestions.ToList(), geoGraphicLevel, geoGraphic);

                List<ShellTableQuestionResponseDto> questionOptionData = geoGraphicWiseQuestions.Where(q => q.ShellTableQuestionCode.ToLower().Trim() == shellTableQuestionCode.ToLower().Trim() && q.OptionOrder == optionOrderNo).ToList();

                int value = Convert.ToInt32(questionOptionData.Sum(q => q.Value));

                double averageVal = 0;

                averageVal = (double)value / questionOptionData.Count();

                questioOptions.Add(Convert.ToString(Math.Round(averageVal, 0, MidpointRounding.AwayFromZero)));

                questioOptions.Add("");
            });

            optionValues.Add(questioOptions);

            return optionValues;
        }

        /// <summary>
        /// Get geo graphical wise shell table questions details
        /// </summary>
        /// <param name="shellTableQuestions">Shell table questions</param>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="geoGraphicRegion">Geo graphic region</param>  
        /// <returns>List of questions</returns>
        private List<ShellTableQuestionResponseDto> GetQuestionsByGeographicLevel(IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions, GeoGraphicLevels geoGraphicLevel, string geoGraphicRegion)
        {
            List<ShellTableQuestionResponseDto> questionData = new List<ShellTableQuestionResponseDto>();

            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    questionData = shellTableQuestions.Where(q => q.Region == geoGraphicRegion).ToList();
                    break;

                case GeoGraphicLevels.District:
                    questionData = shellTableQuestions.Where(q => q.DistrictCode == geoGraphicRegion).ToList();
                    break;

                default:
                    questionData = shellTableQuestions.ToList();
                    break;
            }

            return questionData;
        }


        /// <summary>
        /// Get the geo graphical wise shell table regions
        /// </summary>
        /// <param name="questionData">Shell table questions</param>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <returns>List of regions</returns>
        private List<string> GetQuestionsRegionsByGeographicLevel(IEnumerable<ShellTableQuestionResponseDto> questionData, GeoGraphicLevels geoGraphicLevel)
        {
            List<string> geoGraphicRegions = new List<string>();
            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    geoGraphicRegions = questionData.Select(que => que.DistrictCode).Distinct().ToList();
                    break;

                case GeoGraphicLevels.District:
                    geoGraphicRegions = questionData.Select(que => que.HealthFacilityCode).Distinct().ToList();
                    break;

                default:
                    geoGraphicRegions = questionData.Select(que => que.Region).Distinct().ToList();
                    break;
            }
            return geoGraphicRegions;
        }

        /// <summary>
        /// Get headers that will contain question, regions/districts/regions
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level type</param>
        /// <param name="question">Shell table question</param>
        /// <param name="countries">List of countries</param>
        /// <param name="regions">List of regions</param>
        /// <param name="districts">List of districts</param>
        /// <param name="regionsOfDistricts">List of district regions</param>
        /// <returns>List of headers</returns>
        private List<ShellTableHeader> GetTableHeaders(GeoGraphicLevels geoGraphicLevel, string question, List<string> countries, List<string> regions, List<string> districts, List<string> regionsOfDistricts)
        {
            List<ShellTableHeader> headers = new List<ShellTableHeader>();

            headers.Add(new ShellTableHeader { HeaderName = question });

            return PopulateHeader(geoGraphicLevel, headers, regions, districts, countries, regionsOfDistricts);
        }

        /// <summary>
        /// Populate healders of shell table questions
        /// </summary>
        /// <param name="geoGraphicLevel">Selected geoGraphic level</param>
        /// <param name="headers">List of headers</param>
        /// <param name="regions">List of regions</param>
        /// <param name="districts">List of districts</param>
        /// <param name="countries">List of countries</param>
        /// <param name="regionsOfDistricts">List of regions of districts</param>
        /// <returns>Shell table header object</returns>
        private List<ShellTableHeader> PopulateHeader(GeoGraphicLevels geoGraphicLevel, List<ShellTableHeader> headers, List<string> regions, List<string> districts, List<string> countries, List<string> regionsOfDistricts)
        {
            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    {
                        foreach (string region in regions)
                        {
                            headers.Add(new ShellTableHeader { HeaderName = region });
                        }
                    }
                    break;
                case GeoGraphicLevels.District:
                    {
                        var districtWithRegions = districts.Zip(regionsOfDistricts, (d, r) => new { District = d, Region = r });

                        foreach (var item in districtWithRegions)
                        {
                            headers.Add(new ShellTableHeader { HeaderName = item.District, RegionName = item.Region });
                        }
                    }
                    break;
                default:
                    {
                        foreach (string country in countries)
                        {
                            headers.Add(new ShellTableHeader { HeaderName = country });
                        }
                    }
                    break;
            }
            return headers;
        }

        /// <summary>
        /// Get question totals
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="countries">List of countries</param>
        /// <param name="regions">List of regions</param>
        /// <param name="districtCodes">List of districts</param>
        /// <param name="shellTableQuestions">List of shell table questions</param>
        /// <returns>List of totals</returns>
        private List<int> GetQuestionTotals(GeoGraphicLevels geoGraphicLevel, int healthFacilityCount, List<string> countries, List<string> regions, List<string> districtCodes, IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions)
        {
            List<int> totals = new List<int>();

            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    for (int i = 0; i < regions.Count; i++)
                    {
                        healthFacilityCount = shellTableQuestions.Where(t => t.Region == regions[i]).GroupBy(x => new { x.HealthFacilityCode, x.Region }).Count();
                        totals.Add(healthFacilityCount);
                    }
                    break;

                case GeoGraphicLevels.District:
                    for (int i = 0; i < districtCodes.Count; i++)
                    {
                        healthFacilityCount = shellTableQuestions.Where(t => t.DistrictCode == districtCodes[i]).GroupBy(x => new { x.HealthFacilityCode, x.District, x.DistrictCode }).Count();
                        totals.Add(healthFacilityCount);
                    }
                    break;

                default:
                    for (int i = 0; i < countries.Count; i++)
                    {
                        totals.Add(healthFacilityCount);
                    }
                    break;
            }

            return totals;
        }

        /// <summary>
        /// Get health facility count based on geo graphic type
        /// </summary>
        /// <param name="geoGraphicLevel">Geo graphic level type</param>
        /// <param name="shellTableQuestions">Shell table questions</param>        
        /// <returns>Health facility count</returns>
        private int GetHealthFacilityCount(GeoGraphicLevels geoGraphicLevel, IEnumerable<ShellTableQuestionResponseDto> shellTableQuestions, IEnumerable<ShellTableQuestionResponseDto> questionData)
        {
            switch (geoGraphicLevel)
            {
                case GeoGraphicLevels.Regional:
                    var region = questionData.FirstOrDefault()?.Region;
                    return shellTableQuestions.Where(t => t.Region == region).GroupBy(x => new { x.HealthFacilityCode, x.Region, x.DistrictCode }).Count();

                case GeoGraphicLevels.District:
                    var district = questionData.FirstOrDefault()?.District;
                    string districtCode = questionData.FirstOrDefault()?.DistrictCode;
                    return shellTableQuestions.Where(t => t.District == district && t.DistrictCode == districtCode).GroupBy(x => new { x.HealthFacilityCode, x.District, x.DistrictCode }).Count();

                default:
                    return shellTableQuestions.GroupBy(x => new { x.HealthFacilityCode }).Count();
            }
        }

        /// <summary>
        /// Get question option calculated value
        /// </summary>
        /// <param name="questions">List of shell table questions</param>
        /// <param name="healthFacilityCount">Health facility count</param>
        /// <param name="shellTableQuestionCalculationResultType">Shell table question calculation result type</param>
        /// <returns>Calculated value</returns>
        private string CalculateOptionValue(IEnumerable<ShellTableQuestionResponseDto> questions, int healthFacilityCount = 1, ShellTableQuestionCalculationResultTypes? shellTableQuestionCalculationResultType = null)
        {
            switch (shellTableQuestionCalculationResultType)
            {
                case ShellTableQuestionCalculationResultTypes.Percentage:
                    return Convert.ToString(Convert.ToInt32(Math.Round((questions.Sum(q => q.Value).Value).CalculatePercentage(healthFacilityCount), 0, MidpointRounding.AwayFromZero)));

                case ShellTableQuestionCalculationResultTypes.Average:
                    string averageValue = string.Empty;
                    double? questionAverageValue = questions.Select(q => q.Value).Average();
                    if (questionAverageValue.HasValue)
                    {
                        averageValue = Convert.ToString(Math.Round(questionAverageValue.Value, 0, MidpointRounding.AwayFromZero));
                    }
                    else
                    {
                        averageValue = "0";
                    }
                    return averageValue;

                default:
                    return Convert.ToString(questions.Where(q => q.Value != null).Sum(q => q.Value).Value);
            }
        }

        #endregion
    }
}
