﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to Data Analysis Reports
    /// </summary>
    public interface IDataAnalysisReportQueries
    {
        Task<DataAnalysisReportDto> GetReportsAsync(Guid assessmentId);
        Task<DataAnalysisReport> GetReportDocumentAsync(Guid reportId);
    }
}
