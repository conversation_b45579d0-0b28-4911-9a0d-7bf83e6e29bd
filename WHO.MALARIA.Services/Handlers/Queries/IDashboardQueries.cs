﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Contains all methods specific to country dashboard
    /// </summary>
    public interface IDashboardQueries
    {
        Task<DashboardResultDto> GetDashboardRecordsAsync(Guid userId, Guid countryId, int year, bool checkForUserAceess = true);
        Task<IEnumerable<int>> GetYearsAsync(Guid userId, Guid countryId);
        Task<GlobalDashboardRegionalSummaryDto> GetRegionalSummaryDetailsAsync(string language);
        Task<GlobalDashboardIndicatorSummaryDto> GetIndicatorSummaryDetailsAsync(string language);
        Task<GlobalDashboardObjectiveMapSummaryDto> GetObjectiveMapSummaryDetailsAsync(string language,Guid userId );
        Task<DashboardAssessmentStatusApproachDTO> GetDashboardAssessmentStatusesAndApproachesAsync();
        Task<DashboardAssessmentStatusDTO> GetDashboardAssessmentStatusesAsync(int assessmentYear);
    }
}
