﻿using System;
using System.Globalization;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using IdentityModel.Client;

using Microsoft.AspNetCore.Http;

using Serilog;

using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;

namespace WHO.MALARIA.Services.Infrastructure
{
    /// <summary>
    /// Validates current access token expiration and renews token if required
    /// </summary
    public class TokenRefresherService : ITokenRefresherService
    {
        private readonly AppSettings _applicationSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public TokenRefresherService(AppSettings applicationSettings, IHttpContextAccessor httpContextAccessor)
        {
            _applicationSettings = applicationSettings;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        ///Checks if token needs to be refreshed or not by validating current access token expiration
        /// </summary>
        /// <param name="refreshToken">Token used to refresh access token </param>
        /// <param name="expiresAt">Time at which access token expires</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Status of request token request as Failed, Refresh Not Required, Success </returns>
        public async Task<TokenRefreshResult> TryRefreshTokenIfRequiredAsync(string refreshToken, string expiresAt, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(refreshToken))
            {
                return TokenRefreshResult.Failed();
            }

            //Check if the Access token is expired or about to expire. If access token expiration time is grater than or equal to Threshold time of 5 Min then token refresh is not needed
            if (!DateTime.TryParse(expiresAt, out var expiresAtDate) || expiresAtDate >= GetRefreshThreshold())
            {
                return TokenRefreshResult.NoRefreshNeeded();
            }

            //Attempt to refresh Token
            TokenResponse tokenResponse = await new HttpClient().RequestRefreshTokenAsync(
                new RefreshTokenRequest
                {
                    Address = $"{_httpContextAccessor.HttpContext.GetApplicationURL()}/connect/token",
                    ClientId = _applicationSettings.AzureAD.ClientId,
                    ClientSecret = _applicationSettings.AzureAD.ClientSecret,
                    RefreshToken = refreshToken
                }, cancellationToken);

            //check if refresh token was successful else return failed result
            if (tokenResponse.IsError)
            {
                Log.Information("Unable to refresh token, reason: {refreshTokenErrorDescription}", tokenResponse.ErrorDescription);
                return TokenRefreshResult.Failed();
            }

            string newAccessToken = tokenResponse.AccessToken;
            string newRefreshToken = tokenResponse.RefreshToken;
            string newExpiresAt = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn).ToString("o", CultureInfo.InvariantCulture);

            return TokenRefreshResult.Success(newAccessToken, newRefreshToken, newExpiresAt);
        }

        /// <summary>
        /// Provides Time to be compared with the access token expiration time to verify is the token needs to be renewed
        /// </summary>
        private DateTime GetRefreshThreshold()
        {
            return DateTime.UtcNow + TimeSpan.FromSeconds(_applicationSettings.TokenRefreshThreshold);
        }
    }
}
