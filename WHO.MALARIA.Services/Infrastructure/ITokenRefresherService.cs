﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Services.Infrastructure
{
    /// <summary>
    /// Provides a way to ensure the user's access token is up to date. Token renewal is performed if required
    /// </summary>
    public interface ITokenRefresherService
    {
        /// <summary>
        /// Tries to refresh the current user's access token if required.
        /// </summary>
        /// <param name="refreshToken">The current refresh token.</param>
        /// <param name="expiresAt">The current token expiration information.</param>
        /// <param name="cancellationToken">Notify the cancellation request.</param>
        /// <returns>Token renewal status</returns>
        Task<TokenRefreshResult> TryRefreshTokenIfRequiredAsync(string refreshToken, string expiresAt, CancellationToken cancellationToken);
    }
}
